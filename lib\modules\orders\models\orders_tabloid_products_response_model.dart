import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/core/enuns/product_state_enum.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';

class TabloidProductsResponse extends SqfLiteBase<TabloidProductsResponse> {
  String? corTextoBotaoVisualizacao;
  String? corTextoExpiracao;
  String? dataFim;
  String? dataInicio;
  String? descricao;
  String? descricaoTabloide;
  int? idTabloide;
  bool? mostrarBotaoVisualizacao;
  bool? mostrarDataExpiracao;
  int? posicionamento;
  String? urlImagem;
  double? valorMinimoPedido;

  List<TabloidProductsPrazoPagamentoResponse>? prazoPagamento;
  List<TabloidProductsFamiliasProdutosResponse>? familiasProdutos;
  List<TabloidProductsProdutosResponse>? produtos;
  List<TabloidProductsCombosOfertaResponse>? combosOferta;
  List<TabloidProductsTiposProdutosResponse>? tiposProdutos;

  TabloidProductsResponse({
    this.corTextoBotaoVisualizacao,
    this.corTextoExpiracao,
    this.dataFim,
    this.dataInicio,
    this.descricao,
    this.descricaoTabloide,
    this.idTabloide,
    this.mostrarBotaoVisualizacao,
    this.mostrarDataExpiracao,
    this.posicionamento,
    this.urlImagem,
    this.prazoPagamento,
    this.familiasProdutos,
    this.produtos,
    this.combosOferta,
    this.tiposProdutos,
    this.valorMinimoPedido,
  }) : super(DatabaseModels.tabloidProductsResponse);

  TabloidProductsResponse.fromJson(Map<String, dynamic> json)
    : super(DatabaseModels.tabloidProductsResponse) {
    corTextoBotaoVisualizacao = json['CorTextoBotaoVisualizacao'];
    corTextoExpiracao = json['CorTextoExpiracao'];
    dataFim = json['DataFim'];
    dataInicio = json['DataInicio'];
    descricao = json['Descricao'];
    descricaoTabloide = json['DescricaoTabloide'];
    idTabloide = json['IdTabloide'];
    mostrarBotaoVisualizacao = json['MostrarBotaoVisualizacao'];
    mostrarDataExpiracao = json['MostrarDataExpiracao'];
    posicionamento = json['Posicionamento'];
    urlImagem = json['UrlImagem'];
    valorMinimoPedido = json['ValorMinimoPedido'];
    if (json['PrazoPagamento'] != null) {
      prazoPagamento = <TabloidProductsPrazoPagamentoResponse>[];
      json['PrazoPagamento'].forEach((v) {
        prazoPagamento!.add(TabloidProductsPrazoPagamentoResponse.fromJson(v));
      });
    }
    if (json['FamiliasProdutos'] != null) {
      familiasProdutos = <TabloidProductsFamiliasProdutosResponse>[];
      json['FamiliasProdutos'].forEach((v) {
        familiasProdutos!.add(
          TabloidProductsFamiliasProdutosResponse.fromJson(v),
        );
      });
    }
    if (json['Produtos'] != null) {
      produtos = <TabloidProductsProdutosResponse>[];
      json['Produtos'].forEach((v) {
        produtos!.add(TabloidProductsProdutosResponse.fromJson(v));
      });
    }
    if (json['CombosOferta'] != null) {
      combosOferta = <TabloidProductsCombosOfertaResponse>[];
      json['CombosOferta'].forEach((v) {
        combosOferta!.add(TabloidProductsCombosOfertaResponse.fromJson(v));
      });
    }
    if (json['TiposProdutos'] != null) {
      tiposProdutos = <TabloidProductsTiposProdutosResponse>[];
      json['TiposProdutos'].forEach((v) {
        tiposProdutos!.add(TabloidProductsTiposProdutosResponse.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['CorTextoBotaoVisualizacao'] = corTextoBotaoVisualizacao;
    data['CorTextoExpiracao'] = corTextoExpiracao;
    data['DataFim'] = dataFim;
    data['DataInicio'] = dataInicio;
    data['Descricao'] = descricao;
    data['DescricaoTabloide'] = descricaoTabloide;
    data['IdTabloide'] = idTabloide;
    data['MostrarBotaoVisualizacao'] = mostrarBotaoVisualizacao;
    data['MostrarDataExpiracao'] = mostrarDataExpiracao;
    data['Posicionamento'] = posicionamento;
    data['UrlImagem'] = urlImagem;
    data['ValorMinimoPedido'] = valorMinimoPedido;
    if (prazoPagamento != null) {
      data['PrazoPagamento'] = prazoPagamento!.map((v) => v.toJson()).toList();
    }
    if (familiasProdutos != null) {
      data['FamiliasProdutos'] =
          familiasProdutos!.map((v) => v.toJson()).toList();
    }
    if (produtos != null) {
      data['Produtos'] = produtos!.map((v) => v.toJson()).toList();
    }
    if (combosOferta != null) {
      data['CombosOferta'] = combosOferta!.map((v) => v.toJson()).toList();
    }
    if (tiposProdutos != null) {
      data['TiposProdutos'] = tiposProdutos!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  Future<TabloidProductsResponse?> getFirst({
    required int storeId,
    required String tabloidId,
  }) async {
    var list = await getAll<TabloidProductsResponse>(
      workspaceId: appController.workspace!.workspaceId!,
      userId: appController.userLogged!.userId!,
      storeId: storeId,
      hashCode: tabloidId,
      TabloidProductsResponse.fromJson,
    );
    return list.firstOrNull;
  }

  Future<bool> exists({required int storeId, required String tabloidId}) async {
    var list = await getAll<TabloidProductsResponse>(
      workspaceId: appController.workspace!.workspaceId!,
      userId: appController.userLogged!.userId!,
      storeId: storeId,
      hashCode: tabloidId,
      TabloidProductsResponse.fromJson,
    );
    return list.isNotEmpty;
  }
}

class TabloidProductsPrazoPagamentoResponse {
  int? idPrazoPagamento;
  String? codigo;
  String? descricao;
  int? prazo;
  bool? rep;
  bool? padrao;
  bool? especial;
  bool? repDefault;
  bool? padraoDefault;
  bool? especialDefault;
  List<TabloidProductsDistribuidoresResponse>? distribuidores;
  bool? selecionado;

  TabloidProductsPrazoPagamentoResponse({
    this.idPrazoPagamento,
    this.codigo,
    this.descricao,
    this.prazo,
    this.rep,
    this.padrao,
    this.especial,
    this.repDefault,
    this.padraoDefault,
    this.especialDefault,
    this.distribuidores,
    this.selecionado,
  });

  TabloidProductsPrazoPagamentoResponse.fromJson(Map<String, dynamic> json) {
    idPrazoPagamento = json['IdPrazoPagamento'];
    codigo = json['Codigo'];
    descricao = json['Descricao'];
    prazo = json['Prazo'];
    rep = json['Rep'];
    padrao = json['Padrao'];
    especial = json['Especial'];
    repDefault = json['RepDefault'];
    padraoDefault = json['PadraoDefault'];
    especialDefault = json['EspecialDefault'];
    if (json['Distribuidores'] != null) {
      distribuidores = <TabloidProductsDistribuidoresResponse>[];
      json['Distribuidores'].forEach((v) {
        distribuidores!.add(TabloidProductsDistribuidoresResponse.fromJson(v));
      });
    }
    selecionado = json['Selecionado'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPrazoPagamento'] = idPrazoPagamento;
    data['Codigo'] = codigo;
    data['Descricao'] = descricao;
    data['Prazo'] = prazo;
    data['Rep'] = rep;
    data['Padrao'] = padrao;
    data['Especial'] = especial;
    data['RepDefault'] = repDefault;
    data['PadraoDefault'] = padraoDefault;
    data['EspecialDefault'] = especialDefault;
    if (distribuidores != null) {
      data['Distribuidores'] = distribuidores!.map((v) => v.toJson()).toList();
    }
    data['Selecionado'] = selecionado;
    return data;
  }
}

class TabloidProductsDistribuidoresResponse {
  int? pdvId;
  int? distribuidorId;
  int? ordemDePreferencia;
  int? ordemMelhorAtendimento;
  TabloidProductsDistribuidorResponse? distribuidor;

  TabloidProductsDistribuidoresResponse({
    this.pdvId,
    this.distribuidorId,
    this.ordemDePreferencia,
    this.ordemMelhorAtendimento,
    this.distribuidor,
  });

  TabloidProductsDistribuidoresResponse.fromJson(Map<String, dynamic> json) {
    pdvId = json['PdvId'];
    distribuidorId = json['DistribuidorId'];
    ordemDePreferencia = json['OrdemDePreferencia'];
    ordemMelhorAtendimento = json['OrdemMelhorAtendimento'];
    distribuidor =
        json['Distribuidor'] != null
            ? TabloidProductsDistribuidorResponse.fromJson(json['Distribuidor'])
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['PdvId'] = pdvId;
    data['DistribuidorId'] = distribuidorId;
    data['OrdemDePreferencia'] = ordemDePreferencia;
    data['OrdemMelhorAtendimento'] = ordemMelhorAtendimento;
    if (distribuidor != null) {
      data['Distribuidor'] = distribuidor!.toJson();
    }
    return data;
  }
}

class TabloidProductsDistribuidorResponse {
  int? id;
  bool? ativo;
  double? valorMinimoDePedido;
  String? nomeFantasia;
  String? razaoSocial;
  bool? habilitaContaCorrente;

  TabloidProductsDistribuidorResponse({
    this.id,
    this.ativo,
    this.valorMinimoDePedido,
    this.nomeFantasia,
    this.razaoSocial,
    this.habilitaContaCorrente,
  });

  TabloidProductsDistribuidorResponse.fromJson(Map<String, dynamic> json) {
    id = json['Id'];
    ativo = json['Ativo'];
    valorMinimoDePedido = json['ValorMinimoDePedido'];
    nomeFantasia = json['NomeFantasia'];
    razaoSocial = json['RazaoSocial'];
    habilitaContaCorrente = json['HabilitaContaCorrente'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Id'] = id;
    data['Ativo'] = ativo;
    data['ValorMinimoDePedido'] = valorMinimoDePedido;
    data['NomeFantasia'] = nomeFantasia;
    data['RazaoSocial'] = razaoSocial;
    data['HabilitaContaCorrente'] = habilitaContaCorrente;
    return data;
  }
}

class TabloidProductsFamiliasProdutosResponse {
  int? idFamilia;
  String? descricao;
  String? caminhoFoto;

  TabloidProductsFamiliasProdutosResponse({
    this.idFamilia,
    this.descricao,
    this.caminhoFoto,
  });

  TabloidProductsFamiliasProdutosResponse.fromJson(Map<String, dynamic> json) {
    idFamilia = json['IdFamilia'];
    descricao = json['Descricao'];
    caminhoFoto = json['CaminhoFoto'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdFamilia'] = idFamilia;
    data['Descricao'] = descricao;
    data['CaminhoFoto'] = caminhoFoto;
    return data;
  }
}

class TabloidProductsProdutosResponse {
  int? idProduto;
  String? ean;
  String? descricao;
  String? laboratorio;
  String? familia;
  double? preco;
  bool? destaque;
  String? status;
  List<TabloidProductsFaixasDescontoResponse>? faixasDesconto;
  DateTime? menorDataVigencia;
  int? quantidadeEstoque;
  bool? precoDistribuidor;
  String? caminhoFoto;
  int? idFamilia;
  int? idTipoProduto;
  String? apresentacaoDUN;
  String? dUN;
  int? idProdutoDUN;
  bool? isDemonstraGridPedido;
  List<TabloidProductsFiltrosPersonalizadosResponse>? filtrosPersonalizados;
  MetricaMdtr? metricaMdtr;
  int? quantidade;
  double? desconto;
  double? substituicaoTributaria;
  String? codigo;
  String? principioAtivo;
  double? precoMaximo;
  double? caixaEmbarque;

  TabloidProductsProdutosResponse({
    this.idProduto,
    this.ean,
    this.descricao,
    this.laboratorio,
    this.familia,
    this.preco,
    this.destaque,
    this.status,
    this.faixasDesconto,
    this.menorDataVigencia,
    this.quantidadeEstoque,
    this.precoDistribuidor,
    this.caminhoFoto,
    this.idFamilia,
    this.idTipoProduto,
    this.apresentacaoDUN,
    this.dUN,
    this.idProdutoDUN,
    this.isDemonstraGridPedido,
    this.filtrosPersonalizados,
    this.metricaMdtr,
    this.quantidade,
    this.desconto,
    this.substituicaoTributaria,
    this.codigo,
    this.principioAtivo,
    this.precoMaximo,
    this.caixaEmbarque,
  });

  TabloidProductsProdutosResponse.fromJson(Map<String, dynamic> json) {
    idProduto = json['IdProduto'];
    ean = json['Ean'];
    descricao = json['Descricao'];
    laboratorio = json['Laboratorio'];
    familia = json['Familia'];
    preco = json['Preco'];
    destaque = json['Destaque'];
    status = json['Status'];
    if (json['FaixasDesconto'] != null) {
      faixasDesconto = <TabloidProductsFaixasDescontoResponse>[];
      json['FaixasDesconto'].forEach((v) {
        faixasDesconto!.add(TabloidProductsFaixasDescontoResponse.fromJson(v));
      });
    }
    menorDataVigencia =
        json['MenorDataVigencia'] != null
            ? DateTime.parse(json['MenorDataVigencia'])
            : null;
    quantidadeEstoque = json['QuantidadeEstoque'];
    precoDistribuidor = json['PrecoDistribuidor'];
    caminhoFoto = json['CaminhoFoto'];
    idFamilia = json['IdFamilia'];
    idTipoProduto = json['IdTipoProduto'];
    apresentacaoDUN = json['ApresentacaoDUN'];
    dUN = json['DUN'];
    idProdutoDUN = json['IdProdutoDUN'];
    isDemonstraGridPedido = json['IsDemonstraGridPedido'];
    if (json['FiltrosPersonalizados'] != null) {
      filtrosPersonalizados = <TabloidProductsFiltrosPersonalizadosResponse>[];
      json['FiltrosPersonalizados'].forEach((v) {
        filtrosPersonalizados!.add(
          TabloidProductsFiltrosPersonalizadosResponse.fromJson(v),
        );
      });
    }
    metricaMdtr =
        json['MetricaMdtr'] != null
            ? MetricaMdtr.fromJson(json['MetricaMdtr'])
            : null;
    quantidade = json['Quantidade'];
    desconto = json['Desconto'];
    substituicaoTributaria = json['SubstituicaoTributaria'];
    codigo = json['Codigo'];
    principioAtivo = json['PrincipioAtivo'];
    precoMaximo = json['PrecoMaximo'];
    caixaEmbarque = json['CaixaEmbarque'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdProduto'] = idProduto;
    data['Ean'] = ean;
    data['Descricao'] = descricao;
    data['Laboratorio'] = laboratorio;
    data['Familia'] = familia;
    data['Preco'] = preco;
    data['Destaque'] = destaque;
    data['Status'] = status;
    if (faixasDesconto != null) {
      data['FaixasDesconto'] = faixasDesconto!.map((v) => v.toJson()).toList();
    }
    data['MenorDataVigencia'] = menorDataVigencia?.toIso8601String();
    data['QuantidadeEstoque'] = quantidadeEstoque;
    data['PrecoDistribuidor'] = precoDistribuidor;
    data['CaminhoFoto'] = caminhoFoto;
    data['IdFamilia'] = idFamilia;
    data['IdTipoProduto'] = idTipoProduto;
    data['ApresentacaoDUN'] = apresentacaoDUN;
    data['DUN'] = dUN;
    data['IdProdutoDUN'] = idProdutoDUN;
    data['IsDemonstraGridPedido'] = isDemonstraGridPedido;
    if (filtrosPersonalizados != null) {
      data['FiltrosPersonalizados'] =
          filtrosPersonalizados!.map((v) => v.toJson()).toList();
    }
    if (metricaMdtr != null) {
      data['MetricaMdtr'] = metricaMdtr!.toJson();
    }
    data['Quantidade'] = quantidade;
    data['Desconto'] = desconto;
    data['SubstituicaoTributaria'] = substituicaoTributaria;
    data['Codigo'] = codigo;
    data['PrincipioAtivo'] = principioAtivo;
    data['PrecoMaximo'] = precoMaximo;
    data['CaixaEmbarque'] = caixaEmbarque;
    return data;
  }
}

class TabloidProductsFaixasDescontoResponse {
  int? quantidadeMinima;
  int? quantidadeMaxima;
  double? descontoMinimo;
  double? descontoMaximo;

  TabloidProductsFaixasDescontoResponse({
    this.quantidadeMinima,
    this.quantidadeMaxima,
    this.descontoMinimo,
    this.descontoMaximo,
  });

  TabloidProductsFaixasDescontoResponse.fromJson(Map<String, dynamic> json) {
    quantidadeMinima = json['QuantidadeMinima'];
    quantidadeMaxima = json['QuantidadeMaxima'];
    descontoMinimo = json['DescontoMinimo'];
    descontoMaximo = json['DescontoMaximo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['QuantidadeMinima'] = quantidadeMinima;
    data['QuantidadeMaxima'] = quantidadeMaxima;
    data['DescontoMinimo'] = descontoMinimo;
    data['DescontoMaximo'] = descontoMaximo;
    return data;
  }
}

class TabloidProductsFiltrosPersonalizadosResponse {
  int? idFiltroPersonalizado;
  String? descricao;
  String? descricaoCategoria;
  String? codigo;
  int? idCategoriaFiltroPersonalizado;
  bool? ativo;
  String? icone;
  int? idProduto;

  TabloidProductsFiltrosPersonalizadosResponse({
    this.idFiltroPersonalizado,
    this.descricao,
    this.descricaoCategoria,
    this.codigo,
    this.idCategoriaFiltroPersonalizado,
    this.ativo,
    this.icone,
    this.idProduto,
  });

  TabloidProductsFiltrosPersonalizadosResponse.fromJson(
    Map<String, dynamic> json,
  ) {
    idFiltroPersonalizado = json['IdFiltroPersonalizado'];
    descricao = json['Descricao'];
    descricaoCategoria = json['DescricaoCategoria'];
    codigo = json['Codigo'];
    idCategoriaFiltroPersonalizado = json['IdCategoriaFiltroPersonalizado'];
    ativo = json['Ativo'];
    icone = json['Icone'];
    idProduto = json['IdProduto'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdFiltroPersonalizado'] = idFiltroPersonalizado;
    data['Descricao'] = descricao;
    data['DescricaoCategoria'] = descricaoCategoria;
    data['Codigo'] = codigo;
    data['IdCategoriaFiltroPersonalizado'] = idCategoriaFiltroPersonalizado;
    data['Ativo'] = ativo;
    data['Icone'] = icone;
    data['IdProduto'] = idProduto;
    return data;
  }
}

class TabloidProductsTiposProdutosResponse {
  int? idTipoProduto;
  String? descricao;

  TabloidProductsTiposProdutosResponse({this.idTipoProduto, this.descricao});

  TabloidProductsTiposProdutosResponse.fromJson(Map<String, dynamic> json) {
    idTipoProduto = json['IdTipoProduto'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdTipoProduto'] = idTipoProduto;
    data['Descricao'] = descricao;
    return data;
  }
}

class TabloidProductsCombosOfertaResponse {
  int? idComboOferta;
  String? descricao;
  String? status;
  DateTime? menorDataVigencia;
  List<ProdutosOfertasModel>? produtos;
  double? precoCombo;
  double? precoComboLiquido;
  String? caminhoFoto;
  int? tipoAgrupamentoLojas;
  bool? locked;
  int? qtdy;
  ProductStateEnum? states;
  int? distributorId;
  double? priceOrder;
  double? totalOrder;
  int? orderComboQuantity; // QtdComboPedido
  int? validityComboQuantity; // QtdComboNaVigencia
  bool? orderComboLimit; // LimitarComboPedido
  bool? validityComboLimit; // LimitarComboNaVigencia
  TabloidProductsCombosOfertaResponse({
    this.idComboOferta,
    this.descricao,
    this.status,
    this.menorDataVigencia,
    this.produtos,
    this.precoCombo,
    this.precoComboLiquido,
    this.caminhoFoto,
    this.tipoAgrupamentoLojas,
    this.locked,
    this.qtdy,
    this.states,
    this.distributorId,
    this.priceOrder,
    this.totalOrder,
    this.orderComboQuantity,
    this.validityComboQuantity,
    this.orderComboLimit,
    this.validityComboLimit,
  });

  TabloidProductsCombosOfertaResponse.fromJson(Map<String, dynamic> json) {
    idComboOferta = json['IdComboOferta'];
    descricao = json['Descricao'];
    status = json['Status'];
    menorDataVigencia =
        json['MenorDataVigencia'] != null
            ? DateTime.parse(json['MenorDataVigencia'])
            : null;
    if (json['Produtos'] != null) {
      produtos = <ProdutosOfertasModel>[];
      json['Produtos'].forEach((v) {
        produtos!.add(ProdutosOfertasModel.fromJson(v));
      });
    }
    precoCombo = json['PrecoCombo'];
    precoComboLiquido = json['PrecoComboLiquido'];
    caminhoFoto = json['CaminhoFoto'];
    // tipoAgrupamentoLojas = json['TipoAgrupamentoLojas'];
    locked = json['Locked'];
    qtdy = json['qtdy'] ?? 0;
    orderComboQuantity = json['QtdComboPedido'];
    validityComboQuantity = json['QtdComboNaVigencia'];
    orderComboLimit = json['LimitarComboPedido'];
    validityComboLimit = json['LimitarComboNaVigencia'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdComboOferta'] = idComboOferta;
    data['Descricao'] = descricao;
    data['Status'] = status;
    data['MenorDataVigencia'] = menorDataVigencia?.toIso8601String();
    if (produtos != null) {
      data['Produtos'] = produtos!.map((v) => v.toJson()).toList();
    }
    data['PrecoCombo'] = precoCombo;
    data['PrecoComboLiquido'] = precoComboLiquido;
    data['CaminhoFoto'] = caminhoFoto;
    // data['TipoAgrupamentoLojas'] = tipoAgrupamentoLojas;
    data['Locked'] = locked;
    data['qtdy'] = qtdy;
    data['QtdComboPedido'] = orderComboQuantity;
    data['QtdComboNaVigencia'] = validityComboQuantity;
    data['LimitarComboPedido'] = orderComboLimit;
    data['LimitarComboNaVigencia'] = validityComboLimit;
    return data;
  }
}
