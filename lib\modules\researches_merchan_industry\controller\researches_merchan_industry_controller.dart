import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/researches_merchan_industry/models/researches_merchan_industry_model.dart';
import 'package:pharmalink/modules/researches_merchan_industry/models/researches_merchan_industry_send_model.dart';

class ResearchesMerchanIndustryController
    extends GetxControllerInstrumentado<ResearchesMerchanIndustryController> {
  ResearchesMerchanIndustryDataModel? merchanData;
  int? currentRouteId;

  List<ResearchesMerchanIndustryPesquisaMerchandisingItens> awsersList = [];
  ResearchesMerchanIndustryPesquisaMerchandisingItens? awserModel;
  int tabIndex = 0;

  Future<void> getData(int routeId) async {
    currentRouteId = routeId;
    final researchesBox = await ResearchesMerchanIndustryDataModel().getList(
        workspaceId: appController.workspace!.workspaceId!,
        routeId: currentRouteId!);
    if (researchesBox.isNotEmpty) {
      merchanData = researchesBox.first;
      awsersList = merchanData!.pesquisaMerchandisingItens ?? [];
      Get.toNamed(RoutesPath.researchesMerchanIndustry);
      return;
    }

    final result = await researchesMerchanIndustryApi
        .getResearchesMerchanIndustry(routeId: routeId);
    if (result.error != null) {
      SnackbarCustom.snackbarError(result.error!.message!);
    } else {
      merchanData = result.data!;
      awsersList = merchanData!.pesquisaMerchandisingItens ?? [];
      await dbContext.withControllerAction(this).addData(
          key: DatabaseModels.researchesMerchanIndustryDataModel,
          data: result.data!,
          storeId: routeId,
          workspaceId: appController.workspace!.workspaceId,
          clearCurrentData: true);

      Get.toNamed(RoutesPath.researchesMerchanIndustry);
    }
  }

  Future<void> getDataSync(int routeId) async {
    final result = await researchesMerchanIndustryApi
        .getResearchesMerchanIndustry(routeId: routeId);
    if (result.error != null) {
      SnackbarCustom.snackbarError(result.error!.message!);
    } else {
      if (result.data!.pesquisaMerchandisingItens != null &&
          result.data!.pesquisaMerchandisingItens!.isNotEmpty) {
        result.data!.isSync = SyncEnum.finished;
      } else {
        result.data!.isSync = SyncEnum.none;
      }
      await dbContext.withControllerAction(this).addData(
          key: DatabaseModels.researchesMerchanIndustryDataModel,
          data: result.data!,
          storeId: routeId,
          workspaceId: appController.workspace!.workspaceId,
          clearCurrentData: true);
    }
  }

  void setTabIndexBack() {
    tabIndex--;
    if (tabIndex < 0) {
      tabIndex = 0;
      GetC.close();
    }
    update();
  }

  void setFamily(ResearchesMerchanIndustryFamilias index) {
    awserModel!.idFamilia = index.idFamilia;
    awserModel!.familiaDescricao = index.descricao ?? "";
    tabIndex++;
    update();
  }

  void setMerchandising(ResearchesMerchanIndustryMerchandising index) {
    awserModel!.idMerchandising = index.idMerchandising;
    awserModel!.merchandisingDescricao = index.descricao ?? "";
    tabIndex++;
    update();
  }

  void setTipeActivation(ResearchesMerchanIndustryTipoAtivacao index) {
    awserModel!.idTipoAtivacao = index.idMerchandisingTipoAtivacao;
    awserModel!.tipoAtivacaoDescricao = index.descricao ?? "";
    tabIndex++;
    update();
  }

  void setQuantity(String index) {
    awserModel!.quantidade = int.parse(index);
  }

  String getTitleName() {
    switch (tabIndex) {
      case 0:
        return 'Família';
      case 1:
        return 'Merchandising';
      case 2:
        return 'Tipo de ativação';
      case 3:
        return 'Quantidade';
      default:
        return '';
    }
  }

  Future<void> next() async {
    if (tabIndex < 3) {
      tabIndex++;
    } else {
      await saveQuestion();
    }
  }

  Future<void> saveQuestion() async {
    merchanData!.workspaceId = appController.workspace?.workspaceId;
    merchanData!.routeId = currentRouteId;
    merchanData!.isSync = SyncEnum.awaited;
    awsersList.add(awserModel!);
    merchanData!.pesquisaMerchandisingItens = awsersList;
    await dbContext.withControllerAction(this).addData(
        key: DatabaseModels.researchesMerchanIndustryDataModel,
        data: merchanData,
        workspaceId: appController.workspace!.workspaceId,
        storeId: currentRouteId,
        clearCurrentData: true);
    awserModel = ResearchesMerchanIndustryPesquisaMerchandisingItens();
    tabIndex = 0;
    awsersList = merchanData!.pesquisaMerchandisingItens ?? [];
    update();
    final visitController = Get.find<VisitsController>();
    visitController.refreshVisitsSearch();
    GetC.close();
  }

  void openQuestions() {
    awserModel = ResearchesMerchanIndustryPesquisaMerchandisingItens();
    tabIndex = 0;
    Get.toNamed(RoutesPath.researchesMerchanIndustryQuestion);
  }

  void openMenuAwnser(
      ResearchesMerchanIndustryPesquisaMerchandisingItens awser) {
    showModalBottomSheet(
      context: Get.context!,
      builder: (BuildContext bc) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.all(12.0),
                child: LabelWidget(
                  title: "Deseja excluir esta resposta?",
                  fontSize: DeviceSize.fontSize(16, 20),
                  textColor: Colors.grey,
                ),
              ),
              ListTile(
                title: const Text('Excluir'),
                onTap: () async {
                  merchanData!.pesquisaMerchandisingItens!
                      .removeWhere((element) => element == awser);
                  await dbContext.withControllerAction(this).addData(
                      key: DatabaseModels.researchesMerchanIndustryDataModel,
                      data: merchanData,
                      workspaceId: appController.workspace!.workspaceId,
                      storeId: currentRouteId,
                      clearCurrentData: true);
                  update();
                  GetC.close();
                },
              ),
              ListTile(
                title: const Text('Cancelar'),
                onTap: () async {
                  GetC.close();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> syncResearches() async {
    final listResearches = await ResearchesMerchanIndustryDataModel()
        .getListToSync(workspaceId: appController.workspace!.workspaceId!);

    final listFiltered =
        listResearches.where((e) => e.routeId != null).toList();
    if (listFiltered.isEmpty) return;

    for (var itemSync in listFiltered) {
      final model = ResearchesMerchanIndustrySendModel(
        idRota: itemSync.routeId,
        pesquisaMerchandisingItensRemovidos: [],
        pesquisaMerchandisingItens: itemSync.pesquisaMerchandisingItens!
            .map(
              (e) => ResearchesMerchanIndustrySendPesquisaMerchandisingItens(
                enviado: true,
                dataInclusao: DateTime.now().toString(),
                familiaDescricao: e.familiaDescricao,
                idFamilia: e.idFamilia,
                idMerchandising: e.idMerchandising,
                merchandisingDescricao: e.merchandisingDescricao,
                idTipoAtivacao: e.idTipoAtivacao,
                tipoAtivacaoDescricao: e.tipoAtivacaoDescricao,
                quantidade: e.quantidade,
                idPesquisaMerchandising: e.idPesquisaMerchandising,
              ),
            )
            .toList(),
      );
      final result = await researchesMerchanIndustryApi
          .sendResearchesMerchanIndustry(model: model);
      if (result.error != null) {
        SnackbarCustom.snackbarError(
            "Erro ao sincronizar pesquisa de Merchandising Industria");
        return;
      } else {
        itemSync.isSync = SyncEnum.finished;
      }
    }
  }
}
