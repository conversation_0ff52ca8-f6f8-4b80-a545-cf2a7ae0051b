import 'package:permission_handler/permission_handler.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class PermissionGlobalPage extends StatelessWidget {
  const PermissionGlobalPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PermissionGlobalController>(
      builder: (controller) {
        return SafeArea(
          child: Scaffold(
            backgroundColor: whiteColor,
            body: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Gap(50),
                Image.asset(F.logo),
                const Gap(50),
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: LabelWidget(
                    title:
                        "Por favor, libere os acessos às permissões necessárias.",
                    fontSize: DeviceSize.fontSize(18, 24),
                    textAlign: TextAlign.center,
                  ),
                ), // 2) Texto explicativo
                Expanded(
                  child: FutureBuilder<Map<Permission, PermissionStatus>>(
                    future: controller.checkPermissionsStatus(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const Center(child: CircularProgressIndicator());
                      }
                      if (snapshot.hasData) {
                        return ListView(
                          children: snapshot.data!.entries.map((entry) {
                            return ListTile(
                              title: Text(entry.key
                                  .toString()
                                  .split('.')
                                  .last
                                  .capitalizeFirstLetter()),
                              trailing: Icon(
                                entry.value == PermissionStatus.granted
                                    ? Icons.check
                                    : Icons.close,
                                color: entry.value == PermissionStatus.granted
                                    ? Colors.green
                                    : Colors.red,
                              ),
                            );
                          }).toList(),
                        );
                      }
                      return const Center(
                          child: Text("Erro ao carregar permissões"));
                    },
                  ),
                ), // 3) Indicadores de permissão
              ],
            ),
            bottomNavigationBar: ElevatedButton(
              onPressed: () async {
                Get.toNamed(RoutesPath.initialize);
              },
              child: const Text("Avançar"),
            ),
          ),
        );
      },
    );
  }
}
