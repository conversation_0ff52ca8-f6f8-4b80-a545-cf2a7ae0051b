class ResultError {
  late String error;
  late int code;

  ResultError({required this.error, required this.code});

  ResultError.fromJson(Map<String, dynamic> json) {
    error = json['error'];
    code = json['code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['error'] = error;
    data['code'] = code;
    return data;
  }
}

class ResultLoginError {
  String? error;
  String? errorDescription;
  String? message;

  ResultLoginError({this.error, this.errorDescription, this.message});

  ResultLoginError.fromJson(Map<String, dynamic> json) {
    error = json['error'];
    errorDescription = json['error_description'];
    message = json['Message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['error'] = error;
    data['error_description'] = errorDescription;
    data['Message'] = message;
    return data;
  }
}
