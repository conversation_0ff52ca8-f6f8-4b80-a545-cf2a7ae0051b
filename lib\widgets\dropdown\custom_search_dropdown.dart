import 'package:flutter/material.dart';

class CustomSearchDropdown<T> extends StatelessWidget {
  final List<T> items;
  final T? selected;
  final void Function(T?) onSelected;
  final TextEditingController controller;
  final String Function(T) labelBuilder;
  final String? hintText;
  final IconData? leadingIcon;

  const CustomSearchDropdown({
    super.key,
    required this.items,
    required this.selected,
    required this.onSelected,
    required this.controller,
    required this.labelBuilder,
    this.hintText,
    this.leadingIcon,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownMenu<T>(
      initialSelection: selected,
      width: double.infinity,
      controller: controller,
      requestFocusOnTap: true,
      enableFilter: true,
      hintText: hintText,
      inputDecorationTheme: const InputDecorationTheme(
        filled: true,
        fillColor: Color.fromRGBO(245, 245, 245, 1),
        border: OutlineInputBorder(),
        focusedBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.transparent, width: 1.0),
        ),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: Colors.transparent, width: 1.0),
        ),
      ),
      menuStyle: MenuStyle(
        backgroundColor: WidgetStateProperty.all(Colors.white),
      ),
      onSelected: onSelected,
      dropdownMenuEntries:
          items.map((item) {
            return DropdownMenuEntry<T>(
              value: item,
              label: labelBuilder(item),
              leadingIcon: leadingIcon != null ? Icon(leadingIcon) : null,
            );
          }).toList(),
    );
  }
}
