import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class GeneralSettingsOrderDiscountRegistrationModel
    extends SqfLiteBase<GeneralSettingsOrderDiscountRegistrationModel> {
  int? tipoResponsabilidade;
  int? tipoCadastroDesconto;
  bool? faixasDescontoSomadas;
  bool? travaMaximaReal;
  bool? permiteOutrasFaixasDesconto;
  int? responsavelVigencia;
  bool? travasDescontoSobreposto;
  bool? todosProdutosPedido;
  bool? ordenacaoDesconto;
  bool? visualizaBaseNoPedido;
  bool? mostrarDescontoMedio;
  bool? mostrarDescontoFracionado;
  bool? utilizaCasasDecimais;
  int? expiracaoVigenciaCondicao;
  int? quantidadeDatasProgramadas;
  String? tipoPedido;
  String? idPerfisCadastro;
  List<HierarquiaAplicacaoDesconto>? hierarquiaAplicacaoDesconto;
  List<Faixas>? faixas;
  List<OrdenacaoPedido>? ordenacaoPedido;
  List<TiposPedido>? tiposPedido;
  List<PerfisProgramacaoPedido>? perfisProgramacaoPedido;
  bool? visualizaModalDescFlex;
  bool? paginacaoPedido;
  List<PerfilAprovacaoDescontoBaseETrava>? perfilAprovacaoDescontoBaseETrava;
  bool? aprovacaoDescontoBase;
  bool? aprovacaoTravaDesconto;
  bool? condicaoMixIdeal;
  bool? edicaoDeDescontosPedidoREP;
  bool? exibeObservacaoPedidoEspecial;
  bool? exibeObservacaoPedidoPadrao;
  bool? exibeObservacaoPedidoFlex;
  bool? exibeObservacaoPedidoREP;
  bool? exibirDescontosCargaProdutosPedidoRep;
  bool? exibeEstrategiasAdicionaisREPEsp;
  int? tipoFonteDeDadosMetricas;
  int? tipoDePedidosParaConsultas;
  int? periodoEmDiasParaConsultas;
  bool? limitarReducaoDescontoBase;
  bool? limitarReducaoDescontoMinimoEspecial;
  bool? permiteAdicionarEditarFluxoAprovacao;
  int? condicaoComercialDistribuidor;
  bool? incluirAnexoPedido;
  double? incluirAnexoPedidoTamanhoMb;
  bool? exibePrecoComSTEspecial;
  bool? exibePrecoComSTRep;

  GeneralSettingsOrderDiscountRegistrationModel({
    this.tipoResponsabilidade,
    this.tipoCadastroDesconto,
    this.faixasDescontoSomadas,
    this.travaMaximaReal,
    this.permiteOutrasFaixasDesconto,
    this.responsavelVigencia,
    this.travasDescontoSobreposto,
    this.todosProdutosPedido,
    this.ordenacaoDesconto,
    this.visualizaBaseNoPedido,
    this.mostrarDescontoMedio,
    this.mostrarDescontoFracionado,
    this.utilizaCasasDecimais,
    this.expiracaoVigenciaCondicao,
    this.quantidadeDatasProgramadas,
    this.tipoPedido,
    this.idPerfisCadastro,
    this.hierarquiaAplicacaoDesconto,
    this.faixas,
    this.ordenacaoPedido,
    this.tiposPedido,
    this.perfisProgramacaoPedido,
    this.visualizaModalDescFlex,
    this.paginacaoPedido,
    this.perfilAprovacaoDescontoBaseETrava,
    this.aprovacaoDescontoBase,
    this.aprovacaoTravaDesconto,
    this.condicaoMixIdeal,
    this.edicaoDeDescontosPedidoREP,
    this.exibeObservacaoPedidoEspecial,
    this.exibeObservacaoPedidoPadrao,
    this.exibeObservacaoPedidoFlex,
    this.exibeObservacaoPedidoREP,
    this.exibirDescontosCargaProdutosPedidoRep,
    this.exibeEstrategiasAdicionaisREPEsp,
    this.tipoFonteDeDadosMetricas,
    this.tipoDePedidosParaConsultas,
    this.periodoEmDiasParaConsultas,
    this.limitarReducaoDescontoBase,
    this.limitarReducaoDescontoMinimoEspecial,
    this.permiteAdicionarEditarFluxoAprovacao,
    this.condicaoComercialDistribuidor,
    this.incluirAnexoPedido,
    this.incluirAnexoPedidoTamanhoMb,
    this.exibePrecoComSTEspecial,
    this.exibePrecoComSTRep,
  }) : super(DatabaseModels.generalSettingsOrderDiscountRegistrationModel);

  GeneralSettingsOrderDiscountRegistrationModel.fromJson(
      Map<String, dynamic> json)
      : super(DatabaseModels.generalSettingsOrderDiscountRegistrationModel) {
    tipoResponsabilidade = json['TipoResponsabilidade'];
    tipoCadastroDesconto = json['TipoCadastroDesconto'];
    faixasDescontoSomadas = json['FaixasDescontoSomadas'];
    travaMaximaReal = json['TravaMaximaReal'];
    permiteOutrasFaixasDesconto = json['PermiteOutrasFaixasDesconto'];
    responsavelVigencia = json['ResponsavelVigencia'];
    travasDescontoSobreposto = json['TravasDescontoSobreposto'];
    todosProdutosPedido = json['TodosProdutosPedido'];
    ordenacaoDesconto = json['OrdenacaoDesconto'];
    visualizaBaseNoPedido = json['VisualizaBaseNoPedido'];
    mostrarDescontoMedio = json['MostrarDescontoMedio'];
    mostrarDescontoFracionado = json['MostrarDescontoFracionado'];
    utilizaCasasDecimais = json['UtilizaCasasDecimais'];
    expiracaoVigenciaCondicao = json['ExpiracaoVigenciaCondicao'];
    quantidadeDatasProgramadas = json['QuantidadeDatasProgramadas'];
    tipoPedido = json['TipoPedido'];
    idPerfisCadastro = json['IdPerfisCadastro'];
    if (json['HierarquiaAplicacaoDesconto'] != null) {
      hierarquiaAplicacaoDesconto = <HierarquiaAplicacaoDesconto>[];
      json['HierarquiaAplicacaoDesconto'].forEach((v) {
        hierarquiaAplicacaoDesconto!
            .add(HierarquiaAplicacaoDesconto.fromJson(v));
      });
    }
    if (json['Faixas'] != null) {
      faixas = <Faixas>[];
      json['Faixas'].forEach((v) {
        faixas!.add(Faixas.fromJson(v));
      });
    }
    if (json['OrdenacaoPedido'] != null) {
      ordenacaoPedido = <OrdenacaoPedido>[];
      json['OrdenacaoPedido'].forEach((v) {
        ordenacaoPedido!.add(OrdenacaoPedido.fromJson(v));
      });
    }
    if (json['TiposPedido'] != null) {
      tiposPedido = <TiposPedido>[];
      json['TiposPedido'].forEach((v) {
        tiposPedido!.add(TiposPedido.fromJson(v));
      });
    }
    if (json['PerfisProgramacaoPedido'] != null) {
      perfisProgramacaoPedido = <PerfisProgramacaoPedido>[];
      json['PerfisProgramacaoPedido'].forEach((v) {
        perfisProgramacaoPedido!.add(PerfisProgramacaoPedido.fromJson(v));
      });
    }
    visualizaModalDescFlex = json['VisualizaModalDescFlex'];
    paginacaoPedido = json['PaginacaoPedido'];
    if (json['PerfilAprovacaoDescontoBaseETrava'] != null) {
      perfilAprovacaoDescontoBaseETrava = <PerfilAprovacaoDescontoBaseETrava>[];
      json['PerfilAprovacaoDescontoBaseETrava'].forEach((v) {
        perfilAprovacaoDescontoBaseETrava!
            .add(PerfilAprovacaoDescontoBaseETrava.fromJson(v));
      });
    }
    aprovacaoDescontoBase = json['AprovacaoDescontoBase'];
    aprovacaoTravaDesconto = json['AprovacaoTravaDesconto'];
    condicaoMixIdeal = json['CondicaoMixIdeal'];
    edicaoDeDescontosPedidoREP = json['EdicaoDeDescontosPedidoREP'];
    exibeObservacaoPedidoEspecial = json['ExibeObservacaoPedidoEspecial'];
    exibeObservacaoPedidoPadrao = json['ExibeObservacaoPedidoPadrao'];
    exibeObservacaoPedidoFlex = json['ExibeObservacaoPedidoFlex'];
    exibeObservacaoPedidoREP = json['ExibeObservacaoPedidoREP'];
    exibirDescontosCargaProdutosPedidoRep =
        json['ExibirDescontosCargaProdutosPedidoRep'];
    exibeEstrategiasAdicionaisREPEsp = json['ExibeEstrategiasAdicionaisREPEsp'];
    tipoFonteDeDadosMetricas = json['TipoFonteDeDadosMetricas'];
    tipoDePedidosParaConsultas = json['TipoDePedidosParaConsultas'];
    periodoEmDiasParaConsultas = json['PeriodoEmDiasParaConsultas'];
    limitarReducaoDescontoBase = json['LimitarReducaoDescontoBase'];
    limitarReducaoDescontoMinimoEspecial =
        json['LimitarReducaoDescontoMinimoEspecial'];
    permiteAdicionarEditarFluxoAprovacao =
        json['PermiteAdicionarEditarFluxoAprovacao'];
    condicaoComercialDistribuidor = json['CondicaoComercialDistribuidor'];
    incluirAnexoPedido = json['IncluirAnexoPedido'];
    incluirAnexoPedidoTamanhoMb =
        json['IncluirAnexoPedidoTamanhoMb']?.toDouble();
    exibePrecoComSTEspecial = json['ExibePrecoComSTEspecial'];
    exibePrecoComSTRep = json['ExibePrecoComSTRep'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['TipoResponsabilidade'] = tipoResponsabilidade;
    data['TipoCadastroDesconto'] = tipoCadastroDesconto;
    data['FaixasDescontoSomadas'] = faixasDescontoSomadas;
    data['TravaMaximaReal'] = travaMaximaReal;
    data['PermiteOutrasFaixasDesconto'] = permiteOutrasFaixasDesconto;
    data['ResponsavelVigencia'] = responsavelVigencia;
    data['TravasDescontoSobreposto'] = travasDescontoSobreposto;
    data['TodosProdutosPedido'] = todosProdutosPedido;
    data['OrdenacaoDesconto'] = ordenacaoDesconto;
    data['VisualizaBaseNoPedido'] = visualizaBaseNoPedido;
    data['MostrarDescontoMedio'] = mostrarDescontoMedio;
    data['MostrarDescontoFracionado'] = mostrarDescontoFracionado;
    data['UtilizaCasasDecimais'] = utilizaCasasDecimais;
    data['ExpiracaoVigenciaCondicao'] = expiracaoVigenciaCondicao;
    data['QuantidadeDatasProgramadas'] = quantidadeDatasProgramadas;
    data['TipoPedido'] = tipoPedido;
    data['IdPerfisCadastro'] = idPerfisCadastro;
    if (hierarquiaAplicacaoDesconto != null) {
      data['HierarquiaAplicacaoDesconto'] =
          hierarquiaAplicacaoDesconto!.map((v) => v.toJson()).toList();
    }
    if (faixas != null) {
      data['Faixas'] = faixas!.map((v) => v.toJson()).toList();
    }
    if (ordenacaoPedido != null) {
      data['OrdenacaoPedido'] =
          ordenacaoPedido!.map((v) => v.toJson()).toList();
    }
    if (tiposPedido != null) {
      data['TiposPedido'] = tiposPedido!.map((v) => v.toJson()).toList();
    }
    if (perfisProgramacaoPedido != null) {
      data['PerfisProgramacaoPedido'] =
          perfisProgramacaoPedido!.map((v) => v.toJson()).toList();
    }
    data['VisualizaModalDescFlex'] = visualizaModalDescFlex;
    data['PaginacaoPedido'] = paginacaoPedido;
    if (perfilAprovacaoDescontoBaseETrava != null) {
      data['PerfilAprovacaoDescontoBaseETrava'] =
          perfilAprovacaoDescontoBaseETrava!.map((v) => v.toJson()).toList();
    }
    data['AprovacaoDescontoBase'] = aprovacaoDescontoBase;
    data['AprovacaoTravaDesconto'] = aprovacaoTravaDesconto;
    data['CondicaoMixIdeal'] = condicaoMixIdeal;
    data['EdicaoDeDescontosPedidoREP'] = edicaoDeDescontosPedidoREP;
    data['ExibeObservacaoPedidoEspecial'] = exibeObservacaoPedidoEspecial;
    data['ExibeObservacaoPedidoPadrao'] = exibeObservacaoPedidoPadrao;
    data['ExibeObservacaoPedidoFlex'] = exibeObservacaoPedidoFlex;
    data['ExibeObservacaoPedidoREP'] = exibeObservacaoPedidoREP;
    data['ExibirDescontosCargaProdutosPedidoRep'] =
        exibirDescontosCargaProdutosPedidoRep;
    data['ExibeEstrategiasAdicionaisREPEsp'] = exibeEstrategiasAdicionaisREPEsp;
    data['TipoFonteDeDadosMetricas'] = tipoFonteDeDadosMetricas;
    data['TipoDePedidosParaConsultas'] = tipoDePedidosParaConsultas;
    data['PeriodoEmDiasParaConsultas'] = periodoEmDiasParaConsultas;
    data['LimitarReducaoDescontoBase'] = limitarReducaoDescontoBase;
    data['LimitarReducaoDescontoMinimoEspecial'] =
        limitarReducaoDescontoMinimoEspecial;
    data['PermiteAdicionarEditarFluxoAprovacao'] =
        permiteAdicionarEditarFluxoAprovacao;
    data['CondicaoComercialDistribuidor'] = condicaoComercialDistribuidor;
    data['IncluirAnexoPedido'] = incluirAnexoPedido;
    data['IncluirAnexoPedidoTamanhoMb'] = incluirAnexoPedidoTamanhoMb;
    data['ExibePrecoComSTEspecial'] = exibePrecoComSTEspecial;
    data['ExibePrecoComSTRep'] = exibePrecoComSTRep;
    return data;
  }

  Future<GeneralSettingsOrderDiscountRegistrationModel?> getFirst(
      {required int workspaceId, required String userId}) async {
    var list = await getAll<GeneralSettingsOrderDiscountRegistrationModel>(
        workspaceId: workspaceId,
        userId: userId,
        GeneralSettingsOrderDiscountRegistrationModel.fromJson);
    return list.isEmpty ? null : list.first;
  }

  Future<bool> exists({required int workspaceId}) async {
    var list = await getAll<GeneralSettingsOrderDiscountRegistrationModel>(
        workspaceId: workspaceId,
        GeneralSettingsOrderDiscountRegistrationModel.fromJson);
    return list.isNotEmpty;
  }

  Future<List<GeneralSettingsOrderDiscountRegistrationModel>> getList(
      {required int workspaceId, required String userId}) async {
    var list = await getAll<GeneralSettingsOrderDiscountRegistrationModel>(
        workspaceId: workspaceId,
        userId: userId,
        GeneralSettingsOrderDiscountRegistrationModel.fromJson);
    return list;
  }
}

class HierarquiaAplicacaoDesconto {
  int? id;
  int? ordem;
  String? descricao;

  HierarquiaAplicacaoDesconto({this.id, this.ordem, this.descricao});

  HierarquiaAplicacaoDesconto.fromJson(Map<String, dynamic> json) {
    id = json['Id'];
    ordem = json['Ordem'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Id'] = id;
    data['Ordem'] = ordem;
    data['Descricao'] = descricao;
    return data;
  }
}

class Faixas {
  int? id;
  String? descricao;
  String? nome;
  String? dataInicio;
  String? dataFim;
  String? dataInicioFutura;
  String? dataFimFutura;
  int? tipo;
  bool? aprovacaoGDNInclusao;
  bool? aprovacaoGDNExclusao;
  bool? aprovacaoGCRInclusao;
  bool? aprovacaoGCRExclusao;
  bool? todosProdutos;
  bool? ativo;
  bool? passivo;
  bool? bandeira;
  int? habilitaPedido;
  bool? permiteVariosDescontos;
  bool? habilitaPedidoMinimo;
  double? valorMaximoPedidoMinimo;
  bool? forcaValorPedidoMinimo;

  Faixas(
      {this.id,
      this.descricao,
      this.nome,
      this.dataInicio,
      this.dataFim,
      this.dataInicioFutura,
      this.dataFimFutura,
      this.tipo,
      this.aprovacaoGDNInclusao,
      this.aprovacaoGDNExclusao,
      this.aprovacaoGCRInclusao,
      this.aprovacaoGCRExclusao,
      this.todosProdutos,
      this.ativo,
      this.passivo,
      this.bandeira,
      this.habilitaPedido,
      this.permiteVariosDescontos,
      this.habilitaPedidoMinimo,
      this.valorMaximoPedidoMinimo,
      this.forcaValorPedidoMinimo});

  Faixas.fromJson(Map<String, dynamic> json) {
    id = json['Id'];
    descricao = json['Descricao'];
    nome = json['Nome'];
    dataInicio = json['DataInicio'];
    dataFim = json['DataFim'];
    dataInicioFutura = json['DataInicioFutura'];
    dataFimFutura = json['DataFimFutura'];
    tipo = json['Tipo'];
    aprovacaoGDNInclusao = json['AprovacaoGDNInclusao'];
    aprovacaoGDNExclusao = json['AprovacaoGDNExclusao'];
    aprovacaoGCRInclusao = json['AprovacaoGCRInclusao'];
    aprovacaoGCRExclusao = json['AprovacaoGCRExclusao'];

    todosProdutos = json['TodosProdutos'];
    ativo = json['Ativo'];
    passivo = json['Passivo'];
    bandeira = json['Bandeira'];
    habilitaPedido = json['HabilitaPedido'];
    permiteVariosDescontos = json['PermiteVariosDescontos'];
    habilitaPedidoMinimo = json['HabilitaPedidoMinimo'];
    valorMaximoPedidoMinimo = json['ValorMaximoPedidoMinimo'];
    forcaValorPedidoMinimo = json['ForcaValorPedidoMinimo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Id'] = id;
    data['Descricao'] = descricao;
    data['Nome'] = nome;
    data['DataInicio'] = dataInicio;
    data['DataFim'] = dataFim;
    data['DataInicioFutura'] = dataInicioFutura;
    data['DataFimFutura'] = dataFimFutura;
    data['Tipo'] = tipo;
    data['AprovacaoGDNInclusao'] = aprovacaoGDNInclusao;
    data['AprovacaoGDNExclusao'] = aprovacaoGDNExclusao;
    data['AprovacaoGCRInclusao'] = aprovacaoGCRInclusao;
    data['AprovacaoGCRExclusao'] = aprovacaoGCRExclusao;

    data['TodosProdutos'] = todosProdutos;
    data['Ativo'] = ativo;
    data['Passivo'] = passivo;
    data['Bandeira'] = bandeira;
    data['HabilitaPedido'] = habilitaPedido;
    data['PermiteVariosDescontos'] = permiteVariosDescontos;
    data['HabilitaPedidoMinimo'] = habilitaPedidoMinimo;
    data['ValorMaximoPedidoMinimo'] = valorMaximoPedidoMinimo;
    data['ForcaValorPedidoMinimo'] = forcaValorPedidoMinimo;
    return data;
  }
}

class TiposPedido {
  int? id;
  String? descricao;
  bool? selecionado;

  TiposPedido({this.id, this.descricao, this.selecionado});

  TiposPedido.fromJson(Map<String, dynamic> json) {
    id = json['Id'];
    descricao = json['Descricao'];
    selecionado = json['Selecionado'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Id'] = id;
    data['Descricao'] = descricao;
    data['Selecionado'] = selecionado;
    return data;
  }
}

class OrdenacaoPedido {
  int? id;
  String? descricao;
  bool? selecionado;

  OrdenacaoPedido({this.id, this.descricao, this.selecionado});

  OrdenacaoPedido.fromJson(Map<String, dynamic> json) {
    id = json['Id'];
    descricao = json['Descricao'];
    selecionado = json['Selecionado'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Id'] = id;
    data['Descricao'] = descricao;
    data['Selecionado'] = selecionado;
    return data;
  }
}

class PerfisProgramacaoPedido {
  String? id;
  String? descricao;
  bool? selecionado;

  PerfisProgramacaoPedido({this.id, this.descricao, this.selecionado});

  PerfisProgramacaoPedido.fromJson(Map<String, dynamic> json) {
    id = json['Id'];
    descricao = json['Descricao'];
    selecionado = json['Selecionado'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Id'] = id;
    data['Descricao'] = descricao;
    data['Selecionado'] = selecionado;
    return data;
  }
}

class PerfilAprovacaoDescontoBaseETrava {
  int? idParametrizacaoAprovacaoDescontoBaseETrava;
  String? roleId;
  bool? ativo;
  bool? isDelted;
  String? roleName;

  PerfilAprovacaoDescontoBaseETrava(
      {this.idParametrizacaoAprovacaoDescontoBaseETrava,
      this.roleId,
      this.ativo,
      this.isDelted,
      this.roleName});

  PerfilAprovacaoDescontoBaseETrava.fromJson(Map<String, dynamic> json) {
    idParametrizacaoAprovacaoDescontoBaseETrava =
        json['IdParametrizacaoAprovacaoDescontoBaseETrava'];
    roleId = json['RoleId'];
    ativo = json['Ativo'];
    isDelted = json['IsDelted'];
    roleName = json['RoleName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdParametrizacaoAprovacaoDescontoBaseETrava'] =
        idParametrizacaoAprovacaoDescontoBaseETrava;
    data['RoleId'] = roleId;
    data['Ativo'] = ativo;
    data['IsDelted'] = isDelted;
    data['RoleName'] = roleName;
    return data;
  }
}
