import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';

/// Controller responsável pelos detalhes das rotas de lojas
class StoreRoutesDetailsController extends GetxController {
  StoreRoutesDetailsController();

  /// Detalhes da loja selecionada
  StoresModel? storeDetail;

  /// Método chamado quando o widget está pronto
  ///
  /// Inicializa os detalhes da loja a partir dos argumentos passados
  @override
  void onReady() {
    super.onReady();
    _initializeStoreDetails();
  }

  /// Inicializa os detalhes da loja a partir dos argumentos
  void _initializeStoreDetails() {
    Map<String, dynamic> arguments = Get.arguments;
    if (arguments.containsKey('store')) {
      storeDetail = arguments['store'] as StoresModel?;
      update();
    }
  }
}
