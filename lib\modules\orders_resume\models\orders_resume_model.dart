import 'dart:convert';

import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/stores_parameters/models/distribuidor_model.dart';

class OrdersResumeModel extends SqfLiteBase<OrdersResumeModel> {
  String? clientNumber;
  String? observation;
  bool? isOrderSchedule;
  DateTime? createOrder;
  DateTime? syncOrder;
  List<DateTime>? scheduleDate;
  List<OrdersResumeProductsModel>? products;
  List<OrdersResumeProductsModel>? combos;

  OrdersResumeModel(
      {this.clientNumber,
      this.createOrder,
      this.syncOrder,
      this.scheduleDate,
      this.isOrderSchedule,
      this.observation,
      this.products,
      this.combos})
      : super(DatabaseModels.storeOrders);

  OrdersResumeModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.storeOrders) {
    clientNumber = json['ClientNumber'];
    observation = json['Observation'];
    isOrderSchedule = json['IsOrderSchedule'];
    createOrder = json['createOrder'] != null
        ? DateTime.parse(json['createOrder'])
        : null;
    syncOrder =
        json['syncOrder'] != null ? DateTime.parse(json['syncOrder']) : null;
    // scheduleDate = json['ScheduleDate'].cast<DateTime>();
    scheduleDate = json['ScheduleDate']
        ?.map<DateTime>((date) => DateTime.parse(date))
        .toList();
    if (json['Products'] != null) {
      products = <OrdersResumeProductsModel>[];
      json['Products'].forEach((v) {
        products!.add(OrdersResumeProductsModel.fromJson(v));
      });
    }
    if (json['Combos'] != null) {
      combos = <OrdersResumeProductsModel>[];
      json['Combos'].forEach((v) {
        combos!.add(OrdersResumeProductsModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ClientNumber'] = clientNumber;
    data['Observation'] = observation;
    data['IsOrderSchedule'] = isOrderSchedule;
    data['createOrder'] = createOrder?.toIso8601String();
    data['syncOrder'] = syncOrder?.toIso8601String();
    data['ScheduleDate'] =
        scheduleDate?.map((date) => date.toIso8601String()).toList();
    if (products != null) {
      data['Products'] = products!.map((v) => v.toJson()).toList();
    }
    if (combos != null) {
      data['Combos'] = combos!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  Future<OrdersResumeModel> getFirst(
      {required int workspaceId, int? storeId}) async {
    var list = await getAll<OrdersResumeModel>(
        workspaceId: workspaceId, storeId: storeId, OrdersResumeModel.fromJson);
    return list.first;
  }

  Future<bool> exists(
      {required int workspaceId, int? storeId, DateTime? today}) async {
    var list = await getAll<OrdersResumeModel>(
        workspaceId: workspaceId, storeId: storeId, OrdersResumeModel.fromJson);

    if (today != null) {
      var dateToday = DateTime(today.year, today.month, today.day);

      return list.isNotEmpty
          ? list.any((element) {
              if (element.createOrder == null) return false;
              var elementDate = DateTime(element.createOrder!.year,
                  element.createOrder!.month, element.createOrder!.day);
              return elementDate == dateToday;
            })
          : false;
    } else {
      return false;
    }
  }

  Future<List<OrdersResumeModel>> getList(
      {required int workspaceId, int? storeId}) async {
    var list = await getAll<OrdersResumeModel>(
        workspaceId: workspaceId, storeId: storeId, OrdersResumeModel.fromJson);
    return list;
  }

  String toJsonHash() {
    return HashCreator.generateHash(jsonEncode(this));
  }
}

class OrdersResumeProductsModel {
  int? distributorId;
  String? distributorName;
  int? ordemDePreferencia;
  int? ordemSelected;
  int? ordemMelhorAtendimento;

  DistribuidorModel? distribuidor;
  bool? isSelected;
  OrdersResumeOrderTypePaymentModel? orderTypePayment;
  List<OrdersResumeProductsItemsModel>? items;

  OrdersResumeProductsModel(
      {this.distributorId,
      this.distributorName,
      this.orderTypePayment,
      this.ordemDePreferencia,
      this.ordemMelhorAtendimento,
      this.ordemSelected,
      this.distribuidor,
      this.isSelected,
      this.items});

  OrdersResumeProductsModel.fromJson(Map<String, dynamic> json) {
    distributorId = json['DistributorId'];
    distributorName = json['DistributorName'];
    orderTypePayment = json['OrderTypePayment'] != null
        ? OrdersResumeOrderTypePaymentModel.fromJson(json['OrderTypePayment'])
        : null;
    if (json['Items'] != null) {
      items = <OrdersResumeProductsItemsModel>[];
      json['Items'].forEach((v) {
        items!.add(OrdersResumeProductsItemsModel.fromJson(v));
      });
    }
    distribuidor = json['Distribuidor'] != null
        ? DistribuidorModel.fromJson(json['Distribuidor'])
        : null;
    ordemDePreferencia = json['OrdemDePreferencia'];
    ordemMelhorAtendimento = json['OrdemMelhorAtendimento'];
    ordemSelected = json['OrdemSelected'];
    isSelected = json['IsSelected'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['DistributorId'] = distributorId;
    data['DistributorName'] = distributorName;
    if (orderTypePayment != null) {
      data['OrderTypePayment'] = orderTypePayment!.toJson();
    }
    if (items != null) {
      data['Items'] = items!.map((v) => v.toJson()).toList();
    }
    if (distribuidor != null) {
      data['Distribuidor'] = distribuidor!.toJson();
    }
    data['OrdemDePreferencia'] = ordemDePreferencia;
    data['OrdemMelhorAtendimento'] = ordemMelhorAtendimento;
    data['OrdemSelected'] = ordemSelected;
    data['IsSelected'] = isSelected;
    return data;
  }
}

class OrdersResumeOrderTypePaymentModel {
  int? id;
  String? name;
  String? code;
  OrdersResumeOrderTypePaymentModel({
    this.id,
    this.name,
    this.code,
  });

  OrdersResumeOrderTypePaymentModel.fromJson(Map<String, dynamic> json) {
    id = json['Id'];
    name = json['Name'];
    code = json['Code'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Id'] = id;
    data['Name'] = name;
    data['Code'] = code;
    return data;
  }
}

class OrdersResumeProductsItemsModel {
  int? productId;
  int? productIdDun;
  int? offerComboId;
  String? name;
  String? code;
  String? codelLabel;
  String? photo;
  bool? isEditDiscount;
  double? discountOrigin;
  double? discount;
  double? discountApply;
  double? price;
  double? total;
  int? qtdy;
  int? qtdyReal;
  int? qtdyDun;
  bool? priceDistributor;
  String? dUN;
  List<OrdersResumeProductsItemsModel>? products;

  OrdersResumeProductsItemsModel({
    this.productId,
    this.productIdDun,
    this.offerComboId,
    this.name,
    this.code,
    this.codelLabel,
    this.photo,
    this.isEditDiscount,
    this.discountOrigin,
    this.discount,
    this.discountApply,
    this.price,
    this.total,
    this.dUN,
    this.qtdy,
    this.qtdyReal,
    this.qtdyDun,
    this.priceDistributor,
    this.products,
  });

  OrdersResumeProductsItemsModel.fromJson(Map<String, dynamic> json) {
    productId = json['ProductId'];
    productIdDun = json['ProductIdDun'];
    offerComboId = json['offerComboId'];
    name = json['Name'];
    code = json['Code'];
    codelLabel = json['CodelLabel'];
    photo = json['Photo'];
    isEditDiscount = json['IsEditDiscount'];
    discount = json['Discount'];
    discountOrigin = json['DiscountOrigin'];
    discountApply = json['DiscountApply'];
    price = json['Price'];
    total = json['Total'];
    qtdy = json['Qtdy'];
    qtdyReal = json['QtdyReal'];
    qtdyDun = json['QtdyDun'];
    dUN = json['DUN'];
    priceDistributor = json['PriceDistributor'];
    if (json['Products'] != null) {
      products = <OrdersResumeProductsItemsModel>[];
      json['Products'].forEach((v) {
        products!.add(OrdersResumeProductsItemsModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ProductId'] = productId;
    data['ProductIdDun'] = productIdDun;
    data['offerComboId'] = offerComboId;
    data['Name'] = name;
    data['Code'] = code;
    data['CodelLabel'] = codelLabel;
    data['Photo'] = photo;
    data['IsEditDiscount'] = isEditDiscount;
    data['Discount'] = discount;
    data['DiscountOrigin'] = discountOrigin;
    data['DiscountApply'] = discountApply;
    data['Price'] = price;
    data['Total'] = total;
    data['Qtdy'] = qtdy;
    data['QtdyReal'] = qtdyReal;
    data['QtdyDun'] = qtdyDun;
    data['PriceDistributor'] = priceDistributor;
    data['DUN'] = dUN;
    if (products != null) {
      data['Products'] = products!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
