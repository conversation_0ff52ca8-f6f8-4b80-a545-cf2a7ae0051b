import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';

class PlannedMessageWidget extends StatelessWidget {
  const PlannedMessageWidget({super.key, required this.data});
  final StoresModel data;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showDialog(
          context: context,
          barrierDismissible: true,
          builder: (BuildContext context) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(10),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
                    decoration: BoxDecoration(
                      color: themesController.getPrimaryColor(),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        LabelWidget(
                          title: '*Planejar*',
                          textColor: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: DeviceSize.fontSize(15, 20),
                        ),
                        IconButton(
                          icon: const Icon(Icons.close, color: Colors.white),
                          onPressed: () => Navigator.of(context).pop(),
                        ),
                      ],
                    ),
                  ),
                  const Gap(16),
                  Padding(
                    padding: const EdgeInsets.all(12),
                    child: Column(
                      children: [
                        //Se o parametro de sincronia offline está ativo, exibir o checkbox
                        if (storeRoutesController.hasSyncOffline)
                          LabelWidget(
                            title:
                                '*Para realizar um pedido sem internet:*\nPrimeiro, sincronize as informações de descontos do PDV no seu app. Toque em *Pedido Offline* e depois em *Sincronizar*',
                            textAlign: TextAlign.justify,
                            fontSize: DeviceSize.fontSize(15, 20),
                            textColor: Colors.grey.shade800,
                          ),
                        //Se o parametro de sincronia offline está ativo, exibir o espaçamento
                        if (storeRoutesController.hasSyncOffline) const Gap(16),
                        if (data.visita == true ||
                            data.dataExtra!.isVisita == true)
                          LabelWidget(
                            title:
                                '*Para agendar uma visita:*\nToque na opção *Agendar Visita* e depois em *Sincronizar*, assim, o agendamento será confirmado e não será mais possível excluí-lo do roteiro.',
                            textAlign: TextAlign.justify,
                            fontSize: DeviceSize.fontSize(15, 20),
                            textColor: Colors.grey.shade900,
                          ),
                        const Gap(16),
                      ],
                    ),
                  ),
                ],
              ),
            );
          },
        );
      },
      child: Row(
        children: [
          LabelWidget(
            title: "Planejar".toUpperCase(),
            fontSize: DeviceSize.fontSize(14, 18),
            fontWeight: FontWeight.bold,
            textColor: Colors.orange.shade700,
          ),
          const Gap(10),
          Icon(
            FontAwesomeIcons.circleQuestion,
            size: 16,
            color: Colors.orange.shade700,
          )
        ],
      ),
    );
  }
}
