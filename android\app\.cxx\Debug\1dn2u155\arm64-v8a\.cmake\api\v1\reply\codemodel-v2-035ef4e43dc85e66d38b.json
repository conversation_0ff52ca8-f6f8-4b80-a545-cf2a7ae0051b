{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "G:/ProgramasDart/MarketPlace-MobilePLK/android/app/.cxx/Debug/1dn2u155/arm64-v8a", "source": "C:/Users/<USER>/fvm/versions/3.29.2/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}