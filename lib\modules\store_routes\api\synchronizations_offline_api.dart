import 'dart:convert';
import 'dart:developer';

import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/store_routes/models/get_sync_result_model.dart';
import 'package:pharmalink/modules/store_routes/models/get_sync_status_model.dart';
import 'package:pharmalink/modules/store_routes/models/start_sync_request_model.dart';
import 'package:pharmalink/modules/store_routes/models/start_sync_response_model.dart';

abstract class ISynchronizationsOfflineApi {
  Future<HttpResponse<List<StartSyncResponseModel>>> startSync(
      {required StartSyncRequestModel request});

  Future<HttpResponse<GetSyncStatusModel>> getSyncStatus({required String id});

  Future<HttpResponse<GetSyncResultModel>> getSyncResult(
      {required String id, required int index, required int size});
}

class SynchronizationsOffline<PERSON>pi extends ISynchronizationsOfflineApi {
  final HttpManager _httpManager;
  SynchronizationsOfflineApi(this._httpManager);

  @override
  Future<HttpResponse<List<StartSyncResponseModel>>> startSync(
      {required StartSyncRequestModel request}) async {
    log('startSync  ');
    log(jsonEncode(request));

    return await _httpManager.request<List<StartSyncResponseModel>>(
      path: '',
      baseUrl: '${appController.apiUrlSyncOffline}synchronization/start',
      method: HttpMethods.post,
      body: request.toJson(),
      headers: {
        'Content-type': 'application/json',
        'Authorization': appController.userLogged!.accessToken,
        'Workspace':
            '${appController.userLogged!.userId}:${appController.workspace!.workspaceId!}'
                .toBase64()
      },
      parser: (data) {
        if (data is List) {
          return data
              .map((item) => StartSyncResponseModel.fromJson(item))
              .toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }

  @override
  Future<HttpResponse<GetSyncResultModel>> getSyncResult(
      {required String id, required int index, required int size}) async {
    return await _httpManager.request<GetSyncResultModel>(
      path: '',
      baseUrl:
          '${appController.apiUrlSyncOffline}synchronization/$id?index=$index&size=$size',
      method: HttpMethods.get,
      headers: {
        'Content-type': 'application/json',
        'Authorization': appController.userLogged!.accessToken,
        'Workspace':
            '${appController.userLogged!.userId}:${appController.workspace!.workspaceId!}'
                .toBase64()
      },
      parser: (data) {
        return GetSyncResultModel.fromJson(data);
      },
    );
  }

  @override
  Future<HttpResponse<GetSyncStatusModel>> getSyncStatus(
      {required String id}) async {
    return await _httpManager.request<GetSyncStatusModel>(
      path: '',
      baseUrl: '${appController.apiUrlSyncOffline}synchronization/status/$id',
      method: HttpMethods.get,
      headers: {
        'Content-type': 'application/json',
        'Authorization': appController.userLogged!.accessToken,
        'Workspace':
            '${appController.userLogged!.userId}:${appController.workspace!.workspaceId!}'
                .toBase64()
      },
      parser: (data) {
        return GetSyncStatusModel.fromJson(data);
      },
    );
  }
}
