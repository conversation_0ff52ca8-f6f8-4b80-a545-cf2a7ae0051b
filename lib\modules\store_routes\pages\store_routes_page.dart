import 'package:flutter/foundation.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/store_routes/pages/widgets/store_routes_header_widget.dart';
import 'package:pharmalink/modules/store_routes/pages/widgets/store_routes_tab_widget.dart';

class StoreRoutesPage extends StatelessWidget {
  const StoreRoutesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StoreRoutesController>(
        init: StoreRoutesController(),
        builder: (ctrl) {
          return Scaffold(
            backgroundColor: whiteColor,
            appBar: AppBar(
              backgroundColor: themesController.getPrimaryColor(),
              toolbarHeight: DeviceSize.height(100, 120),
              leadingWidth: 0,
              leading: const SizedBox.shrink(),
              title: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LabelWidget(
                    title: "Rota<PERSON>",
                    fontSize: DeviceSize.fontSize(18, 22),
                    fontWeight: FontWeight.w600,
                    textColor: whiteColor,
                  ),
                  const Gap(4),
                  LabelWidget(
                    title:
                        "Selecione as opções desejadas e clique em atualizar",
                    fontSize: DeviceSize.fontSize(12, 14),
                    textColor: whiteColor,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ),
              actions: [
                if (kDebugMode)
                  IconButton(
                    onPressed: () async {
                      await storeRoutesPlannedController.clearStoresPlanned();
                    },
                    icon: const Icon(Icons.store),
                  ),
              ],
            ),
            body: Column(
              children: [
                // Header
                const StoreRoutesHeaderWidget(),
                // Tab

                Expanded(
                  child: (ctrl.hasSyncOffline || globalParams.hasVisit)
                      ? const StoreRoutesTabWidget()
                      : const StoreRoutesPanelPage(),
                ),
              ],
            ),
          );
        });
  }
}
