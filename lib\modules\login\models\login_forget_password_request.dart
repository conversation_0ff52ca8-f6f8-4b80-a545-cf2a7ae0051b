class LoginForgetPasswordRequest {
  String? login;
  String? caminhoTela;

  LoginForgetPasswordRequest({this.login, this.caminhoTela});

  LoginForgetPasswordRequest.fromJson(Map<String, dynamic> json) {
    login = json['Login'];
    caminhoTela = json['CaminhoTela'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Login'] = login;
    data['CaminhoTela'] = caminhoTela;
    return data;
  }
}
