import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/store_routes_panel/pages/widgets/planned_message_widget.dart';
import 'package:pharmalink/modules/store_routes_panel/pages/widgets/store_routes_panel_card_options_widget.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';

class StoreRoutesPanelCardWidget extends StatelessWidget {
  const StoreRoutesPanelCardWidget({super.key, required this.data});

  final StoresModel data;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StoreRoutesPanelController>(builder: (ctrl) {
      return Card(
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              //header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Flexible(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        LabelWidget(
                          title: data.cNPJ ?? "-",
                          fontWeight: FontWeight.bold,
                          fontSize: DeviceSize.fontSize(15, 18),
                        ),
                        const Gap(5),
                        // Store Name
                        LabelWidget(
                          title: data.razaoSocial ?? "-",
                          fontSize: DeviceSize.fontSize(14, 18),
                          fontWeight: FontWeight.w500,
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      ColorCircle(
                          height: 24,
                          width: 60,
                          value:
                              '${data.visitasRealizadas!}/${data.metaDeVisita!}',
                          color: (data.visitasRealizadas! >= data.metaDeVisita!)
                              ? Colors.green.shade400
                              : data.visitasRealizadas! > 0
                                  ? Colors.yellow.shade600
                                  : Colors.red.shade400),
                      const SizedBox(width: 8),
                      PopupMenuButton<String>(
                        icon: const Icon(Icons.more_vert),
                        onSelected: (String result) {
                          // Handle menu item selection
                        },
                        itemBuilder: (BuildContext context) =>
                            <PopupMenuEntry<String>>[
                          PopupMenuItem<String>(
                            value: 'view_registration',
                            onTap: () async {
                              storeRoutesController.setStoreDetail(data);
                            },
                            child: Row(
                              children: [
                                const Icon(
                                  FontAwesomeIcons.hospital,
                                  size: 16,
                                ),
                                const Gap(8),
                                Flexible(
                                  child: LabelWidget(
                                    title: 'Visualizar Cadastro',
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    fontSize: DeviceSize.fontSize(15, 18),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          //Se o parametro de sincronia offline está ativo, exibir o checkbox
                          if (storeRoutesController.hasSyncOffline)
                            PopupMenuItem<String>(
                              value: 'update_commercial_conditions',
                              onTap: () async {
                                await storeRoutesController
                                    .updateStoreCommercialConditions(data);
                              },
                              child: Row(
                                children: [
                                  const Icon(
                                    FontAwesomeIcons.arrowsRotate,
                                    size: 16,
                                  ),
                                  const Gap(8),
                                  Flexible(
                                    child: LabelWidget(
                                      title: data.dataExtra!.offlineStatus == 3
                                          ? 'Atualizar Condições Comerciais'
                                          : 'Baixar Condições (Pedidos Offline)',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      fontSize: DeviceSize.fontSize(15, 18),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          PopupMenuItem<String>(
                            value: 'show_on_map',
                            onTap: () async {
                              storeRoutesController.openAddress(data);
                            },
                            child: Row(
                              children: [
                                const Icon(
                                  FontAwesomeIcons.mapLocationDot,
                                  size: 16,
                                ),
                                const Gap(8),
                                Flexible(
                                  child: LabelWidget(
                                    title: 'Mostrar no Mapa',
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    fontSize: DeviceSize.fontSize(15, 18),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),

              const Gap(10),
              // Store Address
              LabelWidget(
                title: data.enderecoPdv ?? "-",
                fontSize: DeviceSize.fontSize(13, 15),
              ),
              const Gap(10),
              // Last Order
              LabelWidget(
                title: "*Último Pedido*: ${data.dataUltimoPedido ?? "-"}",
                fontSize: DeviceSize.fontSize(13, 16),
              ),
              const Gap(10),
              // Make Order
              if (data.pedido == true)
                PrimaryButtonWidget(
                  titleButtom: storeRoutesController.getButtonOrderTitle(false),
                  titleFontSize: DeviceSize.fontSize(16, 20),
                  onTap: () async {
                    await storeRoutesController.orderClick(data);
                  },
                ),
              if (storeRoutesController.hasSyncOffline || data.visita == true)
                const Gap(10),
              // Planned Message
              if (storeRoutesController.hasSyncOffline || data.visita == true)
                PlannedMessageWidget(data: data),
              //Options
              if (storeRoutesController.hasSyncOffline || data.visita == true)
                StoreRoutesPanelCardOptionsWidget(data: data),
            ],
          ),
        ),
      );
    });
  }
}
