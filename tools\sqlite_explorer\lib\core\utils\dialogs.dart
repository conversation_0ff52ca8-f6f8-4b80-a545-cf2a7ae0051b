import 'package:flutter/material.dart';
import 'package:get/get.dart';

class Dialogs {
  static Future<void> info(String title, String message,
      {String? buttonName, VoidCallback? buttonOnPressed}) async {
    return showDialog<void>(
      context: Get.context!,
      barrierDismissible:
          false, // impede o usuário de fechar o diálogo ao tocar fora dele
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                parseStyledText(
                    message), // Usando a função para texto estilizado
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: buttonOnPressed ??
                  () {
                    Get.back();
                  },
              child: Text(buttonName ?? 'OK'),
            ),
          ],
        );
      },
    );
  }

  static Future<void> confirm(String title, String message,
      {String? buttonNameOk,
      String? buttonNameCancel,
      VoidCallback? onPressedOk,
      VoidCallback? onPressedCancel}) async {
    return showDialog<void>(
      context: Get.context!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                parseStyledText(
                    message), // Usando a função para texto estilizado
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              onPressed: onPressedCancel ??
                  () {
                    Get.back();
                  },
              child: Text(buttonNameCancel ?? 'Não Obrigado'.toUpperCase()),
            ),
            TextButton(
              onPressed: onPressedOk ??
                  () {
                    Get.back();
                  },
              child: Text(buttonNameOk ?? 'Sim'.toUpperCase()),
            ),
          ],
        );
      },
    );
  }

  static RichText parseStyledText(String text) {
    final textSpans = <TextSpan>[];
    final regex = RegExp(r'\*(.*?)\*');
    final matches = regex.allMatches(text);

    int lastMatchEnd = 0;

    for (final match in matches) {
      if (match.start > lastMatchEnd) {
        textSpans.add(TextSpan(
            text: text.substring(lastMatchEnd, match.start),
            style: const TextStyle(fontWeight: FontWeight.normal)));
      }

      textSpans.add(TextSpan(
          text: match.group(1),
          style: const TextStyle(fontWeight: FontWeight.bold)));

      lastMatchEnd = match.end;
    }

    if (lastMatchEnd < text.length) {
      textSpans.add(TextSpan(
          text: text.substring(lastMatchEnd),
          style: const TextStyle(fontWeight: FontWeight.normal)));
    }

    return RichText(
      text: TextSpan(
        style: const TextStyle(
            color: Colors.black, fontSize: 16), // Cor padrão do texto
        children: textSpans,
      ),
    );
  }
}
