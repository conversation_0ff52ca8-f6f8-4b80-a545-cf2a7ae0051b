import 'package:intl/intl.dart';
import 'package:pharmalink/modules/routes/models/pdvs_dia_model.dart';

class RoutesSaveModel {
  String? userId;
  DateTime? data;
  List<PdvsDiaModel>? pdvsDia;
  RoutesSaveModel({this.userId, this.data, this.pdvsDia});

  RoutesSaveModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    data = json['data'] != null ? DateTime.parse(json['data']) : null;
    if (json['pdvsDia'] != null) {
      pdvsDia = <PdvsDiaModel>[];
      json['pdvsDia'].forEach((v) {
        pdvsDia!.add(PdvsDiaModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['userId'] = userId;

    final dateFormat = DateFormat('yyyy-MM-dd');
    data['data'] = this.data != null ? dateFormat.format(this.data!) : null;

    if (pdvsDia != null) {
      data['pdvsDia'] = pdvsDia!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
