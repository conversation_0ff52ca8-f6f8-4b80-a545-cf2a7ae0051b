abstract class AppImages {
  static const workspace = 'assets/images/workspace.svg';
  static const productGeneric = "assets/images/pedidos/produto_generico.png";
  static const syncVisitas = 'assets/images/sync/visitas.svg';
  static const syncConteudo = 'assets/images/sync/conteudo.svg';
  static const syncRotas = 'assets/images/sync/rotas.svg';
  static const syncFactory = 'assets/images/sync/factory.svg';
  static const syncShop = 'assets/images/sync/shop.svg';
  static const syncDocumentSettings =
      'assets/images/sync/document-settings.svg';
  static const syncInfoVisit = 'assets/images/sync/info-visit.svg';
  static const syncPedido = 'assets/images/sync/pedido.svg';
  static const syncTheme = 'assets/images/sync/theme.svg';
  static const orderDefault = 'assets/images/pedidos/pedido_padrao.svg';
  static const orderRep = 'assets/images/pedidos/pedido_rep.svg';
  static const orderSpecial = 'assets/images/pedidos/pedido_especial.svg';
  static const researchIndustry = 'assets/images/research/product-industry.svg';
  static const researchShare = 'assets/images/research/pesquisas-branco.svg';
  static const researchConcurrent =
      'assets/images/research/product-concurrent.svg';
  static const researchMerchanIndustry =
      'assets/images/research/merchan-industry.svg';
  static const researchMerchanConcurrent =
      'assets/images/research/merchan-concurrent.svg';
  static const researchComplement = 'assets/images/research/complement.svg';
  static const researchTrade = 'assets/images/research/trade.svg';
}
