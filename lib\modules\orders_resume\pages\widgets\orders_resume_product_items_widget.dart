import 'package:cached_network_image/cached_network_image.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/orders_resume/models/orders_resume_model.dart';
import 'package:pharmalink/modules/stores_parameters/enuns/type_order_enum.dart';

class OrdersResumeProductItemsWidget extends StatelessWidget {
  final OrdersResumeProductsItemsModel item;

  const OrdersResumeProductItemsWidget({
    super.key,
    required this.item,
  });
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 0.w, vertical: 8.h),
        child: Column(
          children: [
            Row(
              children: [
                SizedBox(
                  width: 60,
                  child: item.photo != null
                      ? CachedNetworkImage(
                          imageUrl: item.photo ?? "",
                          placeholder: (context, url) =>
                              Image.asset(AppImages.productGeneric),
                          errorWidget: (context, url, error) =>
                              Image.asset(AppImages.productGeneric))
                      : Image.asset(AppImages.productGeneric),
                ),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LabelWidget(
                        title: item.name ?? "-",
                        fontSize: DeviceSize.fontSize(15, 18),
                        fontWeight: FontWeight.bold,
                      ),
                      LabelWidget(
                        title: "${item.codelLabel}: ${item.code}",
                        fontSize: DeviceSize.fontSize(13, 16),
                      ),
                    ],
                  ),
                )
              ],
            ),
            if (globalParams.getTypeOrderId() == TyperOrderEnum.especial)
              _rowSpecial()
            else if (globalParams.getTypeOrderId() == TyperOrderEnum.rep)
              _rowRep()
            else
              _rowNormal(),
          ],
        ),
      ),
    );
  }

  Widget _rowSpecial() {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TilesInfoWidget(
            title: "Desconto",
            value: item.discount?.formatPercent() ?? "-",
            titleFontSize: DeviceSize.fontSize(10, 13),
            valueFontSize: DeviceSize.fontSize(13, 16),
            hasDivider: false,
            valueFontWeight: FontWeight.bold,
          ),
          TilesInfoWidget(
            title: "Desconto aplicado",
            value: item.discountApply?.formatPercent() ?? "-",
            titleFontSize: DeviceSize.fontSize(10, 13),
            valueFontSize: DeviceSize.fontSize(13, 16),
            hasDivider: false,
            valueFontWeight: FontWeight.bold,
          ),
          TilesInfoWidget(
            title: "Quantidade",
            value: item.qtdy?.toString() ?? "-",
            titleFontSize: DeviceSize.fontSize(10, 13),
            valueFontSize: DeviceSize.fontSize(13, 16),
            hasDivider: false,
            valueFontWeight: FontWeight.bold,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TilesInfoWidget(
                title: "De",
                value: item.price!.formatReal(),
                isRitchText: true,
                titleFontSize: DeviceSize.fontSize(10, 13),
                valueFontSize: DeviceSize.fontSize(13, 16),
                hasDivider: false,
                crossAxisAlignment: CrossAxisAlignment.start,
              ),
              TilesInfoWidget(
                title: "Por",
                value: item.total!.formatReal(),
                titleFontSize: DeviceSize.fontSize(10, 13),
                valueFontSize: DeviceSize.fontSize(13, 16),
                hasDivider: false,
                valueFontWeight: FontWeight.bold,
                crossAxisAlignment: CrossAxisAlignment.start,
              ),
            ],
          ),
          IconButton(
              onPressed: () {
                ordersResumeController.removeProduct(item);
              },
              icon: Icon(
                FontAwesomeIcons.trash,
                color: Colors.red,
                size: 18.w,
              ))
        ],
      ),
    );
  }

  Widget _rowNormal() {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TilesInfoWidget(
            title: "Desconto",
            value: item.discount?.formatPercent() ?? "-",
            titleFontSize: DeviceSize.fontSize(10, 13),
            valueFontSize: DeviceSize.fontSize(13, 16),
            hasDivider: false,
            valueFontWeight: FontWeight.bold,
          ),
          TilesInfoWidget(
            title: "Quantidade",
            value: item.qtdy?.toString() ?? "-",
            titleFontSize: DeviceSize.fontSize(10, 13),
            valueFontSize: DeviceSize.fontSize(13, 16),
            hasDivider: false,
            valueFontWeight: FontWeight.bold,
          ),
          Column(
            children: [
              TilesInfoWidget(
                title: "De",
                value: item.price!.formatReal(),
                isRitchText: true,
                titleFontSize: DeviceSize.fontSize(10, 13),
                valueFontSize: DeviceSize.fontSize(13, 16),
                hasDivider: false,
                crossAxisAlignment: CrossAxisAlignment.start,
              ),
              TilesInfoWidget(
                title: "Por",
                value: item.total!.formatReal(),
                titleFontSize: DeviceSize.fontSize(10, 13),
                valueFontSize: DeviceSize.fontSize(13, 16),
                hasDivider: false,
                valueFontWeight: FontWeight.bold,
                crossAxisAlignment: CrossAxisAlignment.start,
              ),
            ],
          ),
          IconButton(
              onPressed: () {
                ordersResumeController.removeProduct(item);
              },
              icon: Icon(
                FontAwesomeIcons.trash,
                color: Colors.red,
                size: 18.w,
              ))
        ],
      ),
    );
  }

  Widget _rowRep() {
    return Padding(
      padding: const EdgeInsets.all(10.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TilesInfoWidget(
            title: "Desconto",
            value: item.discount?.formatPercent() ?? "-",
            titleFontSize: DeviceSize.fontSize(10, 13),
            valueFontSize: DeviceSize.fontSize(13, 16),
            hasDivider: false,
            valueFontWeight: FontWeight.bold,
          ),
          TilesInfoWidget(
            title: "Desconto aplicado",
            value: item.discountApply?.formatPercent() ?? "-",
            titleFontSize: DeviceSize.fontSize(10, 13),
            valueFontSize: DeviceSize.fontSize(13, 16),
            hasDivider: false,
            valueFontWeight: FontWeight.bold,
          ),
          TilesInfoWidget(
            title: "Quantidade",
            value: item.qtdy?.toString() ?? "-",
            titleFontSize: DeviceSize.fontSize(10, 13),
            valueFontSize: DeviceSize.fontSize(13, 16),
            hasDivider: false,
            valueFontWeight: FontWeight.bold,
          ),
          Column(
            children: [
              TilesInfoWidget(
                title: "De",
                value: item.price!.formatReal(),
                isRitchText: true,
                titleFontSize: DeviceSize.fontSize(10, 13),
                valueFontSize: DeviceSize.fontSize(13, 16),
                hasDivider: false,
                crossAxisAlignment: CrossAxisAlignment.start,
              ),
              TilesInfoWidget(
                title: "Por",
                value: item.total!.formatReal(),
                titleFontSize: DeviceSize.fontSize(10, 13),
                valueFontSize: DeviceSize.fontSize(13, 16),
                hasDivider: false,
                valueFontWeight: FontWeight.bold,
                crossAxisAlignment: CrossAxisAlignment.start,
              ),
            ],
          ),
          IconButton(
              onPressed: () {
                ordersResumeController.removeProduct(item);
              },
              icon: Icon(
                FontAwesomeIcons.trash,
                color: Colors.red,
                size: 18.w,
              ))
        ],
      ),
    );
  }
}
