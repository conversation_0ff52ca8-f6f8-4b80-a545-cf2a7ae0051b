import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class CheckBoxHorizontalWidget extends StatelessWidget {
  const CheckBoxHorizontalWidget({
    super.key,
    required this.items,
    this.disable,
  });
  final List<CheckboxItem> items;
  final bool? disable;
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: items.map((item) {
          return Row(
            children: [
              Checkbox(
                value: item.value,
                onChanged: disable == true ? null : item.onChanged,
                tristate: false,
                checkColor: themesController.getMenuColor(),
                activeColor: themesController.getIconColor(),
              ),
              CustomInkWell(
                  onTap: disable == true
                      ? null
                      : () {
                          item.onChanged(!item.value);
                        },
                  child: LabelWidget(
                    title: item.label,
                    fontSize: item.labelSize ?? DeviceSize.fontSize(12, 14),
                  )),
            ],
          );
        }).toList(),
      ),
    );
  }
}
