import 'package:pharmalink/exports/basic_exports.dart';

class OrderInfoRow extends StatelessWidget {
  final String title;
  final TextEditingController controller;
  final Function(String)? onChanged;
  final IconData? trailingIcon;
  final Function()? trailingTap;
  final String? hintText;
  final TextInputType? keyboardType;

  const OrderInfoRow({
    super.key,
    required this.controller,
    required this.title,
    this.hintText,
    this.trailingIcon,
    this.trailingTap,
    this.onChanged,
    this.keyboardType,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          LabelWidget(title: title, fontSize: 14.sp),
          30.toWidthSpace(),
          Flexible(
            child: CustomTextFillField(
              controller: controller,
              keyboardType: keyboardType,
              hintText: hintText,
              onChanged: onChanged,
              trailingIcon: trailingIcon,
              trailingTap: trailingTap,
            ),
          ),
        ],
      ),
    );
  }
}
