import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/order_payment_type/controller/order_payment_type_controller.dart';
import 'package:pharmalink/modules/order_types/controller/order_types_controller.dart';
import 'package:pharmalink/modules/orders/controller/orders_controller.dart';
import 'package:pharmalink/modules/orders/controller/orders_controller_part_offline.dart';
import 'package:pharmalink/modules/orders/models/orders_footer_model.dart';
import 'package:pharmalink/modules/orders_resume/controller/orders_resume_controller.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';
import 'package:pharmalink/modules/orders_resume/models/orders_resume_model.dart';

class OrdersControllerPartEdit {
  static Future<void> onEditOrder(OrdersController ctrl, String orderId) async {
    final loading = PlkLoading();
    loading.show(title: AppStrings.load);
    ctrl.isEditOrder = true;
    ctrl.currendEditOrderId = orderId;
    orderTypesController = Get.find<OrderTypesController>();
    ordersResumeController = Get.find<OrdersResumeController>();
    orderPaymentTypeController = Get.find<OrderPaymentTypeController>();
    ctrl.filterSelectedString = "";
    ctrl.searchFilter = "";
    ctrl.footer = OrdersFooterModel(
      qtyReal: 0,
      totalApresentation: 0,
      totalNet: 0,
      totalUnits: 0,
      totalGross: 0,
      discount: 0,
    );

    ctrl.syncOrders = [];
    ctrl.syncOrders = await SyncronizationModel().getList(
        workspaceId: appController.workspace!.workspaceId!, storeId: null);

    if (ctrl.syncOrders.any((element) => element.transactionKey == orderId)) {
      ctrl.syncOrderEdit = ctrl.syncOrders
          .where((element) => element.transactionKey == orderId)
          .first;
      //Recuperar os dados do PDV
      await ctrl.getPdvData();

      globalParams.order.typeOrderId =
          ctrl.syncOrderEdit!.parameters!.typeOrderId!;
      ctrl.update();
    } else {
      GetC.close();
      return;
    }
    //recupera os itens do pedido
    ctrl.editItensOrder = getItensDoPedido(ctrl.syncOrderEdit!);
    ctrl.editItensComboOrder = getItensDoPedidoCombo(ctrl.syncOrderEdit!);
    ctrl.editDistributors = ctrl.getDistributorList(ctrl.syncOrderEdit!);
    // Mostra um diálogo de confirmação
    await generalParameterizationController.getData();
    await generalParameterizationController.getFilterCustom();
    ctrl.filterDefaults = await ctrl.loadGeneralParameters();
    loading.hide();

    if (!ctrl.isOfflineData) {
      await Dialogs.confirm(
        AppStrings.attention,
        AppStrings.editOrderMessage1,
        buttonNameOk: "Atualizar".toUpperCase(),
        buttonNameCancel: "Cancelar".toUpperCase(),
        onPressedCancel: () async {
          GetC.close();
          Get.until((route) => Get.currentRoute == RoutesPath.reportOrders);
        },
        onPressedOk: () async {
          GetC.close();
          await ctrl.loadInfoEditOrder(orderId);
        },
      );
    } else {
      await OrdersControllerPartOffline.loadInfoEditOrderOffline(ctrl, orderId);
    }
  }

  static List<OrdersResumeProductsItemsModel> getItensDoPedido(
      SyncronizationModel orderEdit) {
    List<OrdersResumeProductsItemsModel> itensDoPedido = [];

    for (var info in orderEdit.payLoad!.backupOrder!.products!) {
      itensDoPedido.addAll(info.items!);
    }

    return itensDoPedido;
  }

  static List<OrdersResumeProductsItemsModel> getItensDoPedidoCombo(
      SyncronizationModel orderEdit) {
    List<OrdersResumeProductsItemsModel> itensDoPedido = [];

    for (var info in orderEdit.payLoad!.backupOrder!.combos!) {
      itensDoPedido.addAll(info.items!);
    }
    return itensDoPedido;
  }
}
