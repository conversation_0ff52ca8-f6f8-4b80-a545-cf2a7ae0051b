import 'package:pharmalink/core/extensions/export.dart';
import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/core/http_result/http_result.dart';
import 'package:pharmalink/core/utils/constants.dart';
import 'package:pharmalink/modules/routes/models/list_routes_model.dart';
import 'package:pharmalink/modules/routes/models/routes_list_request_model.dart';
import 'package:pharmalink/modules/routes/models/routes_save_model.dart';

abstract class IRoutesApi {
  Future<HttpResult<bool>> saveRoutes({required RoutesSaveModel model});

  Future<HttpResult<List<ListRoutesModel>>> getRoutesList(
      {required RoutesListRequestModel model});
}

class RoutesApi extends IRoutesApi {
  final HttpManager _httpManager;
  RoutesApi(this._httpManager);

  @override
  Future<HttpResult<bool>> saveRoutes({required RoutesSaveModel model}) async {
    final result = await _httpManager.restRequest(
      url: 'visitas/salvarRota',
      method: HttpMethods.post,
      body: model.toJson(),
    );

    if (result.statusCode!.isStatusOk()) {
      return HttpResult.sucess(true);
    } else {
      return HttpResult.error(getOthersStatusCodes(result));
    }
  }

  @override
  Future<HttpResult<List<ListRoutesModel>>> getRoutesList(
      {required RoutesListRequestModel model}) async {
    final result = await _httpManager.restRequest(
      url: 'visitas/listarPlanejamentosVisita',
      method: HttpMethods.post,
      body: model.toJson(),
    );

    if (result.data.isNotEmpty && result.statusCode!.isStatusOk()) {
      if (result.data is List) {
        final response = (result.data as List)
            .map((item) => ListRoutesModel.fromJson(item))
            .toList();

        return HttpResult.sucess(response);
      } else {
        return HttpResult.error(getOthersStatusCodes(result));
      }
    } else {
      return HttpResult.error(getOthersStatusCodes(result));
    }
  }
}
