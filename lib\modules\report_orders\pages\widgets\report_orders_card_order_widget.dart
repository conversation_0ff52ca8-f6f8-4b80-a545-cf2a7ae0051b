import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/order_types/order_type_colors.dart';
import 'package:pharmalink/modules/orders_resume/models/file_attachment.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';

class ReportOrdersCardOrdersWidget extends StatelessWidget {
  const ReportOrdersCardOrdersWidget({
    super.key,
    required this.title,
    required this.state,
    required this.stateColor,
    required this.date,
    required this.orderId,
    required this.canEdit,
    required this.isOnline,
    required this.data,
    this.fileAttachmentStatus,
    this.canIncludeFile = false,
  });
  final String title;
  final String state;
  final Color stateColor;
  final String date;
  final String orderId;
  final bool canEdit;
  final bool isOnline;
  final bool canIncludeFile;
  final SyncronizationModel data;
  final FileAttachmentStatus? fileAttachmentStatus;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReportOrdersController>(builder: (ctrl) {
      return SizedBox(
        width: 300,
        child: Card(
          elevation: 5,
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          LabelWidget(
                            title: title,
                            fontWeight: FontWeight.bold,
                            fontSize: DeviceSize.fontSize(14, 17),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          LabelWidget(
                            title: data.storeData?.cNPJ ?? "",
                            fontSize: DeviceSize.fontSize(12, 15),
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        ctrl.openMenuOrders(
                            orderId, canEdit, isOnline, canIncludeFile);
                      },
                      icon: Icon(
                        FontAwesomeIcons.ellipsisVertical,
                        color: Colors.black,
                        size: DeviceSize.fontSize(16, 21),
                      ),
                    )
                  ],
                ),
                Flexible(
                  child: Align(
                    alignment: Alignment.topLeft,
                    child: LabelWidget(
                      title: data.storeData?.enderecoPdv ?? "",
                      fontSize: DeviceSize.fontSize(11, 13),
                    ),
                  ),
                ),
                const Gap(5),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    LabelWidget(
                      title: "Data do Pedido:",
                      fontSize: DeviceSize.fontSize(12, 14),
                    ),
                    const Gap(5),
                    LabelWidget(
                      title: date,
                      fontSize: DeviceSize.fontSize(11, 13),
                    )
                  ],
                ),
                const Gap(5),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    LabelWidget(
                      title: "Valor Total Líquido:",
                      fontSize: DeviceSize.fontSize(12, 14),
                    ),
                    const Gap(5),
                    LabelWidget(
                      title: ctrl.getOrderTotal(data),
                      fontSize: DeviceSize.fontSize(11, 13),
                    )
                  ],
                ),
                const Gap(10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: OrderTypeColors.getBackgroundColor(
                            data.typeOrder ?? 1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.all(5),
                      child: LabelWidget(
                        title: ctrl.getTypeOrderName(data.typeOrder),
                        textColor:
                            OrderTypeColors.getTextColor(data.typeOrder ?? 1),
                        fontSize: DeviceSize.fontSize(11, 13),
                      ),
                    ),
                    const Gap(5),
                    Flexible(
                      child: Align(
                        alignment: Alignment.centerRight,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            if (fileAttachmentStatus != null)
                              Icon(
                                FontAwesomeIcons.solidFile,
                                size: 14.sp,
                                color: fileAttachmentStatus ==
                                        FileAttachmentStatus.waiting
                                    ? Colors.amber[300]
                                    : fileAttachmentStatus ==
                                            FileAttachmentStatus.synced
                                        ? Colors.green
                                        : Colors.red,
                              ),
                            if (fileAttachmentStatus != null) const Gap(5),
                            Icon(
                              FontAwesomeIcons.solidCircle,
                              size: 14.sp,
                              color: stateColor,
                            ),
                            const Gap(10),
                            Flexible(
                                child: LabelWidget(
                              title: state,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              fontSize: DeviceSize.fontSize(12, 14),
                            )),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
