import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_send_model.dart';

class ResearchesComplementaryModel
    extends SqfLiteBase<ResearchesComplementaryModel> {
  int? workspaceId;
  int? routeId;
  int? isSync;
  int? idPesquisa;
  String? titulo;
  String? inicioVigencia;
  String? fimVigencia;
  bool? podeSerRespondidaMaisDeUmaVezPorLoja;
  bool? isAnswered;
  List<ResearchesComplementaryRotas>? rotas;
  List<ResearchesComplementaryPerguntas>? perguntas;
  bool? isCompleteAnswered;
  DateTime? synchronizationReicivingDate;

  ResearchesComplementaryModel(
      {this.workspaceId,
      this.routeId,
      this.isSync,
      this.idPesquisa,
      this.titulo,
      this.inicioVigencia,
      this.fimVigencia,
      this.podeSerRespondidaMaisDeUmaVezPorLoja,
      this.rotas,
      this.perguntas,
      this.isAnswered,
      this.isCompleteAnswered,
      this.synchronizationReicivingDate})
      : super(DatabaseModels.researchesComplementaryModel);

  ResearchesComplementaryModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.researchesComplementaryModel) {
    workspaceId = json['workspaceId'];
    routeId = json['routeId'];
    isSync = json['isSync'] ?? SyncEnum.none;
    idPesquisa = json['IdPesquisa'];
    titulo = json['Titulo'];
    inicioVigencia = json['InicioVigencia'];
    fimVigencia = json['FimVigencia'];
    podeSerRespondidaMaisDeUmaVezPorLoja =
        json['PodeSerRespondidaMaisDeUmaVezPorLoja'];
    if (json['Rotas'] != null) {
      rotas = <ResearchesComplementaryRotas>[];
      json['Rotas'].forEach((v) {
        rotas!.add(ResearchesComplementaryRotas.fromJson(v));
      });
    }
    if (json['Perguntas'] != null) {
      perguntas = <ResearchesComplementaryPerguntas>[];
      json['Perguntas'].forEach((v) {
        perguntas!.add(ResearchesComplementaryPerguntas.fromJson(v));
      });
    }
    isAnswered = json['IsAnswered'] ?? false;
    isCompleteAnswered = json['IsCompletedAnswered'] ?? false;
    synchronizationReicivingDate = json['synchronizationReicivingDate'] != null
        ? DateTime.parse(json['synchronizationReicivingDate'])
        : DateTime.now();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workspaceId'] = workspaceId;
    data['routeId'] = routeId;
    data['isSync'] = isSync;
    data['IdPesquisa'] = idPesquisa;
    data['Titulo'] = titulo;
    data['InicioVigencia'] = inicioVigencia;
    data['FimVigencia'] = fimVigencia;
    data['PodeSerRespondidaMaisDeUmaVezPorLoja'] =
        podeSerRespondidaMaisDeUmaVezPorLoja;
    if (rotas != null) {
      data['Rotas'] = rotas!.map((v) => v.toJson()).toList();
    }
    if (perguntas != null) {
      data['Perguntas'] = perguntas!.map((v) => v.toJson()).toList();
    }
    data['IsAnswered'] = isAnswered ?? false;
    data['IsCompletedAnswered'] = isCompleteAnswered ?? false;
    data['synchronizationReicivingDate'] =
        synchronizationReicivingDate?.toIso8601String() ??
            DateTime.now().toIso8601String();
    return data;
  }

  Future<ResearchesComplementaryModel> getFirst() async {
    var list = await getAll<ResearchesComplementaryModel>(
        ResearchesComplementaryModel.fromJson);
    return list.first;
  }

  Future<List<ResearchesComplementaryModel>> getList(int routeId) async {
    var list = await getAll<ResearchesComplementaryModel>(
        workspaceId: appController.workspace!.workspaceId!,
        storeId: routeId,
        ResearchesComplementaryModel.fromJson);
    return list;
  }

  Future<List<ResearchesComplementaryModel>> getListToSync(
      {required int workspaceId}) async {
    var list = await getAll<ResearchesComplementaryModel>(
        workspaceId: workspaceId, ResearchesComplementaryModel.fromJson);
    var listsOfRoutesIds = list
        .where((element) =>
            _isTodaysResearch(element) && element.isSync == SyncEnum.awaited)
        .map((e) => e.routeId)
        .toSet()
        .toList();

    List<ResearchesComplementaryModel> listToReturn = [];

    for (var routeId in listsOfRoutesIds) {
      if (!listToReturn.any((element) => element.routeId == routeId)) {
        var listFiltered = list
            .where((element) =>
                element.rotas!.first.idRota == routeId &&
                element.isSync == SyncEnum.awaited)
            .toList();
        listFiltered.map((e) => e.routeId = routeId).toList();
        listToReturn.addAll(listFiltered);
      }
    }
    return listToReturn.isNotEmpty
        ? listToReturn.sortedBy((e) => e.routeId!).toList()
        : [];
  }
}

bool _isTodaysResearch(ResearchesComplementaryModel research) {
  var dateWasSync = DateTime(
      research.synchronizationReicivingDate!.year,
      research.synchronizationReicivingDate!.month,
      research.synchronizationReicivingDate!.day,
      0,
      0,
      0,
      0,
      0);
  var dateTodayOnlyDate = DateTime(DateTime.now().year, DateTime.now().month,
      DateTime.now().day, 0, 0, 0, 0, 0);

  var result =
      dateWasSync.toIso8601String() == dateTodayOnlyDate.toIso8601String();
  return result;
}

class ResearchesComplementaryRotas {
  int? idLoja;
  int? idRota;

  ResearchesComplementaryRotas({this.idLoja, this.idRota});

  ResearchesComplementaryRotas.fromJson(Map<String, dynamic> json) {
    idLoja = json['IdLoja'];
    idRota = json['IdRota'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdLoja'] = idLoja;
    data['IdRota'] = idRota;
    return data;
  }
}

class ResearchesComplementaryPerguntas {
  int? idPergunta;
  int? idPerguntaPai;
  String? titulo;
  String? numeroPergunta;
  int? ordem;
  int? tipo;
  bool? perguntaObrigatoria;
  List<ResearchesComplementaryPossiveisRespostas>? possiveisRespostas;
  int? limiteNumeroImagens;
  bool? incluiOutraResposta;
  int? idRespostaPaiParaExibirNovaRamificacao;
  bool? hasDivisor;
  List<ResearchesComplementarySendRespostasUsuario>? answers;
  int? orderReal;
  bool? isShown;
  bool showAnotherAnswer = false;

  ResearchesComplementaryPerguntas(
      {this.idPergunta,
      this.idPerguntaPai,
      this.titulo,
      this.ordem,
      this.tipo,
      this.perguntaObrigatoria,
      this.possiveisRespostas,
      this.limiteNumeroImagens,
      this.incluiOutraResposta,
      this.idRespostaPaiParaExibirNovaRamificacao,
      this.answers,
      this.orderReal,
      this.isShown,
      this.numeroPergunta,
      this.hasDivisor});

  ResearchesComplementaryPerguntas.fromJson(Map<String, dynamic> json) {
    idPergunta = json['IdPergunta'];
    idPerguntaPai = json['IdPerguntaPai'];
    titulo = json['Titulo'];
    ordem = json['Ordem'];
    tipo = json['Tipo'];
    perguntaObrigatoria = json['PerguntaObrigatoria'];
    if (json['PossiveisRespostas'] != null) {
      possiveisRespostas = <ResearchesComplementaryPossiveisRespostas>[];
      json['PossiveisRespostas'].forEach((v) {
        possiveisRespostas!
            .add(ResearchesComplementaryPossiveisRespostas.fromJson(v));
      });
    }
    limiteNumeroImagens = json['LimiteNumeroImagens'];
    incluiOutraResposta = json['IncluiOutraResposta'];
    idRespostaPaiParaExibirNovaRamificacao =
        json['IdRespostaPaiParaExibirNovaRamificacao'];

    if (json['answers'] != null) {
      answers = <ResearchesComplementarySendRespostasUsuario>[];
      json['answers'].forEach((v) {
        answers!.add(ResearchesComplementarySendRespostasUsuario.fromJson(v));
      });
    }
    isShown = json['isShown'] ??
        (idRespostaPaiParaExibirNovaRamificacao == null ? true : false);
    orderReal = json['orderReal'] ?? ordem;
    hasDivisor = json['hasDivisor'];
    showAnotherAnswer = json['showAnotherAnswer'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPergunta'] = idPergunta;
    data['IdPerguntaPai'] = idPerguntaPai;
    data['Titulo'] = titulo;
    data['Ordem'] = ordem;
    data['Tipo'] = tipo;
    data['PerguntaObrigatoria'] = perguntaObrigatoria;
    if (possiveisRespostas != null) {
      data['PossiveisRespostas'] =
          possiveisRespostas!.map((v) => v.toJson()).toList();
    }
    data['LimiteNumeroImagens'] = limiteNumeroImagens;
    data['IncluiOutraResposta'] = incluiOutraResposta;
    data['IdRespostaPaiParaExibirNovaRamificacao'] =
        idRespostaPaiParaExibirNovaRamificacao;

    if (answers != null) {
      data['answers'] = answers!.map((v) => v.toJson()).toList();
    }
    data['isShown'] = isShown;
    data['orderReal'] = orderReal;
    data['hasDivisor'] = hasDivisor;
    data['showAnotherAnswer'] = showAnotherAnswer;
    return data;
  }
}

class ResearchesComplementaryPossiveisRespostas {
  int? idResposta;
  String? resposta;

  ResearchesComplementaryPossiveisRespostas({this.idResposta, this.resposta});

  ResearchesComplementaryPossiveisRespostas.fromJson(
      Map<String, dynamic> json) {
    idResposta = json['IdResposta'];
    resposta = json['Resposta'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdResposta'] = idResposta;
    data['Resposta'] = resposta;
    return data;
  }
}
