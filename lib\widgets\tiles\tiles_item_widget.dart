import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class ListTileItemWidget extends StatelessWidget {
  final String title;
  final GestureTapCallback? onTap;
  const ListTileItemWidget({
    super.key,
    required this.title,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding:
          const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      title: LabelWidget(
        title: title,
        textColor: Colors.black,
        fontSize: 15.sp,
      ),
      trailing: const Icon(Icons.chevron_right),
      onTap: onTap ??
          () {
            SnackbarCustom.snackbarError("Nenhuma ação definida!");
          },
    );
  }
}
