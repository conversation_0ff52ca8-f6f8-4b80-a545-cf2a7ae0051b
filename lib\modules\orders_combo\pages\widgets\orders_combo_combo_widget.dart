import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';
import 'package:pharmalink/core/enuns/product_state_enum.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';
import 'package:pharmalink/modules/orders_combo/pages/widgets/orders_combo_products_widget.dart';
import 'package:pharmalink/widgets/label/label_ritch_widget.dart';

class OrdersComboCardWidget extends StatelessWidget {
  const OrdersComboCardWidget({super.key, required this.combo});

  final CombosOfertaModel combo;
  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<OrdersComboController>("Orders Combo",
        builder: (ctrl) {
      return Padding(
        padding: EdgeInsets.only(bottom: 12.h),
        child: Card(
          elevation: 5,
          child: Padding(
            padding: EdgeInsets.only(bottom: 12.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      color: combo.states! == ProductStateEnum.done
                          ? Colors.green
                          : combo.states! == ProductStateEnum.alert
                              ? Colors.yellow.shade600
                              : Colors.red,
                      width: 30.w,
                      height: 110.h,
                      child: Icon(
                        combo.states! == ProductStateEnum.done
                            ? FontAwesomeIcons.check
                            : combo.states! == ProductStateEnum.alert
                                ? FontAwesomeIcons.exclamation
                                : FontAwesomeIcons.lock,
                        color: Colors.white,
                        size: 18.w,
                      ),
                    ),
                    Flexible(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                SizedBox(
                                  width: 60,
                                  child: combo.caminhoFoto != null
                                      ? CachedNetworkImage(
                                          imageUrl: combo.caminhoFoto ?? "",
                                          placeholder: (context, url) =>
                                              Image.asset(
                                            AppImages.productGeneric,
                                          ),
                                          errorWidget: (context, url, error) =>
                                              Image.asset(
                                            AppImages.productGeneric,
                                          ),
                                        )
                                      : Image.asset(AppImages.productGeneric),
                                ),
                                Flexible(
                                  child: LabelWidget(
                                    title:
                                        combo.descricao?.toUpperCase() ?? "-",
                                    fontSize: 14.sp,
                                    fontWeight: FontWeight.bold,
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 5,
                                  ),
                                )
                              ],
                            ),
                            24.toHeightSpace(),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      LabelWidget(
                                        title: "De",
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      LabelRitchTextWidget(
                                        title: combo.qtdy! > 0
                                            ? combo.priceOrder!.formatReal()
                                            : combo.precoCombo!.formatReal(),
                                        fontSize: 13.sp,
                                      ),
                                    ],
                                  ),
                                ),
                                Flexible(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      LabelWidget(
                                        title: "Por",
                                        fontSize: 12.sp,
                                        fontWeight: FontWeight.bold,
                                      ),
                                      LabelWidget(
                                        title: combo.qtdy! > 0
                                            ? combo.totalOrder!.formatReal()
                                            : combo.precoComboLiquido!
                                                .formatReal(),
                                        fontSize: 13.sp,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Visibility(
                              visible: ctrl.isLimitOver(combo),
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: LabelWidget(
                                  title:
                                      "O limite de envios de pedidos para esta oferta já foi atingido, selecione outra oferta para prosseguir com seu pedido!",
                                  fontSize: DeviceSize.fontSize(16, 20),
                                  textColor: Colors.red,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Visibility(
                      visible: !ctrl.isLimitOver(combo),
                      child: SizedBox(
                        width: DeviceSize.width(40, 100),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            IconButton(
                              onPressed: combo.states! != ProductStateEnum.block
                                  ? () async {
                                      await ctrl.setQtdyUp(combo);
                                    }
                                  : null,
                              icon: Icon(
                                FontAwesomeIcons.plus,
                                size: 18.w,
                              ),
                            ),
                            Center(
                              child: TextField(
                                focusNode: combo.focusNode!,
                                keyboardType: TextInputType.number,
                                controller: combo.qtdyController,
                                maxLength: 5,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly
                                ],
                                style: TextStyle(
                                    fontSize: DeviceSize.fontSize(18, 21)),
                                textAlign: TextAlign.center,
                                readOnly:
                                    combo.states! == ProductStateEnum.block,
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  counterText: "",
                                ),
                                onChanged: (value) async {
                                  if (value.isEmpty) {
                                    value = '0';
                                  }
                                  await ctrl.setQtdyEdit(
                                      combo, int.parse(value));
                                },
                                onSubmitted: (value) async {
                                  if (value.isEmpty) {
                                    value = '0';
                                  }
                                  await ctrl.setQtdyEdit(
                                      combo, int.parse(value));
                                },
                              ),
                            ),
                            IconButton(
                              onPressed: combo.states! != ProductStateEnum.block
                                  ? () async {
                                      await ctrl.setQtdyDown(combo);
                                    }
                                  : null,
                              icon: Icon(
                                FontAwesomeIcons.minus,
                                size: 18.w,
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                8.toHeightSpace(),
                ExpansionTile(
                  title: LabelWidget(
                    title: "Ver produtos",
                    fontSize: 15.sp,
                    fontWeight: FontWeight.bold,
                  ),
                  children: [
                    ...combo.produtos!.map((e) => OrdersComboProductsWidget(
                          data: e,
                          comboQtdy: combo.qtdy ?? 0,
                        )),
                  ],
                )
              ],
            ),
          ),
        ),
      );
    });
  }
}
