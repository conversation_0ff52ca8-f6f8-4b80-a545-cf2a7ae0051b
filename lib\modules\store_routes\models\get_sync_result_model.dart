import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/modules/store_routes/models/params/sync_result_items_params.dart';
import 'package:pharmalink/modules/store_routes/models/params/synchronization_data_model.dart';

class GetSyncResultModel extends SqfLiteBase<GetSyncResultModel> {
  SynchronizationData? synchronizationData;
  List<SyncResultItemsModel>? items;
  int? pageIndex;
  int? pageSize;
  int? totalPages;

  GetSyncResultModel(
      {this.synchronizationData,
      this.items,
      this.pageIndex,
      this.pageSize,
      this.totalPages})
      : super(DatabaseModels.getSyncResultModel);

  GetSyncResultModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.getSyncResultModel) {
    synchronizationData = json['synchronizationData'] != null
        ? SynchronizationData.fromJson(json['synchronizationData'])
        : null;
    if (json['items'] != null) {
      items = <SyncResultItemsModel>[];
      json['items'].forEach((v) {
        items!.add(SyncResultItemsModel.fromJson(v));
      });
    }
    pageIndex = json['pageIndex'];
    pageSize = json['pageSize'];
    totalPages = json['totalPages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (synchronizationData != null) {
      data['synchronizationData'] = synchronizationData!.toJson();
    }
    if (items != null) {
      data['items'] = items!.map((v) => v.toJson()).toList();
    }
    data['pageIndex'] = pageIndex;
    data['pageSize'] = pageSize;
    data['totalPages'] = totalPages;
    return data;
  }

  Future<GetSyncResultModel> getFirst() async {
    var list = await getAll<GetSyncResultModel>(
      workspaceId: appController.workspace!.workspaceId,
      userId: appController.userLogged!.userId,
      GetSyncResultModel.fromJson,
    );
    return list.first;
  }

  Future<List<GetSyncResultModel>> getList() async {
    var list = await getAll<GetSyncResultModel>(
      workspaceId: appController.workspace!.workspaceId,
      userId: appController.userLogged!.userId,
      GetSyncResultModel.fromJson,
    );
    return list;
  }
}
