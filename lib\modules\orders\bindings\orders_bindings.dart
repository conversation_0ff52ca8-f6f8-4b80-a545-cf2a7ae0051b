import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class OrdersBinding implements Bindings {
  @override
  void dependencies() {
    // Get.put<IOrdersApi>(OrdersApi(Get.find()), permanent: true);
    Get.lazyPut<IOrdersApi>(() => OrdersApi(Get.find()), fenix: true);
    Get.lazyPut<OrdersController>(() => OrdersController(), fenix: true);
  }
}
