import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';

// ignore: must_be_immutable
class QuestionListMultipleWidget extends StatefulWidget {
  QuestionListMultipleWidget(
      {super.key,
      required this.title,
      required this.awsers,
      this.selectedAwser,
      required this.onAnswerSelected});

  final String title;
  late List<QuestionListModel> awsers;
  late String? selectedAwser;
  final Function(int?) onAnswerSelected;
  @override
  State<QuestionListMultipleWidget> createState() =>
      _QuestionListMultipleWidgetState();
}

class _QuestionListMultipleWidgetState
    extends State<QuestionListMultipleWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Column(
        children: [
          CustomInkWell(
            onTap: () => openAwnsers(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  flex: 2,
                  child: LabelWidget(
                    title: widget.title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    fontSize: DeviceSize.fontSize(16, 18),
                    textColor: Colors.grey.shade600,
                  ),
                ),
                15.toWidthSpace(),
                if (widget.selectedAwser != null)
                  Flexible(
                    flex: 2,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Flexible(
                          flex: 2,
                          child: LabelWidget(
                              title: widget.selectedAwser!,
                              fontSize: DeviceSize.fontSize(16, 18),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis),
                        ),
                        Flexible(
                          child: IconButton(
                            icon: Icon(
                              FontAwesomeIcons.caretDown,
                              color: Colors.grey.shade400,
                            ),
                            onPressed: () {
                              openAwnsers();
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                if (widget.selectedAwser == null)
                  Flexible(
                    flex: 1,
                    child: IconButton(
                      icon: Icon(
                        FontAwesomeIcons.caretDown,
                        color: Colors.grey.shade400,
                      ),
                      onPressed: () {
                        openAwnsers();
                      },
                    ),
                  ),
              ],
            ),
          ),
          Divider(
            thickness: 1,
            color: Colors.grey.shade300,
          )
        ],
      ),
    );
  }

  void openAwnsers() {
    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          title: Text(widget.title),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: widget.awsers.map((item) {
              return CheckboxListTile(
                title: Text(item.text!),
                value: item.isSelected,
                onChanged: (bool? resp) {
                  setState(() {
                    widget.awsers.map((e) => e.isSelected = false).toList();
                    item.isSelected = true;
                    widget.selectedAwser = item.text!;
                    if (resp != null) {
                      widget.onAnswerSelected(item.value);
                    }
                  });
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              child: const Text('Cancelar'),
              onPressed: () {
                Get.back();
              },
            ),
          ],
        );
      },
    );
  }
}
