import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/widgets/steps/models/steps_model.dart';

class StepsWidget extends StatelessWidget {
  const StepsWidget(
      {super.key, required this.steps, required this.currentStep});

  final List<StepsModel> steps;
  final int currentStep;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: steps
            .map(
              (e) => Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: themesController.getPrimaryColor(),
                        width: 2.0,
                      ),
                    ),
                    child: CircleAvatar(
                      backgroundColor: e.isDone!
                          ? themesController.getPrimaryColor()
                          : Colors.white,
                      child: LabelWidget(
                        title: '${e.index! + 1}',
                        fontSize: DeviceSize.fontSize(16, 18),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  10.toHeightSpace(),
                  LabelWidget(
                    title: e.title?.toUpperCase() ?? "-",
                    fontSize: DeviceSize.fontSize(14, 18),
                    fontWeight: currentStep == e.index
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ],
              ),
            )
            .toList(),
      ),
    );
  }
}
