import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/store_routes_panel/pages/widgets/planned_message_widget.dart';
import 'package:pharmalink/modules/store_routes_planned/pages/widgets/store_routes_planned_card_options_widget.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';

class StpreRoutesPlannedCardWidget extends StatelessWidget {
  const StpreRoutesPlannedCardWidget({super.key, required this.data});

  final StoresModel data;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StoreRoutesPlannedController>(builder: (ctrl) {
      return Card(
        elevation: 4,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            children: [
              Flexible(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    //header
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              LabelWidget(
                                title: data.cNPJ ?? "-",
                                fontWeight: FontWeight.bold,
                                fontSize: DeviceSize.fontSize(15, 18),
                              ),
                              const Gap(5),
                              // Store Name
                              LabelWidget(
                                title: data.razaoSocial ?? "-",
                                fontSize: DeviceSize.fontSize(14, 18),
                                fontWeight: FontWeight.w500,
                              ),
                            ],
                          ),
                        ),
                        PopupMenuButton<String>(
                          icon: const Icon(Icons.more_vert),
                          onSelected: (String result) {
                            // Handle menu item selection
                          },
                          itemBuilder: (BuildContext context) =>
                              <PopupMenuEntry<String>>[
                            PopupMenuItem<String>(
                              value: 'view_registration',
                              onTap: () async {
                                storeRoutesController.setStoreDetail(data);
                              },
                              child: Row(
                                children: [
                                  const Icon(
                                    FontAwesomeIcons.hospital,
                                    size: 16,
                                  ),
                                  const Gap(8),
                                  Flexible(
                                    child: LabelWidget(
                                      title: 'Visualizar Cadastro',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      fontSize: DeviceSize.fontSize(15, 18),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            //Se o parametro de sincronia offline está ativo, exibir o checkbox
                            if (storeRoutesController.hasSyncOffline)
                              PopupMenuItem<String>(
                                value: 'update_commercial_conditions',
                                onTap: () async {
                                  await storeRoutesController
                                      .updateStoreCommercialConditions(data);
                                },
                                child: Row(
                                  children: [
                                    const Icon(
                                      FontAwesomeIcons.arrowsRotate,
                                      size: 16,
                                    ),
                                    const Gap(8),
                                    Flexible(
                                      child: LabelWidget(
                                        title: data.dataExtra!.offlineStatus ==
                                                3
                                            ? 'Atualizar Condições Comerciais'
                                            : 'Baixar Condições (Pedidos Offline)',
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        fontSize: DeviceSize.fontSize(15, 18),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            PopupMenuItem<String>(
                              value: 'show_on_map',
                              onTap: () async {
                                storeRoutesController.openAddress(data);
                              },
                              child: Row(
                                children: [
                                  const Icon(
                                    FontAwesomeIcons.mapLocationDot,
                                    size: 16,
                                  ),
                                  const Gap(8),
                                  Flexible(
                                    child: LabelWidget(
                                      title: 'Mostrar no Mapa',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      fontSize: DeviceSize.fontSize(15, 18),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            PopupMenuItem<String>(
                              value: 'remove_from_route',
                              onTap: () async {
                                await ctrl.removeStoreFromRoute(data);
                              },
                              child: Row(
                                children: [
                                  const Icon(
                                    FontAwesomeIcons.trash,
                                    size: 16,
                                  ),
                                  const Gap(8),
                                  Flexible(
                                    child: LabelWidget(
                                      title: 'Remover dos Planejados',
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      fontSize: DeviceSize.fontSize(15, 18),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            if (data.dataExtra!.canOrderOfflineSync == true &&
                                data.dataExtra!.offlineStatus == 3)
                              PopupMenuItem<String>(
                                value: 'switch_to_online',
                                onTap: () async {
                                  // Logic to switch back to online mode
                                  await ctrl.switchToOnlineMode(data);
                                },
                                child: Row(
                                  children: [
                                    const Icon(
                                      FontAwesomeIcons.wifi,
                                      size: 16,
                                    ),
                                    const Gap(8),
                                    Flexible(
                                      child: LabelWidget(
                                        title: 'Voltar para o Modo Online',
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                        fontSize: DeviceSize.fontSize(15, 18),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ],
                    ),

                    const Gap(10),
                    // Store Address
                    LabelWidget(
                      title: data.enderecoPdv ?? "-",
                      fontSize: DeviceSize.fontSize(13, 15),
                    ),
                    const Gap(10),
                    // Last Order
                    LabelWidget(
                      title: "*Último Pedido*: ${data.dataUltimoPedido ?? "-"}",
                      fontSize: DeviceSize.fontSize(13, 16),
                    ),
                    const Gap(10),
                    // Make Order

                    _buildButtonOrder(data, ctrl),

                    if (!ctrl.isReorderEnabled) const Gap(10),
                    // Planned Message
                    if (!ctrl.isReorderEnabled)
                      PlannedMessageWidget(data: data),
                    if (!ctrl.isReorderEnabled) const Gap(10),
                    //Options
                    if (!ctrl.isReorderEnabled)
                      StoreRoutesPlannedCardOptionsWidget(data: data),
                  ],
                ),
              ),
              if (ctrl.isReorderEnabled)
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  child: Icon(
                    FontAwesomeIcons.list,
                    size: 20,
                    color: Colors.grey.shade600,
                  ),
                ),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildButtonOrder(
      StoresModel data, StoreRoutesPlannedController ctrl) {
    return ResponsiveFlexLayout(
      enableTablet: true,
      children: [
        if (data.pedido == true && !ctrl.isReorderEnabled)
          PrimaryButtonWidget(
            titleButtom: storeRoutesController.getButtonOrderTitle(
                data.dataExtra!.canOrderOfflineSync == true &&
                    data.dataExtra!.offlineStatus == 3),
            titleFontSize: DeviceSize.fontSize(16, 18),
            buttonColor: (data.dataExtra!.offlineDateSync != null &&
                    (data.dataExtra!.offlineStatus == 1 ||
                        data.dataExtra!.offlineStatus == 2))
                ? Colors.grey.shade400
                : themesController.getColorButton(),
            onTap: (data.dataExtra!.offlineDateSync != null &&
                    (data.dataExtra!.offlineStatus == 1 ||
                        data.dataExtra!.offlineStatus == 2))
                ? null
                : () async {
                    await storeRoutesController.orderClick(data);
                  },
          ),
        if (data.dataExtra!.hasRouteSync == true &&
            (data.visita == true || data.dataExtra!.isVisita == true) &&
            !ctrl.isReorderEnabled)
          PrimaryButtonWidget(
            titleButtom: "Preencher Visita",
            titleFontSize: DeviceSize.fontSize(16, 18),
            buttonColor: Colors.white,
            borderColor: themesController.getColorButton(),
            titleColor: themesController.getColorButton(),
            onTap: () async {
              await ctrl.openVisit(data);
            },
          ),
      ],
    );
  }
}
