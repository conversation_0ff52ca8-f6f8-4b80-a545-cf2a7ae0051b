import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

class PdvWidget {
  static pw.Column setInfo(String title, String value) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            fontWeight: pw.FontWeight.bold,
            color: const PdfColor.fromInt(0x00497A),
          ),
        ),
        pw.Text(
          value,
          style: pw.TextStyle(
            fontWeight: pw.FontWeight.normal,
            color: PdfColors.black,
          ),
        ),
      ],
    );
  }

  static pw.Row setInfoRow(String title, String value) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      children: [
        pw.Text(
          title,
          style: pw.TextStyle(
            fontWeight: pw.FontWeight.bold,
            color: const PdfColor.fromInt(0x00497A),
          ),
        ),
        pw.Text(
          value,
          style: pw.TextStyle(
            fontWeight: pw.FontWeight.normal,
            color: PdfColors.black,
          ),
        ),
      ],
    );
  }

  static pw.Text setTableHeader(String title, {pw.TextAlign? textAlign}) {
    return pw.Text(
      title,
      style: pw.TextStyle(
        fontWeight: pw.FontWeight.bold,
        fontSize: 6,
        color: const PdfColor.fromInt(0x00497A),
      ),
      textAlign: textAlign ?? pw.TextAlign.left,
    );
  }

  static pw.Text setTableBody(String title, {pw.TextAlign? textAlign}) {
    return pw.Text(
      title,
      style: pw.TextStyle(
        fontWeight: pw.FontWeight.normal,
        fontSize: 6,
        color: PdfColors.black,
      ),
      textAlign: textAlign ?? pw.TextAlign.left,
    );
  }
}
