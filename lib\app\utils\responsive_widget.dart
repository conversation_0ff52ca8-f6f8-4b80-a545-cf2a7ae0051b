import 'package:pharmalink/exports/basic_exports.dart';

class Responsive extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? smallMobile;
  final Widget desktop;

  const Responsive({
    super.key,
    required this.mobile,
    this.tablet,
    required this.desktop,
    this.smallMobile,
  });

  static bool isSmallMobile() => ScreenUtil().screenWidth < 320;

  static bool isMobile() => ScreenUtil().screenWidth < 400;

  static bool isTablet() =>
      ScreenUtil().screenWidth < 1080 && ScreenUtil().screenWidth >= 650;

  static bool isDesktop() => ScreenUtil().screenWidth >= 1080;

  @override
  Widget build(BuildContext context) {
    if (isDesktop()) {
      return desktop;
    } else if (isTablet() && tablet != null) {
      return tablet!;
    } else if (isSmallMobile() && smallMobile != null) {
      return smallMobile!;
    } else {
      return mobile;
    }
  }

  static double doubleSizes(
      {double? small,
      required double mobile,
      required double tablet,
      required double desktop}) {
    if (isSmallMobile()) return small ?? mobile;
    if (isMobile()) return mobile;
    if (isTablet()) return tablet;
    if (isDesktop()) return desktop;
    return mobile;
  }

  static dynamic sizes({
    dynamic small,
    dynamic mobile,
    required dynamic tablet,
    required dynamic desktop,
  }) {
    if (isSmallMobile()) return small ?? mobile;
    if (isMobile()) return mobile;
    if (isTablet()) return tablet;
    if (isDesktop()) return desktop;
    return mobile;
  }

  static double maxWidth() {
    return 700;
  }
}
