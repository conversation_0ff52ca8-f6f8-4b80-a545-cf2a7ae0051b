import 'dart:io' show Platform;

import 'package:flutter/foundation.dart';
import 'package:flutter_appauth/flutter_appauth.dart';
import 'package:jwt_decode/jwt_decode.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:pharmalink/app/controller/app_controller.dart';
import 'package:pharmalink/config/databases/database_exports.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/models/result_error.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/login/models/login_forget_password_request.dart';
import 'package:pharmalink/modules/login/models/login_model.dart';
import 'package:pharmalink/modules/login/models/login_response_model.dart';
import 'package:pharmalink/modules/visits/models/visits_by_routes_response_model.dart';

class LoginController extends GetxControllerInstrumentado<LoginController> {
  LoginController();
  FlutterAppAuth appAuth = const FlutterAppAuth();
  String versionApp = "Versão";
  Color versionColor = redColor;
  String? userName;
  String? password;
  bool passwordObscure = true;
  bool loginButtonLoad = false;
  bool userLogged = false;
  TextEditingController passwordController = TextEditingController();
  TextEditingController userNameController = TextEditingController();
  String b2cTenant = "ecspocad";
  String b2cPolicyName = "B2C_1_POC_FINAL";
  String b2cClientId = "6c2a3f48-73be-4e61-9c2c-f9fd28814639";
  String b2cRedirectUrl = "com.davetest.wow://oauth/redirect";
  List<String> b2cScopes = ['openid', 'profile'];

  String? urlPortal;
  @override
  Future<void> onReady() async {
    super.onReady();
    await getVersionApp();
  }

  void reset() {
    userName = null;
    password = null;
    if (kDebugMode) {
      final workspaceId = appController.workspace?.workspaceId;
      if (workspaceId == 10144) {
        userName = 'rep_plk';
      } else if (workspaceId == 10173) {
        userName = 'thais.felippe';
      } else if (workspaceId == 10143) {
        userName = 'rep_teste';
      } else {
        userName = 'vendedorSE';
      }
      password = 'A123456@';

      if (workspaceId == 10146) {
        userName = 'PHP.SP01';
        password = 'Aa12345678@!';
      }
      password = 'A123456@';
    }
    userNameController.text = userName ?? '';
    passwordController.text = password ?? '';
    loginButtonLoad = false;
    passwordObscure = true;
    userLogged = false;
    update();
  }

  void resetRefresh() {
    if (kDebugMode) {
      if (appController.workspace?.workspaceId == 10144) {
        userName = 'rep_plk';
        password = 'A123456@';
      } else {
        userName = 'admerick';
        password = 'A123456@';
      }
    }
    userNameController.text = userName ?? '';
    passwordController.text = password ?? '';
    loginButtonLoad = false;
    passwordObscure = true;
    userLogged = false;

    update();
  }

  void setUserLogged(bool v) {
    userLogged = v;
    update();
  }

  void setUserName(String? v) {
    userName = v;
    update();
  }

  void setPassword(String? v) {
    password = v;
    update();
  }

  void setPasswordObscure() {
    passwordObscure = !passwordObscure;
    update();
  }

  void setLoadingButton(bool v) {
    loginButtonLoad = v;
    update();
  }

  Future<void> getVersionApp() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "getVersionApp",
    );
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      versionApp = "Versão: ${packageInfo.version} (${packageInfo.buildNumber})";
      getVersionColor();
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
    } finally {
      leaveAction();
    }
  }

  getVersionColor() {
    if (appController.currentEnvironment == Environment.production) {
      versionColor = textColor;
      update();
    }
  }

  Future<void> changeUser() async {
    reset();
    setUserLogged(false);
    update();
  }

  Future<void> continueLogged() async {
    await synchronizationsController.onReady();
    synchronizationsController.setHasStores(true);
    Get.toNamed(
      RoutesPath.synchronizations,
      arguments: {'all': false, 'isLogin': true},
    );
    dynatrace.collectAndReportSessionData();
  }

  Future<void> loginB2c() async {
    String authorizeUrl =
        'https://$b2cTenant.b2clogin.com/te/$b2cTenant.onmicrosoft.com/$b2cPolicyName/oauth2/v2.0/authorize';
    String tokenUrl =
        'https://$b2cTenant.b2clogin.com/te/$b2cTenant.onmicrosoft.com/$b2cPolicyName/oauth2/v2.0/token';

    var (leaveAction, subAction) = dynatraceAction.subActionReport("loginB2c");

    try {
      final AuthorizationTokenResponse result = await appAuth
          .authorizeAndExchangeCode(
            AuthorizationTokenRequest(
              b2cClientId,
              b2cRedirectUrl,
              serviceConfiguration: AuthorizationServiceConfiguration(
                authorizationEndpoint: authorizeUrl,
                tokenEndpoint: tokenUrl,
              ),
              scopes: b2cScopes,
            ),
          );

      await setLoginB2c(result);
      dynatrace.collectAndReportSessionData();
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> setLoginB2c(AuthorizationTokenResponse auth) async {
    final loading = PlkLoading();
    loading.show(title: AppStrings.load);
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "setLoginB2c",
    );

    try {
      appController.userLogged = LoginResponseModel(
        accessToken: auth.idToken,
        tokenType: 'Bearer',
      );
      final result = await loginApi.loginInfo();
      result.when(
        sucess: (data) async {
          data.workspaceId = appController.workspace?.workspaceId;
          data.tokenType = 'Bearer';
          data.accessToken = auth.idToken;
          data.expires = getExpirationByB2cToken(auth.idToken!);
          data.isB2c = true;

          await dbContext
              .withControllerAction(this)
              .addData(
                key: DatabaseModels.loginResponseModel,
                // workspaceId: appController.workspace?.workspaceId,
                data: data,
                clearCurrentData: true,
              );
          appController.userLogged = data;
          await themesController.getData();

          setUserLogged(true);
          globalParams.order.setCurrentStore(null);

          final storeExists = await storesModel.exists(
            workspaceId: appController.workspace!.workspaceId!,
          );
          if (storeExists) {
            await VisitsByRoutesResponseModel().removeOldVisits();
            await storeRoutesController
                .withControllerAction(this)
                .syncStoreData();
            await generalParameterizationController.getSystemParameterization();
            await generalParameterizationController
                .getGeneralSettingsOrderDiscountRegistration(forceUpdate: true);
            await generalParameterizationController
                .withControllerAction(this)
                .getParameterByKeyOffline(forceUpdate: true);
            loading.hide();
            navigationPageController.selectedIndex = 0;
            Get.offAllNamed(RoutesPath.navigationPage);
            update();
          } else {
            await synchronizationsController.onReady();

            synchronizationsController.setHasStores(true);
            loading.hide();
            Get.toNamed(
              RoutesPath.synchronizations,
              arguments: {'all': false, 'isLogin': true},
            );
            update();
          }
        },
        error: (error) {
          loading.hide();
          //SnackbarCustom.snackbarError(error.error);
          Dialogs.info("Atenção", error.error);
        },
      );
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> logoutB2c() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport("logoutB2c");

    try {
      //for some reason the API works differently on iOS and Android
      Map<String, String>? additionalParameters;
      if (Platform.isAndroid) {
        //works on Android but is missing p parameter when redirected back to authorize on iOS
        additionalParameters = {
          "id_token_hint": appController.userLogout!.accessToken!,
          "post_logout_redirect_uri": b2cRedirectUrl,
        };
      } else if (Platform.isIOS) {
        //missing p parameter when redirected back to authorize on iOS so the below difference
        additionalParameters = {
          "id_token_hint": appController.userLogout!.accessToken!,
          "post_logout_redirect_uri": b2cRedirectUrl,
          'p': b2cPolicyName,
        };
      }
      await appAuth.authorizeAndExchangeCode(
        AuthorizationTokenRequest(
          b2cClientId,
          b2cRedirectUrl,
          promptValues: ['login'],
          discoveryUrl:
              'https://$b2cTenant.b2clogin.com/$b2cTenant.onmicrosoft.com/v2.0/.well-known/openid-configuration?p=$b2cPolicyName',
          additionalParameters: additionalParameters,
          scopes: b2cScopes,
        ),
      );
      // Navigator.pop(context);
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  DateTime getExpirationByB2cToken(String token) {
    // Decodificar o token
    Map<String, dynamic> payload = Jwt.parseJwt(token);

    // Acessar o tempo de expiração
    int expiration = payload['exp'];

    // Converter o tempo de expiração para DateTime
    DateTime expirationDate = DateTime.fromMillisecondsSinceEpoch(
      expiration * 1000,
    );
    return expirationDate;
  }

  void logout() {
    showDialog(
      context: Get.context!,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirmar saída'),
          content: const Text('Deseja realmente sair?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancelar'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            TextButton(
              child: const Text('Sair'),
              onPressed: () async {
                reset();
                await dbContext.deleteByKey(
                  key: DatabaseModels.loginResponseModel,
                );
                appController.userLogout = appController.userLogged;
                appController.userLogged = null;

                Get.offAllNamed(RoutesPath.login);
              },
            ),
          ],
        );
      },
    );
  }

  Future<void> logoutForce() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "logoutForce",
    );
    try {
      reset();
      await dbContext.deleteByKey(key: DatabaseModels.loginResponseModel);
      appController.userLogout = appController.userLogged;
      appController.userLogged = null;
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
    } finally {
      leaveAction();
      Get.offAllNamed(RoutesPath.login);
    }
  }

  Future<void> sendLogin() async {
    setLoadingButton(true);
    appController.hasErrorRefreshToken = false;

    var (leaveAction, subAction) = dynatraceAction.subActionReport("sendLogin");

    try {
      if (appController.workspace == null) {
        Dialogs.info(
          "Atenção",
          "Escolha um workspace para prosseguir.",
          buttonName: "Escolher meu Workspace",
          buttonOnPressed: () {
            Get.offAllNamed(
              RoutesPath.workspaces,
              arguments: {'settings': false},
            );
          },
        );
        return;
      }

      final result = await loginApi.login(
        model: LoginModel(
          userName: userName!,
          password: password!,
          grantType: 'password',
        ),
      );

      if (result.data != null) {
        result.data!.workspaceId = appController.workspace?.workspaceId;
        result.data!.userName = userName!;
        result.data!.password = password!;
        await dbContext
            .withControllerAction(this)
            .addData(
              key: DatabaseModels.loginResponseModel,
              //workspaceId: appController.workspace?.workspaceId,
              data: result.data!,
              clearCurrentData: true,
            );
        appController.userLogged = result.data!;
        await themesController.getData();
        globalParams.order.setCurrentStore(null);
        final storeExists = await storesModel.exists(
          workspaceId: appController.workspace!.workspaceId!,
        );
        if (storeExists) {
          await VisitsByRoutesResponseModel().removeOldVisits();
          await storeRoutesController
              .withControllerAction(this)
              .syncStoreData();
          await generalParameterizationController.getSystemParameterization();
          await generalParameterizationController
              .getGeneralSettingsOrderDiscountRegistration(forceUpdate: true);
          await generalParameterizationController
              .withControllerAction(this)
              .getParameterByKeyOffline(forceUpdate: true);
          setLoadingButton(false);
          navigationPageController.selectedIndex = 0;
          Get.offAllNamed(RoutesPath.navigationPage);
          //setUserLogged(true);
        } else {
          await synchronizationsController.onReady();
          synchronizationsController.setHasStores(true);
          navigationPageController.selectedIndex = 0;
          setLoadingButton(false);
          setUserLogged(true);
          Get.toNamed(
            RoutesPath.synchronizations,
            arguments: {'all': false, 'isLogin': true},
          );
          update();
        }
      } else {
        
       String errorMessage = "Erro ao logar";
      
     
      if (result.error!.data != null && result.error!.data is Map) {
        final data = result.error!.data as Map;
        errorMessage = data['error_description'] ?? data['error'] ?? "Erro ao realizar o login";
      } 
      
      else if (result.error!.data != null && result.error!.data is ResultLoginError) {
        final errorObj = result.error!.data as ResultLoginError;
        errorMessage = errorObj.errorDescription ?? "Usuário ou senha inválidos";
      } 
     
      else if (result.error?.statusCode == 400) {
        errorMessage = "Usuário ou senha inválidos";
      } 
     
      else {
        errorMessage = result.error!.message ?? "Erro ao logar";
      }
      
      Dialogs.info("Atenção", errorMessage);
      setLoadingButton(false);
      }
      dynatrace.collectAndReportSessionData();
    } catch (e, s) {
      setLoadingButton(false);
      subAction.reportZoneStacktrace(e, s);
      SnackbarCustom.snackbarError(e.toString());
    } finally {
      leaveAction();
    }
  }

  Future<void> refreshToken(
    LoginResponseModel model, {
    bool? noMessages,
  }) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "refreshToken",
    );
    try {
      final result = await loginApi.login(
        model: LoginModel(userName: model.userName!, password: model.password!),
      );
      if (result.data != null) {
        appController.hasErrorRefreshToken = false;
        result.data!.workspaceId = appController.workspace?.workspaceId;
        result.data!.userName = model.userName!;
        result.data!.password = model.password!;
        await dbContext
            .withControllerAction(this)
            .addData(
              key: DatabaseModels.loginResponseModel,
              //workspaceId: appController.workspace?.workspaceId,
              data: result.data!,
              clearCurrentData: true,
            );
        appController.userLogged = result.data!;
        update();
      } else {
        appController.hasErrorRefreshToken = true;
        if (noMessages == null || noMessages == false) {
          if (result.error!.data is ResultLoginError) {
            final error = result.error!.data as ResultLoginError;
            SnackbarCustom.snackbarError(
              error.errorDescription ?? "Erro ao logar",
            );
            subAction.reportError(
              error.error,
              errorCode: result.error?.statusCode,
            );
          }
        }

        setLoadingButton(false);
        resetRefresh();
        await logoutForce();
      }
    } catch (e, s) {
      await subAction.reportZoneStacktrace(e, s);
      setLoadingButton(false);
    } finally {
      leaveAction();
    }
  }

  Future<void> forgetPassword() async {
    if (userName == null || userName!.isEmpty) {
      Dialogs.info("Atenção", "Preencha com o seu usuário.");
      return;
    }
    final loading = PlkLoading();
    loading.show(title: "Validando");

    await getURLPortal();
    final result = await loginApi.forgetPassword(
      model: LoginForgetPasswordRequest(
        login: userName,
        caminhoTela: "$urlPortal/#/redefinirSenha?token=",
      ),
    );
    loading.hide();
    if (result.data != null) {
      Dialogs.info(
        "Sucesso",
        "Enviamos informações para você resetar a sua senha para o seu email.",
      );
    } else {
      Dialogs.info("Erro", "Ocorreu algum erro ao resetar a sua senha.");
    }
  }

  Future<void> getURLPortal() async {
    final result = await loginApi.getURLPortal();
    if (result.data != null) {
      urlPortal = result.data;
    }
  }
}
