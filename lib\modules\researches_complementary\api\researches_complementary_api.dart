import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_model.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_request_model.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_send_model.dart';

abstract class IResearchesComplementaryApi {
  Future<HttpResponse<List<ResearchesComplementaryModel>>>
      getResearchesComplementary(
          {required ResearchesComplementaryRequestModel model});

  Future<HttpResponse<String?>> sendResearchesComplementary(
      {required ResearchesComplementarySendModel model});
}

class ResearchesComplementaryApi extends IResearchesComplementaryApi {
  final HttpManager _httpManager;
  ResearchesComplementaryApi(this._httpManager);

  @override
  Future<HttpResponse<List<ResearchesComplementaryModel>>>
      getResearchesComplementary(
          {required ResearchesComplementaryRequestModel model}) async {
    return await _httpManager.request<List<ResearchesComplementaryModel>>(
      path: 'pesquisaComplementar/listarVigentes',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        if (data is List) {
          return data
              .map((item) => ResearchesComplementaryModel.fromJson(item))
              .toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }

  @override
  Future<HttpResponse<String?>> sendResearchesComplementary(
      {required ResearchesComplementarySendModel model}) async {
    return await _httpManager.request<String?>(
      path: 'pesquisaComplementar/responder',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        return data.toString();
      },
    );
  }
}
