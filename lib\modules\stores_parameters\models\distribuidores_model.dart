import 'package:pharmalink/modules/stores_parameters/models/distribuidor_model.dart';

class DistribuidoresModel {
  int? pdvId;
  int? distribuidorId;
  int? ordemDePreferencia;
  int? ordemSelected;
  int? ordemMelhorAtendimento;

  DistribuidorModel? distribuidor;
  bool? isSelected;
  int? lockId;

  DistribuidoresModel({
    this.pdvId,
    this.distribuidorId,
    this.ordemDePreferencia,
    this.ordemSelected,
    this.ordemMelhorAtendimento,
    this.distribuidor,
    this.isSelected,
    this.lockId,
  });

  DistribuidoresModel.fromJson(Map<String, dynamic> json) {
    pdvId = json['PdvId'];
    distribuidorId = json['DistribuidorId'];
    ordemDePreferencia = json['OrdemDePreferencia'];
    ordemSelected = json['ordemSelected'];

    ordemMelhorAtendimento = json['OrdemMelhorAtendimento'];

    distribuidor = json['Distribuidor'] != null
        ? DistribuidorModel.fromJson(json['Distribuidor'])
        : null;
    isSelected = json['isSelected'] ?? false;
    lockId = json['LockId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['PdvId'] = pdvId;
    data['DistribuidorId'] = distribuidorId;
    data['OrdemDePreferencia'] = ordemDePreferencia;
    data['ordemSelected'] = ordemSelected;

    data['OrdemMelhorAtendimento'] = ordemMelhorAtendimento;

    if (distribuidor != null) {
      data['Distribuidor'] = distribuidor!.toJson();
    }
    data['isSelected'] = isSelected;
    data['LockId'] = lockId;
    return data;
  }
}
