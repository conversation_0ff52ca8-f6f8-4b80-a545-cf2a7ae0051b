import 'package:pharmalink/app/controller/app_controller.dart';
import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/core/extensions/bool_extensions.dart';
import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/core/http_result/http_result.dart';
import 'package:pharmalink/core/utils/constants.dart';
import 'package:pharmalink/modules/workspaces/models/workspaces_model.dart';

abstract class IWorkspacesApi {
  Future<HttpResult<List<WorkspacesModel>>> getWorkspaces();
}

class WorkspacesApi extends IWorkspacesApi {
  final HttpManager _httpManager;
  WorkspacesApi(this._httpManager);

  @override
  Future<HttpResult<List<WorkspacesModel>>> getWorkspaces() async {
    String versionApi = "v1";

    if (appController.currentEnvironment == Environment.production) {
      versionApi = "";
    }
    final result = await _httpManager.restRequestWorkspace(
      url: '$versionApi/workspaces',
      method: HttpMethods.get,
    );

    if (result.statusCode!.isStatusOk()) {
      if (result.data is List) {
        final response = (result.data as List)
            .map((item) => WorkspacesModel.fromJson(item))
            .toList();

        return HttpResult.sucess(response);
      } else {
        return HttpResult.error(getOthersStatusCodes(result));
      }
    } else {
      return HttpResult.error(getOthersStatusCodes(result));
    }
  }
}
