import 'dart:io';

import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/researches_share_of_shelf/enums/researches_share_of_shelf_type_enum.dart';
import 'package:pharmalink/modules/researches_share_of_shelf/pages/components/researches_share_of_shelf_qtdy_widget.dart';

class ResearchesShareOfShelfPage extends StatelessWidget {
  const ResearchesShareOfShelfPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ResearchesShareOfShelfController>(
        "ResearchesShareOfShelfPage", builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelWidget(
                title: globalParams.getCurrentStore()?.razaoSocial ?? "-",
                fontSize: DeviceSize.fontSize(14, 18),
                fontWeight: FontWeight.w600,
                textColor: whiteColor,
              ),
              LabelWidget(
                title: globalParams.getCurrentStore()?.cNPJ ?? "-",
                fontSize: DeviceSize.fontSize(11, 13),
                textColor: whiteColor,
              ),
            ],
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              Get.back();
            },
          ),
          actions: [
            IconButton(
                onPressed: () async {
                  //await ctrl.save();
                },
                icon: const Icon(
                  Icons.send,
                  color: whiteColor,
                ))
          ],
        ),
        body: ctrl.selected != null
            ? SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LabelWidget(
                        title: ctrl.selected!.descricao ?? "-",
                        fontSize: DeviceSize.fontSize(22, 26),
                        fontWeight: FontWeight.bold,
                      ),
                      10.toHeightSpace(),
                      CustomInkWell(
                        onTap: () async {
                          await ctrl.saveQuestion();
                        },
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Column(
                            children: [
                              Icon(
                                FontAwesomeIcons.floppyDisk,
                                size: 20.w,
                              ),
                              LabelWidget(
                                title: "Salvar",
                                fontSize: DeviceSize.fontSize(12, 15),
                              )
                            ],
                          ),
                        ),
                      ),
                      20.toHeightSpace(),
                      ...ctrl.selected!.produtos!.map(
                        (e) => Padding(
                          padding: const EdgeInsets.only(bottom: 10),
                          child: Card(
                            elevation: 4,
                            child: SizedBox(
                              width: double.infinity,
                              child: Padding(
                                padding: const EdgeInsets.all(12.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Flexible(
                                          child: LabelValueVerticalWidget(
                                            title: "Família",
                                            value: e.familia ?? "-",
                                            textAlign: TextAlign.center,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            fontSize:
                                                DeviceSize.fontSize(15, 18),
                                            fontSizeTitle:
                                                DeviceSize.fontSize(14, 17),
                                          ),
                                        ),
                                        Flexible(
                                          child: LabelValueVerticalWidget(
                                            title: "Share Ideal",
                                            value:
                                                "${e.shareIdeal?.formatPercent() ?? "-"}%",
                                            textAlign: TextAlign.center,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            fontSize:
                                                DeviceSize.fontSize(15, 18),
                                            fontSizeTitle:
                                                DeviceSize.fontSize(14, 17),
                                          ),
                                        ),
                                        Flexible(
                                          child: LabelValueVerticalWidget(
                                            title: "Share Apurado",
                                            value:
                                                "${e.shareApurado?.formatPercent() ?? "-"}%",
                                            textAlign: TextAlign.center,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            fontSize:
                                                DeviceSize.fontSize(15, 18),
                                            fontSizeTitle:
                                                DeviceSize.fontSize(14, 17),
                                          ),
                                        )
                                      ],
                                    ),
                                    if (e.tipoResposta == null ||
                                        e.tipoResposta == 2)
                                      10.toHeightSpace(),
                                    if (e.tipoResposta == null ||
                                        e.tipoResposta == 2)
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Flexible(
                                            child:
                                                ResearchesShareOfShelfQtdyWidget(
                                              title: "Facing Gôndola",
                                              value:
                                                  "${e.facingGondola ?? "0"}",
                                              controller: TextEditingController(
                                                  text: e.facingGondola
                                                      ?.toString()),
                                              textAlign: TextAlign.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              fontSize:
                                                  DeviceSize.fontSize(14, 17),
                                              onPressedDown: () {
                                                ctrl.setDown(
                                                    e,
                                                    ShareOfShelfTypeEnum
                                                        .pedishelfdoPadrao);
                                              },
                                              onPressedUp: () {
                                                ctrl.setUp(
                                                    e,
                                                    ShareOfShelfTypeEnum
                                                        .pedishelfdoPadrao);
                                              },
                                              onSubmitted: (String? value) {
                                                ctrl.setChangeText(
                                                    e,
                                                    ShareOfShelfTypeEnum
                                                        .pedishelfdoPadrao,
                                                    value);
                                              },
                                            ),
                                          ),
                                          Flexible(
                                            child:
                                                ResearchesShareOfShelfQtdyWidget(
                                              title: "Facing Produto",
                                              controller: TextEditingController(
                                                  text: e.facingProduto
                                                      ?.toString()),
                                              value:
                                                  "${e.facingProduto ?? "0"}",
                                              textAlign: TextAlign.center,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.center,
                                              fontSize:
                                                  DeviceSize.fontSize(14, 17),
                                              onPressedDown: () {
                                                ctrl.setDown(
                                                    e,
                                                    ShareOfShelfTypeEnum
                                                        .product);
                                              },
                                              onPressedUp: () {
                                                ctrl.setUp(
                                                    e,
                                                    ShareOfShelfTypeEnum
                                                        .product);
                                              },
                                              onSubmitted: (String? value) {
                                                ctrl.setChangeText(
                                                    e,
                                                    ShareOfShelfTypeEnum
                                                        .product,
                                                    value);
                                              },
                                            ),
                                          ),
                                          Flexible(
                                            child: CustomInkWell(
                                              onTap: () {
                                                ctrl.openCameraOption(e);
                                              },
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.start,
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.center,
                                                children: [
                                                  LabelWidget(
                                                    title: "Foto",
                                                    fontSize:
                                                        DeviceSize.fontSize(
                                                            14, 17),
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                  const Icon(
                                                    FontAwesomeIcons.camera,
                                                    size: 42,
                                                  )
                                                ],
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    if (e.image != null)
                                      Align(
                                        alignment: Alignment.center,
                                        child: Column(
                                          mainAxisSize: MainAxisSize.min,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                          children: [
                                            10.toHeightSpace(),
                                            Image.file(
                                              File(e.image!),
                                              fit: BoxFit.cover,
                                              width: 120,
                                              height: 100,
                                            ),
                                            5.toHeightSpace(),
                                            IconButtonWidget(
                                              colorButton: Colors.red,
                                              width: 120,
                                              icon: const Icon(
                                                FontAwesomeIcons.trash,
                                                color: Colors.white,
                                                size: 16,
                                              ),
                                              onTap: () async {
                                                await Dialogs.confirm(
                                                  AppStrings.attention,
                                                  AppStrings
                                                      .researchesShareDelete,
                                                  onPressedOk: () async {
                                                    Get.back(); //close modal
                                                    ctrl.removePicture(e);
                                                  },
                                                  buttonNameOk:
                                                      "Sim".toUpperCase(),
                                                  buttonNameCancel:
                                                      "Não".toUpperCase(),
                                                );
                                              },
                                            ),
                                            5.toHeightSpace(),
                                            LabelWidget(
                                              title: e.tipoResposta == 1
                                                  ? "Salvo como apuração por foto"
                                                  : "Salvo como apuração manual",
                                              fontSize:
                                                  DeviceSize.fontSize(12, 15),
                                            )
                                          ],
                                        ),
                                      )
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
              )
            : const SizedBox(),
      );
    });
  }
}
