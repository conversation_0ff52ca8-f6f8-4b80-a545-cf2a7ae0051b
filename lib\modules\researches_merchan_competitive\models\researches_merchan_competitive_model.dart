import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';

class ResearchesMerchanCompetitiveDataModel
    extends SqfLiteBase<ResearchesMerchanCompetitiveDataModel> {
  int? workspaceId;
  int? routeId;
  int? isSync;
  List<ResearchesMerchanCompetitiveFamilias>? familias;
  List<ResearchesMerchanCompetitiveMerchandising>? merchandising;
  List<ResearchesMerchanCompetitiveTipoAtivacao>? tipoAtivacao;
  List<ResearchesMerchanCompetitivePesquisaMerchandisingItens>?
      pesquisaMerchandisingItens;

  ResearchesMerchanCompetitiveDataModel(
      {this.workspaceId,
      this.routeId,
      this.isSync,
      this.familias,
      this.merchandising,
      this.tipoAtivacao,
      this.pesquisaMerchandisingItens})
      : super(DatabaseModels.researchesMerchanCompetitiveDataModel);

  ResearchesMerchanCompetitiveDataModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.researchesMerchanCompetitiveDataModel) {
    workspaceId = json['workspaceId'];
    routeId = json['routeId'];
    isSync = json['isSync'] ?? SyncEnum.none;
    if (json['Familias'] != null) {
      familias = <ResearchesMerchanCompetitiveFamilias>[];
      json['Familias'].forEach((v) {
        familias!.add(ResearchesMerchanCompetitiveFamilias.fromJson(v));
      });
    }
    if (json['Merchandising'] != null) {
      merchandising = <ResearchesMerchanCompetitiveMerchandising>[];
      json['Merchandising'].forEach((v) {
        merchandising!
            .add(ResearchesMerchanCompetitiveMerchandising.fromJson(v));
      });
    }
    if (json['TipoAtivacao'] != null) {
      tipoAtivacao = <ResearchesMerchanCompetitiveTipoAtivacao>[];
      json['TipoAtivacao'].forEach((v) {
        tipoAtivacao!.add(ResearchesMerchanCompetitiveTipoAtivacao.fromJson(v));
      });
    }
    if (json['PesquisaMerchandisingItens'] != null) {
      pesquisaMerchandisingItens =
          <ResearchesMerchanCompetitivePesquisaMerchandisingItens>[];
      json['PesquisaMerchandisingItens'].forEach((v) {
        pesquisaMerchandisingItens!.add(
            ResearchesMerchanCompetitivePesquisaMerchandisingItens.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workspaceId'] = workspaceId;
    data['routeId'] = routeId;
    data['isSync'] = isSync;
    if (familias != null) {
      data['Familias'] = familias!.map((v) => v.toJson()).toList();
    }
    if (merchandising != null) {
      data['Merchandising'] = merchandising!.map((v) => v.toJson()).toList();
    }
    if (tipoAtivacao != null) {
      data['TipoAtivacao'] = tipoAtivacao!.map((v) => v.toJson()).toList();
    }
    if (pesquisaMerchandisingItens != null) {
      data['PesquisaMerchandisingItens'] =
          pesquisaMerchandisingItens!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  Future<List<ResearchesMerchanCompetitiveDataModel>> getList(
      {required int workspaceId, required int routeId}) async {
    var list = await getAll<ResearchesMerchanCompetitiveDataModel>(
        workspaceId: workspaceId,
        storeId: routeId,
        ResearchesMerchanCompetitiveDataModel.fromJson);
    return list;
  }

  Future<List<ResearchesMerchanCompetitiveDataModel>> getListToSync(
      {required int workspaceId}) async {
    var list = await getAll<ResearchesMerchanCompetitiveDataModel>(
        workspaceId: workspaceId,
        ResearchesMerchanCompetitiveDataModel.fromJson);
    return (list.isNotEmpty &&
            list.any((element) => element.isSync == SyncEnum.awaited))
        ? list.where((element) => element.isSync == SyncEnum.awaited).toList()
        : [];
  }
}

class ResearchesMerchanCompetitiveFamilias {
  int? idFamilia;
  String? descricao;
  String? codigo;
  String? foto;
  String? caminhoFoto;
  bool? apagado;
  bool? trade;
  bool? promovido;
  String? centroCusto;
  String? dataInclusao;
  String? dataAlteracao;
  bool? concorrente;

  ResearchesMerchanCompetitiveFamilias(
      {this.idFamilia,
      this.descricao,
      this.codigo,
      this.foto,
      this.caminhoFoto,
      this.apagado,
      this.trade,
      this.promovido,
      this.centroCusto,
      this.dataInclusao,
      this.dataAlteracao,
      this.concorrente});

  ResearchesMerchanCompetitiveFamilias.fromJson(Map<String, dynamic> json) {
    idFamilia = json['IdFamilia'];
    descricao = json['Descricao'];
    codigo = json['Codigo'];
    foto = json['Foto'];
    caminhoFoto = json['CaminhoFoto'];
    apagado = json['Apagado'];
    trade = json['Trade'];
    promovido = json['Promovido'];
    centroCusto = json['CentroCusto'];
    dataInclusao = json['DataInclusao'];
    dataAlteracao = json['DataAlteracao'];
    concorrente = json['Concorrente'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdFamilia'] = idFamilia;
    data['Descricao'] = descricao;
    data['Codigo'] = codigo;
    data['Foto'] = foto;
    data['CaminhoFoto'] = caminhoFoto;
    data['Apagado'] = apagado;
    data['Trade'] = trade;
    data['Promovido'] = promovido;
    data['CentroCusto'] = centroCusto;
    data['DataInclusao'] = dataInclusao;
    data['DataAlteracao'] = dataAlteracao;
    data['Concorrente'] = concorrente;
    return data;
  }
}

class ResearchesMerchanCompetitiveMerchandising {
  int? idMerchandising;
  String? descricao;

  ResearchesMerchanCompetitiveMerchandising(
      {this.idMerchandising, this.descricao});

  ResearchesMerchanCompetitiveMerchandising.fromJson(
      Map<String, dynamic> json) {
    idMerchandising = json['IdMerchandising'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdMerchandising'] = idMerchandising;
    data['Descricao'] = descricao;
    return data;
  }
}

class ResearchesMerchanCompetitiveTipoAtivacao {
  int? idMerchandisingTipoAtivacao;
  String? descricao;

  ResearchesMerchanCompetitiveTipoAtivacao(
      {this.idMerchandisingTipoAtivacao, this.descricao});

  ResearchesMerchanCompetitiveTipoAtivacao.fromJson(Map<String, dynamic> json) {
    idMerchandisingTipoAtivacao = json['IdMerchandisingTipoAtivacao'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdMerchandisingTipoAtivacao'] = idMerchandisingTipoAtivacao;
    data['Descricao'] = descricao;
    return data;
  }
}

class ResearchesMerchanCompetitivePesquisaMerchandisingItens {
  int? idPesquisaMerchandising;
  int? idMerchandising;
  String? merchandisingDescricao;
  int? idFamilia;
  String? familiaDescricao;
  int? idTipoAtivacao;
  String? tipoAtivacaoDescricao;
  int? quantidade;

  ResearchesMerchanCompetitivePesquisaMerchandisingItens(
      {this.idPesquisaMerchandising,
      this.idMerchandising,
      this.merchandisingDescricao,
      this.idFamilia,
      this.familiaDescricao,
      this.idTipoAtivacao,
      this.tipoAtivacaoDescricao,
      this.quantidade});

  ResearchesMerchanCompetitivePesquisaMerchandisingItens.fromJson(
      Map<String, dynamic> json) {
    idPesquisaMerchandising = json['IdPesquisaMerchandising'];
    idMerchandising = json['IdMerchandising'];
    merchandisingDescricao = json['MerchandisingDescricao'];
    idFamilia = json['IdFamilia'];
    familiaDescricao = json['FamiliaDescricao'];
    idTipoAtivacao = json['IdTipoAtivacao'];
    tipoAtivacaoDescricao = json['TipoAtivacaoDescricao'];
    quantidade = json['Quantidade'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPesquisaMerchandising'] = idPesquisaMerchandising;
    data['IdMerchandising'] = idMerchandising;
    data['MerchandisingDescricao'] = merchandisingDescricao;
    data['IdFamilia'] = idFamilia;
    data['FamiliaDescricao'] = familiaDescricao;
    data['IdTipoAtivacao'] = idTipoAtivacao;
    data['TipoAtivacaoDescricao'] = tipoAtivacaoDescricao;
    data['Quantidade'] = quantidade;
    return data;
  }
}
