import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders_resume/pages/widgets/orders_resume_product_items_widget.dart';

class OrdersResumeProductsPage extends StatelessWidget {
  const OrdersResumeProductsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrdersResumeController>(builder: (ctrl) {
      final products = ctrl.resume.products ?? [];

      return Column(
        children: products.isNotEmpty
            ? products.map((data) {
                return Padding(
                  padding: EdgeInsets.symmetric(horizontal: 1.w, vertical: 8.h),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LabelWidget(
                        title: data.distributorName!,
                        fontSize: DeviceSize.fontSize(18, 21),
                        fontWeight: FontWeight.bold,
                      ),
                      5.toHeightSpace(),
                      LabelWidget(
                        title: data.orderTypePayment!.name!,
                        fontSize: DeviceSize.fontSize(14, 18),
                      ),
                      5.toHeightSpace(),
                      ...data.items!.map((p) => OrdersResumeProductItemsWidget(
                            item: p,
                          )),
                    ],
                  ),
                );
              }).toList()
            : [
                30.toHeightSpace(),
                LabelWidget(
                  title: "Nenhum produto encontrado.",
                  fontSize: DeviceSize.fontSize(16, 19),
                  fontWeight: FontWeight.bold,
                )
              ],
      );
    });
  }
}
