import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class ResearchesComplementaryPage extends StatelessWidget {
  const ResearchesComplementaryPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ResearchesComplementaryController>(
        "ResearchesComplementaryController", builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelWidget(
                title: globalParams.getCurrentStore()?.razaoSocial ?? "-",
                fontSize: DeviceSize.fontSize(14, 18),
                fontWeight: FontWeight.w600,
                textColor: whiteColor,
              ),
              LabelWidget(
                title: globalParams.getCurrentStore()?.cNPJ ?? "-",
                fontSize: DeviceSize.fontSize(11, 13),
                textColor: whiteColor,
              ),
            ],
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              Get.back();
            },
          ),
          actions: [
            Visibility(
              visible: ctrl.dataList
                  .any((element) => element.isSync != SyncEnum.finished),
              child: CustomInkWell(
                onTap: () async {
                  if (ctrl.validateResearchs(ctrl.dataList)) {
                    await synchronizationsController.onReady();
                    synchronizationsController.setHasResearches(true);
                    Get.toNamed(RoutesPath.synchronizations,
                        arguments: {'all': true, 'autostart': true});
                  }
                },
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 16.w, vertical: 10.w),
                  child: Column(
                    children: [
                      Icon(
                        FontAwesomeIcons.paperPlane,
                        size: 16.w,
                      ),
                      LabelWidget(
                        title: "Enviar",
                        fontSize: 10.sp,
                      )
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
        body: ctrl.dataList.isNotEmpty
            ? SizedBox(
                width: double.infinity,
                child: ListView(
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: ctrl.dataList
                          .map(
                            (e) => SizedBox(
                              width: MediaQuery.of(context).size.width,
                              child: CustomInkWell(
                                onTap: () {
                                  ctrl.setSelected(e);
                                },
                                child: Card(
                                  elevation: 2,
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      SizedBox(
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.8,
                                        child: Padding(
                                          padding: const EdgeInsets.all(16.0),
                                          child: LabelWidget(
                                            title: e.titulo ?? "-",
                                            fontSize:
                                                DeviceSize.fontSize(16, 19),
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 5,
                                          ),
                                        ),
                                      ),
                                      if (e.isSync != SyncEnum.none &&
                                          e.isAnswered!)
                                        e.isAnswered! &&
                                                e.isSync == SyncEnum.awaited
                                            ? const Padding(
                                                padding: EdgeInsets.all(16.0),
                                                child: Icon(
                                                  Icons.check_circle_outline,
                                                  color: Colors.yellow,
                                                ),
                                              )
                                            : const Padding(
                                                padding: EdgeInsets.all(16.0),
                                                child: Icon(
                                                  Icons.check_circle_outline,
                                                  color: Colors.green,
                                                ),
                                              ),
                                      if (e.isSync == SyncEnum.none ||
                                          !e.isAnswered!)
                                        const Padding(
                                          padding: EdgeInsets.all(16.0),
                                          child: Icon(
                                            Icons.check_circle_outline,
                                            color: Colors.grey,
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  ],
                ),
              )
            : SizedBox(
                width: double.infinity,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    LabelWidget(
                      title: "Nenhuma Pesquisa foi encontrada para esta rota",
                      fontSize: DeviceSize.fontSize(18, 20),
                      textColor: themesController.getPrimaryColor(),
                    ),
                  ],
                ),
              ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
