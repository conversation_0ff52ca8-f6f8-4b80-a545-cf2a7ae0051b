class DashboardResponse {
  String? usuarioPedido;
  DateTime? dataPedido;
  String? idPessoa;
  String? setor;
  String? representante;
  String? gdn;
  String? gcr;
  String? diretor;
  String? cnpj;
  String? razaoSocial;
  String? bandeira;
  String? cidade;
  String? uf;
  String? nrPedido;
  String? nrPedidoPrincipal;
  String? origemPedido;
  String? tipoPedido;
  String? prazoPagamento;
  String? motivoDeNaoAtendimento;
  String? familia;
  String? ean;
  String? produto;
  int? quantidadeSolicitada;
  int? quantidadeFaturada;
  int? quantidadeNaoFaturada;
  int? quantidadeRespondida;
  String? valorBrutoSolicitado;
  String? valorBrutoFaturado;
  String? valorBrutoNaoFaturado;
  String? valorLiquidoSolicitado;
  String? descontoPrograma;
  String? descontoForaPrograma;
  String? descontoNotaFiscal;
  String? valorLiquidoFaturado;
  String? valorLiquidoNaoFaturado;
  String? notaFiscal;
  String? dataNotaFiscal;
  String? cnpjCentroDistribuicao;
  String? centroDistribuidor;
  String? matrizDistribuidor;
  String? statusRetornoItem;
  String? statusRetornoPedido;
  String? valorLiquidoProgramaFaturado;
  String? valorLiquidoNFFaturado;
  String? dun;
  int? qtdeDUN;
  int? qtdeReal;
  String? laboratorio;
  bool? descontoEditado;
  String? classificacao;
  String? faturamentoStatus;

  DashboardResponse(
      {this.usuarioPedido,
      this.dataPedido,
      this.idPessoa,
      this.setor,
      this.representante,
      this.gdn,
      this.gcr,
      this.diretor,
      this.cnpj,
      this.razaoSocial,
      this.bandeira,
      this.cidade,
      this.uf,
      this.nrPedido,
      this.nrPedidoPrincipal,
      this.origemPedido,
      this.tipoPedido,
      this.prazoPagamento,
      this.motivoDeNaoAtendimento,
      this.familia,
      this.ean,
      this.produto,
      this.quantidadeSolicitada,
      this.quantidadeFaturada,
      this.quantidadeNaoFaturada,
      this.quantidadeRespondida,
      this.valorBrutoSolicitado,
      this.valorBrutoFaturado,
      this.valorBrutoNaoFaturado,
      this.valorLiquidoSolicitado,
      this.descontoPrograma,
      this.descontoForaPrograma,
      this.descontoNotaFiscal,
      this.valorLiquidoFaturado,
      this.valorLiquidoNaoFaturado,
      this.notaFiscal,
      this.dataNotaFiscal,
      this.cnpjCentroDistribuicao,
      this.centroDistribuidor,
      this.matrizDistribuidor,
      this.statusRetornoItem,
      this.statusRetornoPedido,
      this.valorLiquidoProgramaFaturado,
      this.valorLiquidoNFFaturado,
      this.dun,
      this.qtdeDUN,
      this.qtdeReal,
      this.laboratorio,
      this.descontoEditado,
      this.classificacao,
      this.faturamentoStatus});

  DashboardResponse.fromJson(Map<String, dynamic> json) {
    usuarioPedido = json['UsuarioPedido'];
    dataPedido = DateTime.tryParse(json['Datapedido']);
    idPessoa = json['IdPessoa'];
    setor = json['Setor'];
    representante = json['Representante'];
    gdn = json['Gdn'];
    gcr = json['Gcr'];
    diretor = json['Diretor'];
    cnpj = json['Cnpj'];
    razaoSocial = json['RazaoSocial'];
    bandeira = json['Bandeira'];
    cidade = json['Cidade'];
    uf = json['Uf'];
    nrPedido = json['NrPedido'];
    nrPedidoPrincipal = json['NrPedidoPrincipal'];
    origemPedido = json['OrigemPedido'];
    tipoPedido = json['TipoPedido'];
    prazoPagamento = json['PrazoPagamento'];
    motivoDeNaoAtendimento = json['MotivoDeNaoAtendimento'];
    familia = json['Familia'];
    ean = json['Ean'];
    produto = json['Produto'];
    quantidadeSolicitada = json['QuantidadeSolicitada'];
    quantidadeFaturada = json['QuantidadeFaturada'];
    quantidadeNaoFaturada = json['QuantidadeNaoFaturada'];
    quantidadeRespondida = json['QuantidadeRespondida'];
    valorBrutoSolicitado = json['ValorBrutoSolicitado']?.replaceAll("R\$", '')?.replaceAll(',', '.');
    valorBrutoFaturado = json['ValorBrutoFaturado']?.replaceAll("R\$", '')?.replaceAll(',', '.');
    valorBrutoNaoFaturado = json['ValorBrutoNaoFaturado']?.replaceAll("R\$", '')?.replaceAll(',', '.');
    valorLiquidoSolicitado = json['ValorLiquidoSolicitado'].replaceAll("R\$", '')?.replaceAll(',', '.');
    descontoPrograma = json['DescontoPrograma'].replaceAll("R\$", '')?.replaceAll(',', '.');
    descontoForaPrograma =json['DescontoForaPrograma']?.replaceAll("R\$", '')?.replaceAll(',', '.');
    descontoNotaFiscal = json['DescontoNotaFiscal']?.replaceAll("R\$", '')?.replaceAll(',', '.');
    valorLiquidoFaturado = json['ValorLiquidoFaturado']?.replaceAll("R\$", '')?.replaceAll(',', '.');
    valorLiquidoNaoFaturado = json['ValorLiquidoNaoFaturado']?.replaceAll("R\$", '')?.replaceAll(',', '.');
    notaFiscal = json['NotaFiscal'];
    dataNotaFiscal = json['DataNotaFiscal'];
    cnpjCentroDistribuicao = json['CnpjCentroDistribuicao'];
    centroDistribuidor = json['CentroDistribuidor'];
    matrizDistribuidor = json['MatrizDistribuidor'];
    statusRetornoItem = json['StatusRetornoItem'];
    statusRetornoPedido = json['StatusRetornoPedido'];
    valorLiquidoProgramaFaturado = json['ValorLiquidoProgramaFaturado']?.replaceAll("R\$", '').replaceAll(',', '.');
    valorLiquidoNFFaturado = json['ValorLiquidoNFFaturado']?.replaceAll("R\$", '').replaceAll(',', '.');
    dun = json['DUN'];
    qtdeDUN = json['QtdeDUN'];
    qtdeReal = json['QtdeReal'];
    laboratorio = json['Laboratorio'];
    descontoEditado = json['DescontoEditado'];
    classificacao = json['Classificacao'];
    faturamentoStatus = json['FaturamentoStatus'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['UsuarioPedido'] = usuarioPedido;
    data['Datapedido'] = dataPedido;
    data['IdPessoa'] = idPessoa;
    data['Setor'] = setor;
    data['Representante'] = representante;
    data['Gdn'] = gdn;
    data['Gcr'] = gcr;
    data['Diretor'] = diretor;
    data['Cnpj'] = cnpj;
    data['RazaoSocial'] = razaoSocial;
    data['Bandeira'] = bandeira;
    data['Cidade'] = cidade;
    data['Uf'] = uf;
    data['NrPedido'] = nrPedido;
    data['NrPedidoPrincipal'] = nrPedidoPrincipal;
    data['OrigemPedido'] = origemPedido;
    data['TipoPedido'] = tipoPedido;
    data['PrazoPagamento'] = prazoPagamento;
    data['MotivoDeNaoAtendimento'] = motivoDeNaoAtendimento;
    data['Familia'] = familia;
    data['Ean'] = ean;
    data['Produto'] = produto;
    data['QuantidadeSolicitada'] = quantidadeSolicitada;
    data['QuantidadeFaturada'] = quantidadeFaturada;
    data['QuantidadeNaoFaturada'] =quantidadeNaoFaturada;
    data['QuantidadeRespondida'] = quantidadeRespondida;
    data['ValorBrutoSolicitado'] = valorBrutoSolicitado;
    data['ValorBrutoFaturado'] = valorBrutoFaturado;
    data['ValorBrutoNaoFaturado'] = valorBrutoNaoFaturado;
    data['ValorLiquidoSolicitado'] = valorLiquidoSolicitado;
    data['DescontoPrograma'] = descontoPrograma;
    data['DescontoForaPrograma'] = descontoForaPrograma;
    data['DescontoNotaFiscal'] = descontoNotaFiscal;
    data['ValorLiquidoFaturado'] = valorLiquidoFaturado;
    data['ValorLiquidoNaoFaturado'] = valorLiquidoNaoFaturado;
    data['NotaFiscal'] = notaFiscal;
    data['DataNotaFiscal'] = dataNotaFiscal;
    data['CnpjCentroDistribuicao'] = cnpjCentroDistribuicao;
    data['CentroDistribuidor'] = centroDistribuidor;
    data['MatrizDistribuidor'] = matrizDistribuidor;
    data['StatusRetornoItem'] = statusRetornoItem;
    data['StatusRetornoPedido'] = statusRetornoPedido;
    data['ValorLiquidoProgramaFaturado'] = valorLiquidoProgramaFaturado;
    data['ValorLiquidoNFFaturado'] = valorLiquidoNFFaturado;
    data['DUN'] = dun;
    data['QtdeDUN'] = qtdeDUN;
    data['QtdeReal'] = qtdeReal;
    data['Laboratorio'] = laboratorio;
    data['DescontoEditado'] = descontoEditado;
    data['Classificacao'] = classificacao;
    data['FaturamentoStatus'] = faturamentoStatus;
    return data;
  }
}
