import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class SettingsAppOrdersPage extends StatelessWidget {
  const SettingsAppOrdersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<SettingsAppController>(
        "SettingsAppOrdersPage", builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: LabelWidget(
            title: "Configurações de pedido",
            fontSize: DeviceSize.fontSize(18, 21),
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              GetC.close();
            },
          ),
        ),
        body: ListView(
          children: [
            20.toHeightSpace(),
            LabelWidget(
              title: 'Visualizar',
              padding: EdgeInsets.symmetric(horizontal: 16.w),
            ),
            ListTileItemSwitchWidget(
              title: 'Desconto Médio',
              value: settingsAppController.settings.hasAverageDiscount ?? false,
              onChanged: settingsAppController.setAverageDiscount,
            ),
          ],
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
