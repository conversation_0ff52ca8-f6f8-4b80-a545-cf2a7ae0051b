import 'dart:async';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:pharmalink/exports/get_exports.dart';

class ConnectivityController extends GetxService {
  // Observable para o status da conexão
  bool isConnected = true;

  // Instância de Connectivity
  final Connectivity _connectivity = Connectivity();

  // Inicializa o serviço
  @override
  void onInit() {
    super.onInit();
    // Inicia o monitoramento da conectividade

    _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  // Atualiza o status da conectividade
  Future<void> _updateConnectionStatus(List<ConnectivityResult> result) async {
    if (result.first == ConnectivityResult.none) {
      isConnected = false;
    } else {
      isConnected = true;
    }
  }
}
