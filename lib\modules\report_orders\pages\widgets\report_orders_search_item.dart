import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/report_orders/models/report_orders_response_model.dart';
import 'package:pharmalink/modules/report_orders/pages/widgets/report_orders_search_detail.dart';
import 'package:pharmalink/modules/report_orders/pages/widgets/report_orders_search_item_secondary.dart';

class ReportOrdersSearchItemWidget extends StatelessWidget {
  const ReportOrdersSearchItemWidget({super.key, required this.item});
  final ReportOrdersListPedidos item;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReportOrdersController>(builder: (ctrl) {
      return SizedBox(
        width: MediaQuery.sizeOf(context).width,
        child: Padding(
          padding: const EdgeInsets.all(4.0),
          child: Card(
            elevation: 3,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            LabelWidget(
                              title: ctrl.getStoreName(item.cnpj!),
                              fontWeight: FontWeight.bold,
                              fontSize: DeviceSize.fontSize(15, 18),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            LabelWidget(
                              title: item.cnpj!,
                              fontSize: DeviceSize.fontSize(12, 15),
                            ),
                          ],
                        ),
                      ),
                      IconButton(
                        onPressed: () {
                          ctrl.openOrdersDetail(item);
                        },
                        icon: Icon(
                          FontAwesomeIcons.ellipsisVertical,
                          color: Colors.black,
                          size: DeviceSize.fontSize(18, 21),
                        ),
                      )
                    ],
                  ),
                  10.toHeightSpace(),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ReportOrdersSearchDetailWidget(
                              title: "Número do\npedido",
                              value: item.nrPedido ?? "-",
                            ),
                            10.toHeightSpace(),
                            ReportOrdersSearchDetailWidget(
                              title: "Nº do pedido\nOriginal",
                              value: item.nrPedidoPrincipal ?? "-",
                            ),
                            10.toHeightSpace(),
                            ReportOrdersSearchDetailWidget(
                              title: "Status",
                              value: item.statusRetornoPedido ?? "-",
                            ),
                          ],
                        ),
                      ),
                      Flexible(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ReportOrdersSearchDetailWidget(
                              title: "Origem do\npedido",
                              value: item.origemPedido?.trimRight() ?? "-",
                            ),
                            10.toHeightSpace(),
                            ReportOrdersSearchDetailWidget(
                              title: "Classificação",
                              value: item.classificacao ?? "Não identificada",
                            ),
                            10.toHeightSpace(),
                            ReportOrdersSearchDetailWidget(
                              title: "Faturamento",
                              value: item.faturamentoStatus ?? "-",
                            ),
                          ],
                        ),
                      ),
                      Flexible(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ReportOrdersSearchDetailWidget(
                              title: "Valor total",
                              value: item.itens!
                                  .map((i) => i.valorLiquidoSolicitado!)
                                  .reduce((value, element) => value + element)
                                  .formatReal(),
                            ),
                            10.toHeightSpace(),
                            ReportOrdersSearchDetailWidget(
                              title: "Forma de\nPagamento",
                              value: item.prazoPagamento ?? "-",
                            ),
                            10.toHeightSpace(),
                          ],
                        ),
                      ),
                    ],
                  ),
                  15.toHeightSpace(),
                  LabelWidget(
                    title:
                        "Pedido realizado em ${item.datapedido!.formatDate(formatType: DateFormatType.ddMMyyyy, applyTimezone: true)}",
                    fontSize: DeviceSize.fontSize(10, 13),
                  ),
                  if (item.pedidosSecundarios != null &&
                      item.pedidosSecundarios!.isNotEmpty)
                    15.toHeightSpace(),
                  if (item.pedidosSecundarios != null &&
                      item.pedidosSecundarios!.isNotEmpty)
                    Container(
                      color: Colors.grey,
                      child: ExpansionPanelList(
                        elevation: 4,
                        expansionCallback: (int index, bool isExpanded) {
                          item.isExpanded = isExpanded;
                          ctrl.update();
                        },
                        children: [
                          ExpansionPanel(
                            backgroundColor: Colors.grey.shade200,
                            headerBuilder:
                                (BuildContext context, bool isExpanded) {
                              return CustomInkWell(
                                onTap: () {
                                  item.isExpanded = item.isExpanded != null
                                      ? !item.isExpanded!
                                      : true;
                                  ctrl.update();
                                },
                                child: const ListTile(
                                  title: Text('Pedidos Secundarios'),
                                ),
                              );
                            },
                            body: Column(
                              children: item.pedidosSecundarios!
                                  .map((e) =>
                                      ReportOrdersSearchItemSecondaryWidget(
                                          item: e))
                                  .toList(),
                            ),
                            isExpanded: item.isExpanded ?? false,
                          )
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
