import 'package:pharmalink/exports/basic_exports.dart';

// ignore: must_be_immutable
class QuestionInputGroupWidget extends StatefulWidget {
  QuestionInputGroupWidget({
    super.key,
    required this.title,
    this.selectedAwser,
    required this.onAnswerSelected,
    this.hint,
    required this.readonly,
    this.value1,
    this.value2,
    this.value3,
    this.value4,
    this.isRequired,
  });

  final bool readonly;
  final String title;
  final String? hint;
  late String? selectedAwser;
  final Function(String, String?) onAnswerSelected;
  final String? value1;
  final String? value2;
  final String? value3;
  final String? value4;
  final bool? isRequired;
  @override
  State<QuestionInputGroupWidget> createState() =>
      QuestionInputGroupWidgetState();
}

class QuestionInputGroupWidgetState extends State<QuestionInputGroupWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          LabelWidget(
            title: widget.title,
            textColor: widget.readonly != true
                ? Colors.grey.shade600
                : Colors.grey.shade400,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              _buildBox(Colors.red, "qtd1", widget.value1 ?? ""),
              _buildBox(Colors.yellow, "qtd2", widget.value2 ?? ""),
              _buildBox(Colors.orange, "qtd3", widget.value3 ?? ""),
              _buildBox(Colors.blue, "qtd4", widget.value4 ?? ""),
            ],
          ),
          Divider(
            thickness: 1,
            color:
                widget.isRequired == true ? Colors.red : Colors.grey.shade300,
          )
        ],
      ),
    );
  }

  Widget _buildBox(Color boxColor, String key, String textValue) {
    return Flexible(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(width: 32, height: 32, color: boxColor),
          SizedBox(
            width: 40,
            child: CustomTextField(
              readOnly: widget.readonly,
              controller: TextEditingController(text: textValue),
              keyboardType: TextInputType.number,
              onChanged: widget.readonly
                  ? null
                  : (String? value) {
                      widget.onAnswerSelected(key, value);
                    },
            ),
          ),
        ],
      ),
    );
  }
}
