import 'package:pharmalink/modules/orders/models/orders_mix_product_special_response.dart';

class SpecialProductsParamsModel {
  int? tabloidId;
  int? paymentDeadlineId;
  List<GetMixSpecialProductsProdutosDistribuidores>? productDistributors;

  SpecialProductsParamsModel(
      {this.tabloidId, this.paymentDeadlineId, this.productDistributors});

  SpecialProductsParamsModel.fromJson(Map<String, dynamic> json) {
    tabloidId = json['TabloidId'];
    paymentDeadlineId = json['PaymentDeadlineId'];
    if (json['ProductDistributors'] != null) {
      productDistributors = <GetMixSpecialProductsProdutosDistribuidores>[];
      json['ProductDistributors'].forEach((v) {
        productDistributors!
            .add(GetMixSpecialProductsProdutosDistribuidores.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['TabloidId'] = tabloidId;
    data['PaymentDeadlineId'] = paymentDeadlineId;
    if (productDistributors != null) {
      data['ProductDistributors'] =
          productDistributors!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
