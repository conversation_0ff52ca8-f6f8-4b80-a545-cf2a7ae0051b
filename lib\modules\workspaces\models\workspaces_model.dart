import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class WorkspacesModel extends SqfLiteBase<WorkspacesModel> {
  int? workspaceId;
  String? name;
  String? link;
  String? token;
  bool? active;
  String? authenticationType;

  WorkspacesModel({
    this.workspaceId,
    this.name,
    this.link,
    this.token,
    this.active,
    this.authenticationType,
  }) : super(DatabaseModels.workspacesModel);

  WorkspacesModel.fromJson(Map<String, dynamic> json)
      : workspaceId = json['workspaceId'],
        name = json['name'],
        link = json['link'],
        token = json['token'],
        active = json['active'],
        authenticationType = json['AuthenticationType'],
        super(DatabaseModels.workspacesModel);

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workspaceId'] = workspaceId;
    data['name'] = name;
    data['link'] = link;
    data['token'] = token;
    data['active'] = active;
    data['AuthenticationType'] = authenticationType;

    return data;
  }

  Future<WorkspacesModel?> getFirst() async {
    var list = await getAll<WorkspacesModel>(WorkspacesModel.fromJson);
    return list.isNotEmpty ? list.first : null;
  }

  Future<WorkspacesModel?> getFirstByKey(
      {String? key, int? workspaceId}) async {
    var list = await getAllByKey<WorkspacesModel>(
        key!, workspaceId, null, null, null, WorkspacesModel.fromJson);
    return list.isNotEmpty ? list.first : null;
  }

  Future<List<WorkspacesModel>> getListByKey(
      {String? key, int? workspaceId}) async {
    var list = await getAllByKey<WorkspacesModel>(
        key!, workspaceId, null, null, null, WorkspacesModel.fromJson);
    return list;
  }

  Future<List<WorkspacesModel>> getList() async {
    var list = await getAll<WorkspacesModel>(WorkspacesModel.fromJson);
    return list;
  }
}
