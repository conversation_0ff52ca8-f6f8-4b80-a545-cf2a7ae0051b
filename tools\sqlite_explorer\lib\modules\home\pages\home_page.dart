﻿import 'dart:developer';

import 'package:sqlite_explorer/exports/basic_exports.dart';
import 'package:sqlite_explorer/exports/get_exports.dart';
import 'package:sqlite_explorer/modules/app_modules.dart';
import 'package:sqlite_explorer/modules/home/<USER>/components/home_content.dart';
import 'package:sqlite_explorer/modules/home/<USER>/components/home_menu.dart';
import 'package:sqlite_explorer/modules/home/<USER>/components/home_sidebar.dart';
import 'package:sqlite_explorer/widgets/label/label_widget.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    // Obter largura da tela
    final screenWidth = MediaQuery.of(context).size.width;

    // Definir flex do HomeSideBar baseado na largura da tela
    int sideBarFlex;
    if (screenWidth > 1200) {
      sideBarFlex = 2; // Monitores grandes
    } else if (screenWidth > 800) {
      sideBarFlex = 3; // Monitores médios
    } else {
      sideBarFlex = 3; // Tablets ou telas pequenas
    }
    log('sideBarFlex: $sideBarFlex');
    return GetBuilder<HomeController>(builder: (ctrl) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.black26,
          title: const LabelWidget(
            title: "SQLite Explorer - Pharmalink",
            fontSize: 18,
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
        ),
        body: Column(
          children: [
            // Horizontal bar with DropdownSearch
            // const HomeHeader(),
            Expanded(
              child: Row(
                children: [
                  // Menu
                  Container(
                    width: 250, // Suficiente para mostrar um ícone
                    color: Colors.black26,
                    padding: const EdgeInsets.all(10),
                    child: const HomeMenu(),
                  ),

                  // Sidebar
                  Expanded(
                    flex: sideBarFlex,
                    child: const HomeSideBar(),
                  ),
                  // Content
                  const Expanded(
                    flex: 5,
                    child: HomeContent(),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }
}
