import 'package:device_preview/device_preview.dart';
import 'package:dynatrace_flutter_plugin/dynatrace_flutter_plugin.dart';
// ignore: depend_on_referenced_packages
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:pharmalink/app/routes/app_routes.dart';
import 'package:pharmalink/app/theme/app_themes.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/splashscreen/binding/splash_binding.dart';

class MyAppPL extends StatelessWidget {
  const MyAppPL({super.key});

  @override
  Widget build(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    final isTablet = mediaQuery.size.shortestSide > 600;
    themeController.loadThemMode();
    appController.loadUrl();
    appController.setDevice(isTablet);

    return MediaQuery.fromView(
      view: View.of(context),
      child: ScreenUtilInit(
        splitScreenMode: true,
        minTextAdapt: true,
        designSize: isTablet ? const Size(800, 1280) : ScreenUtil.defaultSize,
        builder: (context, child) {
          return GetBuilder<ThemesController>(builder: (_) {
            return MediaQuery(
              data: MediaQuery.of(context)
                  .copyWith(textScaler: TextScaler.noScaling),
              child: GetMaterialApp(
                  debugShowCheckedModeBanner: false,
                  title: F.title,
                  theme: AppThemes.themeLight(),
                  getPages: AppPages.pages,
                  initialRoute: RoutesPath.splashPage,
                  initialBinding: SplashBinding(),
                  locale: const Locale('pt', 'BR'),
                  fallbackLocale: DevicePreview.locale(context),
                  supportedLocales: const [Locale('pt', 'BR')],
                  builder: DevicePreview.appBuilder,
                  navigatorObservers: [DynatraceNavigationObserver()],
                  localizationsDelegates: const [
                    GlobalMaterialLocalizations.delegate,
                    GlobalWidgetsLocalizations.delegate,
                    GlobalCupertinoLocalizations.delegate,
                  ]),
            );
          });
        },
      ),
    );
  }
}
