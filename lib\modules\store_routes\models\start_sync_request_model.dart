import 'start_sync_stores_request_model.dart';

class StartSyncRequestModel {
  List<StartSyncStoresRequestModel>? stores;
  String? userId;
  int? workspaceId;

  StartSyncRequestModel({this.stores, this.userId, this.workspaceId});

  StartSyncRequestModel.fromJson(Map<String, dynamic> json) {
    if (json['stores'] != null) {
      stores = <StartSyncStoresRequestModel>[];
      json['stores'].forEach((v) {
        stores!.add(StartSyncStoresRequestModel.fromJson(v));
      });
    }
    userId = json['userId'];
    workspaceId = json['workspaceId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (stores != null) {
      data['stores'] = stores!.map((v) => v.toJson()).toList();
    }
    data['userId'] = userId;
    data['workspaceId'] = workspaceId;
    return data;
  }
}
