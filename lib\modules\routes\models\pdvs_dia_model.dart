class PdvsDiaModel {
  int? idLoja;
  int? idEndereco;
  int? idPdv;
  int? idSetorVisita;

  PdvsDiaModel({
    this.idLoja,
    this.idEndereco,
    this.idPdv,
    this.idSetorVisita,
  });

  PdvsDiaModel.fromJson(Map<String, dynamic> json) {
    idLoja = json['idLoja'];
    idEndereco = json['idEndereco'];
    idPdv = json['idPdv'];
    idSetorVisita = json['idSetorVisita'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idLoja'] = idLoja;
    data['idEndereco'] = idEndereco;
    data['idPdv'] = idPdv;
    data['idSetorVisita'] = idSetorVisita;

    return data;
  }
}
