import 'dart:async';
import 'dart:convert';
import 'dart:developer';

import 'package:dynatrace_flutter_plugin/dynatrace_flutter_plugin.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/store_routes/models/params/order_products_params.dart';
import 'package:pharmalink/modules/store_routes/models/params/sync_result_items_params.dart';
import 'package:pharmalink/modules/store_routes/models/start_sync_request_model.dart';
import 'package:pharmalink/modules/store_routes/models/start_sync_response_model.dart';
import 'package:pharmalink/modules/store_routes/models/start_sync_stores_request_model.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';
import 'package:pharmalink/modules/stores_parameters/enuns/type_order_enum.dart';
import 'package:pharmalink/modules/stores_parameters/models/store_parameters_model.dart';

class StoreRoutesOfflineSyncService extends GetxService
    with TraceableController {
  Future<StoreRoutesOfflineSyncService> init() async {
    return this;
  }

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  Future<void> startSyncOffline(List<StoresModel> dataListStores) async {
    final reportAll = Dynatrace()
        .enterAction("SynchronizationsOfflineService - StartSyncOffline");

    if (dataListStores.isEmpty) {
      SnackbarCustom.snackbarError(
          'Selecione pelo menos uma loja para sincronizar');
      return;
    }

    try {
      log('teste: startSyncOffline ${jsonEncode(dataListStores)}');
      await _deletePreviousSyncData(dataListStores, reportAll);

      final requestSync = _buildSyncRequest(dataListStores);
      final result =
          await synchronizationsOfflineApi.startSync(request: requestSync);

      if (result.error != null) {
        _handleSyncError(result.error!, requestSync, reportAll);
        return;
      }

      await _updateStoreList(result.data!);
      Future.delayed(const Duration(milliseconds: 500), () async {
        await startStatusSyncOffline();
      });

      await refreshListRoutes();
      SnackbarCustom.snackbarSucess(
          'Sucesso', 'Sincronização iniciada com sucesso');
    } catch (e) {
      SnackbarCustom.snackbarError('Erro ao iniciar sincronização');
    } finally {
      reportAll.leaveAction();
    }
  }

  Future<void> _deletePreviousSyncData(
      List<StoresModel> dataListStores, DynatraceAction reportAll) async {
    for (var store in dataListStores) {
      reportAll.reportStringValue(
          "Excluir histórico PDV", "${store.idLoja} - ${store.nomeFantasia}");
      await deleteSyncData(store.idLoja!);
    }
  }

  StartSyncRequestModel _buildSyncRequest(List<StoresModel> dataListStores) {
    return StartSyncRequestModel(
      userId: appController.userLogged!.userId!,
      stores: dataListStores
          .map((e) =>
              StartSyncStoresRequestModel(storeId: e.idLoja!, cnpj: e.cNPJ!))
          .toList(),
      workspaceId: appController.workspace!.workspaceId!,
    );
  }

  Future<void> _handleSyncError(
    HttpError error,
    StartSyncRequestModel requestSync,
    DynatraceAction reportAll,
  ) async {
    reportAll.reportStringValue(
        "Erro ao sincronizar offline", jsonEncode(requestSync));

    SnackbarCustom.snackbarError(error.message!);
  }

  Future<void> _updateStoreList(List<StartSyncResponseModel> syncData) async {
    final storeList = await StoresModel().getList();
    List<StoresModel> storeListUpdate = [];
    if (storeList.isNotEmpty) {
      for (var sync in syncData) {
        final store = storeList
            .firstWhereOrNull((element) => element.idLoja == sync.storeId);
        if (store != null) {
          _updateStoreSyncData(store, sync);
          storeListUpdate.add(store);
        }
      }
      if (storeListUpdate.isNotEmpty) {
        await Future.wait(
            storeListUpdate.map((store) => updateStoreToSql(store)));
      }
    }
  }

  Future<void> updateStoreToSql(StoresModel item) async {
    return trace('updateStoreToSql', () async {
      await dbContext.deleteByKey(
        key: DatabaseModels.storesModel,
        storeId: item.idLoja,
        userId: appController.userLogged?.userId!,
        workspaceId: appController.workspace!.workspaceId,
        isLog: true,
      );
      await dbContext.addData(
        key: DatabaseModels.storesModel,
        data: item,
        storeId: item.idLoja,
        userId: appController.userLogged?.userId!,
        workspaceId: appController.workspace!.workspaceId,
        hashCode: item.dataExtra!.canPlanned == true ? "planned" : null,
        clearCurrentData: false,
      );
      await item.dataExtra?.updateDataExtra(item.dataExtra!);
    });
  }

  void _updateStoreSyncData(StoresModel store, StartSyncResponseModel sync) {
    store.dataExtra!.offlineId = sync.id;
    store.dataExtra!.offlineIsSync = true;
    store.dataExtra!.offlineDateSync = DateTime.now();
    store.dataExtra!.offlineProgress = 0;
    store.dataExtra!.offlineStatus = 1;
    store.dataExtra!.offlineTotalPages = 0;
    store.dataExtra!.offlineCurrentPages = 0;
    store.dataExtra!.offlineSyncStatus = SyncEnum.none;
  }

  Future<void> deleteSyncData(int storeId) async {
    return trace('deleteSyncData', () async {
      await dbContext.deleteByKey(
        key: DatabaseModels.storeParametersModel,
        storeId: storeId,
        workspaceId: appController.workspace!.workspaceId,
        userId: appController.userLogged!.userId!,
        isOnline: false,
      );
      // await dbContext.deleteByKey(
      //   key: DatabaseModels.syncResultItemsModel,
      //   storeId: storeId,
      //   isOnline: false,
      //   userId: appController.userLooged!.userId!,
      //   workspaceId: appController.workspace!.workspaceId,
      // );
    });
  }

  Future<void> refreshListRoutes() async {
    if (Get.isRegistered<StoreRoutesPlannedController>()) {
      await storeRoutesPlannedController.resetAndRefetch();
    }
  }

  Future<bool> verifyStoreStatusCanceled(int storeId) async {
    if (Get.isRegistered<StoreRoutesPlannedController>()) {
      final store = (Get.find<StoreRoutesPlannedController>())
          .storesList
          .firstWhereOrNull((element) => element.idLoja == storeId);
      if (store != null) {
        return store.dataExtra!.offlineSyncStatus == SyncEnum.canceled;
      }
      return false;
    }
    return false;
  }

  Future<void> saveStoresTake(List<StoresModel> data,
      {bool? validStatus}) async {
    return trace('saveStoresTake', () async {
      if (validStatus == true) {
        for (var item in data) {
          final status = await verifyStoreStatusCanceled(item.idLoja!);
          if (status) {
            item.dataExtra!.offlineSyncStatus = SyncEnum.canceled;
            item.dataExtra!.offlineDateSync = DateTime.now();
            item.dataExtra!.offlineId = null;
            item.dataExtra!.offlineIsSync = false;
            item.dataExtra!.offlineDateSync = null;
            item.dataExtra!.offlineProgress = 0;
            item.dataExtra!.offlineStatus = 1;
            item.dataExtra!.offlineTotalPages = 0;
            item.dataExtra!.offlineCurrentPages = 0;
          }
        }
      }

      await Future.wait(data.map((store) => updateStoreToSql(store)));

      await refreshListRoutes();
    });
  }

  StoresModel? getCurrentStore(int storeId) {
    if (Get.isRegistered<StoreRoutesPlannedController>()) {
      final list = (Get.find<StoreRoutesPlannedController>()).storesList;
      if (list.isEmpty) {
        return null;
      }
      return list.firstWhereOrNull((element) => element.idLoja == storeId);
    }
    return null;
  }

  Future<void> startStatusSyncOffline() async {
    await statusSync();
    await checkSyncStatus();
  }

  Future<void> checkSyncStatus() async {
    return trace('checkSyncStatus', () async {
      log('--------------------------------');
      await JsonStorage.readAllFiles();
      log('--------------------------------');
      log("teste: checkSyncStatus ${DateTime.now().formatDate(formatType: DateFormatType.ddMMyyyyHHmmss)}");
      final storeTakes = storeRoutesPlannedController.storesList;

      if (storeTakes.isNotEmpty) {
        for (var item in storeTakes) {
//          log("teste: checkSyncStatus ${item.idLoja} - ${item.razaoSocial} - offlineId: ${item.dataExtra!.offlineId}");
          if (item.dataExtra!.offlineId == null) {
            continue;
          }

          final parameter = await StoreParametersModel().getList(
              appController.workspace!.workspaceId!, item.idLoja, null, false);
          for (var param in parameter) {
            if (param.tipoPedido != "esp") {
              if (param.tipoPedido == "pad" &&
                  !generalParameterizationController.hasOrderDefault()) {
                continue;
              }
              if (param.tipoPedido == "rep" &&
                  !generalParameterizationController.hasOrderRep()) {
                continue;
              }
              //log("teste: checkSyncStatus ${param.tipoPedido} - ${item.idLoja} - ${param.tipoPedido} - offlineId ${item.dataExtra!.offlineId} - condicaoComercialBase: ${param.parametrizacao?.condicaoComercialBase?.id}");
              for (var deadline in param.parametrizacao!.prazoPagamento!) {
                final offlineData = await SyncResultItemsModel().getFirst(
                    storeId: item.idLoja!,
                    paymentDeadlineId: deadline.idPrazoPagamento,
                    merchantConditionId:
                        param.parametrizacao?.condicaoComercialBase?.id,
                    orderTypeId: getTyperOrderValue(param.tipoPedido!));

                if (offlineData != null) {
                  //log("teste: offlineDataEsp encontrado - ${offlineData.id}");
                  bool existsJson = await JsonStorage.exists(offlineData.id!);
                  if (!existsJson) {
                    log("teste: JsonStorage Não Encontrado - ${offlineData.id} - ${param.tipoPedido} - ${deadline.descricao}");
                    log('teste: ${jsonEncode(offlineData.toJson())}');
                    appLog(
                        "Offline: JsonStorage Não Encontrado - ${offlineData.id}",
                        data: {
                          "id": offlineData.id,
                          "storeId": item.idLoja,
                          "userId": appController.userLogged?.userId,
                          "workspaceId": appController.workspace!.workspaceId,
                          "paymentDeadlineId": deadline.idPrazoPagamento,
                          "merchantConditionId":
                              param.parametrizacao?.condicaoComercialBase?.id,
                          "orderTypeId": getTyperOrderValue(param.tipoPedido!)
                        });
                    await saveRemoveOfflineData(item);
                  }
                } else {
                  log("teste: SyncResultItemsModel Não Encontrado -  ${param.tipoPedido} - ${deadline.descricao}");
                  log('teste: arquivo json não encontrado  ${jsonEncode({
                        'storeId': item.idLoja!,
                        'paymentDeadlineId': deadline.idPrazoPagamento,
                        'merchantConditionId':
                            param.parametrizacao?.condicaoComercialBase?.id,
                        'orderTypeId': getTyperOrderValue(param.tipoPedido!)
                      })}');
                  appLog("Offline: SyncResultItemsModel Não Encontrado", data: {
                    "storeId": item.idLoja,
                    "userId": appController.userLogged?.userId,
                    "workspaceId": appController.workspace!.workspaceId,
                    "paymentDeadlineId": deadline.idPrazoPagamento,
                    "merchantConditionId":
                        param.parametrizacao?.condicaoComercialBase?.id,
                    "orderTypeId": getTyperOrderValue(param.tipoPedido!)
                  });
                  await saveRemoveOfflineData(item);
                }
              }
            } else if (param.parametrizacao?.tabloides != null) {
              if (param.tipoPedido == "esp" &&
                  !generalParameterizationController.hasOrderEspecial()) {
                continue;
              }

              final offlineDataEsp = await SyncResultItemsModel().getFirst(
                  storeId: item.idLoja!,
                  orderTypeId: getTyperOrderValue(param.tipoPedido!));
              if (offlineDataEsp != null) {
                bool existsJson = await JsonStorage.exists(offlineDataEsp.id!);
                if (!existsJson) {
                  log("teste: Especial Não Encontrado - ${offlineDataEsp.id} - ${param.tipoPedido}");
                  log('teste: ${jsonEncode(offlineDataEsp.toJson())}');
                  appLog(
                      "Offline: JsonStorage Não Encontrado - ${offlineDataEsp.id}",
                      data: {
                        "id": offlineDataEsp.id,
                        "storeId": item.idLoja,
                        "userId": appController.userLogged?.userId,
                        "workspaceId": appController.workspace!.workspaceId,
                        "orderTypeId": getTyperOrderValue(param.tipoPedido!)
                      });
                  await saveRemoveOfflineData(item);
                }
              } else {
                log("teste: Especial Não Encontrado -  ${param.tipoPedido}");
                log('teste: ${jsonEncode({
                      "storeId": item.idLoja,
                      "userId": appController.userLogged?.userId,
                      "workspaceId": appController.workspace!.workspaceId,
                      "orderTypeId": getTyperOrderValue(param.tipoPedido!)
                    })}');
                appLog("Offline: SyncResultItemsModel Não Encontrado", data: {
                  "storeId": item.idLoja,
                  "userId": appController.userLogged?.userId,
                  "workspaceId": appController.workspace!.workspaceId,
                  "orderTypeId": getTyperOrderValue(param.tipoPedido!)
                });
                await saveRemoveOfflineData(item);
              }
            }
          }
        }
      }
      await refreshListRoutes();
    });
  }

  Future<void> statusSync() async {
    bool isRunning = true;
    DynatraceRootAction reportAll =
        Dynatrace().enterAction("SynchronizationsOfflineService - StatusSync");

    try {
      while (isRunning) {
        final storeTakes = await StoresModel().getList(hashCode: "planned");
        if (storeTakes.isEmpty ||
            !storeTakes
                .any((element) => element.dataExtra!.offlineIsSync == true)) {
          isRunning = false;
          continue;
        }

        for (var item in storeTakes) {
          if (item.dataExtra!.offlineIsSync != true) continue;

          try {
            final status = await synchronizationsOfflineApi.getSyncStatus(
                id: item.dataExtra!.offlineId!);

            if (status.error != null) {
              isRunning = false;
              throw Exception(status.error?.message ??
                  "Ocorreu um erro ao sincronizar os dados offline!");
            }

            if (status.data == null) continue;

            item.dataExtra!.offlineTotalPages = status.data!.totalPages!;
            final currentPageStatus = status.data!.currentPage!;
            final totalPageStatus = status.data!.totalPages!;
            item.dataExtra!.offlineMessage = "";
            bool syncSuccessful = true;

            if (status.data!.status == 4) {
              item.dataExtra!.offlineStatus = status.data!.status;
              syncSuccessful = false;
            }

            if (status.data!.status == 3 &&
                currentPageStatus > 0 &&
                syncSuccessful) {
              while (item.dataExtra!.offlineCurrentPages! < currentPageStatus &&
                  item.dataExtra!.offlineCurrentPages! < totalPageStatus) {
                final currentStore = getCurrentStore(item.idLoja!);

                if (currentStore?.dataExtra!.offlineSyncStatus ==
                    SyncEnum.canceled) {
                  item.dataExtra!.offlineCurrentPages =
                      item.dataExtra!.offlineCurrentPages! + totalPageStatus;
                  reportAll.reportStringValue("Status sync",
                      "Cancelada - ${item.idLoja} - ${item.razaoSocial}");
                  syncSuccessful = false;
                  break;
                }

                item.dataExtra!.offlineCurrentPages =
                    item.dataExtra!.offlineCurrentPages! + 1;

                bool resultSaved = await getOfflineResult(
                  item.idLoja!,
                  item.dataExtra!.offlineId!,
                  item.dataExtra!.offlineCurrentPages!,
                  status.data!.pageSize!,
                  reportAll,
                );

                if (!resultSaved) {
                  syncSuccessful = false;
                  break;
                }

                item.dataExtra!.offlineProgress =
                    (item.dataExtra!.offlineCurrentPages! /
                            status.data!.totalPages!) *
                        100;

                await saveStoresTake(storeTakes, validStatus: true);
              }

              item.dataExtra!.offlineStatus = syncSuccessful ? 3 : 4;
            }

            if (item.dataExtra!.offlineSyncStatus == SyncEnum.canceled) {
              reportAll.reportStringValue("Status sync",
                  "Cancelada - ${item.idLoja} - ${item.razaoSocial}");
              appLog("Sincronização offline - Cancelada", data: {
                "idLoja": item.idLoja,
                "razaoSocial": item.razaoSocial,
                "offlineId": item.dataExtra!.offlineId,
              });

              item.dataExtra!.offlineStatus = 4;
            }

            updateSyncStatus(item, reportAll);
          } catch (e) {
            handleSyncError(item, e, reportAll);
          }
        }

        await saveStoresTake(storeTakes, validStatus: true);
        //await Future.delayed(const Duration(seconds: 5));
      }
    } catch (e) {
      log('statusSync error: ${e.toString()}');
    } finally {
      reportAll.leaveAction();
    }
  }

  void updateSyncStatus(StoresModel item, DynatraceRootAction reportAll) {
    if (item.dataExtra!.offlineStatus == 3) {
      item.dataExtra!.offlineDateSync = DateTime.now();
      item.dataExtra!.offlineSyncStatus = SyncEnum.finished;
      item.dataExtra!.offlineIsSync = false;
      item.dataExtra!.canOrderOfflineSync = true;

      //Logs
      reportAll.reportStringValue("Status de Sincronização",
          "A sincronização foi concluída com sucesso para a loja ${item.idLoja} - ${item.razaoSocial}.");
      appLog("Sincronização offline concluída", data: {
        "idLoja": item.idLoja,
        "razaoSocial": item.razaoSocial,
        "offlineId": item.dataExtra!.offlineId,
      });

      if (item.dataExtra!.offlineCurrentPages == 0) {
        item.dataExtra!.offlineMessage =
            'Nenhum dado retornado durante a sincronização';
        reportAll.reportStringValue("Mensagem",
            "${item.idLoja} - ${item.razaoSocial} - Nenhum dado retornado durante a sincronização");
      }
    } else if (item.dataExtra!.offlineStatus == 4) {
      item.dataExtra!.offlineDateSync = null;
      item.dataExtra!.offlineSyncStatus = SyncEnum.none;
      item.dataExtra!.offlineIsSync = false;
      item.dataExtra!.offlineMessage =
          'Falha na sincronização. Por favor, tente novamente mais tarde.';

      reportAll.reportStringValue("Mensagem",
          "${item.idLoja} - ${item.razaoSocial} - Falha na sincronização");
      appLog("Erro na sincronização offline", data: {
        "idLoja": item.idLoja,
        "razaoSocial": item.razaoSocial,
        "offlineId": item.dataExtra!.offlineId,
      });
    }
  }

  void handleSyncError(
      StoresModel item, dynamic e, DynatraceRootAction reportAll) {
    item.dataExtra!.offlineStatus = 4;
    item.dataExtra!.offlineMessage =
        'Erro durante a sincronização: ${e.toString()}';
    //Grava no log do app
    appLog("Falha na Sincronização do Offline", data: {
      "message":
          "A sincronização falhou para a loja ${item.idLoja} (${item.razaoSocial}). Detalhes do erro: ${e.toString()}. Por favor, verifique a conexão e tente novamente.",
    });
    //Gravar no Dynatrace
    reportAll.reportStringValue("Status sync",
        "Sincronização com falha - ${item.idLoja} - ${item.razaoSocial} - Error Message: ${e.toString()}");
  }

  Future<bool> getOfflineResult(int storeId, String id, int page, int pageSize,
      DynatraceRootAction reportAll) async {
    try {
      final result = await synchronizationsOfflineApi.getSyncResult(
          id: id, index: page, size: pageSize);

      if (result.data != null) {
        final offlineResult = result.data!;
        if (offlineResult.items!.isNotEmpty) {
          bool parameterizationSaved = await saveStoreParameterizations(
              offlineResult.synchronizationData!.storeParameterizations!);
          if (!parameterizationSaved) {
            throw Exception("Failed to save store parameterizations");
          }
          reportAll.reportStringValue(
              "Salvar parametrização da loja", storeId.toString());

          for (var element in offlineResult.items!) {
            bool itemSaved = await saveStoreItems(element, reportAll);
            if (!itemSaved) {
              throw Exception("Failed to save store items");
            }
          }
          return true; // Indica que tudo foi salvo com sucesso
        }
      } else {
        throw Exception(
            'Erro ao obter o resultado offline: ${result.error?.message ?? "Mensagem de erro não disponível. Por favor, tente novamente."}');
      }
    } catch (e) {
      reportAll.reportStringValue('Erro ao obter resultado offline',
          'ID da Loja: ${storeId.toString()} - Mensagem de Erro: ${e.toString()}');
      appLog("Falha na Sincronização do Offline", data: {
        "message":
            "Erro ao obter resultado offline - ID da Loja: ${storeId.toString()} - Mensagem de Erro: ${e.toString()}",
      });
      return false; // Indica que ocorreu um erro
    }
    return false; // Retorna false se não houver itens para processar
  }

  Future<bool> saveStoreParameterizations(String jsonString) async {
    return trace('saveStoreParameterizations', () async {
      try {
        List<dynamic> list = jsonDecode(jsonString);
        List<StoreParametersModel> storeParametersList =
            list.map((item) => StoreParametersModel.fromJson(item)).toList();
        for (var item in storeParametersList) {
          await dbContext.addData(
            key: DatabaseModels.storeParametersModel,
            data: item,
            workspaceId: appController.workspace!.workspaceId,
            userId: appController.userLogged!.userId!,
            storeId: item.idLoja!,
            hashCode: item.tipoPedido!,
            clearCurrentData: true,
            isOnline: false,
          );
        }
        return true;
      } catch (e) {
        log('saveStoreParameterizations error: ${e.toString()}');
        return false;
      }
    });
  }

  Future<bool> saveStoreItems(
      SyncResultItemsModel item, DynatraceRootAction reportAll) async {
    return trace('saveStoreItems', () async {
      try {
        dynamic paramSync = jsonDecode(item.synchronizationParams!);
        final synchronizationParams =
            OrderProductsParamsModel.fromJson(paramSync);

        item.storeId = synchronizationParams.storeId!;
        item.merchantConditionId = synchronizationParams.merchantConditionId;
        item.paymentDeadlineId = synchronizationParams.paymentDeadlineId;
        item.orderTypeId = synchronizationParams.orderType!;

        log('teste: saveStoreItems storeId: ${item.storeId} - paymentDeadlineId: ${item.paymentDeadlineId} - merchantConditionId: ${item.merchantConditionId} - orderTypeId:${item.orderTypeId}');

        await JsonStorage.save(item, item.id!);
        final labelOrderType = getTyperOrderText(item.orderTypeId ?? 0);

        reportAll.reportStringValue("Salvar json local",
            "${item.storeId} - ${item.id ?? ""} - Tipo: $labelOrderType - Prazo Pagamento: ${item.paymentDeadlineId ?? ""} - Condição comercial: ${item.merchantConditionId ?? ""}");

        item.synchronizationTabloidResult = null;
        item.synchronizationSpecialProductsResult = null;
        item.synchronizationIdealMixResult = null;
        item.synchronizationParams = null;
        item.synchronizationResult = null;

        final hasCode =
            '${item.storeId}:${item.merchantConditionId}:${item.paymentDeadlineId}:${synchronizationParams.orderType!}';

        await dbContext.addData(
            key: DatabaseModels.syncResultItemsModel,
            data: item,
            workspaceId: appController.workspace!.workspaceId,
            userId: appController.userLogged!.userId!,
            storeId: item.storeId!,
            isOnline: false,
            hashCode: hasCode,
            clearCurrentData: true);

        log('teste Save syncResultItemsModel ${jsonEncode({
              'key': DatabaseModels.syncResultItemsModel,
              'data': item,
              'workspaceId': appController.workspace!.workspaceId,
              'userId': appController.userLogged!.userId!,
              'storeId': item.storeId!,
              'isOnline': false,
              'hashCode': hasCode,
              'clearCurrentData': true
            })}');

        return true;
      } catch (e) {
        log('saveStoreItems error: ${e.toString()}  ');
        return false;
      }
    });
  }

  Future<void> saveRemoveOfflineData(StoresModel store) async {
    store.dataExtra!.offlineId = null;
    store.dataExtra!.offlineIsSync = false;
    store.dataExtra!.offlineDateSync = null;
    store.dataExtra!.offlineProgress = 0;
    store.dataExtra!.offlineStatus = 0;
    store.dataExtra!.offlineTotalPages = 0;
    store.dataExtra!.offlineCurrentPages = 0;
    store.dataExtra!.offlineSyncStatus = SyncEnum.none;
    await updateStoreToSql(store);
    appLog(
        "Salvar remoção do offline (saveRemoveOfflineData) ${store.idLoja} - ${store.razaoSocial}",
        data: {
          "key": DatabaseModels.storesTakeModel,
          "data": store.toJson(),
          "storeId": store.idLoja,
          "userId": appController.userLogged?.userId!,
          "workspaceId": appController.workspace!.workspaceId,
        });
    log("teste: saveRemoveOfflineData ${store.idLoja} - ${store.razaoSocial}");
  }
}
