import 'dart:io';
import 'package:flutter/widgets.dart';
import 'package:image_picker/image_picker.dart';

class CameraPickerModel {
  final File file;
  final String path;
  final String name;
  final int size;
  final Orientation orientation;

  CameraPickerModel({
    required this.file,
    required this.path,
    required this.name,
    required this.size,
    this.orientation = Orientation.portrait,
  });

  factory CameraPickerModel.fromXFile(
    XFile xFile, {
    Orientation orientation = Orientation.portrait,
  }) {
    final file = File(xFile.path);
    return CameraPickerModel(
      file: file,
      path: xFile.path,
      name: xFile.name,
      size: file.lengthSync(),
      orientation: orientation,
    );
  }
}
