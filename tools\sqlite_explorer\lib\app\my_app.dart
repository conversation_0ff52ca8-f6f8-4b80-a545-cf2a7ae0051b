// ignore: depend_on_referenced_packages
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:sqlite_explorer/app/routes/app_routes.dart';
import 'package:sqlite_explorer/exports/basic_exports.dart';
import 'package:sqlite_explorer/exports/get_exports.dart';
import 'package:sqlite_explorer/modules/app_modules.dart';

class MyAppPL extends StatelessWidget {
  const MyAppPL({super.key});

  @override
  Widget build(BuildContext context) {
    appController.loadUrl();

    return MediaQuery.fromView(
      view: View.of(context),
      child: GetMaterialApp(
        debugShowCheckedModeBanner: false,
        title: 'SQLite Explorer - Pharmalink',
        getPages: AppPages.pages,
        initialRoute: RoutesPath.home,
        initialBinding: HomeBinding(),
        locale: const Locale('pt', 'BR'),
        supportedLocales: const [Locale('pt', 'BR')],
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        theme: ThemeData(
          useMaterial3: true,
          brightness: Brightness.light,
          // Defina outras propriedades do tema claro aqui
        ),
        darkTheme: ThemeData(
          useMaterial3: true,
          brightness: Brightness.dark,
          // Defina outras propriedades do tema escuro aqui
        ),
        themeMode: ThemeMode.system, // Usa o tema do sistema (claro/escuro)
      ),
    );
  }
}
