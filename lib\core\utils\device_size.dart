import 'package:pharmalink/exports/basic_exports.dart';

class DeviceSize {
  static double fontSize(double mobile, double tablet) {
    return appController.isTablet ? tablet : mobile;
  }

  static double width(double mobile, double tablet) {
    return appController.isTablet ? tablet : mobile;
  }

  static double widthTabletDynamic(double mobile, int count, double fullWidth) {
    return appController.isTablet ? (fullWidth / count) : mobile;
  }

  static double height(double mobile, double tablet) {
    return appController.isTablet ? tablet : mobile;
  }

  static double scale() {
    return appController.isTablet ? 1.0 : 1.3;
  }
}
