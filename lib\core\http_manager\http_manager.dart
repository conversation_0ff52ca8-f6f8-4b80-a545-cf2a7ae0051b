import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:pharmalink/app/utils/app_strings.dart';
import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/core/http_result/http_response.dart';
import 'package:pharmalink/core/models/result_error.dart';
import 'package:pharmalink/modules/logs_http/models/logs_http_model.dart';

abstract class HttpMethods {
  static const String get = 'GET';
  static const String post = 'POST';
  static const String delete = 'DELETE';
  static const String patch = 'PATCH';
  static const String put = 'PUT';
}

typedef ParserError<T> = T Function(dynamic errorData);

class HttpManager {
  final Dio _dio;

  HttpManager(this._dio) {
    final myStatuses = {
      408,
      429,
      503,
      504,
      460,
      499,
      520,
      521,
      522,
      523,
      524,
      525,
      598,
      599,
    };
    _dio.interceptors.add(
      RetryInterceptor(
        dio: _dio,
        logPrint: log, // specify log function (optional)
        retries: 4, // retry count (optional)
        retryEvaluator: DefaultRetryEvaluator(myStatuses).evaluate,
        retryDelays: const [
          Duration(milliseconds: 500), // wait 1 sec before the first retry
          Duration(milliseconds: 500), // wait 2 sec before the second retry
          Duration(seconds: 1), // wait 3 sec before the third retry
          Duration(seconds: 1), // wait 4 sec before the fourth retry
        ],
      ),
    );
    // _dio.interceptors.add(GzipInterceptor());
  }

  Future<Response<dynamic>> restRequestWorkspace({
    required String url,
    required String method,
    Map? headers,
    Map? body,
    Map<String, dynamic>? formData,
  }) async {
    bool isConnected = await appController.checkConnectivity();
    if (!isConnected) {
      final responseError = Response(
        statusCode: 999,
        statusMessage: AppStrings.noInternet,
        data: {'error': AppStrings.noInternet, 'code': 999},
        requestOptions: RequestOptions(method: method, headers: null),
      );
      return responseError;
    }

    final defaultHeaders =
        headers?.cast<String, String>() ?? {}
          ..addAll({
            'Content-type': 'application/json',
            'Content-Encoding': 'gzip',
          });

    try {
      Response response = await _dio.request(
        '${appController.apiUrlAzure}$url',
        options: Options(method: method, headers: defaultHeaders),
        data: formData != null ? FormData.fromMap(formData) : body,
      );

      return response;
    } on DioException catch (error) {
      if (error.response?.statusCode == 401) {
        Future.delayed(Duration.zero, () async {
          await loginController.logoutForce();
        });
        final responseError = Response(
          statusCode: error.response?.statusCode,
          statusMessage: 'Sem autorização, autentique-se novamente',
          data: {
            'error': 'Sem autorização, autentique-se novamente',
            'code': error.response?.statusCode ?? 401,
          },
          requestOptions: RequestOptions(
            method: method,
            headers: defaultHeaders,
          ),
        );
        return responseError;
      }
      final responseError = Response(
        statusCode: error.response?.statusCode,
        statusMessage:
            error.response?.statusMessage ?? 'Atenção ocorreu um erro',
        data: {
          'error': error.error != null ? error.error.toString() : error.message,
          'code': error.response?.statusCode ?? 500,
        },
        requestOptions: RequestOptions(method: method, headers: defaultHeaders),
      );

      return responseError;
    } catch (error) {
      return error as Response;
    }
  }

  Future<void> saveLog(String baseUrl, String defaultHeaders, String body,
      String response, int statusCode, String method) async {
    logsHttpController.addLog(LogsHttpModel(
      workspaceId: appController.workspace?.workspaceId,
      workspaceName: appController.workspace?.name,
      userName: appController.userLogged?.userName ?? "",
      url: baseUrl,
      headers: defaultHeaders,
      request: body,
      response: response,
      createdAt: DateTime.now(),
      method: method,
      statusCode: statusCode,
      isFlutterError: false,
    ));
  }

  Future<Response<dynamic>> restRequest({
    required String url,
    required String method,
    Map? headers,
    String? baseUrl,
    String? contentType,
    Map? body,
    Object? bodyObject,
    Map<String, dynamic>? formData,
  }) async {
    Map<String, String>? defaultHeaders;
    bool isConnected = await appController.checkConnectivity();
    if (!isConnected) {
      final responseError = Response(
        statusCode: 999,
        statusMessage: AppStrings.noInternet,
        data: {'error': AppStrings.noInternet, 'code': 999},
        requestOptions: RequestOptions(method: method, headers: null),
      );
      return responseError;
    }

    defaultHeaders = headers?.cast<String, String>() ?? {}
      ..addAll({
        'Content-type': 'application/json',
        'Content-Encoding': 'gzip',
        'Authorization': appController.hasUserLogged
            ? '${appController.userLogged!.tokenType} ${appController.userLogged!.accessToken}'
            : ''
      });
    try {
      if (baseUrl != null) {
        log('baseUrl $baseUrl');
      } else {
        log('link: ${appController.workspace!.link}$url');
        log('${appController.userLogged!.tokenType} ${appController.userLogged!.accessToken}');
      }
      if (body != null) {
        log('Body: ${jsonEncode(body)}');
      }

      Response response = await _dio.request(
        baseUrl ?? '${appController.workspace!.link}$url',
        options: Options(
          method: method,
          contentType: contentType,
          headers: defaultHeaders,
        ),
        data:
            formData != null ? FormData.fromMap(formData) : body ?? bodyObject,
      );

      return response;
    } on DioException catch (error) {
      try {
        if (error.type == DioExceptionType.connectionError &&
            error.error is SocketException) {
          final errorSocket = error.error as SocketException;
          await saveLog(
            baseUrl ?? '${appController.workspace!.link}$url',
            jsonEncode(defaultHeaders),
            jsonEncode(
              formData != null
                  ? FormData.fromMap(formData)
                  : body ?? bodyObject,
            ),
            jsonEncode({
              'message': errorSocket.message,
              'code': errorSocket.osError?.errorCode ?? 7,
              'response': errorSocket.osError?.message ?? "",
              'statusMessage': errorSocket.osError?.message ?? "",
            }),
            errorSocket.osError?.errorCode ?? 7,
            method,
          );
          final responseError = Response(
            statusCode: errorSocket.osError?.errorCode ?? 7,
            statusMessage:
                "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
            data: {
              'error':
                  "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
              'code': errorSocket.osError?.errorCode ?? 7,
            },
            requestOptions: RequestOptions(
              method: method,
              headers: defaultHeaders,
            ),
          );
          await appController.testInternetConnection(closeMessage: true);

          return responseError;
        } else {
          await saveLog(
            baseUrl ?? '${appController.workspace!.link}$url',
            jsonEncode(defaultHeaders),
            jsonEncode(
              formData != null
                  ? FormData.fromMap(formData)
                  : body ?? bodyObject,
            ),
            jsonEncode({
              'message': error.message,
              'code': error.response?.statusCode ?? 500,
              'response': error.response?.data ?? "",
              'statusMessage': error.response?.statusMessage ?? "",
            }),
            error.response?.statusCode! ?? 500,
            method,
          );

          if (error.response?.statusCode == 401) {
            Future.delayed(Duration.zero, () async {
              await loginController.logoutForce();
            });
            final responseError = Response(
              statusCode: error.response?.statusCode,
              statusMessage: 'Sem autorização, autentique-se novamente',
              data: {
                'error': 'Sem autorização, autentique-se novamente',
                'code': error.response?.statusCode ?? 401,
              },
              requestOptions: RequestOptions(
                method: method,
                headers: defaultHeaders,
              ),
            );
            return responseError;
          }

          final dataError = ResultLoginError.fromJson(error.response?.data);

          final responseError = Response(
            statusCode: error.response?.statusCode ?? 500,
            statusMessage:
                dataError.errorDescription ??
                dataError.message ??
                "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
            data: {
              'error':
                  dataError.errorDescription ??
                  dataError.message ??
                  "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
              'code': error.response?.statusCode ?? 500,
            },
            requestOptions: RequestOptions(
              method: method,
              headers: defaultHeaders,
            ),
          );

          return responseError;
        }
      } catch (ex) {
        final responseError = Response(
          statusCode: error.response?.statusCode ?? 500,
          statusMessage:
              "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
          data: {
            'error':
                "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
            'code': error.response?.statusCode ?? 500,
          },
          requestOptions: RequestOptions(
            method: method,
            headers: defaultHeaders,
          ),
        );

        return responseError;
      }
    } catch (error) {
      return error as Response;
    }
  }

  Future<HttpResponse<T>> request<T>({
    required String path,
    String method = 'GET',
    Map? headers,
    String? baseUrl,
    String? contentType,
    Map? body,
    Object? bodyObject,
    Map<String, dynamic>? formData,
    T Function(dynamic data)? parser,
    ParserError<T>? parserError,
  }) async {
    final stopwatch = Stopwatch()..start();
    log('Starting request to $path');

    bool isConnected = await appController.checkConnectivity();
    if (!isConnected) {
      return HttpResponse.fail<T>(
        statusCode: 999,
        message: AppStrings.noInternet,
        data: ResultError(code: 999, error: AppStrings.noInternet),
      );
    }

    final defaultHeaders = {
      'Content-type': 'application/json',
      'Content-Encoding': 'gzip',
      'Authorization': appController.hasUserLogged
          ? '${appController.userLogged!.tokenType} ${appController.userLogged!.accessToken}'
          : '',
      ...?headers?.cast<String, String>(),
    };

    try {
      final url = baseUrl ?? '${appController.workspace!.link}$path';
      log('Request URL: $url');
      if (headers != null) log('Headers: ${jsonEncode(headers)}');
      if (body != null) log('Body: ${jsonEncode(body)}');
      if (bodyObject != null) log('Body: ${jsonEncode(bodyObject)}');

      final response = await _dio.request(
        url,
        options: Options(
          method: method,
          contentType: contentType,
          headers: defaultHeaders,
        ),
        data:
            formData != null ? FormData.fromMap(formData) : body ?? bodyObject,
      );

      stopwatch.stop();
      log('============================================================');
      log('Request to $path completed in ${stopwatch.elapsedMilliseconds}ms');
      log('============================================================');

      if (parser != null) {
        return HttpResponse.success<T>(parser(response.data));
      }
      return HttpResponse.success<T>(response.data as T);
    } on DioException catch (error) {
      return _handleDioError<T>(
        error,
        path,
        defaultHeaders,
        formData,
        body,
        bodyObject,
        method,
        parserError,
      );
    } catch (error) {
      return HttpResponse.fail<T>(
        statusCode: 400,
        message: error.toString(),
        data: ResultError(code: 500, error: error.toString()),
      );
    }
  }

  Future<HttpResponse<T>> _handleDioError<T>(
    DioException error,
    String path,
    Map<String, String> defaultHeaders,
    Map<String, dynamic>? formData,
    Map? body,
    Object? bodyObject,
    String method,
    ParserError<T>? parserError,
  ) async {
    if (error.type == DioExceptionType.connectionError &&
        error.error is SocketException) {
      return _handleSocketException<T>(
        error,
        path,
        defaultHeaders,
        formData,
        body,
        bodyObject,
        method,
      );
    }

    await _saveErrorLog(error, path, defaultHeaders, formData, body, method);

    if (error.response?.statusCode == 401) {
      await loginController.logoutForce();
      return HttpResponse.fail<T>(
        statusCode: 401,
        message: 'Sem autorização, autentique-se novamente',
        data: ResultError(
          code: 401,
          error: 'Sem autorização, autentique-se novamente',
        ),
      );
    }

    if (parserError != null) {
      return HttpResponse.fail<T>(
        statusCode: error.response?.statusCode ?? 500,
        message: error.message ?? '',
        data: parserError(error.response?.data),
      );
    }

    if (error.type == DioExceptionType.connectionTimeout) {
      return HttpResponse.fail<T>(
        statusCode: 408,
        message: error.toString(),
        data: ResultError(code: 408, error: error.error.toString()),
      );
    }

    return HttpResponse.fail<T>(
      statusCode: error.response?.statusCode ?? 500,
      message: error.toString(),
      data: ResultError(
        code: error.response?.statusCode ?? 500,
        error: error.error.toString(),
      ),
    );
  }

  Future<HttpResponse<T>> _handleSocketException<T>(
    DioException error,
    String path,
    Map<String, String> defaultHeaders,
    Map<String, dynamic>? formData,
    Map? body,
    Object? bodyObject,
    String method,
  ) async {
    final errorSocket = error.error as SocketException;
    await saveLog(
      path,
      jsonEncode(defaultHeaders),
      jsonEncode(formData ?? body ?? bodyObject),
      jsonEncode({
        'message': errorSocket.message,
        'code': errorSocket.osError?.errorCode ?? 7,
        'response': errorSocket.osError?.message ?? "",
        'statusMessage': errorSocket.osError?.message ?? "",
      }),
      errorSocket.osError?.errorCode ?? 7,
      method,
    );

    await appController.testInternetConnection(closeMessage: true);
    return HttpResponse.fail<T>(
      statusCode: 7,
      message:
          "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
      data: ResultError(
        code: 7,
        error:
            "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
      ),
    );
  }

  Future<void> _saveErrorLog(
    DioException error,
    String path,
    Map<String, String> defaultHeaders,
    Map<String, dynamic>? formData,
    Map? body,
    String method,
  ) async {
    await saveLog(
      path,
      jsonEncode(defaultHeaders),
      jsonEncode(formData ?? body),
      jsonEncode({
        'message': error.message,
        'code': error.response?.statusCode ?? 500,
        'response': error.response?.data ?? "",
        'statusMessage': error.response?.statusMessage ?? "",
      }),
      error.response?.statusCode ?? 500,
      method,
    );
  }

  Future<HttpResponse<T>> requestFull<T, E>({
    required String path,
    String method = 'GET',
    Map? headers,
    String? baseUrl,
    String? contentType,
    Map? body,
    Map<String, dynamic>? formData,
    T Function(dynamic data)? parser,
    ParserError<E>? parserError,
    ResponseType? responseType,
  }) async {
    bool isConnected = await appController.checkConnectivity();
    if (!isConnected) {
      return HttpResponse.fail<T>(
        statusCode: 999,
        message: AppStrings.noInternet,
        data: ResultError(code: 999, error: AppStrings.noInternet),
      );
    }

    final defaultHeaders = headers?.cast<String, String>() ?? {}
      ..addAll({
        'Content-type': 'application/json',
        'Content-Encoding': 'gzip',
        'Authorization': appController.hasUserLogged
            ? '${appController.userLogged!.tokenType} ${appController.userLogged!.accessToken}'
            : ''
      });

    try {
      if (baseUrl != null) {
        log('baseUrl $baseUrl');
      } else {
        log('link: ${appController.workspace!.link}$path');
        log('token: ${appController.workspace!.token}');
      }
      if (body != null) {
        log('Body: ${jsonEncode(body)}');
      }

      final response = await _dio.request(
        baseUrl ?? '${appController.workspace!.link}$path',
        options: Options(
          method: method,
          contentType: contentType,
          headers: defaultHeaders,
          responseType: responseType ?? ResponseType.json,
        ),
        data: formData != null ? FormData.fromMap(formData) : body,
      );

      if (parser != null) {
        return HttpResponse.success<T>(parser(response.data));
      }
      // ignore: argument_type_not_assignable
      return HttpResponse.success<T>(response.data);
    } on DioException catch (error) {
      try {
        log('error: ${error.message}', level: 1000, name: 'ERROR');
        log(
          'error message: ${error.response?.data['ModelState']['Erros']}',
          level: 1000,
          name: 'ERROR',
        );
        if (error.type == DioExceptionType.connectionError &&
            error.error is SocketException) {
          final errorSocket = error.error as SocketException;
          await saveLog(
            baseUrl ?? '${appController.workspace!.link}$path',
            jsonEncode(defaultHeaders),
            jsonEncode(
              formData != null ? FormData.fromMap(formData) : body ?? formData,
            ),
            jsonEncode({
              'message': errorSocket.message,
              'code': errorSocket.osError?.errorCode ?? 7,
              'response': errorSocket.osError?.message ?? "",
              'statusMessage': errorSocket.osError?.message ?? "",
            }),
            errorSocket.osError?.errorCode ?? 7,
            method,
          );

          await appController.testInternetConnection(closeMessage: true);

          return HttpResponse.fail<T>(
            statusCode: 7,
            message:
                "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
            data: ResultError(
              code: 7,
              error:
                  "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
            ),
          );
        } else {
          // ignore: unrelated_type_equality_checks
          await saveLog(
            baseUrl ?? '${appController.workspace!.link}$path',
            jsonEncode(defaultHeaders),
            jsonEncode(formData != null ? FormData.fromMap(formData) : body),
            jsonEncode({
              'message': error.message,
              'code': error.response?.statusCode ?? 500,
              'response': error.response?.data ?? "",
              'statusMessage': error.response?.statusMessage ?? "",
            }),
            error.response?.statusCode! ?? 500,
            method,
          );
          if (error.response?.statusCode == 401) {
            Future.delayed(Duration.zero, () async {
              await loginController.logoutForce();
            });
            return HttpResponse.fail<T>(
              statusCode: error.response?.statusCode,
              message: 'Sem autorização, autentique-se novamente',
              data: ResultError(
                code: error.response!.statusCode!,
                error: 'Sem autorização, autentique-se novamente',
              ),
            );
          }

          if (parserError != null) {
            dynamic parsedError = error.response?.data ?? {};
            parsedError = parserError(error.response?.data);

            return HttpResponse.fail<T>(
              statusCode: error.response?.statusCode ?? 500,
              message: error.message,
              data: parsedError,
            );
          } else {
            if (error.type == DioExceptionType.connectionTimeout) {
              return HttpResponse.fail<T>(
                statusCode: 408,
                message: error.toString(),
                data: ResultError(code: 408, error: error.error.toString()),
              );
            }

            // ignore: unrelated_type_equality_checks
            if (error.type == DioException.connectionError) {
              return HttpResponse.fail<T>(
                statusCode: 502,
                message: error.toString(),
                data: ResultError(code: 502, error: error.error.toString()),
              );
            }
            if (parserError != null) {
              return HttpResponse.fail<T>(
                statusCode: error.response?.statusCode! ?? 500,
                message: error.toString(),
                data: parserError(error.response?.data),
              );
            }

            return HttpResponse.fail<T>(
              statusCode: error.response?.statusCode! ?? 500,
              message: error.toString(),
              data: ResultError(
                code: error.response?.statusCode! ?? 500,
                error: error.error.toString(),
              ),
            );
          }
        }
      } catch (ex) {
        final resultError = HttpResponse.fail<T>(
          statusCode: error.response?.statusCode! ?? 500,
          message:
              "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
          data: ResultError(
            code: error.response?.statusCode! ?? 500,
            error:
                "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
          ),
        );

        return resultError;
      }
    } catch (error) {
      return HttpResponse.fail<T>(
        statusCode: 400,
        message: error.toString(),
        data: ResultError(code: 500, error: error.toString()),
      );
    }
  }
}
