import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/enuns/permission_code_enum.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/general_parameterization/models/general_parameterization_model.dart';
import 'package:pharmalink/modules/general_parameterization/models/general_settings_order_discount_registration_model.dart';
import 'package:pharmalink/modules/general_parameterization/models/parameter_by_key_model.dart';
import 'package:pharmalink/modules/general_parameterization/models/system_parameterization_model.dart';
import 'package:pharmalink/modules/orders/models/orders_filter_default_model.dart';

class GeneralParameterizationController
    extends GetxControllerInstrumentado<GeneralParameterizationController> {
  GeneralParameterizationController();

  ParameterByKeyModel? parameterByKey;
  List<SystemParameterizationPermissoesAcessoModel> accessPermission = [];
  GeneralSettingsOrderDiscountRegistrationModel?
      generalSettingsOrderDiscountRegistration;
  ProductParameterModel? parameter;
  List<ProductFilterDefaultModel>? filterCustom;

  Future<void> getData({bool? forceUpdate}) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("General Parameter - GetData");

    try {
      //Buscar na base local
      var hasConnection = await appController
          .withDynatraceAction(dynatraceAction)
          .checkConnectivity();

      if (forceUpdate != true) {
        final generalParameterizationBox = await ProductParameterModel()
            .getFirst(workspaceId: appController.workspace!.workspaceId!);
        if (generalParameterizationBox != null) {
          parameter = generalParameterizationBox;
          return;
        }
      }

      if (!hasConnection) {
        subAction.reportValue("hasConnection", AppStrings.noInternet);
        SnackbarCustom.snackbarError(AppStrings.noInternet);
        return;
      }

      //Buscar na API
      final result = await generalParameterizationApi.getParameters();
      if (result.error != null) {
        // SnackbarCustom.snackbarError(result.error!.message!);
        subAction.reportError(result.error!.message,
            errorCode: result.error!.statusCode);
        throw Exception(result.error!.message);
      } else {
        subAction.reportValue("getParameters", result.data!);

        await dbContext.withControllerAction(this).addData(
            key: DatabaseModels.productParameterModel,
            data: result.data!,
            workspaceId: appController.workspace!.workspaceId,
            clearCurrentData: true);

        parameter = result.data!;
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> getFilterCustom({bool? forceUpdate}) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("General Parameter - GetFilterCustom");

    try {
      if (forceUpdate != true) {
        final productFilterDefaultListBox = await ProductFilterDefaultModel()
            .getList(workspaceId: appController.workspace!.workspaceId!);

        if (productFilterDefaultListBox.isNotEmpty) {
          subAction.reportValue(
              "productFilterDefaultListBox", productFilterDefaultListBox);
          filterCustom = productFilterDefaultListBox;
          return;
        }

        if (!await appController.checkConnectivity()) {
          return;
        }
      }

      final result = await generalParameterizationApi.getParameterCustom();
      if (result.error != null) {
        subAction.reportError(result.error?.message,
            errorCode: result.error?.statusCode);
        throw Exception(result.error?.message);
      } else {
        await dbContext.withControllerAction(this).addData(
            key: DatabaseModels.productFilterDefaultModel,
            data: result.data!,
            workspaceId: appController.workspace!.workspaceId,
            clearCurrentData: true);

        subAction.reportValue("getParameterCustom", result.data!);
        filterCustom = result.data!;
      }
    } catch (e, s) {
      // print("Ocorreu um erro: getFilterCustom");
      // print(e.toString());
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> getSystemParameterization({bool? forceUpdate}) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("General Parameter - GetFilterCustom");

    try {
      //Buscar na base local
      if (forceUpdate != true) {
        final accessPermissionBox =
            await SystemParameterizationPermissoesAcessoModel().getList();
        if (accessPermissionBox.isNotEmpty) {
          accessPermission = accessPermissionBox;
          return;
        }
      }
      //Buscar na API
      final result =
          await generalParameterizationApi.getSystemParameterization();
      if (result.error != null) {
        subAction.reportError(result.error?.message,
            errorCode: result.error?.statusCode);
        throw Exception(result.error?.message ?? "Erro desconhecido");
      } else {
        accessPermission = result.data!.permissoesAcesso!;
        await dbContext.withControllerAction(this).addData(
            key: DatabaseModels.systemParameterizationPermissoesAcessoModel,
            data: accessPermission,
            workspaceId: appController.workspace!.workspaceId,
            userId: appController.userLogged!.userId,
            clearCurrentData: true);
      }
    } catch (e) {
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> getGeneralSettingsOrderDiscountRegistration(
      {bool? forceUpdate}) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
        "General Paramter - getGeneralSettingsOrderDiscountRegistration");
    try {
      //Buscar na base local
      if (forceUpdate != true) {
        final boxInfo =
            await GeneralSettingsOrderDiscountRegistrationModel().getFirst(
          workspaceId: appController.workspace!.workspaceId!,
          userId: appController.userLogged!.userId!,
        );

        if (boxInfo != null) {
          generalSettingsOrderDiscountRegistration = boxInfo;
          return;
        }
      }

      //Buscar na API
      final result = await generalParameterizationApi
          .getGeneralSettingsOrderDiscountRegistration();
      if (result.error != null) {
        SnackbarCustom.snackbarError(
            result.error?.message ?? "Erro desconhecido");
        throw Exception(result.error?.message ?? "Erro desconhecido");
      } else {
        generalSettingsOrderDiscountRegistration = result.data!;

        await dbContext.withControllerAction(this).addData(
              key: DatabaseModels.generalSettingsOrderDiscountRegistrationModel,
              data: generalSettingsOrderDiscountRegistration,
              workspaceId: appController.workspace!.workspaceId,
              userId: appController.userLogged!.userId,
              clearCurrentData: true,
            );
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  bool hasOrderDefault() {
    const type = PermissionCodeEnum.pedidoPadrao;
    return accessPermission.any((element) =>
        element.codigo == type.value && element.visualizar == true);
  }

  bool hasOrderEspecial() {
    const type = PermissionCodeEnum.pedidoEspecial;
    return accessPermission.any((element) =>
        element.codigo == type.value && element.visualizar == true);
  }

  bool hasOrderRep() {
    const type = PermissionCodeEnum.pedidoREP;
    return accessPermission.any((element) =>
        element.codigo == type.value && element.visualizar == true);
  }

  Future<void> getParameterByKeyOffline({bool? forceUpdate}) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("getParameterByKey");

    try {
      //Buscar na base local
      if (forceUpdate != true) {
        final dataBox = await ParameterByKeyModel()
            .getFirst(key: ParameterByKeyEnums.habilitarPedidoOfflineApp);
        if (dataBox != null) {
          parameterByKey = dataBox;
          return;
        }
      }
      //Buscar na API
      final result = await generalParameterizationApi
          .getParameterByKey(ParameterByKeyEnums.habilitarPedidoOfflineApp);
      if (result.error != null) {
        subAction.reportError(result.error!.message,
            errorCode: result.error!.statusCode);
        throw Exception(result.error?.message ?? "Erro desconhecido");
      } else {
        parameterByKey = result.data!;

        await dbContext.withControllerAction(this).addData(
            key: DatabaseModels.parameterByKeyModel,
            data: parameterByKey,
            hashCode: ParameterByKeyEnums.habilitarPedidoOfflineApp,
            workspaceId: appController.workspace!.workspaceId,
            userId: appController.userLogged!.userId,
            isLog: true,
            clearCurrentData: true);
      }
    } catch (e) {
      rethrow;
    } finally {
      leaveAction();
    }
  }
}
