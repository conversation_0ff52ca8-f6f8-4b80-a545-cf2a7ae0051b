import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/stores/models/commercial_condition_model.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';

abstract class IStoresApi {
  Future<HttpResponse<List<StoresModel>>> getStores();

  Future<HttpResponse<List<CommercialConditionDistributorsModel>>>
      getDistributorsByConditionAndStore(
          {required int commercialConditionId, required int storeId});

  Future<HttpResponse<List<CommercialConditionModel>>> getCommercialConditions(
      int storeId);
}

class StoresApi extends IStoresApi {
  final HttpManager _httpManager;
  StoresApi(this._httpManager);

  @override
  Future<HttpResponse<List<StoresModel>>> getStores() async {
    return await _httpManager.request<List<StoresModel>>(
      path: 'lojas/sincronizacao/${appController.userLogged!.userId}',
      method: HttpMethods.get,
      parser: (data) {
        if (data is List) {
          return data.map((item) => StoresModel.fromJson(item)).toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }

  @override
  Future<HttpResponse<List<CommercialConditionModel>>> getCommercialConditions(
      int storeId) async {
    return await _httpManager.request<List<CommercialConditionModel>>(
      path:
          'condicoescomerciais/listarCondicoesComerciaisDistribuidoresPorIdLoja/$storeId',
      method: HttpMethods.get,
      parser: (data) {
        if (data is List) {
          return data
              .map((item) => CommercialConditionModel.fromJson(item))
              .toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }

  @override
  Future<HttpResponse<List<CommercialConditionDistributorsModel>>>
      getDistributorsByConditionAndStore(
          {required int commercialConditionId, required int storeId}) async {
    return await _httpManager
        .request<List<CommercialConditionDistributorsModel>>(
      path:
          'distribuidores/listarDistribuidoresPorCondicaoComercialELoja/$commercialConditionId/$storeId',
      method: HttpMethods.get,
      parser: (data) {
        if (data is List) {
          return data
              .map(
                  (item) => CommercialConditionDistributorsModel.fromJson(item))
              .toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }
}
