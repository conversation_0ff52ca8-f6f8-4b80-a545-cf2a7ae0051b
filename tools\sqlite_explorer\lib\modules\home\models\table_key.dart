class TableKey {
  final String key;
  final String name;
  final int order;

  String getFriendlyName(String key) {
    return keyToFriendlyName[key] ?? key;
  }

  TableKey({required this.key, required this.name, this.order = 0});
}

//keys
final Map<String, String> keyToFriendlyName = {
  "CurrentWorkspace": "Workspace Atual",
  "LoginResponseModel": "Autenticação",
  "ResearchesMerchanCompetitiveDataModel":
      "Pesquisa Competitiva de Merchandising",
  "ResearchesMerchanIndustryDataModel":
      "Pesquisa de Indústria de Merchandising",
  "ResearchesProductIndustryDataModel": "Pesquisa de Indústria de Produto",
  "WorkspacesModel": "Workspaces Disponíveis",
  "generalSettingsOrderDiscountRegistrationModel":
      "Configurações de Desconto de Pedido",
  "logTraceMonitorModel": "Auditoria",
  "logsHttpModel": "Logs de Requisições",
  "ordersLocalDataModel": "Pedidos não finalizados",
  "parameterByKeyModel": "Parâmetro por Chave",
  "productFilterDefaultModel": "Filtro Padrão de Produto",
  "productParameterModel": "Parâmetro de Produto",
  "productsMixModel": "Mix de Produtos",
  "researchesProductConcurrentModel": "Pesquisa de Produto Concorrente",
  "researchesTradeMarketingModel": "Pesquisa de Trade Marketing",
  "routersListModel": "Roteiros",
  "storeParametersModel": "Parâmetros da Loja",
  "storeSelected": "Loja Atual",
  "storesModel": "Lojas",
  "storesTakeModel": "Lojas Selecionadas",
  "syncResultItemsModel": "Offline - Itens ",
  "synchronizationsModel": "Sincronizações de Pedidos",
  "syncronization": "Sincronização",
  "systemParameterizationPermissoesAcessoModel": "Permissões do Sistema",
  "themesModel": "Temas",
  "visitisInfoModel": "Informações de Visita",
  "visitsByRoutesResponseModel": "Visitas por Rotas",
};
