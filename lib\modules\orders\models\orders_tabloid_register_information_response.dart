class TabloidGetRegistrationInformationResponse {
  Tabloide? tabloide;
  bool? permiteEditarProdutosCadastrados;
  bool? permiteExcluirCombosOferta;

  TabloidGetRegistrationInformationResponse(
      {this.tabloide,
      this.permiteEditarProdutosCadastrados,
      this.permiteExcluirCombosOferta});

  TabloidGetRegistrationInformationResponse.fromJson(
      Map<String, dynamic> json) {
    tabloide =
        json['Tabloide'] != null ? Tabloide.fromJson(json['Tabloide']) : null;
    permiteEditarProdutosCadastrados = json['PermiteEditarProdutosCadastrados'];
    permiteExcluirCombosOferta = json['PermiteExcluirCombosOferta'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (tabloide != null) {
      data['Tabloide'] = tabloide!.toJson();
    }
    data['PermiteEditarProdutosCadastrados'] = permiteEditarProdutosCadastrados;
    data['PermiteExcluirCombosOferta'] = permiteExcluirCombosOferta;
    return data;
  }
}

class Tabloide {
  int? idTabloide;
  String? descricao;
  String? descricaoTabloide;
  bool? pdvVisualiza;
  String? dataInicio;
  String? dataFim;
  double? valorMinimoPedido;
  String? dataInclusao;
  String? dataAlteracao;
  String? usuarioCadastro;
  String? usuarioAlteracao;
  int? idTabloideOrigem;
  int? tipoAgrupamentoLojas;
  bool? isDeleted;

  Tabloide(
      {this.idTabloide,
      this.descricao,
      this.descricaoTabloide,
      this.pdvVisualiza,
      this.dataInicio,
      this.dataFim,
      this.valorMinimoPedido,
      this.dataInclusao,
      this.dataAlteracao,
      this.usuarioCadastro,
      this.usuarioAlteracao,
      this.idTabloideOrigem,
      this.tipoAgrupamentoLojas,
      this.isDeleted});

  Tabloide.fromJson(Map<String, dynamic> json) {
    idTabloide = json['IdTabloide'];
    descricao = json['Descricao'];
    descricaoTabloide = json['DescricaoTabloide'];
    pdvVisualiza = json['PdvVisualiza'];
    dataInicio = json['DataInicio'];
    dataFim = json['DataFim'];
    valorMinimoPedido = json['ValorMinimoPedido'];
    dataInclusao = json['DataInclusao'];
    dataAlteracao = json['DataAlteracao'];
    usuarioCadastro = json['UsuarioCadastro'];
    usuarioAlteracao = json['UsuarioAlteracao'];
    idTabloideOrigem = json['IdTabloideOrigem'];
    tipoAgrupamentoLojas = json['TipoAgrupamentoLojas'];
    isDeleted = json['IsDeleted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdTabloide'] = idTabloide;
    data['Descricao'] = descricao;
    data['DescricaoTabloide'] = descricaoTabloide;
    data['PdvVisualiza'] = pdvVisualiza;
    data['DataInicio'] = dataInicio;
    data['DataFim'] = dataFim;
    data['ValorMinimoPedido'] = valorMinimoPedido;
    data['DataInclusao'] = dataInclusao;
    data['DataAlteracao'] = dataAlteracao;
    data['UsuarioCadastro'] = usuarioCadastro;
    data['UsuarioAlteracao'] = usuarioAlteracao;
    data['IdTabloideOrigem'] = idTabloideOrigem;
    data['TipoAgrupamentoLojas'] = tipoAgrupamentoLojas;
    data['IsDeleted'] = isDeleted;
    return data;
  }
}
