import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/orders_resume/models/orders_resume_model.dart';

abstract class IOrdersResumeApi {
  Future<HttpResponse<List<OrdersResumeModel>>> getOrdersResume();
}

class OrdersResumeApi extends IOrdersResumeApi {
  final HttpManager _httpManager;
  OrdersResumeApi(this._httpManager);

  @override
  Future<HttpResponse<List<OrdersResumeModel>>> getOrdersResume() async {
    return await _httpManager.request<List<OrdersResumeModel>>(
      path: 'OrdersResume',
      method: HttpMethods.get,
      parser: (data) {
//se não for lista
//return OrdersResumeModel.fromJson(data);
        if (data is List) {
          return data.map((item) => OrdersResumeModel.fromJson(item)).toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }
}
