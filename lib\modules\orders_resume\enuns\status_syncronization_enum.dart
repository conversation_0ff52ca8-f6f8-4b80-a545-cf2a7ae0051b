import 'package:flutter/material.dart';

enum StatusSynchronization {
  sent,
  notSent,
  receiveWithError,
  receiveWithSuccess,
  sentForApproval,
  receiveReproved,
  receiveApproved,
  receiveAuthError,
  local
}

/*
 * Retorna um valor inteiro representando o status de sincronização com base no valor do enum fornecido.
 *
 * Esta função aceita um valor do enum StatusSynchronization como argumento.
 * Dependendo do valor do status, a função retorna um inteiro diferente.

 * @param status O status para o qual o valor inteiro deve ser retornado.
 * @return O valor inteiro correspondente ao status.
 */
int getStatusSynchronizationValue(StatusSynchronization status) {
  switch (status) {
    case StatusSynchronization.sent:
      return 1;
    case StatusSynchronization.notSent:
      return 2;
    case StatusSynchronization.receiveWithError:
      return 3;
    case StatusSynchronization.receiveWithSuccess:
      return 4;
    case StatusSynchronization.sentForApproval:
      return 5;
    case StatusSynchronization.receiveReproved:
      return 6;
    case StatusSynchronization.receiveApproved:
      return 7;
    case StatusSynchronization.receiveAuthError:
      return 8;
    case StatusSynchronization.local:
      return 9;
    default:
      return 0; // Ou qualquer outro valor padrão
  }
}

bool getStatusSynchronizationCanEdit(int status) {
  switch (status) {
    case 2: //StatusSynchronization.NOT_SENT:
      return true;
    case 1: //StatusSynchronization.SENT:
    case 3: //StatusSynchronization.RECEIVE_WITH_ERROR:
    case 4: //StatusSynchronization.RECEIVE_WITH_SUCCESS:
    case 5: //StatusSynchronization.SENT_FOR_APPROVAL:
    case 6: //StatusSynchronization.RECEIVE_REPROVED:
    case 7: //StatusSynchronization.RECEIVE_APPROVED:
    case 8: //StatusSynchronization.RECEIVE_AUTH_ERROR:
    default:
      return false;
  }
}

/*
 * Retorna uma string representando o status de sincronização com base no valor inteiro do status fornecido.
 *
 * Esta função aceita um inteiro como argumento, que representa um status.
 * Dependendo do valor do status, a função retorna uma string diferente.
 *
 * @param status O status para o qual a string deve ser retornada.
 * @return A string correspondente ao status.
 */
String getStatusSynchronizationText(int status) {
  switch (status) {
    case 1: //StatusSynchronization.SENT:
      return "Sincronizado";
    case 2: //StatusSynchronization.NOT_SENT:
      return "A sincronizar";
    case 3: //StatusSynchronization.RECEIVE_WITH_ERROR:
      return "Recebido com erros";
    case 4: //StatusSynchronization.RECEIVE_WITH_SUCCESS:
      return "Recebido com sucesso";
    case 5: //StatusSynchronization.SENT_FOR_APPROVAL:
      return "Enviado para sincronização";
    case 6: //StatusSynchronization.RECEIVE_REPROVED:
      return "Aprovado";
    case 7: //StatusSynchronization.RECEIVE_APPROVED:
      return "Reprovado";
    case 8: //StatusSynchronization.RECEIVE_AUTH_ERROR:
      return "Recebido com erros";
    default:
      return ""; // Ou qualquer outro valor padrão
  }
}

/*
 * Retorna uma cor baseada no status fornecido.
 *
 * Esta função aceita um inteiro como argumento, que representa um status.
 * Dependendo do valor do status, a função retorna uma cor diferente.
 *
 * @param status O status para o qual a cor deve ser retornada.
 * @return A cor correspondente ao status.
 */
Color getStatusSynchronizationColor(int status) {
  switch (status) {
    case 1: //StatusSynchronization.SENT:
      return Colors.blue.shade800;
    case 2: //StatusSynchronization.NOT_SENT:
      return Colors.yellow;
    case 3: //StatusSynchronization.RECEIVE_WITH_ERROR:
      return Colors.red;
    case 4: //StatusSynchronization.RECEIVE_WITH_SUCCESS:
      return Colors.green;
    case 5: //StatusSynchronization.SENT_FOR_APPROVAL:
      return Colors.yellow;
    case 6: //StatusSynchronization.RECEIVE_REPROVED:
      return Colors.blue.shade800;
    case 7: //StatusSynchronization.RECEIVE_APPROVED:
      return Colors.red;
    case 8: //StatusSynchronization.RECEIVE_AUTH_ERROR:
      return Colors.orange;
    default:
      return Colors.white; // Ou qualquer outro valor padrão
  }
}
