import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/core/http_result/http_response.dart';
import 'package:pharmalink/core/models/result_error_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/parametrizacao_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/store_parameters_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/store_parameters_request_model.dart';

abstract class IStoreParametersApi {
  Future<HttpResponse<List<StoreParametersModel>>> getStoreParameters(
      {required StoreParametersRequestModel req});
}

class StoreParametersApi extends IStoreParametersApi {
  final HttpManager _httpManager;
  StoreParametersApi(this._httpManager);

  @override
  Future<HttpResponse<List<StoreParametersModel>>> getStoreParameters(
      {required StoreParametersRequestModel req}) async {
    return await _httpManager
        .requestFull<List<StoreParametersModel>, ResultErrorModel>(
      path: 'lojas/obterParametrizacoesLoja/true',
      method: HttpMethods.post,
      body: req.toJson(),
      parser: (data) {
        if (data is List) {
          return data
              .map((item) => StoreParametersModel.fromJson(item))
              .toList();
        } else {
          final result = StoreParametersModel(
              idLoja: req.idLojaLista!.first,
              userId: req.userId,
              tipoPedido: req.tipoPedidoLista!.first,
              parametrizacao: ParametrizacaoModel.fromJson(data));
          return [result];
        }
      },
      parserError: (dynamic errorData) {
        return ResultErrorModel.fromJson(errorData);
      },
    );
  }
}
