import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class SearchTextFieldWidget extends StatelessWidget {
  final IconData? leadingIcon;
  final IconData? trailingIcon;
  final bool? isPassword;
  final String? label;
  final String? hintText;
  final TextEditingController controller;
  final ValueChanged<String>? onChanged;
  final GestureTapCallback? trailingTap;
  final TextInputType? keyboardType;
  final double? labelFontSize;
  final Color? labelColor;
  final Color? borderColor;
  final Color? iconColor;
  final double? iconSize;
  const SearchTextFieldWidget({
    super.key,
    this.leadingIcon,
    this.trailingIcon,
    this.isPassword,
    this.label,
    this.hintText,
    required this.controller,
    this.onChanged,
    this.trailingTap,
    this.keyboardType,
    this.labelFontSize,
    this.labelColor,
    this.iconColor,
    this.borderColor,
    this.iconSize,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        if (label != null)
          LabelWidget(
            title: label!,
            textColor: labelColor ?? Colors.grey,
            fontSize: labelFontSize ?? DeviceSize.fontSize(14, 18),
            fontWeight: FontWeight.w400,
          ),
        if (label != null) const Gap(6),
        Container(
          padding: const EdgeInsets.only(right: 16),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
                color: borderColor ?? themesController.getMenuColor(),
                width: 1.0),
          ),
          child: Row(
            children: <Widget>[
              if (leadingIcon != null) Icon(leadingIcon, color: Colors.grey),
              Expanded(
                child: TextField(
                  controller: controller,
                  obscureText: isPassword ?? false,
                  keyboardType: keyboardType ?? TextInputType.text,
                  decoration: InputDecoration(
                    hintText: hintText,
                    hintStyle: TextStyle(fontSize: DeviceSize.fontSize(14, 18)),
                    filled: true,
                    isDense: true,
                    fillColor: Colors.transparent,
                    border: const OutlineInputBorder(),
                    focusedBorder: const OutlineInputBorder(
                      borderSide:
                          BorderSide(color: Colors.transparent, width: 1.0),
                    ),
                    enabledBorder: const OutlineInputBorder(
                      borderSide:
                          BorderSide(color: Colors.transparent, width: 1.0),
                    ),
                  ),
                  onChanged: onChanged,
                ),
              ),
              if (trailingIcon != null)
                CustomInkWell(
                  onTap: trailingTap,
                  child: Icon(
                    trailingIcon,
                    color: iconColor ?? Colors.grey,
                    size: iconSize ?? 20,
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }
}
