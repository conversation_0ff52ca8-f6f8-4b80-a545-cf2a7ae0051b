import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/researches_product_concurrent/models/researches_product_concurrent_model.dart';
import 'package:pharmalink/modules/researches_product_concurrent/models/researches_product_concurrent_save_model.dart';

abstract class IResearchesProductConcurrentApi {
  Future<HttpResponse<ResearchesProductConcurrentModel>>
      getResearchesProductConcurrent({required int routeId});

  Future<HttpResponse<String?>> sendProductConcurrent(
      {required ResearchesProductConcurrentSaveSync model});
}

class ResearchesProductConcurrentApi extends IResearchesProductConcurrentApi {
  final HttpManager _httpManager;
  ResearchesProductConcurrentApi(this._httpManager);

  @override
  Future<HttpResponse<ResearchesProductConcurrentModel>>
      getResearchesProductConcurrent({required int routeId}) async {
    return await _httpManager.request<ResearchesProductConcurrentModel>(
      path:
          'pesquisasProdutoConcorrencia/listarProdutosPesquisaPorIdRota/$routeId',
      method: HttpMethods.get,
      parser: (data) {
        return ResearchesProductConcurrentModel.fromJson(data);
      },
    );
  }

  @override
  Future<HttpResponse<String?>> sendProductConcurrent(
      {required ResearchesProductConcurrentSaveSync model}) async {
    return await _httpManager.request<String?>(
      path: 'pesquisasProdutoConcorrencia/salvarPesquisaProduto',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        return data.toString();
      },
    );
  }
}
