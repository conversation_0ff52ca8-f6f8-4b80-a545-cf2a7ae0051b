{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "iTrade (developer)",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_itrade.dart",
            "flutterMode": "debug",
            "args": [
                "--flavor",
                "itrade",
                "--dart-define=ENV=developer"
            ]
        },
        {
            "name": "iTrade (staging)",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_itrade.dart",
            "flutterMode": "debug",
            "args": [
                "--flavor",
                "itrade",
                "--dart-define=ENV=staging"
            ]
        },
        {
            "name": "iTrade (production)",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_itrade.dart",
            "flutterMode": "release",
            "args": [
                "--flavor",
                "itrade",
                "--dart-define=ENV=production"
            ]
        },
        {
            "name": "<PERSON><PERSON><PERSON> (developer)",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_pharmalink.dart",
            "flutterMode": "debug",
            "args": [
                "--flavor",
                "pharmalink",
                "--dart-define=ENV=developer"
            ]
        },
        {
            "name": "Pharmalink (staging)",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_pharmalink.dart",
            "flutterMode": "debug",
            "args": [
                "--flavor",
                "pharmalink",
                "--dart-define=ENV=staging"
            ]
        },
        {
            "name": "Pharmalink (production)",
            "request": "launch",
            "type": "dart",
            "program": "lib/main_pharmalink.dart",
            "flutterMode": "release",
            "args": [
                "--flavor",
                "pharmalink",
                "--dart-define=ENV=production"
            ]
        },
        {
            "name": "MarketPlace-MobilePLK",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "MarketPlace-MobilePLK (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "MarketPlace-MobilePLK (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        },
        {
            "name": "sqlite_explorer",
            "cwd": "tools\\sqlite_explorer",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "sqlite_explorer (profile mode)",
            "cwd": "tools\\sqlite_explorer",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        },
        {
            "name": "sqlite_explorer (release mode)",
            "cwd": "tools\\sqlite_explorer",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release"
        }
    ]
}
