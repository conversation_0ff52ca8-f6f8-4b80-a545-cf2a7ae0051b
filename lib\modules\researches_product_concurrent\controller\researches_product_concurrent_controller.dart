import 'dart:developer';

import 'package:flutter_masked_text2/flutter_masked_text2.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/researches_product_concurrent/models/researches_product_concurrent_model.dart';
import 'package:pharmalink/modules/researches_product_concurrent/models/researches_product_concurrent_save_model.dart';

class ResearchesProductConcurrentController
    extends GetxControllerInstrumentado<ResearchesProductConcurrentController> {
  ResearchesProductConcurrentController();

  int? currentRouteId;

  int productIndex = 0;
  int productLength = 0;
  int questionLength = 0;
  int questionIndex = 0;
  MoneyMaskedTextController currencyController = MoneyMaskedTextController(
    decimalSeparator: ',',
    thousandSeparator: '.',
  );

  ResearchesProductConcurrentModel? productConcurrentDataModel;
  List<ResearchesProductConcurrentModel> productConcurrentDataList = [];

  Future<void> getData(int routeId) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport("getData");

    currentRouteId = routeId;
    productIndex = 0;
    questionIndex = 0;
    try {
      final researchesBox = await ResearchesProductConcurrentModel().getList(
          workspaceId: appController.workspace!.workspaceId!,
          routeId: currentRouteId!);
      if (researchesBox.isNotEmpty) {
        productConcurrentDataModel = researchesBox.first;
        productLength = productConcurrentDataModel!.produtos!.length;
        questionLength =
            productConcurrentDataModel!.parametrizacaoCampos!.length;
        Get.toNamed(RoutesPath.researchesProductConcurrent);
        return;
      }
      final response = await researchesProductConcurrentApi
          .getResearchesProductConcurrent(routeId: routeId);
      if (response.data != null) {
        productConcurrentDataModel = response.data!;
        productLength = productConcurrentDataModel!.produtos!.length;
        questionLength =
            productConcurrentDataModel!.parametrizacaoCampos!.length;
      } else {}

      if (productLength == 0) {
        Dialogs.info(
          "Atenção",
          "Não há produtos para pesquisa de concorrência.",
        );
      } else {
        Get.toNamed(RoutesPath.researchesProductConcurrent);
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> getDataSync(int routeId) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("getDataSync");
    try {
      final response = await researchesProductConcurrentApi
          .getResearchesProductConcurrent(routeId: routeId);
      if (response.data != null) {
        response.data!.isSync =
            response.data!.enviado == null ? SyncEnum.none : SyncEnum.finished;
        await dbContext.withControllerAction(this).addData(
            key: DatabaseModels.researchesProductConcurrentModel,
            data: response.data!,
            storeId: routeId,
            workspaceId: appController.workspace!.workspaceId,
            clearCurrentData: true);
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }
  }

  void setDistribuicoesNumericas(
      ResearchesProductConcurrentDistribuicoesNumericas item) {
    productConcurrentDataModel!.produtos![productIndex]
            .idPesquisaProdutoDistribuicaoNumericaSelecionado =
        item.idPesquisaProdutoDistribuicaoNumerica;

    next();
  }

  bool isDistribuicoesNumericasSelected(
      ResearchesProductConcurrentDistribuicoesNumericas item) {
    if (productConcurrentDataModel!.produtos![productIndex]
            .idPesquisaProdutoDistribuicaoNumericaSelecionado ==
        null) {
      return false;
    }

    return productConcurrentDataModel!.produtos![productIndex]
            .idPesquisaProdutoDistribuicaoNumericaSelecionado ==
        item.idPesquisaProdutoDistribuicaoNumerica;
  }

  void setNaoInformado(bool value) {
    productConcurrentDataModel!.produtos![productIndex].informado = value;

    update();
  }

  bool isNaoInformado() {
    if (productConcurrentDataModel!.produtos![productIndex].informado == null) {
      return false;
    }

    return productConcurrentDataModel!.produtos![productIndex].informado!;
  }

  void setValue(String value, String typeQuestion) {
    if (value.isEmpty) return;
    switch (typeQuestion) {
      case "Preço":
        productConcurrentDataModel!.produtos![productIndex].preco =
            value.toDouble();
        break;
      case "Desconto":
        productConcurrentDataModel!.produtos![productIndex].desconto =
            value.toDouble();
        break;
      case "Facing":
        productConcurrentDataModel!.produtos![productIndex].facing =
            value.toDouble();
        break;
      case "Estoque Físico":
        productConcurrentDataModel!.produtos![productIndex].estoqueFisico =
            int.parse(value);
        break;
      case "Estoque Sistema":
        productConcurrentDataModel!.produtos![productIndex].estoqueSistema =
            int.parse(value);
        break;
    }

    //update();
  }

  String getValue(String typeQuestion) {
    switch (typeQuestion) {
      case "Preço":
        currencyController.updateValue(
            productConcurrentDataModel!.produtos![productIndex].preco ?? 0);
        break;
      case "Desconto":
        currencyController.updateValue(
            productConcurrentDataModel!.produtos![productIndex].desconto ?? 0);
        break;
      case "Facing":
        return productConcurrentDataModel!.produtos![productIndex].facing
                ?.toString() ??
            "0";
      case "Estoque Físico":
        return productConcurrentDataModel!.produtos![productIndex].estoqueFisico
                ?.toString() ??
            "0";
      case "Estoque Sistema":
        return productConcurrentDataModel!
                .produtos![productIndex].estoqueSistema
                ?.toString() ??
            "0";
    }

    return "";
  }

  void setPosicionamentos(ResearchesProductConcurrentPosicionamentos item) {
    productConcurrentDataModel!.produtos![productIndex]
        .idPesquisaPosicionamentoSelecionado = item.idPesquisaPosicionamento;

    next();
  }

  bool isPosicionamentosSelected(
      ResearchesProductConcurrentPosicionamentos item) {
    return productConcurrentDataModel!
                .produtos![productIndex].idPesquisaPosicionamentoSelecionado ==
            null
        ? false
        : productConcurrentDataModel!
                .produtos![productIndex].idPesquisaPosicionamentoSelecionado ==
            item.idPesquisaPosicionamento;
  }

  bool canNext() {
    // final isFieldRequired = productConcurrentDataModel!
    //     .parametrizacaoCampos![questionIndex].obrigatorio;

    // final currentFieldId = productConcurrentDataModel!
    //     .parametrizacaoCampos![questionIndex].idPesquisaCampo;

    // if (isFieldRequired!) {
    //   final hasEmptyAnswer = productAnswers.any((element) =>
    //       element.idPesquisaCampo == currentFieldId &&
    //           element.response!.isEmpty ||
    //       element.response! == "0,00");

    //   if (hasEmptyAnswer) {
    //     return false;
    //   }
    // }

    return true;
  }

  Future<void> next() async {
    if (!canNext()) {
      SnackbarCustom.snackbarError("O seguinte campo é obrigatório.");
      return;
    }
    if (questionIndex < questionLength - 1) {
      questionIndex++;
      FocusScope.of(Get.context!).unfocus();

      update();
    } else {
      if (productIndex < productLength - 1) {
        productIndex++;
        questionIndex = 0;
        FocusScope.of(Get.context!).unfocus();

        update();
      } else {
        await saveQuestion();
      }
    }
  }

  Future<void> saveQuestion() async {
    productConcurrentDataModel!.workspaceId =
        appController.workspace?.workspaceId;
    productConcurrentDataModel!.routeId = currentRouteId;
    productConcurrentDataModel!.isSync = SyncEnum.awaited;
    productConcurrentDataList.add(productConcurrentDataModel!);
    await dbContext.withControllerAction(this).addData(
        key: DatabaseModels.researchesProductConcurrentModel,
        data: productConcurrentDataList,
        workspaceId: appController.workspace!.workspaceId,
        storeId: currentRouteId,
        clearCurrentData: true);

    final visitController = Get.find<VisitsController>();
    visitController.refreshVisitsSearch();
    update();
    GetC.close();
    SnackbarCustom.snackbarSucess("Produto Indústria",
        "Pesquisa do Produto Indústria salva com sucesso!");
  }

  void back() {
    if (questionIndex > 0) {
      questionIndex--;
      FocusScope.of(Get.context!).unfocus();
      update();
    } else {
      if (productIndex > 0) {
        productIndex--;
        questionIndex = questionLength - 1;
        FocusScope.of(Get.context!).unfocus();
        update();
      } else {
        GetC.close();
      }
    }
  }

  Future<void> syncResearches() async {
    final listResearches = await ResearchesProductConcurrentModel()
        .getListToSync(workspaceId: appController.workspace!.workspaceId!);

    final listFiltered = listResearches.where((e) => e.idRota != null).toList();
    if (listFiltered.isEmpty) return;

    for (var itemSync in listFiltered) {
      final model = ResearchesProductConcurrentSaveSync(
        idRota: itemSync.idRota,
        idParametrizacaoPesquisa: itemSync.idParametrizacaoPesquisa,
        enviado: true,
        tipoPesquisa: "Concorrencia",
        produtos: itemSync.produtos!
            .map(
              (e) => ResearchesProductConcurrentSaveProdutos(
                apresentacao: e.apresentacao,
                caminhoFoto: e.caminhoFoto,
                desconto: e.desconto,
                descricao: e.descricao,
                estoqueFisico: e.estoqueFisico,
                estoqueSistema: e.estoqueSistema,
                facing: e.facing,
                foto: e.foto,
                idPesquisaPosicionamentoSelecionado:
                    e.idPesquisaPosicionamentoSelecionado,
                idPesquisaProduto: e.idPesquisaProduto,
                idPesquisaProdutoDistribuicaoNumericaSelecionado:
                    e.idPesquisaProdutoDistribuicaoNumericaSelecionado,
                idProduto: e.idProduto,
                informado: e.informado,
                preco: e.preco,
                enviadoRuptura: e.enviadoRuptura,
              ),
            )
            .toList(),
      );
      final result = await researchesProductConcurrentApi.sendProductConcurrent(
          model: model);
      if (result.error != null) {
        SnackbarCustom.snackbarError(
            "Erro ao sincronizar pesquisa de produto concorrente");
        return;
      }
    }
  }

  Future<void> saveDebugTest() async {
    for (var e in productConcurrentDataModel!.produtos!) {
      e.idPesquisaProdutoDistribuicaoNumericaSelecionado = 1;
      e.idPesquisaPosicionamentoSelecionado = 1;
      e.facing = 1;
      e.desconto = 2;
      e.estoqueFisico = 3;
      e.estoqueSistema = 4;
      e.informado = true;
      e.preco = 5;
    }
    await saveQuestion();
  }
}
