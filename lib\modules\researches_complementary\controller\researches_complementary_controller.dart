import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/converters/converter_base64.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/camera_picker/models/camera_picker_model.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_model.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_request_model.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_send_model.dart';
import 'package:pharmalink/modules/researches_complementary/pages/components/researches_complementary_camera_widget.dart';
import 'package:pharmalink/modules/researches_complementary/pages/components/researches_complementary_multiple_widget.dart';
import 'package:pharmalink/modules/researches_complementary/pages/components/researches_complementary_number_widget.dart';
import 'package:pharmalink/modules/researches_complementary/pages/components/researches_complementary_single_widget.dart';
import 'package:pharmalink/modules/researches_complementary/pages/components/researches_complementary_text_widget.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';
import 'package:pharmalink/modules/visits/controller/visits_controller.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_answers_model.dart';

class ResearchesComplementaryController
    extends GetxControllerInstrumentado<ResearchesComplementaryController> {
  ResearchesComplementaryController();
  int? currentRouteId;
  ResearchesComplementaryModel? selected;
  List<ResearchesComplementaryModel> dataList = [];
  List<Widget> questionsWidgets = [];
  int newOrder = 0;
  List<ResearchesComplementaryPerguntas> questionsBasic = [];
  ResearchesComplementaryPerguntas currentQuestion =
      ResearchesComplementaryPerguntas();
  int tabCount = 0;

  int tabIndex = 0;

  List<ResearchesComplementaryModel> researchesToUpdate = [];
  List<int> routesId = [];

  bool isCameraOpen = false;

  void setCameraOpen(bool value) {
    isCameraOpen = value;
    update();
  }

  void setDataList(List<ResearchesComplementaryModel> data) {
    dataList = data;
  }

  void setSelected(ResearchesComplementaryModel data) {
    newOrder = 1;
    questionsWidgets = [];

    updateOrder(data.perguntas!);
    data.perguntas!.sort((a, b) => a.orderReal!.compareTo(b.orderReal!));

    for (var element in data.perguntas!) {
      questionsWidgets.add(_buildQuestions(element));
    }

    update();

    selected = data;
    Get.toNamed(RoutesPath.researchesComplementaryQuestion);
  }

  void updateOrder(List<ResearchesComplementaryPerguntas> perguntas) {
    perguntas.sort((a, b) => a.ordem!.compareTo(b.ordem!));

    for (var p in perguntas) {
      p.isShown = p.isShown ?? false;
      if (p.idPerguntaPai == null) {
        if (!p.titulo!.contains("Pergunta")) {
          p.numeroPergunta = "Pergunta ${p.ordem}";
          p.titulo = "${p.numeroPergunta} - ${p.titulo}";
        }

        if (p.perguntaObrigatoria! && !p.titulo!.contains("*")) {
          p.titulo = "${p.titulo} *";
        }
        p.orderReal = newOrder;
        p.hasDivisor = true;
        p.isShown = true;
        newOrder++;
        updateChildOrder(perguntas, p);
      }
    }
  }

  void updateChildOrder(
    List<ResearchesComplementaryPerguntas> perguntas,
    ResearchesComplementaryPerguntas parent,
  ) {
    var childQuestions =
        perguntas
            .where((element) => element.idPerguntaPai == parent.idPergunta)
            .toList();
    for (var child in childQuestions) {
      if (!child.titulo!.contains("Pergunta")) {
        child.numeroPergunta = "${parent.numeroPergunta}.${child.ordem}";
        child.titulo = "${child.numeroPergunta} - ${child.titulo}";
      }

      if (child.perguntaObrigatoria! && !child.titulo!.contains("*")) {
        child.titulo = "${child.titulo} *";
      }

      child.hasDivisor = child.isShown;
      child.orderReal = newOrder;
      newOrder++;
      updateChildOrder(perguntas, child);
    }
  }

  Widget _buildQuestions(ResearchesComplementaryPerguntas pergunta) {
    var type = pergunta.tipo!;

    switch (type) {
      case 0:
        return ResearchesComplementaryTextWidget(question: pergunta);
      case 1:
        return ResearchesComplementarySingleWidget(question: pergunta);
      case 2:
        return ResearchesComplementaryMultipleWidget(question: pergunta);
      case 3:
        return ResearchesComplementaryNumberWidget(question: pergunta);
      case 4:
        return ResearchesComplementaryCameraWidget(
          question: pergunta,
          onPressed: () {
            openCameraOption(pergunta);
          },
        );
      default:
        return Container();
    }
  }

  Future<void> getData(int routeId) async {
    currentRouteId = routeId;

    final researchesComplementaryBox = await ResearchesComplementaryModel()
        .getList(routeId);

    if (researchesComplementaryBox.isNotEmpty) {
      researchesComplementaryBox.map((e) => e.routeId = routeId).toList();
      setDataList(researchesComplementaryBox);
      Get.toNamed(RoutesPath.researchesComplementary);
      return;
    }

    final request = ResearchesComplementaryRequestModel(idsRotas: [routeId]);
    final result = await researchesComplementaryApi.getResearchesComplementary(
      model: request,
    );

    if (result.error != null) {
      SnackbarCustom.snackbarError(result.error!.message!);
    } else {
      setDataList(result.data!);
      await dbContext
          .withControllerAction(this)
          .addData(
            key: DatabaseModels.researchesComplementaryModel,
            data: result.data!,
            workspaceId: appController.workspace!.workspaceId,
            storeId: routeId,
            clearCurrentData: true,
          );

      Get.toNamed(RoutesPath.researchesComplementary);
    }
  }

  Future<void> getDataSync(int routeId) async {
    final request = ResearchesComplementaryRequestModel(idsRotas: [routeId]);
    final result = await researchesComplementaryApi.getResearchesComplementary(
      model: request,
    );

    if (result.error != null) {
      SnackbarCustom.snackbarError(result.error!.message!);
    } else {
      if (result.data!.isEmpty) {
        return;
      }
      var researchsFromRouteId = await ResearchesComplementaryModel().getList(
        routeId,
      );

      var listToUpdate =
          researchsFromRouteId
              .where(
                (element) =>
                    _isTodaysResearch(element) &&
                        element.isSync == SyncEnum.awaited ||
                    (element.isSync == SyncEnum.finished),
              )
              .toList();

      var idsResearchsfromRouteId = listToUpdate.map((e) => e.idPesquisa);

      if (result.data != null) {
        for (var research in result.data!) {
          if (!idsResearchsfromRouteId.contains(research.idPesquisa)) {
            listToUpdate.add(research);
          }
        }
      }

      if (dataList.any((element) => element.routeId == routeId)) {
        dataList = listToUpdate;
        update();
      }
      if (listToUpdate.isNotEmpty) {
        await dbContext
            .withControllerAction(this)
            .addData(
              key: DatabaseModels.researchesComplementaryModel,
              data: listToUpdate,
              workspaceId: appController.workspace!.workspaceId,
              storeId: routeId,
              clearCurrentData: true,
            );
      }
    }
  }

  bool _isTodaysResearch(ResearchesComplementaryModel research) {
    var dateWasSync = DateTime(
      research.synchronizationReicivingDate!.year,
      research.synchronizationReicivingDate!.month,
      research.synchronizationReicivingDate!.day,
      0,
      0,
      0,
      0,
      0,
    );
    var dateTodayOnlyDate = DateTime(
      DateTime.now().year,
      DateTime.now().month,
      DateTime.now().day,
      0,
      0,
      0,
      0,
      0,
    );

    var result =
        dateWasSync.toIso8601String() == dateTodayOnlyDate.toIso8601String();
    return result;
  }

  Future<void> saveResearch() async {
    var isCompleteAnswered =
        !selected!.perguntas!.any(
          (element) =>
              element.perguntaObrigatoria! &&
              element.isShown! &&
              (element.answers == null || element.answers!.isEmpty),
        );

    dataList.where((element) => element.idPesquisa == selected!.idPesquisa).map(
      (e) {
        e.perguntas = selected!.perguntas;
        e.workspaceId = appController.workspace?.workspaceId;
        e.routeId = currentRouteId;
        e.isSync = SyncEnum.awaited;
        e.isCompleteAnswered = isCompleteAnswered;
        e.isAnswered = true;
      },
    ).toList();

    await dbContext
        .withControllerAction(this)
        .addData(
          key: DatabaseModels.researchesComplementaryModel,
          data: dataList,
          workspaceId: appController.workspace!.workspaceId,
          storeId: currentRouteId,
          clearCurrentData: true,
        );

    update();

    final visitController = Get.find<VisitsController>();
    visitController.refreshVisitsSearch();

    GetC.close();
  }

  void setQuestionMultipleAwser(
    ResearchesComplementaryPossiveisRespostas r,
    ResearchesComplementaryPerguntas item,
  ) {
    var resp = ResearchesComplementarySendRespostasUsuario(
      idResposta: r.idResposta,
      resposta: r.resposta,
    );

    item.answers ??= [];

    if (item.answers != null &&
        item.answers!.any((e) => e.idResposta == r.idResposta)) {
      item.answers!.removeWhere((e) => e.idResposta == r.idResposta);
    } else {
      item.answers!.add(resp);
    }

    var ctx = Get.context!;
    FocusScope.of(ctx).unfocus();
    update();
  }

  Future<void> setQuestionSelectAwser(
    ResearchesComplementaryPossiveisRespostas r,
    ResearchesComplementaryPerguntas item,
  ) async {
    if (!item.showAnotherAnswer) {
      item.answers = [
        ResearchesComplementarySendRespostasUsuario(
          idResposta: r.idResposta,
          resposta: r.resposta,
        ),
      ];
    } else {
      item.answers?.removeWhere((element) => element.idResposta != null);
    }

    for (var element in selected!.perguntas!) {
      if (element.idPerguntaPai == item.idPergunta) {
        if (item.answers == null ||
            item.answers!.isEmpty ||
            !item.answers!.any(
              (e) =>
                  e.idResposta ==
                  element.idRespostaPaiParaExibirNovaRamificacao,
            )) {
          element.answers = [];
          element.isShown = false;
          element.hasDivisor = false;
          if (element.showAnotherAnswer) {
            setAnotherAnswer(element);
          }

          selected!.perguntas!
              .where((pergunta) => pergunta.idPerguntaPai == element.idPergunta)
              .map((e) {
                e.answers = [];
                e.isShown = false;
                e.hasDivisor = false;
              })
              .toList();
        }

        if (element.idRespostaPaiParaExibirNovaRamificacao != r.idResposta &&
            element.isShown == true) {
          item.hasDivisor = true;
          element.isShown = false;
          element.hasDivisor = false;
          element.answers = [];
          if (element.showAnotherAnswer) {
            setAnotherAnswer(element);
          }
        }
      }

      if (selected!.perguntas!.any(
        (pergunta) => pergunta.idPerguntaPai == element.idPergunta,
      )) {
        if (element.answers == null || element.answers!.isEmpty) {
          selected!.perguntas!
              .where((pergunta) => pergunta.idPerguntaPai == element.idPergunta)
              .map((e) {
                element.hasDivisor = true;
                e.answers = [];
                e.isShown = false;
                e.hasDivisor = false;
              })
              .toList();
        } else {
          selected!.perguntas!
              .where(
                (pergunta) =>
                    pergunta.idPerguntaPai == element.idPergunta &&
                    pergunta.idRespostaPaiParaExibirNovaRamificacao ==
                        element.answers![0].idResposta,
              )
              .map((e) {
                element.hasDivisor = false;
                item.hasDivisor = false;
                e.isShown = true;
                e.hasDivisor = true;
              })
              .toList();
        }
      }

      if ((element.idPerguntaPai == item.idPergunta) &&
          element.idRespostaPaiParaExibirNovaRamificacao == r.idResposta &&
          element.isShown == false) {
        item.hasDivisor = false;
        element.hasDivisor = true;
        element.isShown = true;
      }

      if (element.idPerguntaPai == item.idPergunta && item.showAnotherAnswer) {
        element.answers = [];
        element.isShown = false;
      }
    }
    var ctx = Get.context!;
    FocusScope.of(ctx).unfocus();
    update();
  }

  String? getTextAnswer(ResearchesComplementaryPerguntas item) {
    if (item.answers != null && item.answers!.isNotEmpty) {
      if (item.answers?.first is ResearchesComplementarySendRespostasUsuario) {
        final resp =
            item.answers?.first as ResearchesComplementarySendRespostasUsuario;
        return resp.resposta;
      }
      return item.answers?.first.toString();
    } else {
      return "";
    }
  }

  String? getFreeAnswer(ResearchesComplementaryPerguntas item) {
    if (item.answers != null &&
        item.answers!.isNotEmpty &&
        item.answers!.any((e) => e.idResposta == null)) {
      if (item.answers?.firstWhere((element) => element.idResposta == null)
          is ResearchesComplementarySendRespostasUsuario) {
        final resp =
            item.answers?.firstWhere((element) => element.idResposta == null)
                as ResearchesComplementarySendRespostasUsuario;
        return resp.resposta;
      }
      return item.answers
          ?.firstWhere((element) => element.idResposta == null)
          .toString();
    } else {
      return "";
    }
  }

  void setTextAnswer(String? value, ResearchesComplementaryPerguntas item) {
    item.answers = [
      ResearchesComplementarySendRespostasUsuario(resposta: value),
    ];
  }

  void setFreeAnswer(String? value, ResearchesComplementaryPerguntas item) {
    item.answers ??= [];
    if (item.answers == null ||
        item.answers!.isEmpty ||
        !item.answers!.any((e) => e.idResposta == null)) {
      var resp = ResearchesComplementarySendRespostasUsuario(resposta: value);
      item.answers!.add(resp);
    } else {
      item.answers!
          .where((element) => element.idResposta == null)
          .map((e) => e.resposta = value)
          .toList();
    }
  }

  void setAnotherAnswer(ResearchesComplementaryPerguntas item) {
    item.showAnotherAnswer = !item.showAnotherAnswer;

    if (!item.showAnotherAnswer) {
      if (item.answers != null &&
          item.answers!.isNotEmpty &&
          item.answers!.any((element) => element.idResposta == null)) {
        item.answers?.removeWhere((element) => element.idResposta == null);
      }
    }

    update();
  }

  bool hasAnswer(ResearchesComplementaryPerguntas item, int id) {
    if (item.answers == null || item.answers!.isEmpty) {
      return false;
    }
    if (item.answers is List &&
        (item.answers as List).every(
          (element) => element is ResearchesComplementarySendRespostasUsuario,
        )) {
      final resp =
          (item.answers as List)
              .map(
                (item) => item as ResearchesComplementarySendRespostasUsuario,
              )
              .toList();
      return resp.any((element) => element.idResposta == id);
    }
    return false;
  }

  Future<void> openCameraPicker(ResearchesComplementaryPerguntas item) async {
    if (!await appController.checkCameraPermission()) {
      Get.toNamed(RoutesPath.permissionRequest)?.then((data) async {
        if (data) {
          await _handleCameraPicker(item);
        }
      });
    } else {
      await _handleCameraPicker(item);
    }
  }

  Future<void> _handleCameraPicker(
    ResearchesComplementaryPerguntas item,
  ) async {
    Get.toNamed(RoutesPath.cameraPicker)?.then((data) async {
      if (data != null) {
        await setPicture(item, data);
      }
    });
  }

  void openCameraOption(ResearchesComplementaryPerguntas item) {
    if (item.answers != null &&
        item.answers!.length >= item.limiteNumeroImagens!) {
      Dialogs.info(
        AppStrings.attention,
        "Você pode adicionar no máximo ${item.limiteNumeroImagens!} imagem(s)!",
      );

      return;
    }
    showModalBottomSheet(
      context: Get.context!,
      builder: (BuildContext bc) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(
                  FontAwesomeIcons.camera,
                  color: Colors.black,
                ),
                title: const Text('Ir Para Câmera'),
                onTap:
                    isCameraOpen
                        ? null
                        : () async {
                          try {
                            if (!isCameraOpen) {
                              setCameraOpen(true);
                              if (Get.isSnackbarOpen) {
                                await Get.closeCurrentSnackbar();
                              }
                              Future.delayed(
                                const Duration(milliseconds: 150),
                                () async {
                                  Get.back();
                                  await openCameraPicker(item);
                                  setCameraOpen(false);
                                },
                              );
                            }
                          } catch (e) {
                            setCameraOpen(false);
                          }
                        },
              ),
              ListTile(
                leading: const Icon(
                  FontAwesomeIcons.xmark,
                  color: Colors.black,
                ),
                title: const Text('Cancelar'),
                onTap: () async {
                  GetC.close();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> setPicture(
    ResearchesComplementaryPerguntas item,
    CameraPickerModel photo,
  ) async {
    final picture = ResearchesComplementarySendRespostasUsuario(
      resposta: photo.path,
      extensaoImagem: photo.name,
    );
    item.answers ??= [];

    if (item.answers!.any((e) => e.extensaoImagem == photo.name)) {
      item.answers!.removeWhere((e) => e.extensaoImagem == photo.name);
    } else {
      item.answers!.add(picture);
    }
    update();
  }

  Future<void> removePicture(
    ResearchesComplementaryPerguntas item,
    ResearchesComplementarySendRespostasUsuario picture,
  ) async {
    await Dialogs.confirm(
      AppStrings.attention,
      AppStrings.visitRemoveImage,
      buttonNameCancel: "Não",
      buttonNameOk: "Sim",
      onPressedOk: () async {
        GetC.close();
        item.answers!.remove(picture);

        update();
      },
    );
  }

  clearComplementaryResearch() {
    selected!.perguntas!.map((e) {
      e.answers = [];
      e.hasDivisor = false;
      e.isShown = false;
      e.showAnotherAnswer = false;
    }).toList();
  }

  Future<void> syncResearches() async {
    final listResearches = await ResearchesComplementaryModel().getListToSync(
      workspaceId: appController.workspace!.workspaceId!,
    );
    final List<ResearchesComplementaryModel> itemsToSync = [];
    final List<int> routesIdsWithNotAllResearchesAnsered = [];
    researchesToUpdate = [];
    routesId = [];
    if (listResearches.isEmpty) return;

    for (var research in listResearches) {
      var numberOfResearchs =
          listResearches
              .where((element) => element.routeId == research.routeId)
              .toList();

      var numberOfResearchsAnswered =
          listResearches
              .where(
                (element) =>
                    element.routeId == research.routeId &&
                    element.isAnswered! == true &&
                    element.isCompleteAnswered!,
              )
              .toList();

      if (itemsToSync.isEmpty ||
          itemsToSync.firstWhereOrNull(
                (element) => element.routeId == research.routeId,
              ) ==
              null) {
        if (numberOfResearchs.length == numberOfResearchsAnswered.length) {
          itemsToSync.addAll(
            listResearches.where(
              (element) => element.routeId == research.routeId,
            ),
          );
        }

        if ((routesIdsWithNotAllResearchesAnsered.isEmpty ||
                routesIdsWithNotAllResearchesAnsered.firstWhereOrNull(
                      (element) => element == research.routeId,
                    ) ==
                    null) &&
            numberOfResearchs.length != numberOfResearchsAnswered.length) {
          routesIdsWithNotAllResearchesAnsered.add(research.routeId!);
        }
      }
    }

    if (itemsToSync.isNotEmpty) {
      await sendResearchesComplementary(itemsToSync);

      await saveStateSync();
    }

    update();

    if (listResearches.any((element) => !element.isCompleteAnswered!)) {
      validateResearchs(
        listResearches
            .where((element) => !element.isCompleteAnswered!)
            .toList(),
      );
    }

    if (routesIdsWithNotAllResearchesAnsered.isNotEmpty) {
      List<StoresModel> stores = [];
      var storesWithRouteId = storeRoutesPlannedController.storesList;
      for (var routeId in routesIdsWithNotAllResearchesAnsered) {
        var store = storesWithRouteId.firstWhereOrNull(
          (element) => element.dataExtra?.routeId == routeId,
        );
        if (store != null) {
          stores.add(store);
        }
      }

      if (stores.isNotEmpty) {
        var message =
            "Pesquisas Complementares foram respondidas parcialmente para as seguintes lojas: ";
        for (var store in stores) {
          message = "$message${store.nomeFantasia!}, ";
        }
        message =
            "${message.substring(0, message.length - 2)}. Se responder uma Pesquisa para a loja deverá responder as demais!";
        SnackbarCustom.snackbarWarning(message);
      } else {
        SnackbarCustom.snackbarWarning(
          "Pesquisas Complementares foram respondidas parcialmente em uma loja. Se responder uma Pesquisa para a loja deverá responder as demais!",
        );
      }
    }
  }

  bool validateResearchs(List<ResearchesComplementaryModel> dataList) {
    var researchsWhereNotCompleteAnswered = "";

    for (var element in dataList) {
      var isNotCompleteAnswered = element.perguntas!.any(
        (e) =>
            e.perguntaObrigatoria! &&
            e.isShown! &&
            (e.answers == null || e.answers!.isEmpty),
      );

      if (isNotCompleteAnswered) {
        researchsWhereNotCompleteAnswered =
            "$researchsWhereNotCompleteAnswered ${element.titulo}, ";
      }
    }

    if (researchsWhereNotCompleteAnswered.isNotEmpty) {
      researchsWhereNotCompleteAnswered =
          "${researchsWhereNotCompleteAnswered.substring(0, researchsWhereNotCompleteAnswered.length - 2)}.";
      SnackbarCustom.snackbarWarning(
        "Pesquisas Complementares que tem perguntas obrigatorias por responder: $researchsWhereNotCompleteAnswered",
      );
      return false;
    }

    return true;
  }

  Future<void> sendResearchesComplementary(
    List<ResearchesComplementaryModel> itemsToSync,
  ) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "sendResearchesComplementary",
    );
    try {
      for (var itemSync in itemsToSync) {
        try {
          final model = ResearchesComplementarySendModel(
            idPesquisa: itemSync.idPesquisa,
            idRota: itemSync.routeId,
            respostas: await Future.wait(
              itemSync.perguntas!.map((e) async {
                return ResearchesComplementarySendRespostas(
                  idPergunta: e.idPergunta,
                  tipo: e.tipo,
                  respostasUsuario:
                      ((e.perguntaObrigatoria! &&
                                  (e.answers != null &&
                                      e.answers!.isNotEmpty)) ||
                              (!e.perguntaObrigatoria! &&
                                  (e.answers != null && e.answers!.isNotEmpty)))
                          ? await Future.wait(
                            e.answers!.map((x) async {
                              return ResearchesComplementarySendRespostasUsuario(
                                idResposta: x.idResposta,
                                extensaoImagem:
                                    e.tipo == 4
                                        ? ConverterBase64.getExtensions(
                                          x.extensaoImagem!,
                                        )
                                        : x.extensaoImagem,
                                resposta:
                                    e.tipo == 4
                                        ? await ConverterBase64.getImageBase64(
                                          x.resposta!,
                                        )
                                        : x.resposta,
                              );
                            }).toList(),
                          )
                          : [],
                );
              }).toList(),
            ),
          );
          final result = await researchesComplementaryApi
              .sendResearchesComplementary(model: model);
          if (result.error != null) {
            SnackbarCustom.snackbarError(
              "Erro ao sincronizar pesquisa complementar ${itemSync.titulo}",
            );
            itemSync.isSync = SyncEnum.awaited;
          } else {
            itemSync.isSync = SyncEnum.finished;
          }
        } catch (e, s) {
          subAction.reportZoneStacktrace(e, s);
          itemSync.isSync = SyncEnum.awaited;
        } finally {
          researchesToUpdate.add(itemSync);
          if (routesId.isEmpty || !routesId.contains(itemSync.routeId)) {
            routesId.add(itemSync.routeId!);
          }
        }
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
    } finally {
      leaveAction();
    }
  }

  Future<void> saveStateSync() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "saveStateSync",
    );
    try {
      if (researchesToUpdate.isNotEmpty) {
        for (var routeId in routesId) {
          var listToUpdate =
              researchesToUpdate
                  .where((element) => element.routeId == routeId)
                  .toList();
          await dbContext
              .withControllerAction(this)
              .addData(
                key: DatabaseModels.researchesComplementaryModel,
                data: listToUpdate,
                workspaceId: appController.workspace!.workspaceId,
                storeId: routeId,
                clearCurrentData: true,
              );
        }
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
    } finally {
      leaveAction();
    }
  }
}
