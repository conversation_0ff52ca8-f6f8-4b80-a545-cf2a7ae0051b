import 'package:sqlite_explorer/exports/basic_exports.dart';
import 'package:sqlite_explorer/exports/get_exports.dart';
import 'package:sqlite_explorer/modules/home/<USER>/home_controller.dart';
import 'package:sqlite_explorer/widgets/widgets_exports.dart';

class HomeSideBar extends StatelessWidget {
  const HomeSideBar({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(builder: (ctrl) {
      return Container(
        color: Colors.black12,
        padding: const EdgeInsets.all(10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            //const Gap(10),

            Expanded(
              child: ListView.builder(
                itemCount: ctrl.dataModels.length,
                itemBuilder: (context, index) {
                  final item = ctrl.dataModels[index];
                  return GestureDetector(
                    onTap: () {
                      ctrl.setJsonData(item);
                    },
                    child: Card(
                      elevation: 5,
                      shadowColor:
                          ctrl.selectedDataModel == item ? Colors.blue : null,
                      borderOnForeground: true,
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ListTile(
                              title: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  LabelWidget(title: "#${item.id}"),
                                  LabelWidget(
                                    title:
                                        item.createdAt?.formatDateTimeLocal() ??
                                            "",
                                  )
                                ],
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (ctrl.hasValueInLogTraceMonitor(
                                      item, "type"))
                                    LabelWidget(
                                      title: ctrl.getValueFromLogTraceMonitor(
                                          item, "type"),
                                      fontWeight: FontWeight.w600,
                                      fontSize: 13,
                                    ),
                                  if (ctrl.isTypeInLogTraceMonitor(
                                          item, "Routes") ||
                                      ctrl.isTypeInLogTraceMonitor(
                                          item, "Routes:"))
                                    LabelWidget(
                                      title: ctrl.getValueFromLogTraceMonitor(
                                          item, "textData"),
                                      italic: true,
                                      fontSize: 12,
                                    ),
                                  if (ctrl.hasValueInLogTraceMonitor(
                                      item, "controllerName"))
                                    LabelWidget(
                                      title:
                                          "Path: ${ctrl.getValueFromLogTraceMonitor(item, "controllerName")}.${ctrl.getValueFromLogTraceMonitor(item, "methodName")}",
                                      fontSize: 12,
                                    ),
                                  LabelWidget(
                                    title: "Tabela: ${item.key}",
                                  ),
                                  if (item.workspaceId != null)
                                    LabelWidget(
                                      title:
                                          "WorkspaceId: ${item.workspaceId?.toString() ?? "-"}",
                                    ),
                                  if (item.storeId != null)
                                    LabelWidget(
                                      title:
                                          "StoreId: ${item.storeId?.toString() ?? "-"}",
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                IconButton(
                  icon: const Icon(Icons.chevron_left),
                  onPressed: () async {
                    await ctrl.previousPage();
                  },
                ),
                Row(
                  children: [
                    Text('Página ${ctrl.currentPage}'),
                    const Gap(16),
                    DropdownButton<int>(
                      value: ctrl.pageSize,
                      items: [10, 20, 50, 100, 500, 1000].map((int value) {
                        return DropdownMenuItem<int>(
                          value: value,
                          child: Text(value.toString()),
                        );
                      }).toList(),
                      onChanged: (int? newValue) async {
                        if (newValue != null) {
                          await ctrl.setPageSize(newValue);
                        }
                      },
                    ),
                  ],
                ),
                IconButton(
                  icon: const Icon(Icons.chevron_right),
                  onPressed: () async {
                    await ctrl.nextPage();
                  },
                ),
              ],
            ),
          ],
        ),
      );
    });
  }
}
