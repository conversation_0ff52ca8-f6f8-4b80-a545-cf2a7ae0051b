import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/order_types/order_type_colors.dart';
import 'package:pharmalink/modules/orders_resume/models/file_attachment.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';

class ReportOrdersSyncCardOrdersWidget extends StatefulWidget {
  const ReportOrdersSyncCardOrdersWidget({
    super.key,
    required this.title,
    required this.state,
    required this.stateColor,
    required this.date,
    required this.orderId,
    required this.canEdit,
    required this.isOnline,
    required this.data,
    this.fileAttachmentStatus,
  });
  final String title;
  final String state;
  final Color stateColor;
  final String date;
  final String orderId;
  final bool canEdit;
  final bool isOnline;
  final SyncronizationModel data;
  final FileAttachmentStatus? fileAttachmentStatus;

  @override
  State<ReportOrdersSyncCardOrdersWidget> createState() =>
      _ReportOrdersSyncCardOrdersWidgetState();
}

class _ReportOrdersSyncCardOrdersWidgetState
    extends State<ReportOrdersSyncCardOrdersWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReportOrdersSyncController>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Card(
          elevation: 5,
          color: Colors.white,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              children: [
                Checkbox(
                  value: widget.data.isSelected,
                  onChanged: (bool? value) {
                    setState(() {
                      widget.data.isSelected = value ?? false;
                      ctrl.badgeRefresh();
                    });
                  },
                ),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                LabelWidget(
                                  title: widget.title,
                                  fontWeight: FontWeight.bold,
                                  fontSize: DeviceSize.fontSize(14, 17),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                LabelWidget(
                                  title: widget.data.storeData?.cNPJ ?? "",
                                  fontSize: DeviceSize.fontSize(12, 15),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      Flexible(
                        child: Align(
                          alignment: Alignment.topLeft,
                          child: LabelWidget(
                            title: widget.data.storeData?.enderecoPdv ?? "",
                            fontSize: DeviceSize.fontSize(11, 13),
                          ),
                        ),
                      ),
                      const Gap(5),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          LabelWidget(
                            title: "Data do Pedido:",
                            fontSize: DeviceSize.fontSize(12, 14),
                          ),
                          const Gap(5),
                          LabelWidget(
                            title: widget.date,
                            fontSize: DeviceSize.fontSize(11, 13),
                          )
                        ],
                      ),
                      const Gap(5),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          LabelWidget(
                            title: "Valor Total Líquido:",
                            fontSize: DeviceSize.fontSize(12, 14),
                          ),
                          const Gap(5),
                          LabelWidget(
                            title: ctrl.getOrderTotal(widget.data),
                            fontSize: DeviceSize.fontSize(11, 13),
                          )
                        ],
                      ),
                      const Gap(10),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              color: OrderTypeColors.getBackgroundColor(
                                  widget.data.typeOrder ?? 1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            padding: const EdgeInsets.all(5),
                            child: LabelWidget(
                              title:
                                  ctrl.getTypeOrderName(widget.data.typeOrder),
                              textColor: OrderTypeColors.getTextColor(
                                  widget.data.typeOrder ?? 1),
                              fontSize: DeviceSize.fontSize(11, 13),
                            ),
                          ),
                          const Gap(5),
                          Flexible(
                            child: Align(
                              alignment: Alignment.centerRight,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  if (widget.fileAttachmentStatus != null)
                                    Icon(
                                      FontAwesomeIcons.solidFile,
                                      size: 14.sp,
                                      color: widget.fileAttachmentStatus ==
                                              FileAttachmentStatus.waiting
                                          ? Colors.amber[300]
                                          : widget.fileAttachmentStatus ==
                                                  FileAttachmentStatus.synced
                                              ? Colors.green
                                              : Colors.red,
                                    ),
                                  if (widget.fileAttachmentStatus != null)
                                    const Gap(5),
                                  Icon(
                                    FontAwesomeIcons.solidCircle,
                                    size: 14.sp,
                                    color: widget.stateColor,
                                  ),
                                  10.toWidthSpace(),
                                  Flexible(
                                      child: LabelWidget(
                                    title: widget.state,
                                    maxLines: 1,
                                    overflow: TextOverflow.ellipsis,
                                    fontSize: DeviceSize.fontSize(12, 14),
                                  )),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
