import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/converters/converter_base64.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/camera_picker/models/camera_picker_model.dart';
import 'package:pharmalink/modules/researches_share_of_shelf/enums/researches_share_of_shelf_type_enum.dart';
import 'package:pharmalink/modules/researches_share_of_shelf/models/researches_share_of_shelf_error_model.dart';
import 'package:pharmalink/modules/researches_share_of_shelf/models/researches_share_of_shelf_model.dart';
import 'package:pharmalink/modules/researches_share_of_shelf/models/researches_share_of_shelf_send_model.dart';

class ResearchesShareOfShelfController
    extends GetxControllerInstrumentado<ResearchesShareOfShelfController> {
  ResearchesShareOfShelfController();
  int? currentStoreId;
  int? routeId;
  ResearchesShareOfShelfModel? selected;
  List<ResearchesShareOfShelfModel> dataList = [];

  ResearchesShareOfShelfProdutos? currentPhoto;

  String? photoPicturePath = '';
  String? photoPictureCropPath = '';
  String? photoPictureCroppedPath = '';

  void setDataList(List<ResearchesShareOfShelfModel> data) {
    dataList = data;
    dataList.map((e) {
      e.workspaceId = appController.workspace!.workspaceId;
      e.routeId = routeId;
      e.produtos!.map((e) {
        e.facingProduto = e.facingProduto ?? 0;
        e.facingGondola = e.facingGondola ?? 0;
        e.shareApurado = e.shareApurado ?? 0;
        return e;
      }).toList();
    }).toList();
    if (dataList.any((element) =>
        element.pdvRelacionadosPesquisa!.contains(currentStoreId))) {
      selected = dataList
          .where((element) =>
              element.pdvRelacionadosPesquisa!.contains(currentStoreId))
          .first;
      if (selected != null) {
        selected!.storeId = currentStoreId;
        selected!.routeId = globalParams.getCurrentStore()!.dataExtra?.routeId;
      }
    }
  }

  Future<void> getData() async {
    currentStoreId = globalParams.getCurrentStore()!.idLoja!;
    routeId = globalParams.getCurrentStore()!.dataExtra?.routeId!;
    final researchesShareOfShelfBox =
        await ResearchesShareOfShelfModel().getList();
    if (researchesShareOfShelfBox.isNotEmpty) {
      researchesShareOfShelfController.setDataList(researchesShareOfShelfBox);
      Get.toNamed(RoutesPath.researchesShareOfShelf);
      return;
    }
    final result = await researchesShareOfShelfApi.getResearchesShareOfShelf();
    if (result.error != null) {
      SnackbarCustom.snackbarError(result.error!.message!);
    } else {
      setDataList(result.data!);
      await dbContext.withControllerAction(this).addData(
          key: DatabaseModels.researchesShareOfShelfModel,
          data: dataList,
          workspaceId: appController.workspace!.workspaceId,
          storeId: currentStoreId,
          clearCurrentData: true);

      Get.toNamed(RoutesPath.researchesShareOfShelf);

      if (selected == null) {
        await Dialogs.info(
          AppStrings.attention,
          AppStrings.researchShareNotFound(currentStoreId!.toString()),
          buttonName: "ENTENDI",
        );
      }
    }
  }

  Future<void> getDataSync({required int storeId}) async {
    final result = await researchesShareOfShelfApi.getResearchesShareOfShelf();
    if (result.error != null) {
      SnackbarCustom.snackbarError(result.error!.message!);
    } else {
      await dbContext.withControllerAction(this).addData(
          key: DatabaseModels.researchesShareOfShelfModel,
          data: result.data!,
          workspaceId: appController.workspace!.workspaceId,
          storeId: storeId,
          clearCurrentData: true);
    }
  }

  Future<void> loadDatalist() async {
    final researchesShareOfShelfBox =
        await ResearchesShareOfShelfModel().getList();
    if (researchesShareOfShelfBox.isNotEmpty) {
      dataList = researchesShareOfShelfBox;
      return;
    }
  }

  Future<void> saveSync(ResearchesShareOfShelfModel item) async {
    final saveItem = dataList
        .where((element) =>
            element.idCiclo == item.idCiclo &&
            element.idPesquisa == item.idPesquisa &&
            element.idRede == item.idRede)
        .first;

    saveItem.isSync = item.isSync;

    await dbContext.withControllerAction(this).addData(
        key: DatabaseModels.researchesShareOfShelfModel,
        data: dataList,
        storeId: currentStoreId,
        workspaceId: appController.workspace!.workspaceId,
        clearCurrentData: true);
  }

  Future<void> syncResearches() async {
    final listResearches = await ResearchesShareOfShelfModel()
        .getListToSync(workspaceId: appController.workspace!.workspaceId!);

    if (listResearches.isEmpty) return;

    await loadDatalist();

    for (var itemSync in listResearches) {
      final model = ResearchesShareOfShelfSendModel(
        idPesquisaShareOfShelf: itemSync.idPesquisa,
        idRota: itemSync.routeId,
        dataPlanejamentoVisita:
            DateTime.now().formatDate(formatType: DateFormatType.mMddyyyy),
        produtosShare: await Future.wait(itemSync.produtos!
            .map((e) async => ResearchesShareOfShelfSendProdutosShare(
                  idPesquisaShareOfShelfProduto: e.idPesquisaProduto,
                  idFamilia: e.idFamilia,
                  shareIdeal: e.shareIdeal,
                  facingProduto: e.facingProduto,
                  facingGondola: e.facingGondola,
                  shareApurado: e.shareApurado,
                  tipoResposta: e.tipoResposta,
                  imagem: e.imageConvert == true && e.image != null
                      ? await ConverterBase64.getImageBase64(e.image!)
                      : e.image,
                ))
            .toList()),
      );
      final result =
          await researchesShareOfShelfApi.sendShareOfShelf(model: model);
      if (result.error != null) {
        final error = result.error!.data as ResearchesShareOfShelfErrorModel;
        late String? message =
            error.modelState?.erros!.map((e) => e).join("\n");

        if (message != null) {
          message = "Share Of Shelf: $message";
        }
        SnackbarCustom.snackbarError(
            message ?? "Erro ao sincronizar pesquisa de share of shelf");

        return;
      } else {
        itemSync.isSync = SyncEnum.finished;

        currentStoreId = itemSync.storeId!;
        await saveSync(itemSync);
      }
    }
  }

  void setUp(ResearchesShareOfShelfProdutos e, ShareOfShelfTypeEnum type) {
    switch (type) {
      case ShareOfShelfTypeEnum.product:
        e.facingProduto = e.facingProduto! + 1;
        if (e.facingProduto! > e.facingGondola!) {
          e.facingProduto = e.facingGondola!;
        }
        break;
      case ShareOfShelfTypeEnum.pedishelfdoPadrao:
        e.facingGondola = e.facingGondola! + 1;
        break;
    }
    setShareApurado(e);
    update();
  }

  void setDown(ResearchesShareOfShelfProdutos e, ShareOfShelfTypeEnum type) {
    switch (type) {
      case ShareOfShelfTypeEnum.product:
        e.facingProduto = e.facingProduto! - 1;
        if (e.facingProduto! < 0) e.facingProduto = 0;
        break;
      case ShareOfShelfTypeEnum.pedishelfdoPadrao:
        e.facingGondola = e.facingGondola! - 1;
        if (e.facingGondola! < 0) e.facingGondola = 0;
        if (e.facingProduto! > e.facingGondola!) {
          e.facingProduto = e.facingGondola!;
        }
        break;
    }
    setShareApurado(e);
    update();
  }

  void setShareApurado(ResearchesShareOfShelfProdutos e) {
    if (e.facingProduto == 0 && e.facingGondola == 0) {
      e.shareApurado = 0;
      return;
    }
    e.shareApurado = (e.facingProduto! * 100) / e.facingGondola!;
  }

  void setChangeText(ResearchesShareOfShelfProdutos e,
      ShareOfShelfTypeEnum type, String? value) {
    final valueInt = value != null ? int.tryParse(value) : 0;
    switch (type) {
      case ShareOfShelfTypeEnum.product:
        e.facingProduto = valueInt;
        if (e.facingProduto! < 0) {
          e.facingProduto = 0;
        } else if (e.facingProduto! > e.facingGondola!) {
          e.facingProduto = e.facingGondola!;
        }
        break;
      case ShareOfShelfTypeEnum.pedishelfdoPadrao:
        e.facingGondola = valueInt;
        if (e.facingGondola! < 0) e.facingGondola = 0;
        if (e.facingProduto! > e.facingGondola!) {
          e.facingProduto = e.facingGondola!;
        }
        break;
    }
    setShareApurado(e);
    update();
  }

  Future<void> openCameraPicker(
      ResearchesShareOfShelfProdutos e, bool isManual) async {
    if (!await appController.checkCameraPermission()) {
      Get.toNamed(RoutesPath.permissionRequest)?.then((data) async {
        if (data) {
          await _handleCameraPicker(e, isManual);
        }
      });
    } else {
      await _handleCameraPicker(e, isManual);
    }
  }

  Future<void> _handleCameraPicker(
      ResearchesShareOfShelfProdutos e, bool isManual) async {
    Get.toNamed(RoutesPath.cameraPicker)?.then((data) async {
      if (data != null) {
        final cp = data as CameraPickerModel;
        e.image = cp.file.path;
        e.imageConvert = true;
        photoPicturePath = cp.file.path;

        if (isManual) {
          update();
        } else {
          currentPhoto = e;
          update();
          Get.toNamed(RoutesPath.researchesShareOfShelfPhotoCrop);
        }
      }
    });
  }

  void openCameraOption(ResearchesShareOfShelfProdutos e) {
    showModalBottomSheet(
      context: Get.context!,
      builder: (BuildContext bc) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(
                  FontAwesomeIcons.camera,
                  color: Colors.black,
                ),
                title: const Text('Apuração Por Foto'),
                onTap: () async {
                  GetC.close();
                  e.tipoResposta = 1;

                  await openCameraPicker(e, false);
                },
              ),
              ListTile(
                leading: const Icon(
                  FontAwesomeIcons.images,
                  color: Colors.black,
                ),
                title: const Text('Apuração Manual'),
                onTap: () async {
                  GetC.close();
                  e.tipoResposta = 2;

                  await openCameraPicker(e, true);
                },
              ),
              ListTile(
                leading:
                    const Icon(FontAwesomeIcons.xmark, color: Colors.black),
                title: const Text('Cancelar'),
                onTap: () async {
                  GetC.close();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> saveQuestion() async {
    selected!.isSync = SyncEnum.awaited;
    await dbContext.withControllerAction(this).addData(
        key: DatabaseModels.researchesShareOfShelfModel,
        data: dataList,
        storeId: currentStoreId,
        workspaceId: appController.workspace!.workspaceId,
        clearCurrentData: true);

    final visitController = Get.find<VisitsController>();
    visitController.refreshVisitsSearch();

    update();
    GetC.close();
    SnackbarCustom.snackbarSucess(
        "Share de Gôndola", "Pesquisa do Share de Gôndola salva com sucesso!");
  }

  Future<void> closeCropAndOpenMarking() async {
    GetC.close();
    SnackbarCustom.snackbarSucess("Apuração", "Apuração salva com sucesso!",
        secondsDuration: 2);
    Future.delayed(const Duration(milliseconds: 200), () {
      Get.toNamed(RoutesPath.researchesShareOfShelfPhotoArea);
    });
  }

  void removePicture(ResearchesShareOfShelfProdutos e) {
    e.image = null;
    if (e.tipoResposta == 1) {
      e.shareApurado = 0;
    }

    e.tipoResposta = null;

    update();
  }
}
