#!/usr/bin/env bash

set -a # Export all variables

export BASE_PRJ_PATH="."

# Função para imprimir mensagens coloridas
# Uso: colored_print <cor> <mensagem>
# Cores disponíveis: red, green, yellow, blue
# Se nenhuma cor for especificada, a mensagem será impressa sem cor
# Exemplo: colored_print red "Erro ao executar o comando"
function colored_print() {
  COR=$1
  MSG=$2
  
  case $COR in
    "red")
      echo -e "\033[0;31m${MSG}\033[0m"
      ;;
    "green")
      echo -e "\033[0;32m${MSG}\033[0m"
      ;;
    "yellow")
      echo -e "\033[0;33m${MSG}\033[0m"
      ;;
    "blue")
      echo -e "\033[0;34m${MSG}\033[0m"
      ;;
    *)
      echo "${MSG:-$COR}"
      ;;
  esac

}

# Função para executar comandos e verificar o status
# Uso: executar <comando>
# Exemplo: executar ls -l
# Se o comando falhar, imprime uma mensagem de erro e sai com o código de erro
function executar() {
  colored_print blue "================================================================================"
  colored_print blue "================================================================================"
  colored_print yellow "Executando: $*"
  colored_print blue "vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv"
  "$@"
  status=$?
  if [ $status -ne 0 ]; then
    colored_print red "Erro ao executar: $*"
    colored_print red "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^"
    colored_print red "Codigo de erro: ${status}"
    echo ""
    echo ""
    exit $status
  else
    colored_print green "Sucesso ao executar: $*"
    colored_print green "^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^"
    
    echo "|"
    echo "|"

    return $status
  fi
}

# Função para obter o caminho relativo entre dois diretórios
# Uso: relative_path <target> [<base>]
# Exemplo: relative_path /home/<USER>/project /home/<USER>
# Se o segundo argumento não for fornecido, o diretório atual será usado como base
# Se o comando realpath falhar, usa Python para calcular o caminho relativo
relative_path() {
  local target="$1"
  local base="${2:-$PWD}"
  # Tenta com realpath GNU
  local result
  result=$(realpath --relative-to="$base" "$target" 2>/dev/null)
  if [ $? -eq 0 ]; then
    echo "$result"
  else
    # Fallback para Python
    python3 -c "import os.path; print(os.path.relpath('${target}', '${base}'))"
  fi
}

# Descobre o diretório do projeto base
# Busca pelo arquivo pubspec.yaml no diretório atual e nos diretórios pais
# Se o arquivo for encontrado, muda para o diretório do projeto
# Se não for encontrado, imprime uma mensagem de erro e sai do script
export BASE_PRJ_PATH=$(pwd)
while [ "$BASE_PRJ_PATH" != "/" ]; do
  if [ -f "${BASE_PRJ_PATH}/pubspec.yaml" ]; then
    colored_print green "Diretorio do projeto encontrado: ${BASE_PRJ_PATH}"
    cd "$BASE_PRJ_PATH" || colored_print red "Erro ao mudar para o diretório ${BASE_PRJ_PATH}"
    break
  else
    BASE_PRJ_PATH=$(dirname "$BASE_PRJ_PATH")
  fi
done
export BASE_PRJ_PATH=$(relative_path "$BASE_PRJ_PATH" "$PWD")

# Função para instalar o ambiente Ruby usando rbenv
# Uso: install_ruby_env
# Exemplo: install_ruby_env
# Esta função instala o rbenv, ruby-build e a versão 3.1.1 do Ruby
# Se o diretório do rbenv já existir, a instalação é pulada
# Variáveis de ambiente são configuradas para o rbenv e o Ruby
function install_ruby_env {

  colored_print yellow "Entrando no diretório do projeto iOS: ${PROJECT_PATH}"
  cd "${BASE_PRJ_PATH}/ios/" || colored_print red "Erro ao mudar para o diretório ${BASE_PRJ_PATH}/ios"

  export BASE_RBENV_PATH_INSTALL="/tmp"
  export RBENV_ROOT="${BASE_RBENV_PATH_INSTALL}/.rbenv"

#  rm -Rf "$RBENV_ROOT"
  if [ -d "$RBENV_ROOT" ]; then
    colored_print yellow  "Diretorio ${RBENV_ROOT} ja existe. Pulando instalacao do ruby"
  else
    echo $RBENV_ROOT
    executar git clone https://github.com/rbenv/rbenv.git "$RBENV_ROOT"
  fi

  export PATH="$RBENV_ROOT/bin:$PATH"
  executar eval "$(rbenv init - bash)"
  colored_print yellow  "Versao rbenv: $(rbenv --version)"

  export PATH_RUBY_BUILD="$RBENV_ROOT/plugins/ruby-build"
  if [ -d "$PATH_RUBY_BUILD" ]; then
    colored_print yellow  "Diretorio ${PATH_RUBY_BUILD} ja existe. Pulando instalacao do ruby-build"
  else
    executar git clone https://github.com/rbenv/ruby-build.git "$PATH_RUBY_BUILD"

    executar rbenv install 3.4.4
  fi

  executar rbenv local 3.4.4
  
  colored_print yellow "Versao ruby: $(ruby --version)"

  export GEM_HOME="${BASE_RBENV_PATH_INSTALL}/.gem"
  export PATH="$GEM_HOME/bin:$PATH"

  executar gem install cocoapods

  executar flutter precache --ios

  colored_print yellow "Versao pod: $(pod --version)"
  # pod update AppAuth Dynatrace --repo-update
  executar pod install --repo-update

  # executar pod update AppAuth


  if [[ "$*" =~ change-ios-envs ]]; then
    colored_print yellow "Mudando variáveis de ambiente do iOS"
    # Lista de variáveis a serem atualizadas/adicionadas
    VARS=("GEM_HOME" "PATH")

    XC_CONFIG_FILES=$(find "./Flutter/" -name "*.xcconfig")
    
    for XC_CONFIG_FILE in "${XC_CONFIG_FILES[@]}"; do 
      for VAR in "${VARS[@]}"; do
        VALUE="${!VAR}"
        if grep -q "^${VAR}=" "$XC_CONFIG_FILE"; then
          # Atualiza a variável existente
          sed -i '' "s|^${VAR}=.*|${VAR}=${VALUE}|" "$XC_CONFIG_FILE"
        else
          # Adiciona a variável ao final do arquivo
          echo "${VAR}=${VALUE}" >> "$XC_CONFIG_FILE"
        fi
      done
    done
  fi


  cd ".." || colored_print red "Erro ao mudar para o diretório ${BASE_PRJ_PATH}"

}

# Executar somente se o script não estiver sendo sourceado
if [[ "${BASH_SOURCE[0]}" == "$0" ]]; then
  executar install_ruby_env
fi

