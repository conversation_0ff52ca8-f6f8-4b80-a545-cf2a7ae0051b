{"buildFiles": ["C:\\Users\\<USER>\\fvm\\versions\\3.29.2\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "G:\\ProgramasDart\\MarketPlace-MobilePLK\\android\\app\\.cxx\\Debug\\1dn2u155\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "G:\\ProgramasDart\\MarketPlace-MobilePLK\\android\\app\\.cxx\\Debug\\1dn2u155\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}