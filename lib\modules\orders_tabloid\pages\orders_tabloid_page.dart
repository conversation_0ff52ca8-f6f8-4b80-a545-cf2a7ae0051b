import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders_tabloid/pages/widgets/orders_tabloid_card_widget.dart';

class OrdersTabloidPage extends StatelessWidget {
  const OrdersTabloidPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<OrdersTabloidController>("OrdersTabloidPage",
        builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelWidget(
                title: ordersController.isEditOrder == true
                    ? ordersController.pdvName!
                    : globalParams.getCurrentStore()?.razaoSocial ?? "-",
                fontSize: DeviceSize.fontSize(14, 18),
                fontWeight: FontWeight.w600,
                textColor: whiteColor,
              ),
              LabelWidget(
                title: ordersController.isEditOrder == true
                    ? ordersController.pdvCnpj!
                    : globalParams.getCurrentStore()?.cNPJ ?? "-",
                fontSize: DeviceSize.fontSize(11, 13),
                textColor: whiteColor,
              ),
            ],
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              GetC.close();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                OrdersCardInfoWidget(
                  orderType: globalParams.getTypeOrderName(),
                  tabloidName: globalParams.order.tabloidName,
                  distributors: globalParams.order.currentDistributors
                      ?.map((e) =>
                          e.distribuidor?.razaoSocial ??
                          e.distribuidor?.nomeFantasia ??
                          "Sem identificação")
                      .toList(),
                  paymentType: globalParams.order.deadlinePayment?.descricao,
                ),
                const Gap(10),
                LabelWidget(
                  title: "*Tabloides*",
                  fontSize: 14.sp,
                  textColor: Colors.grey.shade600,
                ),
                const Gap(20),
                if (ctrl.tabloids != null && ctrl.tabloids!.isNotEmpty)
                  ...ctrl.tabloids!.map((e) => CustomInkWell(
                      onTap: () async => await ctrl.verifyAndAdvance(e),
                      child: OrdersTabloidCardWidget(item: e)))
              ],
            ),
          ),
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
