import 'dart:convert';
import 'dart:developer';

import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders_ideal_mix/models/orders_ideal_mix_model.dart';
import 'package:pharmalink/modules/orders_ideal_mix/models/orders_ideal_mix_request_model.dart';

class OrdersIdealMixController
    extends GetxControllerInstrumentado<OrdersIdealMixController>
    with TraceableController {
  OrdersIdealMixController();
  List<int> distributorIds = [];
  MixIdealModel? dataList;
  List<MixIdealProdutosModel> productCart = [];
  final Map<MixIdealProdutosMixModel, List<MixIdealProdutosModel>>
  produtosOriginaisMap = {};
  final Map<MixIdealProdutosCondicaoModel, List<MixIdealProdutosModel>>
  produtosOriginaisCondMap = {};

  @override
  void onReady() {
    ordersResumeController = Get.find<OrdersResumeController>();

    super.onReady();
  }

  void setDistributorId({List<int>? ids, int? singleId}) {
    distributorIds.clear();
    if (ids != null) {
      distributorIds = ids;
    } else {
      distributorIds.add(singleId!);
    }
  }

  Future<bool> mixIdealIsAvailable() async {
    final result = await ordersIdealMixApi.getIdealMixVerify(
      model: MixIdealRequestModel(
        idLoja: globalParams.getCurrentStore()?.idLoja,
        idsDistribuidores: distributorIds,
      ),
    );
    if (result.data != null) {
      return result.data!.status!;
    } else {
      return false;
    }
  }

  void filtrarProdutosDoMix(MixIdealModel data) {
    for (var condicao in data.condicoes ?? []) {
      for (var familia in condicao.produtosMix ?? []) {
        familia.produtos =
            familia.produtos
                ?.where((mix) => produtoExisteNoPedido(mix.idProduto))
                .toList();
      }
      for (var familiaCond in condicao.produtosCondicao ?? []) {
        familiaCond.produtos =
            familiaCond.produtos
                ?.where((mix) => produtoExisteNoPedido(mix.idProduto))
                .toList();
      }
    }
  }

  bool produtoExisteNoPedido(int? idProduto) {
    final produtosPedido = globalParams.getProducts()?.produtos?.skus ?? [];
    return produtosPedido.any((p) => p.idProduto == idProduto);
  }

  void aplicarRegrasExibicao(MixIdealModel data) {
    final skus = globalParams.getProducts()?.produtos?.skus ?? [];

    final idsPermitidos =
        skus
            .where(
              (produto) =>
                  produto.isDemonstraGridPedido == true &&
                  produto.preco != null &&
                  produto.preco! > 0,
            )
            .map((p) => p.idProduto)
            .toSet();

    final produtosOriginais = produtosOriginaisMap.values.expand((e) => e);

    final novasCondicoes = <MixIdealCondicoesModel>[];

    for (var condicao in data.condicoes ?? []) {
      final novasFamilias = <MixIdealProdutosMixModel>[];
      final novasFamiliasCond = <MixIdealProdutosCondicaoModel>[];

      for (var familia in condicao.produtosMix ?? []) {
        // Filtra os produtos permitidos
        final produtosPermitidos =
            (familia.produtos ?? [])
                .where((p) => idsPermitidos.contains(p.idProduto))
                .toList();

        familia.produtos = produtosPermitidos;

        if (familia.porFamilia == true) {
          if (produtosPermitidos.isNotEmpty) {
            novasFamilias.add(familia);
          }
        } else {
          // Se tiver pelo menos 1 que não está nos originais, deve ser descartado
          final todosValidos = produtosPermitidos.every(
            (produto) => produtosOriginais.contains(produto),
          );

          if (todosValidos && produtosPermitidos.isNotEmpty) {
            novasFamilias.add(familia);
          }
        }
      }

      for (MixIdealProdutosCondicaoModel familia
          in condicao.produtosCondicao ?? []) {
        if (familia.descricaoFamilia == 'FEMIBION') {
          print('aaaa');
        }
        // Filtra os produtos permitidos
        final produtosPermitidos =
            (familia.produtos ?? [])
                .where((p) => idsPermitidos.contains(p.idProduto))
                .toList();

        familia.produtos = produtosPermitidos;

        if (produtosPermitidos.isNotEmpty) {
          novasFamiliasCond.add(familia);
        }
      }

      // Adiciona mix somente se tiver famílias válidas
      if (condicao.produtosMix.length == novasFamilias.length) {
        condicao.produtosMix = novasFamilias;
        condicao.produtosCondicao = novasFamiliasCond;
        novasCondicoes.add(condicao);
      }
    }

    data.condicoes = novasCondicoes;
  }

  void salvarProdutosOriginais(MixIdealModel data) {
    for (var condicao in data.condicoes ?? []) {
      for (var familia in condicao.produtosMix ?? []) {
        produtosOriginaisMap[familia] = List<MixIdealProdutosModel>.from(
          familia.produtos ?? [],
        );
      }
      for (var familiaCond in condicao.produtosCondicao ?? []) {
        produtosOriginaisCondMap[familiaCond] =
            List<MixIdealProdutosModel>.from(familiaCond.produtos ?? []);
      }
    }
  }

  Future<void> getData() async {
    if (globalParams.getCurrentStore()!.dataExtra?.offlineDateSync != null) {
      final syncData =
          orderPaymentTypeController.offlineData?.synchronizationIdealMixResult;
      if (syncData != null) {
        final decoded = jsonDecode(syncData);
        final mixIdealData = MixIdealModel.fromJson(decoded);

        salvarProdutosOriginais(mixIdealData);
        filtrarProdutosDoMix(mixIdealData);
        aplicarRegrasExibicao(mixIdealData);

        dataList = mixIdealData;

        Get.toNamed(RoutesPath.ordersIdealMix);
      }
    } else {
      final loading = PlkLoading();
      loading.show(title: AppStrings.load);
      final result = await ordersIdealMixApi.getIdealMix(
        model: MixIdealRequestModel(
          idLoja: globalParams.getCurrentStore()?.idLoja,
          idsDistribuidores: distributorIds,
        ),
      );
      loading.hide();
      result.when(
        sucess: (data) {
          salvarProdutosOriginais(data);
          filtrarProdutosDoMix(data);
          aplicarRegrasExibicao(data);

          dataList = data;

          Get.toNamed(RoutesPath.ordersIdealMix);
        },
        error: (error) {
          SnackbarCustom.snackbarError(error.error);
        },
      );
    }
  }

  void refreshQtdyMix(
    MixIdealCondicoesModel mixIdealCondicoes,
    MixIdealProdutosMixModel? familyMix,
    MixIdealProdutosCondicaoModel? familyConditionalMix,
  ) {
    mixIdealCondicoes.qtdySelected = 0;
    mixIdealCondicoes.qtdyCondicaoSelected = 0;
    if (mixIdealCondicoes.produtosMix != null) {
      for (var mix in mixIdealCondicoes.produtosMix!) {
        if (mix.produtos != null) {
          for (var produto in mix.produtos!) {
            if (produto.qtdySelected != null) {
              mixIdealCondicoes.qtdySelected =
                  (mixIdealCondicoes.qtdySelected ?? 0) + produto.qtdySelected!;
            }
          }
        }
      }
      for (var mix in mixIdealCondicoes.produtosCondicao!) {
        if (mix.produtos != null) {
          for (var produto in mix.produtos!) {
            if (produto.qtdySelected != null) {
              mixIdealCondicoes.qtdyCondicaoSelected =
                  (mixIdealCondicoes.qtdyCondicaoSelected ?? 0) +
                  produto.qtdySelected!;
            }
          }
        }
      }
    }
    if (familyMix != null) {
      familyMix.qtdySelected = 0;
      if (familyMix.produtos != null) {
        for (var produto in familyMix.produtos!) {
          familyMix.qtdySelected =
              (familyMix.qtdySelected ?? 0) + produto.qtdySelected!;
        }
      }
      print(familyMix.qtdySelected.toString());
    }
    if (familyConditionalMix != null) {
      familyConditionalMix.qtdySelected = 0;
      if (familyConditionalMix.produtos != null) {
        for (var produto in familyConditionalMix.produtos!) {
          familyConditionalMix.qtdySelected =
              (familyConditionalMix.qtdySelected ?? 0) + produto.qtdySelected!;
        }
      }
      print(familyConditionalMix.qtdySelected.toString());
    }
  }

  void setQtdyDown(
    MixIdealProdutosModel item,
    int? minQtdy,
    String nameFamily,
    MixIdealCondicoesModel mixIdealCondicoes,
    MixIdealProdutosMixModel? familyMix,
    MixIdealProdutosCondicaoModel? familyConditionalMix,
  ) {
    item.qtdySelected = item.qtdySelected! - 1;
    if (item.qtdySelected! < 0) item.qtdySelected = 0;

    refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);

    update();
  }

  Future<void> setQtdyUp(
    MixIdealProdutosModel item,
    int? minQtdy,
    String nameFamily,
    MixIdealCondicoesModel mixIdealCondicoes,
    MixIdealProdutosMixModel? familyMix,
    MixIdealProdutosCondicaoModel? familyConditionalMix,
  ) async {
    int counter = 1;
    item.qtdySelected = item.qtdySelected! + counter;

    refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
    if (mixIdealCondicoes.possuiQuantidadeMaximaMix == true) {
      if (mixIdealCondicoes.qtdySelected! >
          mixIdealCondicoes.quantidadeMaximaMix!) {
        item.qtdySelected = item.qtdySelected! - counter;
        await showMessageErrorMixLimit();
        refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
      }
    } else if (mixIdealCondicoes.possuiQuantidadeMaximaCondicao == true) {
      if (mixIdealCondicoes.qtdyCondicaoSelected! >
          mixIdealCondicoes.quantidadeMaximaCondicao!) {
        item.qtdySelected = item.qtdySelected! - counter;
        await showMessageErrorMixLimit();
        refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
      } else {
        if (item.quantidadeMaxima != null) {
          if (item.qtdySelected! > item.quantidadeMaxima!) {
            item.qtdySelected = item.quantidadeMaxima!;
            await showMessageErrorMix();
            refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
          }
        }
      }
    } else if (familyMix != null) {
      if (familyMix.quantidadeMaxima != null) {
        if (familyMix.qtdySelected! > familyMix.quantidadeMaxima!) {
          item.qtdySelected = item.qtdySelected! - counter;
          await showMessageErrorMix();
          refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
        }
      } else {
        if (item.quantidadeMaxima != null) {
          if (item.qtdySelected! > item.quantidadeMaxima!) {
            item.qtdySelected = item.quantidadeMaxima!;
            await showMessageErrorMix();
          }
        }
      }
    } else if (familyConditionalMix != null) {
      if (familyConditionalMix.quantidadeMaxima != null) {
        if (familyConditionalMix.qtdySelected! >
            familyConditionalMix.quantidadeMaxima!) {
          item.qtdySelected = item.qtdySelected! - counter;
          await showMessageErrorMix();
          refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
        }
      } else {
        if (item.quantidadeMaxima != null) {
          if (item.qtdySelected! > item.quantidadeMaxima!) {
            item.qtdySelected = item.quantidadeMaxima!;
            await showMessageErrorMix();
          }
        }
      }
    }

    update();
  }

  Future<void> setQtdyEdit(
    MixIdealProdutosModel item,
    int newQty,
    int? minQtdy,
    String nameFamily,
    MixIdealCondicoesModel mixIdealCondicoes,
    MixIdealProdutosMixModel? familyMix,
    MixIdealProdutosCondicaoModel? familyConditionalMix,
  ) async {
    int oldQtdySelected = item.qtdySelected ?? 0;
    int oldQtdyCondicaoSelected = mixIdealCondicoes.qtdyCondicaoSelected ?? 0;
    int oldQtdyMixSelected = mixIdealCondicoes.qtdySelected ?? 0;
    log('teste: oldQtdyCondicaoSelected: $oldQtdyCondicaoSelected');
    item.qtdySelected = newQty;

    refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
    if (mixIdealCondicoes.possuiQuantidadeMaximaMix == true) {
      if (mixIdealCondicoes.qtdySelected! >
          mixIdealCondicoes.quantidadeMaximaMix!) {
        if (mixIdealCondicoes.quantidadeMaximaMix! > oldQtdyMixSelected) {
          item.qtdySelected =
              mixIdealCondicoes.quantidadeMaximaMix! - oldQtdyMixSelected;
        } else {
          item.qtdySelected = oldQtdySelected;
        }

        item.qtdyController!.text = item.qtdySelected!.toString();
        await showMessageErrorMixLimit();
        refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
      }
    } else if (mixIdealCondicoes.possuiQuantidadeMaximaCondicao == true) {
      if (mixIdealCondicoes.qtdyCondicaoSelected! >
          mixIdealCondicoes.quantidadeMaximaCondicao!) {
        if (mixIdealCondicoes.quantidadeMaximaCondicao! >
            oldQtdyCondicaoSelected) {
          item.qtdySelected =
              mixIdealCondicoes.quantidadeMaximaCondicao! -
              oldQtdyCondicaoSelected;
        } else {
          item.qtdySelected = oldQtdySelected;
        }
        item.qtdyController!.text = item.qtdySelected!.toString();
        await showMessageErrorMixLimit();
        refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
      }
    } else if (familyMix != null) {
      if (familyMix.quantidadeMaxima != null) {
        if (familyMix.qtdySelected! > familyMix.quantidadeMaxima!) {
          item.qtdySelected = oldQtdySelected;
          item.qtdyController!.text = item.qtdySelected!.toString();
          await showMessageErrorMix();
          refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
        }
      } else {
        if (item.quantidadeMaxima != null) {
          if (item.qtdySelected! > item.quantidadeMaxima!) {
            item.qtdySelected = item.quantidadeMaxima!;
            item.qtdyController!.text = item.qtdySelected!.toString();
            await showMessageErrorMix();
          }
        }
      }
    } else if (familyConditionalMix != null) {
      if (familyConditionalMix.quantidadeMaxima != null) {
        if (familyConditionalMix.qtdySelected! >
            familyConditionalMix.quantidadeMaxima!) {
          item.qtdySelected = oldQtdySelected;
          item.qtdyController!.text = item.qtdySelected!.toString();
          await showMessageErrorMix();
          refreshQtdyMix(mixIdealCondicoes, familyMix, familyConditionalMix);
        }
      } else {
        if (item.quantidadeMaxima != null) {
          if (item.qtdySelected! > item.quantidadeMaxima!) {
            item.qtdySelected = item.quantidadeMaxima!;
            item.qtdyController!.text = item.qtdySelected!.toString();
            await showMessageErrorMix();
          }
        }
      }
    }

    update();
  }

  Future<void> showMessageErrorMix() async {
    if (Get.isSnackbarOpen) {
      await Get.closeCurrentSnackbar();
    }
    SnackbarCustom.snackbarError(
      "A quantidade que você digitou é superior ao limite permitido para este item/família. Ajustamos a quantidade para a máxima permitida",
    );
  }

  Future<void> showMessageErrorMixLimit() async {
    if (Get.isSnackbarOpen) {
      await Get.closeCurrentSnackbar();
    }
    SnackbarCustom.snackbarError(
      'Esta oferta possui limite máximo de itens obrigatórios, a soma dos itens solicitados até agora ultrapassou o limite, por pavor, ajuste a quantidade você digitou para seguir',
    );
  }

  Future<void> advance() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport("advance");

    final loading = PlkLoading();
    loading.show(title: AppStrings.mixIdealValidate);

    List<String> mixIdealErrors = [];
    List<String> familiaErrors = [];
    List<String> produtoErrors = [];
    List<String> condicaoErrors = [];

    try {
      for (var c in dataList!.condicoes!) {
        // Verifica se existe pelo menos 1 produto selecionado
        final temProdutoSelecionado = c.produtosMix!
            .expand((pm) => pm.produtos!)
            .any((p) => (p.qtdySelected ?? 0) > 0);

        if (!temProdutoSelecionado) continue;

        if (c.possuiQuantidadeMaximaMix == true &&
            c.qtdySelected! > c.quantidadeMaximaMix!) {
          final errorItem =
              "${c.descricao ?? 'Mix Ideal'} - Qtd: ${c.qtdySelected} - Max Mix: ${c.quantidadeMaximaMix}";
          mixIdealErrors.add(errorItem);
        }

        if (c.possuiQuantidadeMaximaCondicao == true &&
            c.qtdySelected! > c.quantidadeMaximaCondicao!) {
          final errorItem =
              "${c.descricao ?? 'Condição'} - Qtd: ${c.qtdySelected} - Max: ${c.quantidadeMaximaCondicao}";
          condicaoErrors.add(errorItem);
        }

        for (var pm in c.produtosMix!) {
          final isPorProduto = pm.porFamilia == false;

          if (isPorProduto) {
            // Validar produto a produto
            for (var p in pm.produtos!) {
              final q = p.qtdySelected ?? 0;

              if (p.quantidadeMinima != null && q < p.quantidadeMinima!) {
                final errorItem =
                    "${p.descricaoProduto ?? 'Produto'} - Qtd: $q - Min: ${p.quantidadeMinima}";
                produtoErrors.add(errorItem);
              }

              if (p.quantidadeMaxima != null && q > p.quantidadeMaxima!) {
                final errorItem =
                    "${p.descricaoProduto ?? 'Produto'} - Qtd: $q - Max: ${p.quantidadeMaxima}";
                produtoErrors.add(errorItem);
              }
            }
          } else {
            // Validar por família (soma de produtos)
            final qTotal = pm.qtdySelected ?? 0;
            final min = pm.quantidadeMinima;
            final max = pm.quantidadeMaxima;

            if (min != null && qTotal < min || max != null && qTotal > max) {
              final errorItem =
                  "${pm.descricaoFamilia ?? 'Família'} - Qtd: $qTotal - Min: ${min ?? '-'} - Max: ${max ?? '-'}";
              familiaErrors.add(errorItem);
            }
          }
        }
      }

      final hasValidationError = [
        mixIdealErrors,
        familiaErrors,
        produtoErrors,
        condicaoErrors,
      ].any((list) => list.isNotEmpty);

      loading.hide();

      if (hasValidationError) {
        String errorMessage = '';

        if (mixIdealErrors.isNotEmpty) {
          errorMessage += "Mix Ideal:\n- ${mixIdealErrors.join("\n- ")}\n\n";
        }
        if (familiaErrors.isNotEmpty) {
          errorMessage += "Família:\n- ${familiaErrors.join("\n- ")}\n\n";
        }
        if (produtoErrors.isNotEmpty) {
          errorMessage += "Produto:\n- ${produtoErrors.join("\n- ")}\n\n";
        }
        if (condicaoErrors.isNotEmpty) {
          errorMessage += "Condição:\n- ${condicaoErrors.join("\n- ")}\n\n";
        }

        await Dialogs.confirm(
          AppStrings.attention,
          AppStrings.mixIdealQtyMessage(errorMessage.trim()),
          onPressedOk: () async {
            GetC.close();
            await saveCart();
          },
          buttonNameCancel: "CORRIGIR",
          buttonNameOk: "SIM",
        );
        update();
        return;
      }

      await saveCart();
    } catch (e, s) {
      loading.hide();
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> saveCart() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport("saveCart");

    final loading = PlkLoading();
    try {
      loading.show(title: AppStrings.validMixIdeal);

      final listMix = getProdutosValidos();

      await ordersController.mixIdealRefreshCart(listMix);
      subAction.reportValue("listMix", listMix);

      loading.hide();
      GetC.close();
    } catch (e, s) {
      loading.hide();
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  bool isFamilyMixShowLimit(MixIdealProdutosMixModel item) {
    return item.quantidadeMinima != null ||
        item.quantidadeMaxima != null ||
        item.desconto != null;
  }

  List<MixIdealProdutosModel> getProdutosValidos() {
    List<MixIdealProdutosModel> produtosValidos = [];

    // Se dataList for nulo ou condicoes for nulo, retorna lista vazia
    if (dataList == null || dataList!.condicoes == null) {
      return produtosValidos;
    }

    for (final c in dataList!.condicoes!) {
      for (final pm in c.produtosMix!) {
        for (final p in pm.produtos!.where((p) => p.qtdySelected! > 0)) {
          bool produtoValido = true;

          // Validação para possuiQuantidadeMaximaMix
          if (c.possuiQuantidadeMaximaMix == true) {
            if (c.qtdySelected! > c.quantidadeMaximaMix!) {
              produtoValido = false;
            }
          } else {
            // Validação para família (pm)
            if (pm.quantidadeMinima != null && pm.quantidadeMaxima != null) {
              if (pm.qtdySelected! < pm.quantidadeMinima! ||
                  pm.qtdySelected! > pm.quantidadeMaxima!) {
                produtoValido = false;
              }
            } else {
              // Validação para produto (p)
              if (p.quantidadeMinima != null && p.quantidadeMaxima != null) {
                if (p.qtdySelected! < p.quantidadeMinima! ||
                    p.qtdySelected! > p.quantidadeMaxima!) {
                  produtoValido = false;
                }
              }
            }
          }

          // Validação para possuiQuantidadeMaximaCondicao
          if (c.possuiQuantidadeMaximaCondicao == true) {
            if (c.qtdySelected! > c.quantidadeMaximaCondicao!) {
              produtoValido = false;
            }
          }

          // Se o produto passou em todas as validações, adiciona à lista
          if (produtoValido) {
            // Se o desconto do produto for null, usa o desconto da família
            if (p.desconto == null && pm.desconto != null) {
              p.desconto = pm.desconto;
            }
            produtosValidos.add(p);
          }
        }
      }

      for (MixIdealProdutosCondicaoModel pc in c.produtosCondicao ?? []) {
        for (final p in pc.produtos!.where((p) => p.qtdySelected! > 0)) {
          bool produtoValido = true;

          // Validação para quantidade máxima da família condição
          if (pc.quantidadeMaxima != null &&
              pc.qtdySelected != null &&
              pc.qtdySelected! > pc.quantidadeMaxima!) {
            produtoValido = false;
          }
          // Validação para quantidade mínima do produto
          if (p.quantidadeMinima != null &&
              p.qtdySelected! < p.quantidadeMinima!) {
            produtoValido = false;
          }
          // Validação para quantidade máxima do produto
          if (p.quantidadeMaxima != null &&
              p.qtdySelected! > p.quantidadeMaxima!) {
            produtoValido = false;
          }

          if (produtoValido) {
            // Se o desconto do produto for null, usa o desconto da família condicional
            if (p.desconto == null && pc.desconto != null) {
              p.desconto = pc.desconto;
            }
            produtosValidos.add(p);
          }
        }
      }
    }

    return produtosValidos;
  }
}
