import 'package:flutter/material.dart';
import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/widgets/widgets_exports.dart';

class ButtonStateWidget extends StatelessWidget {
  const ButtonStateWidget({
    super.key,
    required this.title,
    required this.fillColor,
    required this.onTap,
  });

  final String title;
  final bool fillColor;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    return CustomInkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 8.0),
        decoration: BoxDecoration(
          color: fillColor ? themesController.getColorButton() : Colors.white,
          borderRadius: BorderRadius.circular(20.0),
          border: Border.all(
              color: fillColor
                  ? themesController.getColorButton()
                  : themesController.getColorButton()),
        ),
        child: Center(
          child: Text(
            title,
            style: TextStyle(
              color:
                  fillColor ? Colors.white : themesController.getColorButton(),
              fontSize: DeviceSize.fontSize(12, 14),
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
    );
  }
}
