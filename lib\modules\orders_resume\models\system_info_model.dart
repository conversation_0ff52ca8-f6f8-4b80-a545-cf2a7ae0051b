import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

class SystemInfoModel {
  String version;
  String deviceName;
  String operationSystem;
  String appName;

  SystemInfoModel._({
    required this.version,
    required this.deviceName,
    required this.operationSystem,
    required this.appName,
  });

  static Future<SystemInfoModel> create() async {
    final deviceInfo = DeviceInfoPlugin();
    final packageInfo = await PackageInfo.fromPlatform();

    String deviceName;
    String operationSystem;

    if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      deviceName = '${androidInfo.manufacturer} ${androidInfo.model}';
      operationSystem = 'Android ${androidInfo.version.release}';
    } else if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      deviceName = iosInfo.name;
      operationSystem = '${iosInfo.systemName} ${iosInfo.systemVersion}';
    } else {
      deviceName = 'Unknown Device';
      operationSystem = 'Unknown OS';
    }

    return SystemInfoModel._(
      version: packageInfo.version,
      deviceName: deviceName,
      operationSystem: operationSystem,
      appName: packageInfo.appName,
    );
  }

  factory SystemInfoModel.fromJson(Map<String, dynamic> json) {
    return SystemInfoModel._(
      version: json['version'] as String,
      deviceName: json['deviceName'] as String,
      operationSystem: json['operationSystem'] as String,
      appName: json['appName'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'version': version,
      'deviceName': deviceName,
      'operationSystem': operationSystem,
      'appName': appName, // Adicionando o nome do aplicativo
    };
  }
}
