class OrdersFooterModel {
  int? totalApresentation;
  int? totalUnits;
  int? qtyReal;
  double? totalGross;
  double? totalNet;
  double? discount;
  OrdersFooterModel({
    this.totalApresentation,
    this.totalUnits,
    this.qtyReal,
    this.totalGross,
    this.totalNet,
    this.discount,
  });

  OrdersFooterModel.fromJson(Map<String, dynamic> json) {
    totalApresentation = json['totalApresentation'];
    totalUnits = json['totalUnits'];
    qtyReal = json['QtyReal'];
    totalNet = json['totalNet'];
    totalGross = json['totalGross'];
    discount = json['discount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['totalApresentation'] = totalApresentation;
    data['totalUnits'] = totalUnits;
    data['QtyReal'] = qtyReal;
    data['totalNet'] = totalNet;
    data['totalGross'] = totalGross;
    data['discount'] = discount;
    return data;
  }
}
