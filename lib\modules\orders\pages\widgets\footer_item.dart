import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class OrderFooterItem extends StatelessWidget {
  const OrderFooterItem(
      {super.key, required this.title, required this.value, this.color});

  final String title;
  final String value;
  final Color? color;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LabelWidget(
          title: title,
          textColor: themesController.getMenuColor(),
          fontSize: DeviceSize.fontSize(10, 12),
        ),
        LabelWidget(
          title: value,
          textColor: color ?? themesController.getMenuColor(),
          fontSize: DeviceSize.fontSize(16, 20),
          fontWeight: FontWeight.bold,
        ),
      ],
    );
  }
}
