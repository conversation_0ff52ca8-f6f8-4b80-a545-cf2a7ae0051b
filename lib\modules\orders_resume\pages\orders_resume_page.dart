import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders/pages/widgets/footer_item.dart';
import 'package:pharmalink/modules/orders_resume/pages/widgets/orders_resume_schedules_widget.dart';
import 'package:pharmalink/modules/orders_resume/pages/widgets/orders_resume_tabs.dart';
import 'package:pharmalink/modules/orders_resume/pages/widgets/orders_resume_upload_file_widget.dart';

class OrdersResumePage extends StatelessWidget {
  const OrdersResumePage({super.key});

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: GetBuilderInstrumentado<OrdersResumeController>(
          "Orders Resume Page", builder: (ctrl) {
          return Scaffold(
            backgroundColor: whiteColor,
            appBar: AppBar(
              backgroundColor: themesController.getPrimaryColor(),
              title: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LabelWidget(
                  title: ordersController.isEditOrder == true
                            ? ordersController.pdvName!
                      : globalParams.getCurrentStore()?.razaoSocial ?? "-",
                    fontSize: DeviceSize.fontSize(14, 18),
                    fontWeight: FontWeight.w600,
                    textColor: whiteColor,
                  ),
                  LabelWidget(
                  title: ordersController.isEditOrder == true
                            ? ordersController.pdvCnpj!
                            : globalParams.getCurrentStore()?.cNPJ ?? "-",
                    fontSize: DeviceSize.fontSize(11, 13),
                    textColor: whiteColor,
                  ),
                ],
              ),
              actions: [
                CustomInkWell(
                  onTap:
                      ctrl.isFinishingOrder
                          ? null
                          : () async => await ctrl.finishOrder(),
                  child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
                    child: LabelWidget(
                      title: "Finalizar".toUpperCase(),
                      fontSize: DeviceSize.fontSize(16, 19),
                      textColor:
                          ctrl.isFinishingOrder
                              ? whiteColor.withValues(alpha: 0.5)
                              : whiteColor,
                    ),
                  ),
                ),
              ],
              leading: IconButton(
              icon: const Icon(
                Icons.arrow_back,
                color: whiteColor,
              ),
                onPressed: () {
                  GetC.close();
                },
              ),
            ),
            body: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    OrdersCardInfoWidget(
                      orderType: globalParams.getTypeOrderName(),
                      tabloidName: globalParams.order.tabloidName,
                    distributors: globalParams.order.currentDistributors
                        ?.map((e) =>
                                    e.distribuidor?.razaoSocial ??
                                    e.distribuidor?.nomeFantasia ??
                            "Sem identificação")
                              .toList(),
                    paymentType: globalParams.order.deadlinePayment?.descricao,
                    ),
                    LabelWidget(
                      title: "Resumo do Pedido",
                      fontSize: DeviceSize.fontSize(20, 23),
                      fontWeight: FontWeight.bold,
                    ),
                    20.toHeightSpace(),
                    LabelWidget(
                      title: "Campos Adicionais",
                      fontSize: DeviceSize.fontSize(16, 19),
                      fontWeight: FontWeight.bold,
                    ),
                    20.toHeightSpace(),
                    SearchTextFieldWidget(
                      label: "Número do cliente",
                      hintText: "Digite o número do cliente",
                      borderColor: themesController.getIconColor(),
                      iconColor: themesController.getIconColor(),
                      iconSize: 18,
                      labelColor: Colors.grey,
                      labelFontSize: DeviceSize.fontSize(14, 18),
                      keyboardType: TextInputType.text,
                      controller: ctrl.numberClientController,
                      onChanged: ctrl.setNumberClient,
                    trailingIcon: (ctrl.resume.clientNumber != null &&
                                  ctrl.resume.clientNumber!.isNotEmpty)
                              ? FontAwesomeIcons.xmark
                              : null,
                      trailingTap: () {
                        ctrl.setNumberClient(null);
                      },
                    ),

                    10.toHeightSpace(),
                    SearchTextFieldWidget(
                      label: "Observações",
                      hintText: "Digite uma observação",
                      borderColor: themesController.getIconColor(),
                      iconColor: themesController.getIconColor(),
                      iconSize: 18,
                      labelColor: Colors.grey,
                      labelFontSize: DeviceSize.fontSize(14, 18),
                      keyboardType: TextInputType.text,
                      controller: ctrl.observationsController,
                      onChanged: ctrl.setObservations,
                    trailingIcon: (ctrl.resume.observation != null &&
                                  ctrl.resume.observation!.isNotEmpty)
                              ? FontAwesomeIcons.xmark
                              : null,
                      trailingTap: () {
                        ctrl.setObservations(null);
                      },
                    ),

                    const OrdersResumeScheduleWidget(),
                    const Gap(10),
                    if (ctrl.isOrderAttachmentAllowed())
                      const OrdersResumeUploadFileWidget(),
                    if (ctrl.isOrderAttachmentAllowed()) const Gap(10),
                    // Aqui está o TabBar (As abas para seleção)
                    const OrdersResumeTabsWidget(),
                  ],
                ),
              ),
            ),
            bottomNavigationBar: Container(
              color: themesController.getPrimaryColor(),
              height: DeviceSize.height(70, 90),
              child: SingleChildScrollView(
              scrollDirection: Axis.horizontal, // Habilita a rolagem horizontal
                child: Padding(
                  padding: EdgeInsets.symmetric(
                    horizontal: DeviceSize.width(8, 16),
                    vertical: DeviceSize.height(10, 16)),
                  child: Row(
                    children: [
                      SizedBox(
                        width: DeviceSize.width(85, 105),
                        child: OrderFooterItem(
                          title: AppStrings.orderFooterTotalApresentation,
                          value: ctrl.footer.totalApresentation!.toString(),
                        ),
                      ),
                      SizedBox(
                        width: DeviceSize.width(75, 95),
                        child: OrderFooterItem(
                          title: AppStrings.orderFooterTotalUnits,
                          value: ctrl.footer.totalUnits!.toString(),
                        ),
                      ),
                      SizedBox(
                        width: DeviceSize.width(75, 95),
                        child: OrderFooterItem(
                          title: AppStrings.orderFooterQtdyReal,
                          value: ctrl.footer.qtyReal!.toString(),
                        ),
                      ),
                      if (settingsAppController.settings.hasAverageDiscount ==
                          true)
                        SizedBox(
                          width: DeviceSize.width(90, 145),
                          child: OrderFooterItem(
                            title: AppStrings.orderFooterDiscount,
                            value: ctrl.footer.discount!.formatPercent(),
                          ),
                        ),
                      SizedBox(
                        width: DeviceSize.width(120, 165),
                        child: OrderFooterItem(
                          title: AppStrings.orderFooterTotalNet,
                          value: ctrl.footer.totalGross!.formatReal(),
                        ),
                      ),
                      SizedBox(
                        width: DeviceSize.width(120, 165),
                        child: OrderFooterItem(
                          title: AppStrings.orderFooterTotalNet2,
                          value: ctrl.footer.totalNet!.formatReal(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
            bottomSheet: const VersionWidget(),
          );
      }),
    );
  }
}
