import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class ColorCircle extends StatelessWidget {
  final Color color;
  final double width;
  final double height;
  final String? value;
  const ColorCircle({
    super.key,
    required this.color,
    this.width = 16,
    this.height = 16,
    this.value,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: color,
        shape: BoxShape.circle,
      ),
      child: value != null
          ? Center(
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
                child: LabelWidget(
                  title: value!,
                  textColor: Colors.white,
                  fontSize: DeviceSize.fontSize(12, 12),
                  fontWeight: FontWeight.bold,
                ),
              ),
            )
          : null,
    );
  }
}
