import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/order_types/models/order_parameters_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_list_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';

class OrdersLocalDataModel extends SqfLiteBase<OrdersLocalDataModel> {
  int? storeId;
  List<CombosOfertaModel>? combosOferta;
  List<OrdersProductsListModel>? productListFull;
  OrderParametersModel? parameters;
  String? clientNumber;
  String? observation;
  bool? isOrderSchedule;
  List<DateTime>? scheduleDate;

  OrdersLocalDataModel({
    this.storeId,
    this.combosOferta,
    this.productListFull,
    this.parameters,
    this.clientNumber,
    this.observation,
    this.scheduleDate,
    this.isOrderSchedule,
  }) : super(DatabaseModels.ordersLocalDataModel);

  OrdersLocalDataModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.ordersLocalDataModel) {
    storeId = json['storeId'];
    parameters = json['parameters'] != null
        ? OrderParametersModel.fromJson(json['parameters'])
        : null;

    if (json['combosOferta'] != null) {
      combosOferta = <CombosOfertaModel>[];
      json['combosOferta'].forEach((v) {
        combosOferta!.add(CombosOfertaModel.fromJson(v));
      });
    }
    if (json['productListFull'] != null) {
      productListFull = <OrdersProductsListModel>[];
      json['productListFull'].forEach((v) {
        productListFull!.add(OrdersProductsListModel.fromJson(v));
      });
    }
    clientNumber = json['clientNumber'];
    observation = json['observation'];
    scheduleDate = json['scheduleDate']
        ?.map<DateTime>((date) => DateTime.parse(date))
        .toList();
    isOrderSchedule = json['isOrderSchedule'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['storeId'] = storeId;
    if (combosOferta != null) {
      data['combosOferta'] = combosOferta!.map((v) => v.toJson()).toList();
    }
    if (productListFull != null) {
      data['productListFull'] =
          productListFull!.map((v) => v.toJson()).toList();
    }
    if (parameters != null) {
      data['parameters'] = parameters!.toJson();
    }
    data['clientNumber'] = clientNumber;
    data['observation'] = observation;
    data['scheduleDate'] =
        scheduleDate?.map((date) => date.toIso8601String()).toList();
    data['isOrderSchedule'] = isOrderSchedule;
    return data;
  }

  // Método para obter o primeiro registro de um tipo de pedido
  Future<OrdersLocalDataModel?> getFirst({
    required int storeId,
    required int typeOrder,
    String? hashCode,
  }) async {
    var list = await getAll<OrdersLocalDataModel>(
      workspaceId: appController.workspace!.workspaceId!,
      userId: appController.userLogged?.userId,
      storeId: storeId,
      hashCode: hashCode ?? typeOrder.toString(),
      OrdersLocalDataModel.fromJson,
    );
    return list.firstWhereOrNull(
        (order) => order.parameters!.typeOrderId == typeOrder);
  }

  // Método para verificar se um tipo de pedido já existe
  Future<bool> exists({
    required int storeId,
    required int typeOrder,
    String? hashCode,
  }) async {
    var list = await getAll<OrdersLocalDataModel>(
      workspaceId: appController.workspace!.workspaceId!,
      userId: appController.userLogged?.userId,
      storeId: storeId,
      hashCode: hashCode ?? typeOrder.toString(),
      OrdersLocalDataModel.fromJson,
    );
    return list.any((e) => e.parameters!.typeOrderId == typeOrder);
  }

  // Método para salvar (inserir ou atualizar) um pedido local
  Future<void> saveOrder({
    required int storeId,
    required int typeOrder,
    String? hashCode,
  }) async {
    // Insere um novo pedido
    await dbContext.addData(
      key: DatabaseModels.ordersLocalDataModel,
      workspaceId: appController.workspace!.workspaceId!,
      userId: appController.userLogged?.userId,
      storeId: storeId,
      data: toJson(),
      hashCode: hashCode ?? typeOrder.toString(),
      clearCurrentData: true,
    );
  }

  // Método para excluir um pedido local pelo tipo de pedido
  Future<void> deleteOrder({
    required int storeId,
    required int typeOrder,
    String? hashCode,
  }) async {
    var list = await getAll<OrdersLocalDataModel>(
      workspaceId: appController.workspace!.workspaceId!,
      storeId: storeId,
      hashCode: hashCode ?? typeOrder.toString(),
      userId: appController.userLogged?.userId,
      OrdersLocalDataModel.fromJson,
    );

    var orderToDelete = list.firstWhereOrNull(
        (order) => order.parameters!.typeOrderId == typeOrder);

    if (orderToDelete != null) {
      await dbContext.deleteByKey(
        key: DatabaseModels.ordersLocalDataModel,
        workspaceId: appController.workspace!.workspaceId!,
        userId: appController.userLogged?.userId,
        storeId: storeId,
        hashCode: hashCode ?? typeOrder.toString(),
      );
    }
  }
}
