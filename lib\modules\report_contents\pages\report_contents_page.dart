import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class ReportContentsPage extends StatelessWidget {
  const ReportContentsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ReportContentsController>(
        "ReportContentsController", builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: LabelWidget(
            title: "Listagem de conteúdos",
            fontSize: DeviceSize.fontSize(18, 21),
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              GetC.close();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              TextField(
                controller: TextEditingController(),
                style: TextStyle(fontSize: 22.sp, color: Colors.green),
                onChanged: (value) {
                  if (value.isEmpty || value.length >= 3) {
                    workspacesController.setSearch(value);
                  }
                },
                decoration: InputDecoration(
                  fillColor: Colors.transparent,
                  prefixIconColor: Colors.red,
                  focusColor: Colors.red,
                  hintText: 'Conteúdos',
                  hintStyle: TextStyle(
                      color: Colors.green,
                      fontSize: DeviceSize.fontSize(28, 32)),
                  contentPadding: const EdgeInsets.all(20),
                  focusedBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.green, width: 2.0),
                  ),
                  enabledBorder: const UnderlineInputBorder(
                    borderSide: BorderSide(color: Colors.green, width: 2.0),
                  ),
                ),
              ),
              20.toHeightSpace(),
              ...ctrl.dataList.map((e) => Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SizedBox(
                      width: double.infinity,
                      child: Card(
                        elevation: 3,
                        child: Padding(
                          padding: const EdgeInsets.all(12.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Flexible(
                                flex: 4,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    LabelWidget(
                                      title: e.fileName!,
                                      fontWeight: FontWeight.bold,
                                      fontSize: DeviceSize.fontSize(14, 17),
                                    ),
                                    5.toHeightSpace(),
                                    LabelWidget(
                                      fontSize: DeviceSize.fontSize(14, 17),
                                      title:
                                          "Descrição: ${e.description ?? ""}",
                                    ),
                                  ],
                                ),
                              ),
                              IconButton(
                                onPressed: () async {
                                  ctrl.download(
                                      e.fileVirtualPath!, e.fileName!);
                                },
                                icon: const Icon(
                                  Icons.download,
                                  size: 32,
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  )),
            ],
          ),
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
