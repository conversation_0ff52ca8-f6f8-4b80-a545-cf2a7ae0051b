import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class LoginResponseModel extends SqfLiteBase<LoginResponseModel> {
  String? accessToken;
  String? tokenType;
  int? expiresIn;
  String? refreshToken;
  String? userId;
  String? userIdCriptografadoAmbienteLegado;
  String? nome;
  String? perfilId;
  String? perfil;
  String? alteraSenha;
  String? tokenAmbienteLegado;
  String? issued;
  DateTime? expires;
  int? workspaceId;
  String? userName;
  String? password;
  bool? isB2c;

  LoginResponseModel({
    this.accessToken,
    this.tokenType,
    this.expiresIn,
    this.refreshToken,
    this.userId,
    this.userIdCriptografadoAmbienteLegado,
    this.nome,
    this.perfilId,
    this.perfil,
    this.alteraSenha,
    this.tokenAmbienteLegado,
    this.issued,
    this.expires,
    this.workspaceId,
    this.userName,
    this.password,
    this.isB2c,
  }) : super(DatabaseModels.loginResponseModel);

  LoginResponseModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.loginResponseModel) {
    accessToken = json['access_token'];
    tokenType = json['token_type'];
    expiresIn = json['expires_in'];
    refreshToken = json['refresh_token'];
    userId = json['UserId'];
    userIdCriptografadoAmbienteLegado =
        json['UserIdCriptografadoAmbienteLegado'];
    nome = json['Nome'];
    perfilId = json['PerfilId'];
    perfil = json['Perfil'];
    alteraSenha = json['AlteraSenha'];
    tokenAmbienteLegado = json['TokenAmbienteLegado'];
    issued = json['.issued'];
    expires = json['.expires'].toString().formatDateByGmt();

    workspaceId = json['workspaceId'];
    userName = json['userName'];
    password = json['password'];
    isB2c = json['isB2c'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['access_token'] = accessToken;
    data['token_type'] = tokenType;
    data['expires_in'] = expiresIn;
    data['refresh_token'] = refreshToken;
    data['UserId'] = userId;
    data['UserIdCriptografadoAmbienteLegado'] =
        userIdCriptografadoAmbienteLegado;
    data['Nome'] = nome;
    data['PerfilId'] = perfilId;
    data['Perfil'] = perfil;
    data['AlteraSenha'] = alteraSenha;
    data['TokenAmbienteLegado'] = tokenAmbienteLegado;
    data['.issued'] = issued;
    data['.expires'] = expires?.toIso8601String();

    data['workspaceId'] = workspaceId;
    data['userName'] = userName;
    data['password'] = password;
    data['isB2c'] = isB2c;

    return data;
  }

  Future<LoginResponseModel?> getFirst() async {
    var list = await getAll<LoginResponseModel>(LoginResponseModel.fromJson);
    return list.isNotEmpty ? list.first : null;
  }

  Future<List<LoginResponseModel>> getList() async {
    var list = await getAll<LoginResponseModel>(LoginResponseModel.fromJson);
    return list;
  }
}
