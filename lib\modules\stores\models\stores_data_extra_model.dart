import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class StoresDataExtraModel extends SqfLiteBase<StoresDataExtraModel> {
  int? idLoja;
  bool? selected;
  bool? isTake;
  int? order;
  int? orderLocal;
  bool? sync;
  bool? isVisita;
  DateTime? currentDate;
  bool? selectedModal;
  bool? deleted;
  int? routeId;
  int? roteiroId;
  bool? selectedVisita;
  bool? isPedido;
  int? visitGoal;
  int? visitsMade;
  bool? isVisitaSync;
  bool? isVisitaCanEdit;
  int? statusVisit;
  bool? isCheckSync;

  //Controle da sincronizacação de dados offline
  bool? offlineIsSync;
  DateTime? offlineDateSync;
  int? offlineSyncStatus;
  double? offlineProgress;
  String? offlineId;
  int? offlineStatus;
  int? offlineTotalPages;
  int? offlineCurrentPages;
  String? offlineMessage;

  bool? canOrderOfflineSync;
  bool? canVisitaSync;
  bool? canPlanned;
  bool? hasRouteSync;

  StoresDataExtraModel({
    this.idLoja,
    this.selected,
    this.isTake,
    this.order,
    this.orderLocal,
    this.sync,
    this.isVisita,
    this.currentDate,
    this.selectedModal,
    this.deleted,
    this.routeId,
    this.roteiroId,
    this.selectedVisita,
    this.isPedido,
    this.visitGoal,
    this.visitsMade,
    this.isVisitaSync,
    this.isVisitaCanEdit,
    this.statusVisit,
    this.isCheckSync,
    this.offlineIsSync,
    this.offlineDateSync,
    this.offlineSyncStatus,
    this.offlineProgress,
    this.offlineId,
    this.offlineStatus,
    this.offlineTotalPages,
    this.offlineCurrentPages,
    this.offlineMessage,
    this.canOrderOfflineSync,
    this.canVisitaSync,
    this.canPlanned,
    this.hasRouteSync,
  }) : super(DatabaseModels.storesDataExtraModel);

  StoresDataExtraModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.storesDataExtraModel) {
    idLoja = json['idLoja'];
    selected = json['selected'] ?? false;
    isTake = json['isTake'] ?? false;
    order = json['order'];
    orderLocal = json['orderLocal'];
    sync = json['sync'] ?? false;
    isVisita = json['isVisita'] ?? false;
    currentDate = json['currentDate'] != null
        ? DateTime.parse(json['currentDate'])
        : null;
    selectedModal = json['selectedModal'] ?? false;
    deleted = json['deleted'] ?? false;
    routeId = json['routeId'];
    roteiroId = json['roteiroId'];
    selectedVisita = json['selectedVisita'] ?? false;
    isPedido = json['isPedido'] ?? false;
    visitGoal = json['visitGoal'] ?? 0;
    visitsMade = json['visitsMade'] ?? 0;
    isVisitaSync = json['isVisitaSync'] ?? false;
    isVisitaCanEdit = json['isVisitaCanEdit'] ?? false;
    statusVisit = json['statusVisit'];
    isCheckSync = json['isCheckSync'] ?? false;
    offlineIsSync = json['offlineIsSync'] ?? false;
    offlineDateSync = json['offlineDateSync'] != null
        ? DateTime.parse(json['offlineDateSync'])
        : null;
    offlineSyncStatus = json['offlineSyncStatus'] ?? 0;
    offlineProgress = json['offlineProgress'] ?? 0;
    offlineId = json['offlineId'];
    offlineStatus = json['offlineStatus'] ?? 1;
    offlineTotalPages = json['offlineTotalPages'] ?? 0;
    offlineCurrentPages = json['offlineCurrentPages'] ?? 0;
    offlineMessage = json['offlineMessage'];
    canOrderOfflineSync = json['canOrderOfflineSync'] ?? false;
    canVisitaSync = json['canVisitaSync'] ?? false;
    canPlanned = json['canPlanned'] ?? false;
    hasRouteSync = json['hasRouteSync'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idLoja'] = idLoja;
    data['selected'] = selected;
    data['isTake'] = isTake;
    data['order'] = order;
    data['orderLocal'] = orderLocal;
    data['sync'] = sync;
    data['isVisita'] = isVisita;
    data['currentDate'] = currentDate?.toIso8601String();
    data['selectedModal'] = selectedModal;
    data['deleted'] = deleted;
    data['routeId'] = routeId;
    data['roteiroId'] = roteiroId;
    data['selectedVisita'] = selectedVisita;
    data['isPedido'] = isPedido;
    data['visitGoal'] = visitGoal;
    data['visitsMade'] = visitsMade;
    data['isVisitaSync'] = isVisitaSync;
    data['isVisitaCanEdit'] = isVisitaCanEdit;
    data['statusVisit'] = statusVisit;
    data['isCheckSync'] = isCheckSync;
    data['offlineIsSync'] = offlineIsSync;
    data['offlineDateSync'] = offlineDateSync?.toIso8601String();
    data['offlineSyncStatus'] = offlineSyncStatus;
    data['offlineProgress'] = offlineProgress;
    data['offlineId'] = offlineId;
    data['offlineStatus'] = offlineStatus;
    data['offlineTotalPages'] = offlineTotalPages;
    data['offlineCurrentPages'] = offlineCurrentPages;
    data['offlineMessage'] = offlineMessage;
    data['canOrderOfflineSync'] = canOrderOfflineSync;
    data['canVisitaSync'] = canVisitaSync;
    data['canPlanned'] = canPlanned;
    data['hasRouteSync'] = hasRouteSync;
    return data;
  }

  Future<StoresDataExtraModel> getFirst({required int storeId}) async {
    var list = await getAll<StoresDataExtraModel>(
        userId: appController.userLogged?.userId!,
        workspaceId: appController.workspace?.workspaceId!,
        storeId: storeId,
        StoresDataExtraModel.fromJson);
    if (list.isNotEmpty) {
      return list.first;
    } else {
      final newData = StoresDataExtraModel(idLoja: storeId);
      await updateDataExtra(newData);

      return newData;
    }
  }

  Future<bool> exists({required int storeId}) async {
    var list = await getAll<StoresDataExtraModel>(
        userId: appController.userLogged?.userId!,
        workspaceId: appController.workspace?.workspaceId!,
        storeId: storeId,
        StoresDataExtraModel.fromJson);
    return list.isNotEmpty;
  }

  Future<void> updateDataExtra(StoresDataExtraModel dataExtra) async {
    await dbContext.addData(
      key: DatabaseModels.storesDataExtraModel,
      data: dataExtra,
      storeId: dataExtra.idLoja,
      userId: appController.userLogged?.userId!,
      workspaceId: appController.workspace!.workspaceId,
      clearCurrentData: true,
    );
  }
}
