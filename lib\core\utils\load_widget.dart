import 'dart:developer';

import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';

enum PlkLoadingAlignment {
  centerTop,
  centerBottom,
  center,
  bottomRight,
  topRight
}

class PlkLoading {
  OverlayEntry? _currentOverlay;

  void show({
    PlkLoadingAlignment alignment = PlkLoadingAlignment.center,
    bool blockScreen = true,
    String? title,
    bool showDelayMessage = false,
    int delayDuration = 45,
    String delayMessage =
        "A operação esta demorando mais que o esperado! deseja aguardar?",
    String continueButtonText = "Sim",
    String cancelButtonText = "Não",
    VoidCallback? onCancel,
  }) {
    if (_currentOverlay != null) {
      // Se já existir um overlay sendo exibido, não faça nada ou remova o existente antes de criar um novo
      return;
    }
    _currentOverlay = OverlayEntry(
      builder: (context) => Material(
        color: Colors.transparent,
        child: _PlkLoadingWidget(
          alignment: alignment,
          blockScreen: blockScreen,
          title: title,
          showDelayMessage: showDelayMessage,
          delayDuration: delayDuration,
          delayMessage: delayMessage,
          continueButtonText: continueButtonText,
          cancelButtonText: cancelButtonText,
          onCancel: onCancel,
          overlayEntry: _currentOverlay!,
        ),
      ),
    );

    Overlay.of(Get.overlayContext!).insert(_currentOverlay!);
  }

  void hide() {
    if (_currentOverlay != null) {
      log('_currentOverlay remove');
      _currentOverlay?.remove();
      _currentOverlay = null;
    } else {
      log('_currentOverlay não mounted');
    }
  }
}

class _PlkLoadingWidget extends StatefulWidget {
  final PlkLoadingAlignment alignment;
  final bool blockScreen;
  final String? title;
  final bool showDelayMessage;
  final int delayDuration;
  final String delayMessage;
  final String continueButtonText;
  final String cancelButtonText;
  final VoidCallback? onCancel;
  final OverlayEntry overlayEntry;

  const _PlkLoadingWidget({
    required this.alignment,
    required this.blockScreen,
    this.title,
    required this.showDelayMessage,
    required this.delayDuration,
    required this.delayMessage,
    required this.continueButtonText,
    required this.cancelButtonText,
    this.onCancel,
    required this.overlayEntry,
  });

  @override
  State<_PlkLoadingWidget> createState() => __PlkLoadingWidgetState();
}

class __PlkLoadingWidgetState extends State<_PlkLoadingWidget> {
  bool _showDelayMessage = false;

  @override
  void initState() {
    super.initState();
    if (widget.showDelayMessage) {
      Future.delayed(Duration(seconds: widget.delayDuration), () {
        if (mounted) {
          setState(() {
            _showDelayMessage = true;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        if (widget.blockScreen)
          ModalBarrier(
            dismissible: false,
            color: Colors.black.withOpacity(0.5),
          ),
        Align(
          alignment: _getAlignment(),
          child: Material(
            color: Colors.transparent,
            child: _showDelayMessage ? _buildDelayMessage() : _buildLoading(),
          ),
        ),
      ],
    );
  }

  Alignment _getAlignment() {
    switch (widget.alignment) {
      case PlkLoadingAlignment.centerTop:
        return Alignment.topCenter;
      case PlkLoadingAlignment.centerBottom:
        return Alignment.bottomCenter;
      case PlkLoadingAlignment.bottomRight:
        return Alignment.bottomRight;
      case PlkLoadingAlignment.topRight:
        return Alignment.topRight;
      case PlkLoadingAlignment.center:
      default:
        return Alignment.center;
    }
  }

  Widget _buildLoading() {
    return Padding(
      padding: (widget.alignment == PlkLoadingAlignment.bottomRight ||
              widget.alignment == PlkLoadingAlignment.topRight)
          ? const EdgeInsets.all(24.0)
          : const EdgeInsets.all(0),
      child: Container(
        padding: const EdgeInsets.all(16.0),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(20.0),
          boxShadow: const [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10,
              offset: Offset(0, 10),
            ),
          ],
        ),
        child: (widget.alignment == PlkLoadingAlignment.center ||
                widget.alignment == PlkLoadingAlignment.centerBottom ||
                widget.alignment == PlkLoadingAlignment.centerTop)
            ? Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    height: 32.0,
                    width: 32.0,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                    ),
                  ),
                  if (widget.title != null) const Gap(10),
                  if (widget.title != null) ...[
                    const SizedBox(height: 5),
                    Text(
                      widget.title!,
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                ],
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const SizedBox(
                    height: 20.0,
                    width: 20.0,
                    child: CircularProgressIndicator(),
                  ),
                  if (widget.title != null) ...[
                    const SizedBox(width: 10),
                    Text(
                      widget.title!,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Theme.of(context).colorScheme.onSurface,
                          ),
                    ),
                  ],
                ],
              ),
      ),
    );
  }

  Widget _buildDelayMessage() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: const [
          BoxShadow(
            color: Colors.black26,
            blurRadius: 10,
            offset: Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            widget.delayMessage,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: Theme.of(context).colorScheme.onSurface,
                ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              TextButton(
                onPressed: () {
                  setState(() {
                    _showDelayMessage = false;
                  });
                  if (widget.showDelayMessage) {
                    Future.delayed(Duration(seconds: widget.delayDuration), () {
                      if (mounted) {
                        setState(() {
                          _showDelayMessage = true;
                        });
                      }
                    });
                  }
                },
                child: Text(widget.continueButtonText),
              ),
              const SizedBox(width: 8),
              TextButton(
                onPressed: () {
                  widget.onCancel?.call();
                  if (widget.overlayEntry.mounted) {
                    widget.overlayEntry.remove();
                  }
                },
                child: Text(widget.cancelButtonText),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
