import 'package:intl/intl.dart';

extension DateExtensions on DateTime {
  static DateTime setTimezone(DateTime current) {
    return current.subtract(const Duration(hours: 3));
  }

  String formatDateSystem() {
    return DateFormat('dd/MM/yyyy', 'pt_Br').format(this);
  }

  String formatDateToApi() {
    return DateFormat('yyyy-MM-dd', 'pt_Br').format(this);
  }

  String formatDateMDY() {
    return DateFormat('MM/dd/yyyy', 'pt_Br').format(this);
  }

  String formatDate() {
    return DateFormat('dd/MM/yyyy', 'pt_Br').format(setTimezone(this));
  }

  String formatDateTime() {
    return DateFormat('dd/MM/yyyy HH:mm:ss', 'pt_Br').format(setTimezone(this));
  }

  String formatDateTimeWrapped() {
    return DateFormat('dd/MM/yyyy\nHH:mm:ss', 'pt_Br')
        .format(setTimezone(this));
  }

  String formatDateTimeLocal() {
    return DateFormat('dd/MM/yyyy HH:mm:ss', 'pt_Br').format(this);
  }

  String formatTime() {
    return DateFormat('HH:mm:ss', 'pt_Br').format(this);
  }

  String formatDM() {
    return DateFormat('dd/MM', 'pt_Br').format(this);
  }

  String formatHMS() {
    return DateFormat('HH:mm:ss', 'pt_Br').format(this);
  }
}

extension DurationExtensions on Duration {
  String formatDuration() {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    final twoDigitMinutes = twoDigits(inMinutes.remainder(60));
    final twoDigitSeconds = twoDigits(inSeconds.remainder(60));
    return "${twoDigits(inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }
}
