import 'package:pharmalink/exports/basic_exports.dart';

class ResponsiveFlexLayout extends StatelessWidget {
  final bool enableMobile;
  final bool enableTablet;
  final List<Widget> children;

  const ResponsiveFlexLayout({
    super.key,
    this.enableMobile = true,
    this.enableTablet = false,
    required this.children,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (enableTablet && isTablet(constraints)) {
          // Use Row layout for tablet mode
          return Row(
            children: _addGapsBetweenChildren(
              children.map((child) => Flexible(child: child)).toList(),
              isRow: true,
            ),
          );
        } else {
          // Use Column layout for mobile or when tablet is disabled
          return Column(
            children: _addGapsBetweenChildren(children),
          );
        }
      },
    );
  }

  List<Widget> _addGapsBetweenChildren(List<Widget> widgets,
      {bool isRow = false}) {
    if (widgets.length <= 1) return widgets;

    List<Widget> result = [];
    for (int i = 0; i < widgets.length; i++) {
      if (i > 0) {
        result.add(isRow ? const Gap(10) : const Gap(0));
      }
      result.add(widgets[i]);
    }
    return result;
  }

  bool isTablet(BoxConstraints constraints) {
    // Define a threshold for tablet width (e.g., 600dp)
    const tabletWidthThreshold = 600;
    return constraints.maxWidth >= tabletWidthThreshold;
  }
}
