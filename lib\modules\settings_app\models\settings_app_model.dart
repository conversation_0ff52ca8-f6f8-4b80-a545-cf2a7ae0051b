import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class SettingsAppModel extends SqfLiteBase<SettingsAppModel> {
  bool? hasTotalInvoicedThisMonth;
  bool? hasTotalRequestedThisMonth;
  bool? hasSentOrdersThisMonth;
  bool? hasPDVOrdersRequestedThisMonth;
  bool? hasPDVOrdersSentThisMonth;
  bool? hasAverageDiscount;

  SettingsAppModel({
    this.hasTotalInvoicedThisMonth,
    this.hasTotalRequestedThisMonth,
    this.hasSentOrdersThisMonth,
    this.hasPDVOrdersRequestedThisMonth,
    this.hasPDVOrdersSentThisMonth,
    this.hasAverageDiscount,
  }) : super(DatabaseModels.settingsAppModel);

  SettingsAppModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.settingsAppModel) {
    hasTotalInvoicedThisMonth = json['hasTotalInvoicedThisMonth'];
    hasTotalRequestedThisMonth = json['hasTotalRequestedThisMonth'];
    hasSentOrdersThisMonth = json['hasSentOrdersThisMonth'];
    hasPDVOrdersRequestedThisMonth = json['hasPDVOrdersRequestedThisMonth'];
    hasPDVOrdersSentThisMonth = json['hasPDVOrdersSentThisMonth'];
    hasAverageDiscount = json['hasAverageDiscount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['hasTotalInvoicedThisMonth'] = hasTotalInvoicedThisMonth ?? false;
    data['hasTotalRequestedThisMonth'] = hasTotalRequestedThisMonth ?? false;
    data['hasSentOrdersThisMonth'] = hasSentOrdersThisMonth ?? false;
    data['hasPDVOrdersRequestedThisMonth'] =
        hasPDVOrdersRequestedThisMonth ?? false;
    data['hasPDVOrdersSentThisMonth'] = hasPDVOrdersSentThisMonth ?? false;
    data['hasAverageDiscount'] = hasAverageDiscount ?? false;

    return data;
  }

  Future<SettingsAppModel?> getFirst(int workspaceId) async {
    var list = await getAll<SettingsAppModel>(
        workspaceId: workspaceId, SettingsAppModel.fromJson);
    return list.isEmpty ? null : list.first;
  }

  Future<bool> exists(int workspaceId) async {
    var list = await getAll<SettingsAppModel>(
        workspaceId: workspaceId, SettingsAppModel.fromJson);
    return list.isNotEmpty;
  }

  Future<List<SettingsAppModel>> getList(int workspaceId) async {
    var list = await getAll<SettingsAppModel>(
        workspaceId: workspaceId, SettingsAppModel.fromJson);
    return list;
  }
}
