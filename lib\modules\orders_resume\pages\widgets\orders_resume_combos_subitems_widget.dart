import 'package:cached_network_image/cached_network_image.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders_resume/models/orders_resume_model.dart';

class OrdersResumeCombosSubItemsWidget extends StatelessWidget {
  const OrdersResumeCombosSubItemsWidget({
    super.key,
    required this.data,
    required this.comboQtdy,
  });
  final int comboQtdy;
  final OrdersResumeProductsItemsModel data;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrdersComboController>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Row(
              children: [
                SizedBox(
                  width: 60,
                  child: CachedNetworkImage(
                      imageUrl: data.photo!,
                      placeholder: (context, url) =>
                          Image.asset(AppImages.productGeneric),
                      errorWidget: (context, url, error) =>
                          Image.asset(AppImages.productGeneric)),
                ),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LabelWidget(
                        title: data.name ?? "-",
                        fontSize: DeviceSize.fontSize(15, 18),
                        fontWeight: FontWeight.bold,
                      ),
                      LabelWidget(
                        title: "${data.codelLabel}: ${data.code}",
                        fontSize: DeviceSize.fontSize(13, 16),
                      ),
                    ],
                  ),
                )
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(10.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TilesInfoWidget(
                    title: "Qtd",
                    value: ordersResumeController
                        .getComboProductQty(data, comboQtdy)
                        .toString(),
                    titleFontSize: DeviceSize.fontSize(11, 13),
                    valueFontSize: DeviceSize.fontSize(13, 16),
                  ),
                  TilesInfoWidget(
                    title: "Desc. disp.",
                    value: data.discount?.formatPercent() ?? "0,00",
                    titleFontSize: DeviceSize.fontSize(11, 13),
                    valueFontSize: DeviceSize.fontSize(13, 16),
                  ),
                  TilesInfoWidget(
                    title: "De",
                    value: ordersResumeController
                        .getComboProductPrice(data, comboQtdy, false)
                        .formatReal(),
                    isRitchText: true,
                    titleFontSize: DeviceSize.fontSize(11, 13),
                    valueFontSize: DeviceSize.fontSize(13, 16),
                  ),
                  TilesInfoWidget(
                    title: "Por",
                    value: ordersResumeController
                        .getComboProductPrice(data, comboQtdy, true)
                        .formatReal(),
                    titleFontSize: DeviceSize.fontSize(11, 13),
                    valueFontSize: DeviceSize.fontSize(13, 16),
                  ),
                ],
              ),
            ),
            const Divider(
              thickness: 1.5,
            ),
          ],
        ),
      );
    });
  }
}
