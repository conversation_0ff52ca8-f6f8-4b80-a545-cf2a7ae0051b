import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/home/<USER>/home_controller.dart';

class HomePdvSelectedWidget extends StatelessWidget {
  const HomePdvSelectedWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(builder: (ctrl) {
      return globalParams.getCurrentStore() == null
          ? Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: LabelWidget(
                    title: 'Selecione um PDV',
                    fontSize: 13.sp,
                    textColor: whiteColor,
                  ),
                ),
                Flexible(
                  child: PrimaryButtonWidget(
                    onTap: () async {
                      navigationPageController.onTapSelectedIndex(1);
                    },
                    height: 25.h,
                    width: 150.w,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Selecionar".toUpperCase(),
                          style: TextStyle(color: whiteColor, fontSize: 11.sp),
                        ),
                        Icon(
                          FontAwesomeIcons.arrowRight,
                          size: 14.w,
                          color: whiteColor,
                        )
                      ],
                    ),
                  ),
                ),
              ],
            )
          : Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LabelWidget(
                        title: globalParams
                            .getCurrentStore()!
                            .nomeFantasia!
                            .toString(),
                        fontSize: 15.sp,
                        textColor: whiteColor,
                        fontWeight: FontWeight.bold,
                      ),
                      LabelWidget(
                        title: globalParams.getCurrentStore()!.cNPJ!,
                        fontSize: 13.sp,
                        textColor: whiteColor,
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    IconButtonWidget(
                      title: "Ver",
                      textColor: themesController.getMenuColor(),
                      icon: Icon(
                        FontAwesomeIcons.solidEye,
                        size: 20,
                        color: themesController.getMenuColor(),
                      ),
                      onTap: () async {
                        await ctrl.openStoreDetail();
                      },
                    ),
                    IconButtonWidget(
                      title: "Excluir",
                      textColor: themesController.getMenuColor(),
                      icon: Icon(
                        FontAwesomeIcons.trash,
                        size: 20,
                        color: themesController.getMenuColor(),
                      ),
                      onTap: () async {
                        globalParams.order.setCurrentStore(null);
                        await homeController.updateDashBoardPdv();
                      },
                    ),
                  ],
                ),
              ],
            );
    });
  }
}
