import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class ThemesModel extends SqfLiteBase<ThemesModel> {
  int? idTema;
  String? descricao;
  bool? status;
  String? paletaCorA;
  String? paletaCorB;
  String? paletaCorC;
  String? paletaCorD;
  String? paletaCorFonteMenu;
  String? paletaCorFundo;
  String? paletaCorPrimaria;
  String? paletaCorSecundaria;
  String? paletaCorBotao;
  String? paletaCorIcone;
  bool? padrao;
  DateTime? dataInclusao;
  DateTime? dataAlteracao;
  bool? apagado;

  ThemesModel({
    this.idTema,
    this.descricao,
    this.status,
    this.paletaCorA,
    this.paletaCorB,
    this.paletaCorC,
    this.paletaCorD,
    this.paletaCorFonteMenu,
    this.paletaCorFundo,
    this.paletaCorPrimaria,
    this.paletaCorSecundaria,
    this.paletaCorBotao,
    this.paletaCorIcone,
    this.padrao,
    this.dataInclusao,
    this.dataAlteracao,
    this.apagado,
  }) : super(DatabaseModels.themesModel);

  ThemesModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.themesModel) {
    idTema = json['IdTema'];
    descricao = json['Descricao'];
    status = json['Status'];
    paletaCorA = json['paletaCorA'];
    paletaCorB = json['paletaCorB'];
    paletaCorC = json['paletaCorC'];
    paletaCorD = json['paletaCorD'];
    paletaCorFonteMenu = json['paletaCorFonteMenu'];

    if (json['paletaCorFundo'] == null) {
      paletaCorFundo = json['paletaCorA'];
    } else {
      paletaCorFundo = json['paletaCorFundo'];
    }
    if (json['paletaCorPrimaria'] == null) {
      paletaCorPrimaria = json['paletaCorA'];
    } else {
      paletaCorPrimaria = json['paletaCorPrimaria'];
    }
    if (json['paletaCorSecundaria'] == null) {
      paletaCorSecundaria = json['paletaCorB'];
    } else {
      paletaCorSecundaria = json['paletaCorSecundaria'];
    }

    if (json['paletaCorBotao'] == null) {
      paletaCorBotao = json['paletaCorC'];
    } else {
      paletaCorBotao = json['paletaCorBotao'];
    }

    if (json['paletaCorIcone'] == null) {
      paletaCorIcone = json['paletaCorD'];
    } else {
      paletaCorIcone = json['paletaCorIcone'];
    }

    padrao = json['Padrao'];
    dataInclusao = json['DataInclusao'] != null
        ? DateTime.parse(json['DataInclusao'])
        : null;

    dataAlteracao = json['DataAlteracao'] != null
        ? DateTime.parse(json['DataAlteracao'])
        : null;

    apagado = json['Apagado'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdTema'] = idTema;
    data['Descricao'] = descricao;
    data['Status'] = status;
    data['paletaCorA'] = paletaCorA;
    data['paletaCorB'] = paletaCorB;
    data['paletaCorC'] = paletaCorC;
    data['paletaCorD'] = paletaCorD;
    data['paletaCorFonteMenu'] = paletaCorFonteMenu;
    data['paletaCorFundo'] = paletaCorFundo;
    data['paletaCorPrimaria'] = paletaCorPrimaria;
    data['paletaCorSecundaria'] = paletaCorSecundaria;
    data['paletaCorBotao'] = paletaCorBotao;
    data['paletaCorIcone'] = paletaCorIcone;
    data['Padrao'] = padrao;
    data['DataInclusao'] = dataInclusao?.toIso8601String();
    data['DataAlteracao'] = dataAlteracao?.toIso8601String();
    data['Apagado'] = apagado;

    return data;
  }

  Future<ThemesModel> getFirst() async {
    var list = await getAll<ThemesModel>(ThemesModel.fromJson);
    return list.first;
  }

  Future<List<ThemesModel>> getList({required int workspaceId}) async {
    var list = await getAllByKey<ThemesModel>(DatabaseModels.themesModel,
        workspaceId, null, null, null, ThemesModel.fromJson);
    return list;
  }
}
