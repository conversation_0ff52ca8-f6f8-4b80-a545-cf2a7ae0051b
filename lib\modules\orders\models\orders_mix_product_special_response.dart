import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class GetMixSpecialProductsResponse
    extends SqfLiteBase<GetMixSpecialProductsResponse> {
  int? tabloidId;
  int? paymentDeadlineId;
  List<GetMixSpecialProductsProdutosDistribuidores>? produtosDistribuidores;
  List<GetMixSpecialProductsProdutosDUNDistribuidor>? produtosDUNDistribuidor;

  GetMixSpecialProductsResponse(
      {this.tabloidId,
      this.paymentDeadlineId,
      this.produtosDistribuidores,
      this.produtosDUNDistribuidor})
      : super(DatabaseModels.getMixSpecialProductsResponse);

  GetMixSpecialProductsResponse.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.getMixSpecialProductsResponse) {
    tabloidId = json['TabloidId'];
    paymentDeadlineId = json['PaymentDeadlineId'];
    if (json['ProdutosDistribuidores'] != null) {
      produtosDistribuidores = <GetMixSpecialProductsProdutosDistribuidores>[];
      json['ProdutosDistribuidores'].forEach((v) {
        produtosDistribuidores!
            .add(GetMixSpecialProductsProdutosDistribuidores.fromJson(v));
      });
    }
    if (json['ProdutosDUNDistribuidor'] != null) {
      produtosDUNDistribuidor =
          <GetMixSpecialProductsProdutosDUNDistribuidor>[];
      json['ProdutosDUNDistribuidor'].forEach((v) {
        produtosDUNDistribuidor!
            .add(GetMixSpecialProductsProdutosDUNDistribuidor.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['TabloidId'] = tabloidId;
    data['PaymentDeadlineId'] = paymentDeadlineId;
    if (produtosDistribuidores != null) {
      data['ProdutosDistribuidores'] =
          produtosDistribuidores!.map((v) => v.toJson()).toList();
    }
    if (produtosDUNDistribuidor != null) {
      data['ProdutosDUNDistribuidor'] =
          produtosDUNDistribuidor!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  Future<GetMixSpecialProductsResponse?> getFirst(
      {required int storeId, required String tabloidId}) async {
    var list = await getAll<GetMixSpecialProductsResponse>(
        workspaceId: appController.workspace!.workspaceId!,
        userId: appController.userLogged!.userId!,
        storeId: storeId,
        hashCode: tabloidId,
        GetMixSpecialProductsResponse.fromJson);
    return list.firstOrNull;
  }

  Future<bool> exists({required int storeId, required String tabloidId}) async {
    var list = await getAll<GetMixSpecialProductsResponse>(
        workspaceId: appController.workspace!.workspaceId!,
        userId: appController.userLogged!.userId!,
        storeId: storeId,
        hashCode: tabloidId,
        GetMixSpecialProductsResponse.fromJson);
    return list.isNotEmpty;
  }
}

class GetMixSpecialProductsProdutosDistribuidores {
  int? idProduto;
  int? idPrazoPagamento;
  List<int>? idsDistribuidores;

  GetMixSpecialProductsProdutosDistribuidores(
      {this.idProduto, this.idPrazoPagamento, this.idsDistribuidores});

  GetMixSpecialProductsProdutosDistribuidores.fromJson(
      Map<String, dynamic> json) {
    idProduto = json['idProduto'];
    idPrazoPagamento = json['IdPrazoPagamento'];
    idsDistribuidores = json['idsDistribuidores'].cast<int>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idProduto'] = idProduto;
    data['IdPrazoPagamento'] = idPrazoPagamento;
    data['idsDistribuidores'] = idsDistribuidores;
    return data;
  }
}

class GetMixSpecialProductsProdutosDUNDistribuidor {
  int? idProdutoDUN;
  List<int>? idsDistribuidores;

  GetMixSpecialProductsProdutosDUNDistribuidor(
      {this.idProdutoDUN, this.idsDistribuidores});

  GetMixSpecialProductsProdutosDUNDistribuidor.fromJson(
      Map<String, dynamic> json) {
    idProdutoDUN = json['idProdutoDUN'];
    idsDistribuidores = json['idsDistribuidores'].cast<int>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idProdutoDUN'] = idProdutoDUN;
    data['idsDistribuidores'] = idsDistribuidores;
    return data;
  }
}
