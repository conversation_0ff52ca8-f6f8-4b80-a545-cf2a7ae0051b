import 'package:pharmalink/config/databases/call_context.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/models/result_error.dart';
import 'package:pharmalink/core/models/result_error_model.dart';
import 'package:pharmalink/core/utils/dynatrace/dynatrace_logger.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/stores_parameters/models/store_parameters_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/store_parameters_request_model.dart';

class StoreParametersController
    extends GetxControllerInstrumentado<StoreParametersController>
    with TraceableController {
  StoreParametersController();

  double progressPerBatch = 1.0;
  StoreParametersModel? selected;
  List<StoreParametersModel> dataList = [];
  List<StoreParametersModel> storeSelected = [];

  void setDataList(List<StoreParametersModel> data) {
    dataList = data;
  }

  Future<void> saveStoreParameters(
      List<StoreParametersModel> storeParameters) async {
    return trace('saveStoreParameters', () async {
      for (var item in storeParameters) {
        await dbContext.withControllerAction(this).addData(
              key: DatabaseModels.storeParametersModel,
              data: item,
              workspaceId: appController.workspace!.workspaceId,
              userId: appController.userLogged!.userId,
              clearCurrentData: true,
              storeId: item.idLoja!,
              hashCode: item.tipoPedido!,
              isOnline: true,
            );
      }
    });
  }

  Future<void> loadOfflineData({
    required int storeId,
    required String typeOrder,
  }) async {
    final workspaceId = appController.workspace!.workspaceId!;
    final userId = appController.userLogged!.userId!;

    final storeParametersBox = await StoreParametersModel()
        .getList(workspaceId, storeId, typeOrder, false);

    final logData = {
      'workspaceId': workspaceId,
      'userId': userId,
      'storeId': storeId,
      'typeOrder': typeOrder,
      'parameters': storeParametersBox,
    };

    if (storeParametersBox.isNotEmpty) {
      setDataList(storeParametersBox);
      storeSelected =
          dataList.where((element) => element.idLoja == storeId).toList();
      DynatraceLogger.logToDynatrace(
          'Load Offline Data - Store Parameters', logData);
    } else {
      DynatraceLogger.logToDynatrace(
          'Load Offline Data - Store Parameters - NÃO ENCONTRADO', logData);
    }
  }

  Future<void> getDataByStoreIdAndType({
    required int storeId,
    required String typeOrder,
    bool? isOffline = false,
  }) async {
    if (!connectivityController.isConnected || isOffline == true) {
      final storeParametersBox = await StoreParametersModel().getList(
          appController.workspace!.workspaceId!, storeId, typeOrder, true);
      if (storeParametersBox.isNotEmpty) {
        storeParametersController.setDataList(storeParametersBox);

        storeSelected =
            dataList.where((element) => element.idLoja == storeId).toList();
        return;
      }
    }

    List<int> allIds = [storeId];
    final result = await storeParametersApi.getStoreParameters(
      req: StoreParametersRequestModel(
        userId: appController.userLogged!.userId!,
        tipoPedidoLista: [
          typeOrder //TyperOrderStringEnum.rep,
        ],
        idLojaLista: allIds,
      ),
    );
    if (result.data != null) {
      // Save data to the database
      await saveStoreParameters(result.data!);

      // Setting data to the dataList
      setDataList(result.data!);

      storeSelected =
          dataList.where((element) => element.idLoja == storeId).toList();
    } else {
      if (result.error!.data is ResultErrorModel) {
        final error = result.error!.data as ResultErrorModel;
        final String? message =
            error.modelState?.erros!.map((e) => e).join("\n");

        SnackbarCustom.snackbarError(message ?? "");
        return;
      } else if (result.error!.data is ResultError) {
        final error = result.error!.data as ResultError;
        final String message = error.error;

        SnackbarCustom.snackbarError(message);
        return;
      }

      setDataList([]);
    }
  }
}
