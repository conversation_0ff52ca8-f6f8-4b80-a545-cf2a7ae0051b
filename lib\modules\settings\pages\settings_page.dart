import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:url_launcher/url_launcher.dart';

class SettingsPage extends StatelessWidget {
  const SettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: whiteColor,
      appBar: AppBar(
        backgroundColor: themesController.getPrimaryColor(),
        title: LabelWidget(
          title: "Configurações",
          fontSize: DeviceSize.fontSize(18, 21),
          fontWeight: FontWeight.w600,
          textColor: whiteColor,
        ),
        leading: const SizedBox.shrink(),
        leadingWidth: 0,
        actions: [
          CustomInkWell(
            onTap: loginController.logout,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
              child: LabelWidget(
                title: "SAIR",
                fontSize: 15.sp,
                textColor: whiteColor,
              ),
            ),
          )
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.only(bottom: 10),
        child: Column(
          children: [
            Expanded(
              child: ListView(
                children: [
                  20.toHeightSpace(),
                  LabelWidget(
                    title: 'Workspace',
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                  ),
                  ListTileItemWidget(
                    title: 'Gerenciar',
                    onTap: () {
                      Get.toNamed(RoutesPath.workspaces,
                          arguments: {'settings': true});
                    },
                  ),
                  const Divider(),
                  LabelWidget(
                    title: 'Sincronização',
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                  ),
                  ListTileItemWidget(
                    title: 'Sincronizar',
                    onTap: () async {
                      await synchronizationsController.onReady();
                      Get.toNamed(RoutesPath.synchronizations,
                          arguments: {'all': true});
                    },
                  ),
                  const Divider(),
                  LabelWidget(
                    title: 'Aplicativo',
                    padding: EdgeInsets.symmetric(horizontal: 16.w),
                  ),
                  ListTileItemWidget(
                    title: 'Configurações',
                    onTap: () {
                      Get.toNamed(RoutesPath.settingsApp);
                    },
                  ),
                  if (kDebugMode) const Divider(),
                  if (kDebugMode)
                    ListTileItemWidget(
                      title: 'QA',
                      onTap: () {
                        Get.toNamed(RoutesPath.qATester);
                      },
                    ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12),
              child: PrimaryButtonWidget(
                buttonColor: themesController.getPrimaryColor(),
                borderRadius: 5,
                onTap: () async {
                  final url = appController.formsUrl;
                  if (Platform.isAndroid || Platform.isIOS) {
                    if (await canLaunchUrl(Uri.parse(url))) {
                      await launchUrl(Uri.parse(url),
                          mode: LaunchMode.externalApplication);
                    }
                  }
                },
                textFontWeight: FontWeight.w600,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      FontAwesomeIcons.listCheck,
                      size: 18,
                      color: themesController.getMenuColor(),
                    ),
                    const Gap(10),
                    LabelWidget(
                      title: 'Clique aqui para avaliar o aplicativo',
                      textColor: themesController.getMenuColor(),
                      fontSize: DeviceSize.fontSize(14, 20),
                      fontWeight: FontWeight.w600,
                      textAlign: TextAlign.center,
                    ),
                    const Gap(10),
                    Icon(
                      FontAwesomeIcons.upRightFromSquare,
                      size: 18,
                      color: themesController.getMenuColor(),
                    ),
                  ],
                ),
              ),
            ),
            LabelWidget(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
              title:
                  'Você será redirecionado para um formulário online. O tempo estimado para preenchimento é de 8 minutos.',
              textColor: Colors.grey.shade500,
              fontSize: DeviceSize.fontSize(14, 18),
            ),
            const Gap(5),
          ],
        ),
      ),
    );
  }
}
