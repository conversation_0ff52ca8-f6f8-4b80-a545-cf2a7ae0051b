import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:sqlite_explorer/app/app_exports.dart';
import 'package:sqlite_explorer/app_constants.dart';
import 'package:sqlite_explorer/config/databases/database_init.dart';
import 'package:sqlite_explorer/config/databases/ilocal_db.dart';
import 'package:sqlite_explorer/core/http_manager/http_manager.dart';
import 'package:sqlite_explorer/exports/get_exports.dart';

abstract class DependencyInjection {
  static Future<void> init() async {
    Get.put<HttpManager>(
      HttpManager(
        Dio(
          BaseOptions(
            connectTimeout: const Duration(minutes: 20),
            receiveTimeout: const Duration(minutes: 20),
          ),
        ),
      ),
    );
    Get.put<ILocalDb>(LocalDb(databaseDefault));
    //Register Controllers
    Get.put<AppController>(AppController());
  }
}
