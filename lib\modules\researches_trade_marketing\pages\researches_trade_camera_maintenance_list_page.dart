import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

// ignore: must_be_immutable
class ResearchesTradeMaintenanceListPage extends StatelessWidget {
  ResearchesTradeMaintenanceListPage({
    super.key,
  });
  int count = 0;
  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ResearchesTradeMarketingController>(
        "ResearchesTradeMaintenanceListPage", builder: (ctrl) {
      return Scaffold(
          backgroundColor: whiteColor,
          appBar: AppBar(
            backgroundColor: themesController.getPrimaryColor(),
            title: LabelWidget(
              title: "Manutenções enviadas",
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              textColor: whiteColor,
            ),
            leading: IconButton(
              icon: const Icon(
                Icons.arrow_back,
                color: whiteColor,
              ),
              onPressed: () {
                GetC.close();
              },
            ),
          ),
          body: SingleChildScrollView(
            child: Column(
              children: ctrl.maintenanceListDate.map((e) {
                count = count + 1;
                return SizedBox(
                  width: double.infinity,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: CustomInkWell(
                      onTap: () {
                        ctrl.openMaintenanceDetail(e.date!);
                      },
                      child: Card(
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              LabelWidget(
                                title: "Manutenção ${e.index.toString()}",
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                              ),
                              5.toHeightSpace(),
                              LabelWidget(
                                title: e.date!.formatDate(
                                    formatType: DateFormatType.ddMMyyyy),
                                fontSize: 14,
                                textColor: Colors.grey,
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ),
          ));
    });
  }
}
