class Endereco {
  String? bairro;
  String? cep;
  Cidade? cidade;
  String? complemento;
  String? dataAlteracao;
  String? dataInclusao;
  EnderecoStatus? enderecoStatus;
  String? errosValidacao;
  int? idCidade;
  int? idEndereco;
  bool? isDeleted;
  String? kmRodado;
  String? latitude;
  String? logradouro;
  String? longitude;
  String? numero;
  Uf? uf;

  Endereco(
      {this.bairro,
      this.cep,
      this.cidade,
      this.complemento,
      this.dataAlteracao,
      this.dataInclusao,
      this.enderecoStatus,
      this.errosValidacao,
      this.idCidade,
      this.idEndereco,
      this.isDeleted,
      this.kmRodado,
      this.latitude,
      this.logradouro,
      this.longitude,
      this.numero,
      this.uf});

  Endereco.fromJson(Map<String, dynamic> json) {
    bairro = json['Bairro'];
    cep = json['Cep'];
    cidade = json['Cidade'] != null ? Cidade.fromJson(json['Cidade']) : null;
    complemento = json['Complemento'];
    dataAlteracao = json['DataAlteracao'];
    dataInclusao = json['DataInclusao'];
    enderecoStatus = json['EnderecoStatus'] != null
        ? EnderecoStatus.fromJson(json['EnderecoStatus'])
        : null;
    errosValidacao = json['ErrosValidacao'];
    idCidade = json['IdCidade'];
    idEndereco = json['IdEndereco'];
    isDeleted = json['IsDeleted'];
    kmRodado = json['KmRodado'];
    latitude = json['Latitude'];
    logradouro = json['Logradouro'];
    longitude = json['Longitude'];
    numero = json['Numero'];
    uf = json['Uf'] != null ? Uf.fromJson(json['Uf']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Bairro'] = bairro;
    data['Cep'] = cep;
    if (cidade != null) {
      data['Cidade'] = cidade!.toJson();
    }
    data['Complemento'] = complemento;
    data['DataAlteracao'] = dataAlteracao;
    data['DataInclusao'] = dataInclusao;
    if (enderecoStatus != null) {
      data['EnderecoStatus'] = enderecoStatus!.toJson();
    }
    data['ErrosValidacao'] = errosValidacao;
    data['IdCidade'] = idCidade;
    data['IdEndereco'] = idEndereco;
    data['IsDeleted'] = isDeleted;
    data['KmRodado'] = kmRodado;
    data['Latitude'] = latitude;
    data['Logradouro'] = logradouro;
    data['Longitude'] = longitude;
    data['Numero'] = numero;
    if (uf != null) {
      data['Uf'] = uf!.toJson();
    }
    return data;
  }
}

class Cidade {
  int? idCidade;
  String? descricao;
  String? uf;
  bool? isZonaFranca;

  Cidade({this.idCidade, this.descricao, this.uf, this.isZonaFranca});

  Cidade.fromJson(Map<String, dynamic> json) {
    idCidade = json['IdCidade'];
    descricao = json['Descricao'];
    uf = json['Uf'];
    isZonaFranca = json['IsZonaFranca'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdCidade'] = idCidade;
    data['Descricao'] = descricao;
    data['Uf'] = uf;
    data['IsZonaFranca'] = isZonaFranca;
    return data;
  }
}

class EnderecoStatus {
  int? idEnderecoStatusKBackingField;
  String? descricaoKBackingField;

  EnderecoStatus(
      {this.idEnderecoStatusKBackingField, this.descricaoKBackingField});

  EnderecoStatus.fromJson(Map<String, dynamic> json) {
    idEnderecoStatusKBackingField = json['<IdEnderecoStatus>k__BackingField'];
    descricaoKBackingField = json['<Descricao>k__BackingField'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['<IdEnderecoStatus>k__BackingField'] = idEnderecoStatusKBackingField;
    data['<Descricao>k__BackingField'] = descricaoKBackingField;
    return data;
  }
}

class Uf {
  String? uf;
  String? descricao;

  Uf({this.uf, this.descricao});

  Uf.fromJson(Map<String, dynamic> json) {
    uf = json['Uf'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Uf'] = uf;
    data['Descricao'] = descricao;
    return data;
  }
}
