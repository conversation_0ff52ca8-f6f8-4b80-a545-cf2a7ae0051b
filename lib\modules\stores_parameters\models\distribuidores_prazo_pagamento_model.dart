import 'package:pharmalink/modules/stores_parameters/models/distribuidor_model.dart';

class DistribuidoresPrazoPagamentoModel {
  int? pdvId;
  int? distribuidorId;
  int? ordemDePreferencia;
  int? ordemMelhorAtendimento;

  DistribuidorModel? distribuidor;

  DistribuidoresPrazoPagamentoModel({
    this.pdvId,
    this.distribuidorId,
    this.ordemDePreferencia,
    this.ordemMelhorAtendimento,
    this.distribuidor,
  });

  DistribuidoresPrazoPagamentoModel.fromJson(Map<String, dynamic> json) {
    pdvId = json['PdvId'];
    distribuidorId = json['DistribuidorId'];
    ordemDePreferencia = json['OrdemDePreferencia'];
    ordemMelhorAtendimento = json['OrdemMelhorAtendimento'];

    distribuidor = json['Distribuidor'] != null
        ? DistribuidorModel.fromJson(json['Distribuidor'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['PdvId'] = pdvId;
    data['DistribuidorId'] = distribuidorId;
    data['OrdemDePreferencia'] = ordemDePreferencia;
    data['OrdemMelhorAtendimento'] = ordemMelhorAtendimento;

    if (distribuidor != null) {
      data['Distribuidor'] = distribuidor!.toJson();
    }

    return data;
  }
}
