import 'dart:convert';

import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/settings_app/models/settings_app_model.dart';

class SettingsAppController
    extends GetxControllerInstrumentado<SettingsAppController> {
  SettingsAppController();

  SettingsAppModel settings = SettingsAppModel();

  Future<void> init() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport("init");
    try {
      settings = await SettingsAppModel()
              .getFirst(appController.workspace!.workspaceId!) ??
          SettingsAppModel(
              hasAverageDiscount: false,
              hasPDVOrdersRequestedThisMonth: false,
              hasPDVOrdersSentThisMonth: false,
              hasSentOrdersThisMonth: false,
              hasTotalInvoicedThisMonth: false,
              hasTotalRequestedThisMonth: false);
      subAction.reportValue("settings", jsonEncode(settings));
    } finally {
      leaveAction();
    }
  }

  Future<void> setTotalInvoicedThisMonth(bool? v) async {
    settings.hasTotalInvoicedThisMonth = v;
    await save();
    update();
  }

  Future<void> setTotalRequestedThisMonth(bool? v) async {
    settings.hasTotalRequestedThisMonth = v;
    await save();
    update();
  }

  Future<void> setSentOrdersThisMonth(bool? v) async {
    settings.hasSentOrdersThisMonth = v;
    await save();
    update();
  }

  Future<void> setPDVOrdersRequestedThisMonth(bool? v) async {
    settings.hasPDVOrdersRequestedThisMonth = v;
    await save();
    update();
  }

  Future<void> setPDVOrdersSentThisMonth(bool? v) async {
    settings.hasPDVOrdersSentThisMonth = v;
    await save();
    update();
  }

  Future<void> setAverageDiscount(bool? v) async {
    settings.hasAverageDiscount = v;
    await save();
    update();
  }

  Future<void> save() async {
    await dbContext
        .withControllerAction(this)
        .addData(
        key: DatabaseModels.settingsAppModel,
        data: settings,
        workspaceId: appController.workspace?.workspaceId,
        clearCurrentData: true);
  }

  bool hasConfiguration() {
    if (settings.hasAverageDiscount == null) {
      settings = SettingsAppModel(
          hasAverageDiscount: false,
          hasPDVOrdersRequestedThisMonth: false,
          hasPDVOrdersSentThisMonth: false,
          hasSentOrdersThisMonth: false,
          hasTotalInvoicedThisMonth: false,
          hasTotalRequestedThisMonth: false);
    }
    return settings.hasPDVOrdersRequestedThisMonth! ||
        settings.hasPDVOrdersSentThisMonth! ||
        settings.hasSentOrdersThisMonth! ||
        settings.hasTotalInvoicedThisMonth! ||
        settings.hasTotalRequestedThisMonth!;
  }
}
