package br.com.interplayers.pharmalink

import android.app.ActivityManager
import android.content.Context
import android.os.Process
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
    private val MEMORY_CHANNEL = "memory_info"

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, MEMORY_CHANNEL).setMethodCallHandler { call, result ->
            if (call.method == "getMemoryUsage") {
                result.success(getAppMemoryUsage())
            } else {
                result.notImplemented()
            }
        }


    }

    private fun getAppMemoryUsage(): Long {
        val activityManager = getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val pid = Process.myPid()
        val pMemory = activityManager.getProcessMemoryInfo(intArrayOf(pid))
        return pMemory[0].totalPss * 1024L  // Convert to bytes
    }
}
