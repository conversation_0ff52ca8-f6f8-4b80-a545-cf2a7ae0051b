import java.util.Properties
import java.io.FileInputStream

plugins {
    id("com.android.application")
    id("kotlin-android")
    // The Flutter Gradle Plugin must be applied after the Android and Kotlin Gradle plugins.
    id("dev.flutter.flutter-gradle-plugin")
}

val keystoreProperties = Properties()
fun getKeystoreProperties(flavor: String): Properties? {
    val properties = Properties()
    val propertiesFile = rootProject.file("key.properties.$flavor")
    if (propertiesFile.exists()) {
        properties.load(FileInputStream(propertiesFile))
        return properties
    }
    return null
}

// ----- BEGIN flavorDimensions (autogenerated by flutter_flavorizr) -----
apply { from("flavorizr.gradle.kts") }
// ----- END flavorDimensions (autogenerated by flutter_flavorizr) -----

android {

    namespace = "br.com.interplayers.pharmalink"
    compileSdk = flutter.compileSdkVersion
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_11.toString()
        freeCompilerArgs += listOf(
            "-Xopt-in=kotlin.ExperimentalStdlibApi",
            "-Xjvm-default=all"
        )
        apiVersion = "1.9"
        languageVersion = "1.9"
    }

    sourceSets {
        getByName("main").java.srcDirs("src/main/kotlin")
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId = "br.com.interplayers.pharmalink"
        // You can update the following values to match your application needs.
        // For more information, see: https://docs.flutter.dev/deployment/android#reviewing-the-gradle-build-configuration.
        minSdk = 21
        targetSdk = flutter.targetSdkVersion
        versionCode = flutter.versionCode.toInt()
        versionName = flutter.versionName
        multiDexEnabled = true
        ndk {
            abiFilters.addAll(listOf("armeabi-v7a", "arm64-v8a", "x86", "x86_64"))
        }
        manifestPlaceholders["appAuthRedirectScheme"] = "com.davetest.wow"
    }

    signingConfigs {
        val itradeProps = getKeystoreProperties("itrade")
        if (itradeProps != null) {
            create("itrade") {
                keyAlias = itradeProps.getProperty("keyAlias")
                keyPassword = itradeProps.getProperty("keyPassword")
                storeFile = itradeProps.getProperty("storeFile")?.let { file(it) }
                storePassword = itradeProps.getProperty("storePassword")
            }
        }
        val pharmalinkProps = getKeystoreProperties("pharmalink")
        if (pharmalinkProps != null) {
            create("pharmalink") {
                keyAlias = pharmalinkProps.getProperty("keyAlias")
                keyPassword = pharmalinkProps.getProperty("keyPassword")
                storeFile = pharmalinkProps.getProperty("storeFile")?.let { file(it) }
                storePassword = pharmalinkProps.getProperty("storePassword")
            }
        }
    }

    buildTypes {
        release {
            signingConfigs.findByName("itrade")?.let {
                productFlavors.getByName("itrade").signingConfig = it
            }
            signingConfigs.findByName("pharmalink")?.let {
                productFlavors.getByName("pharmalink").signingConfig = it
            }
        }
        debug {
            applicationIdSuffix = ".debug"
            signingConfig = signingConfigs.getByName("debug")
        }
    }
}

flutter {
    source = "../.."
}
