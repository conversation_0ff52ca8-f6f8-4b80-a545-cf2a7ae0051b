import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/researches_share_of_shelf/models/researches_share_of_shelf_error_model.dart';
import 'package:pharmalink/modules/researches_share_of_shelf/models/researches_share_of_shelf_model.dart';
import 'package:pharmalink/modules/researches_share_of_shelf/models/researches_share_of_shelf_send_model.dart';

abstract class IResearchesShareOfShelfApi {
  Future<HttpResponse<List<ResearchesShareOfShelfModel>>>
      getResearchesShareOfShelf();

  Future<HttpResponse<String?>> sendShareOfShelf(
      {required ResearchesShareOfShelfSendModel model});
}

class ResearchesShareOfShelfApi extends IResearchesShareOfShelfApi {
  final HttpManager _httpManager;
  ResearchesShareOfShelfApi(this._httpManager);

  @override
  Future<HttpResponse<List<ResearchesShareOfShelfModel>>>
      getResearchesShareOfShelf() async {
    return await _httpManager.request<List<ResearchesShareOfShelfModel>>(
      path: 'pesquisa-share-of-shelf/listar/ciclos/ativos',
      method: HttpMethods.get,
      parser: (data) {
        if (data is List) {
          return data
              .map((item) => ResearchesShareOfShelfModel.fromJson(item))
              .toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }

  @override
  Future<HttpResponse<String?>> sendShareOfShelf(
      {required ResearchesShareOfShelfSendModel model}) async {
    return await _httpManager
        .requestFull<String?, ResearchesShareOfShelfErrorModel>(
      path: 'pesquisa-share-of-shelf/responderPesquisa',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        return data.toString();
      },
      parserError: (dynamic errorData) {
        return ResearchesShareOfShelfErrorModel.fromJson(errorData);
      },
    );
  }
}
