import 'package:dropdown_search/dropdown_search.dart';
import 'package:sqlite_explorer/exports/basic_exports.dart';
import 'package:sqlite_explorer/exports/get_exports.dart';
import 'package:sqlite_explorer/modules/home/<USER>/home_controller.dart';
import 'package:sqlite_explorer/modules/home/<USER>/table_key.dart';
import 'package:sqlite_explorer/widgets/widgets_exports.dart';

class HomeMenu extends StatelessWidget {
  const HomeMenu({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(builder: (ctrl) {
      return Column(
        children: [
          PrimaryButtonWidget(
            onTap: () async {
              await ctrl.selectZipFile();
            },
            titleButtom: "Importar SQL",
          ),
          const Gap(30),
          DropdownSearch<TableKey>(
            items: ctrl.tableKeys,
            popupProps: const PopupProps.menu(
              showSelectedItems: true,
              showSearchBox: true,
            ),
            dropdownDecoratorProps: const DropDownDecoratorProps(
              dropdownSearchDecoration: InputDecoration(
                labelText: "Selecionar Tabela:",
                border: OutlineInputBorder(),
              ),
            ),
            compareFn: (TableKey a, TableKey b) =>
                a.key == b.key, // Added compareFn
            onChanged: (TableKey? selectedKey) async {
              ctrl.selectedTableKey = selectedKey;
              ctrl.setSearchKey(selectedKey?.key);
            },
            selectedItem: ctrl.selectedTableKey,
            clearButtonProps: const ClearButtonProps(isVisible: false),
            dropdownBuilder: (context, selectedItem) {
              return Text(selectedItem?.name ?? "Selecionar Tabela");
            },
            itemAsString: (TableKey? item) => item?.name ?? '',
          ),
          const Gap(10),
          TextField(
            controller: ctrl.searchIdController,
            decoration: const InputDecoration(
              labelText: 'Search ID',
              border: OutlineInputBorder(),
            ),
          ),
          const Gap(10),
          TextField(
            controller: ctrl.workspaceIdController,
            decoration: const InputDecoration(
              labelText: 'Workspace ID',
              border: OutlineInputBorder(),
            ),
          ),
          const Gap(10),
          TextField(
            controller: ctrl.storeIdController,
            decoration: const InputDecoration(
              labelText: 'Store ID',
              border: OutlineInputBorder(),
            ),
          ),
          const Gap(10),
          TextField(
            controller: ctrl.startDateController,
            decoration: InputDecoration(
              labelText: 'Data e Hora Inicial',
              border: const OutlineInputBorder(),
              suffixIcon: IconButton(
                icon: const Icon(Icons.calendar_today),
                onPressed: () async {
                  DateTime? pickedDate = await showDatePicker(
                    context: context,
                    initialDate: ctrl.startDate ?? DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2101),
                  );
                  if (pickedDate != null) {
                    TimeOfDay? pickedTime = await showTimePicker(
                      context: Get.context!,
                      initialTime: ctrl.startDate != null
                          ? TimeOfDay.fromDateTime(ctrl.startDate!)
                          : TimeOfDay.now(),
                    );
                    if (pickedTime != null) {
                      DateTime pickedDateTime = DateTime(
                        pickedDate.year,
                        pickedDate.month,
                        pickedDate.day,
                        pickedTime.hour,
                        pickedTime.minute,
                      );
                      ctrl.startDateController.text =
                          pickedDateTime.formatDateTimeLocal();
                      ctrl.startDate = pickedDateTime;
                    }
                  }
                },
              ),
            ),
          ),
          const Gap(10),
          TextField(
            controller: ctrl.endDateController,
            decoration: InputDecoration(
              labelText: 'Data e Hora Final',
              border: const OutlineInputBorder(),
              suffixIcon: IconButton(
                icon: const Icon(Icons.calendar_today),
                onPressed: () async {
                  DateTime? pickedDate = await showDatePicker(
                    context: context,
                    initialDate: ctrl.endDate ?? DateTime.now(),
                    firstDate: DateTime(2000),
                    lastDate: DateTime(2101),
                  );
                  if (pickedDate != null) {
                    TimeOfDay? pickedTime = await showTimePicker(
                      context: Get.context!,
                      initialTime: ctrl.endDate != null
                          ? TimeOfDay.fromDateTime(ctrl.endDate!)
                          : TimeOfDay.now(),
                    );
                    if (pickedTime != null) {
                      DateTime pickedDateTime = DateTime(
                        pickedDate.year,
                        pickedDate.month,
                        pickedDate.day,
                        pickedTime.hour,
                        pickedTime.minute,
                      );
                      ctrl.endDateController.text =
                          pickedDateTime.formatDateTimeLocal();
                      ctrl.endDate = pickedDateTime;
                    }
                  }
                },
              ),
            ),
          ),
          const Gap(10),
          TextField(
            controller: ctrl.searchTypeController,
            decoration: const InputDecoration(
              labelText: 'Event Name',
              border: OutlineInputBorder(),
            ),
          ),
          const Gap(10),
          Row(
            // mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: PrimaryButtonWidget(
                  onTap: () async {
                    await ctrl.filterDataModelByParams();
                  },
                  titleButtom: "Filtrar",
                ),
              ),
              const Gap(5),
              IconButton(
                icon: const Icon(
                  FontAwesomeIcons.xmark,
                  size: 15,
                  color: Colors.red,
                ),
                onPressed: () {
                  ctrl.clearFilter();
                },
              ),
            ],
          ),
          const Gap(10),
        ],
      );
    });
  }
}
