import 'dart:developer';

import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';
import 'package:pharmalink/modules/visits/models/visits_by_routes_response_model.dart';

class NavigationPageController
    extends GetxControllerInstrumentado<NavigationPageController> {
  NavigationPageController();

  int selectedIndex = 0;
  void onTapSelectedIndex(int? v) {
    selectedIndex = v ?? 0;
    update();
  }

  final pages = <String>[RoutesPath.home, RoutesPath.home, RoutesPath.home];

  void changePage(int index) {
    selectedIndex = index;
    if (index == 1) {
      //forca a atualizacao das controllers
      prepareStoreRoutes();
      //  VisitsBinding(),
      //   ResearchesComplementaryBinding(),
      //   ResearchesShareOfShelfBinding(),
      //   ResearchesProductIndustryBinding(),
      //   ResearchesProductConcurrentBinding(),
      //   ResearchesMerchanIndustryBinding(),
      //   ResearchesTradeMarketingBinding(),
      //   ResearchesMerchanCompetitiveBinding(),

      Future.delayed(const Duration(milliseconds: 200), () async {
        if (!globalParams.hasStoreInitialize) {
          await storeRoutesController.verifyHasVisit();
        }
        await storeRoutesController.updateData();
        storeRoutesPanelController.updateData();
      });
    }
    update();
  }

  void prepareStoreRoutes() {
    storeRoutesController = getControllerWithReset<StoreRoutesController>(
      bindingsBuilder: () => StoreRoutesBinding(),
    );
    storeRoutesPanelController =
        getControllerWithReset<StoreRoutesPanelController>(
      bindingsBuilder: () => StoreRoutesPanelBinding(),
    );
    storeRoutesPlannedController =
        getControllerWithReset<StoreRoutesPlannedController>(
      bindingsBuilder: () => StoreRoutesPlannedBinding(),
    );
    //  VisitsBinding(),
    //   ResearchesComplementaryBinding(),
    //   ResearchesShareOfShelfBinding(),
    //   ResearchesProductIndustryBinding(),
    //   ResearchesProductConcurrentBinding(),
    //   ResearchesMerchanIndustryBinding(),
    //   ResearchesTradeMarketingBinding(),
    //   ResearchesMerchanCompetitiveBinding(),
  }

  T getControllerWithReset<T extends GetxController>(
      {required Bindings Function() bindingsBuilder}) {
    Get.delete<T>(force: true);
    bindingsBuilder().dependencies();
    return Get.find<T>();
  }

  Route? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case RoutesPath.home:
        return MaterialPageRoute(builder: (context) => const HomePage());
      // Adicione mais cases conforme necessário para outras rotas
      default:
        return null;
    }
  }

  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("didChangeAppLifecycleState");
    try {
      if (state == AppLifecycleState.paused) {
        log('NavigationPagePage: App em segundo plano');
      } else if (state == AppLifecycleState.resumed) {
        log('NavigationPagePage: App voltou ao primeiro plano');
        await appController.isValidToken(noMessages: true);
        if (appController.hasErrorRefreshToken == true) {
          SnackbarCustom.snackbarError(AppStrings.tokenExpired);
          Get.offAndToNamed(RoutesPath.login);
          return;
        }
        await VisitsByRoutesResponseModel().removeOldVisits();
        await StoresModel().resetPlanned();
        if (Get.isRegistered<StoreRoutesController>()) {
          await storeRoutesController.resetAllControllers();
        }
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }
}
