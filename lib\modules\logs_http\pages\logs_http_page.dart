import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class LogsHttpPage extends StatelessWidget {
  const LogsHttpPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<LogsHttpController>("LogsHttpController",
        builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: LabelWidget(
            title: "Erros de comunicação",
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              GetC.close();
            },
          ),
        ),
        body: ListView.builder(
          itemCount: ctrl.dataList.length,
          itemBuilder: (context, index) {
            final item = ctrl.dataList[index];
            // Substitua isso pelo seu próprio widget que exibe um item
            return ExpansionTile(
              title: Text(
                  '${item.method} - Data: ${item.createdAt!.formatDate(formatType: DateFormatType.ddMMyyyyHHmmss, applyTimezone: false)}'),
              subtitle: item.isFlutterError != true
                  ? Text('Status Code: ${item.statusCode}')
                  : const Text('Erro interno do Flutter'),
              children: <Widget>[
                ListTile(
                  title: const Text('Workspace'),
                  subtitle: Text(item.workspaceName ?? ""),
                ),
                ListTile(
                  title: const Text('Usuário'),
                  subtitle: Text(item.userName ?? ""),
                ),
                if (item.isFlutterError != true)
                  ListTile(
                    title: const Text('URL'),
                    subtitle: Text(item.url ?? ""),
                  ),
                if (item.isFlutterError != true)
                  ListTile(
                    title: const Text('Headers'),
                    subtitle: Text(item.headers ?? ""),
                  ),
                ListTile(
                  title: item.isFlutterError != true
                      ? const Text('Request')
                      : const Text('Stack Trace'),
                  subtitle: Text(item.request ?? ""),
                ),
                ListTile(
                  title: item.isFlutterError != true
                      ? const Text('Response')
                      : const Text('Exception'),
                  subtitle: Text(item.response ?? ""),
                ),
                const Divider(thickness: 2),
                PrimaryButtonWidget(
                  width: 150,
                  titleButtom: 'Copiar Log',
                  onTap: () async {
                    Clipboard.setData(ClipboardData(
                      text: jsonEncode(item),
                    ));
                    SnackbarCustom.snackbarSucess("Log Copiado",
                        "As informações foram copiadas para a área de transferência.");
                  },
                ),
                const Divider(thickness: 2),
              ],
            );
          },
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
