import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/store_routes_planned/pages/widgets/store_routes_planned_card_widget.dart';

class StoreRoutesPlannedPage extends StatelessWidget {
  const StoreRoutesPlannedPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StoreRoutesPlannedController>(builder: (ctrl) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          ctrl.storesList.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      LabelWidget(
                        title: 'Nenhum PDV no\nplanejamento!',
                        fontSize: DeviceSize.fontSize(18, 22),
                        textColor: Colors.grey.shade700,
                        fontWeight: FontWeight.bold,
                        textAlign: TextAlign.center,
                      ),
                      const Gap(6),
                      LabelWidget(
                        title:
                            'Atualize o seu planejamento no\nbotão *Atualizar* ou selecione as\nlojas desejadas para o\nplanejamento na aba ao lado\n*Meu Painel*',
                        fontSize: DeviceSize.fontSize(16, 20),
                        textColor: Colors.grey.shade700,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                )
              : Expanded(
                  child: ReorderableListView.builder(
                    shrinkWrap: true,
                    itemCount: ctrl.storesList.length,
                    itemBuilder: (context, index) {
                      final item = ctrl.storesList[index];
                      return Opacity(
                        key: ValueKey(item.idLoja),
                        opacity: ctrl.isReorderEnabled ? 0.8 : 1.0,
                        child: StpreRoutesPlannedCardWidget(data: item),
                      );
                    },
                    onReorder: (oldIndex, newIndex) {
                      if (ctrl.isReorderEnabled) {
                        ctrl.reorderList(oldIndex, newIndex);
                      }
                    },
                  ),
                ),
        ],
      );
    });
  }
}
