import 'package:sqlite_explorer/app/app_exports.dart';
import 'package:sqlite_explorer/config/databases/ilocal_db.dart';
import 'package:sqlite_explorer/exports/get_exports.dart';
import 'package:sqlite_explorer/modules/app_modules.dart';

final dbContext = Get.find<ILocalDb>();
final storage = Get.find<StorageService>();
final appController = Get.find<AppController>();
HomeController homeController = Get.find<HomeController>();

const databaseDefault = 'sqlite_explorer.db';
