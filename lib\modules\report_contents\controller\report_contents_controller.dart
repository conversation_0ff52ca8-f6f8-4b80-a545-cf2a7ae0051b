import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/modules/report_contents/models/report_contents_model.dart';
import 'package:share_plus/share_plus.dart';

class ReportContentsController
    extends GetxControllerInstrumentado<ReportContentsController> {
  ReportContentsController();

  ReportContentsModel? selected;
  List<ReportContentsModel> dataList = [];

  @override
  Future<void> onReady() async {
    super.onReady();
    await loadData();
  }

  void setDataList(List<ReportContentsModel> data) {
    dataList = data;
    update();
  }

  Future<void> loadData() async {
    final contentBox = await ReportContentsModel().getList();
    if (contentBox.isNotEmpty) {
      setDataList(contentBox);

      return;
    }
  }

  Future<void> getData({bool? forceUpdate}) async {
    final result = await reportContentsApi.getReportContents();
    if (result.error != null) {
      SnackbarCustom.snackbarError(result.error!.message!);
    } else {
      setDataList(result.data!);
      await dbContext.withControllerAction(this).addData(
          key: DatabaseModels.contentModel,
          data: result.data!,
          workspaceId: appController.workspace!.workspaceId,
          clearCurrentData: true);
    }
  }

  Future<void> download(String link, String fileName) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport("download");
    var pkLoading = PlkLoading();
    try {
      pkLoading.show();
      final dio = Dio();
      final dir = await getApplicationDocumentsDirectory();
      final file = File('${dir.path}/$fileName');

      await dio.download(link, file.path, onReceiveProgress: (received, total) {
        if (total != -1) {
          log("${(received / total * 100).toStringAsFixed(0)}%");
        }
      });
      pkLoading.hide();
      await Share.shareXFiles([XFile(file.path)]);
    } catch (e, s) {
      pkLoading.hide();
      subAction.reportZoneStacktrace(e, s);
      SnackbarCustom.snackbarError('Erro ao baixar o arquivo: $link');
      rethrow;
    } finally {
      leaveAction();
    }
  }
}
