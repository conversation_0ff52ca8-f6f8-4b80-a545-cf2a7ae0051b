class PDV {
  String? endereco;
  String? numero;
  String? complemento;
  String? bairro;
  String? cidade;
  String? uf;
  String? cep;
  String? cnpj;
  String? razaoSocial;
  String? nomeFantasia;
  String? codigoPdvCanal;
  String? descricaoPdvCanal;
  String? telefone;

  PDV(
      {this.endereco,
      this.numero,
      this.complemento,
      this.bairro,
      this.cidade,
      this.uf,
      this.cep,
      this.cnpj,
      this.razaoSocial,
      this.nomeFantasia,
      this.codigoPdvCanal,
      this.descricaoPdvCanal,
      this.telefone});

  PDV.fromJson(Map<String, dynamic> json) {
    endereco = json['Endereco'];
    numero = json['Numero'];
    complemento = json['Complemento'];
    bairro = json['Bairro'];
    cidade = json['Cidade'];
    uf = json['Uf'];
    cep = json['Cep'];
    cnpj = json['Cnpj'];
    razaoSocial = json['RazaoSocial'];
    nomeFantasia = json['NomeFantasia'];
    codigoPdvCanal = json['CodigoPdvCanal'];
    descricaoPdvCanal = json['DescricaoPdvCanal'];
    telefone = json['Telefone'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Endereco'] = endereco;
    data['Numero'] = numero;
    data['Complemento'] = complemento;
    data['Bairro'] = bairro;
    data['Cidade'] = cidade;
    data['Uf'] = uf;
    data['Cep'] = cep;
    data['Cnpj'] = cnpj;
    data['RazaoSocial'] = razaoSocial;
    data['NomeFantasia'] = nomeFantasia;
    data['CodigoPdvCanal'] = codigoPdvCanal;
    data['DescricaoPdvCanal'] = descricaoPdvCanal;
    data['Telefone'] = telefone;
    return data;
  }
}
