import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class ResearchesShareOfShelfQtdyWidget extends StatelessWidget {
  const ResearchesShareOfShelfQtdyWidget({
    super.key,
    required this.title,
    this.textColor,
    this.fontWeight,
    this.fontSize,
    this.padding,
    this.overflow,
    this.maxLines,
    this.textAlign,
    required this.value,
    this.crossAxisAlignment,
    required this.controller,
    required this.onPressedUp,
    required this.onPressedDown,
    required this.onSubmitted,
  });
  final String title;
  final String value;
  final Color? textColor;
  final FontWeight? fontWeight;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;
  final TextOverflow? overflow;
  final int? maxLines;
  final TextAlign? textAlign;
  final CrossAxisAlignment? crossAxisAlignment;
  final VoidCallback onPressedUp;
  final VoidCallback onPressedDown;
  final ValueChanged<String> onSubmitted;
  final TextEditingController controller;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: crossAxisAlignment ?? CrossAxisAlignment.start,
      children: [
        Padding(
          padding: padding ?? const EdgeInsets.all(0),
          child: Text(
            title,
            maxLines: maxLines,
            textAlign: textAlign,
            style: TextStyle(
              color: textColor,
              fontWeight: fontWeight ?? FontWeight.bold,
              fontSize: fontSize ?? 14.sp,
              overflow: overflow,
            ),
          ),
        ),
        SizedBox(
          width: 100,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Flexible(
                child: IconButton(
                  onPressed: onPressedDown,
                  icon: const Icon(
                    FontAwesomeIcons.minus,
                    size: 18,
                  ),
                ),
              ),
              Flexible(
                child: Center(
                  child: TextField(
                    //focusNode: data.focusNode!,
                    keyboardType: TextInputType.number,
                    controller: controller,
                    style: TextStyle(fontSize: DeviceSize.fontSize(18, 20)),
                    textAlign: TextAlign.center,

                    decoration: const InputDecoration(
                      border: InputBorder.none,
                    ),
                    onSubmitted: onSubmitted,
                  ),
                ),
              ),
              Flexible(
                child: IconButton(
                  onPressed: onPressedUp,
                  icon: const Icon(
                    FontAwesomeIcons.plus,
                    size: 18,
                  ),
                ),
              )
            ],
          ),
        )
      ],
    );
  }
}
