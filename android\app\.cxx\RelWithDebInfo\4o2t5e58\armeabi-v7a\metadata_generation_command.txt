                        -HC:\Users\<USER>\fvm\versions\3.29.2\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=21
-DAN<PERSON>OID_PLATFORM=android-21
-D<PERSON>DROID_ABI=armeabi-v7a
-DCMAKE_ANDROID_ARCH_ABI=armeabi-v7a
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973
-DCMAKE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=G:\ProgramasDart\MarketPlace-MobilePLK\build\app\intermediates\cxx\RelWithDebInfo\4o2t5e58\obj\armeabi-v7a
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=G:\ProgramasDart\MarketPlace-MobilePLK\build\app\intermediates\cxx\RelWithDebInfo\4o2t5e58\obj\armeabi-v7a
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BG:\ProgramasDart\MarketPlace-MobilePLK\android\app\.cxx\RelWithDebInfo\4o2t5e58\armeabi-v7a
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2