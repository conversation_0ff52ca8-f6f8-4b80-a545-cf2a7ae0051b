class ResearchesProductIndustrySaveSync {
  int? idRota;
  int? idParametrizacaoPesquisa;
  bool? enviado;
  String? tipoPesquisa;
  List<ResearchesProductIndustrySaveProdutos>? produtos;

  ResearchesProductIndustrySaveSync({
    this.idRota,
    this.idParametrizacaoPesquisa,
    this.enviado,
    this.tipoPesquisa,
    this.produtos,
  });

  ResearchesProductIndustrySaveSync.fromJson(Map<String, dynamic> json) {
    idRota = json['IdRota'];
    idParametrizacaoPesquisa = json['IdParametrizacaoPesquisa'];
    enviado = json['Enviado'];
    tipoPesquisa = json['tipoPesquisa'];
    if (json['Produtos'] != null) {
      produtos = <ResearchesProductIndustrySaveProdutos>[];
      json['Produtos'].forEach((v) {
        produtos!.add(ResearchesProductIndustrySaveProdutos.fromJson(v));
      });
    }
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdRota'] = idRota;
    data['IdParametrizacaoPesquisa'] = idParametrizacaoPesquisa;
    data['Enviado'] = enviado;
    data['tipoPesquisa'] = tipoPesquisa;
    if (produtos != null) {
      data['Produtos'] = produtos!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResearchesProductIndustrySaveProdutos {
  int? idPesquisaProduto;
  int? idProduto;
  String? apresentacao;
  String? descricao;
  String? foto;
  String? caminhoFoto;
  int? idPesquisaProdutoDistribuicaoNumericaSelecionado;
  int? idPesquisaPosicionamentoSelecionado;
  double? facing;
  double? preco;
  double? desconto;
  int? estoqueSistema;
  int? estoqueFisico;
  bool? informado;
  bool? enviadoRuptura;
  ResearchesProductIndustrySaveProdutos({
    this.idPesquisaProduto,
    this.idProduto,
    this.apresentacao,
    this.descricao,
    this.foto,
    this.caminhoFoto,
    this.idPesquisaProdutoDistribuicaoNumericaSelecionado,
    this.idPesquisaPosicionamentoSelecionado,
    this.facing,
    this.preco,
    this.desconto,
    this.estoqueSistema,
    this.estoqueFisico,
    this.informado,
    this.enviadoRuptura,
  });

  ResearchesProductIndustrySaveProdutos.fromJson(Map<String, dynamic> json) {
    idPesquisaProduto = json['IdPesquisaProduto'];
    idProduto = json['IdProduto'];
    apresentacao = json['Apresentacao'];
    descricao = json['Descricao'];
    foto = json['Foto'];
    caminhoFoto = json['CaminhoFoto'];
    idPesquisaProdutoDistribuicaoNumericaSelecionado =
        json['IdPesquisaProdutoDistribuicaoNumericaSelecionado'];
    idPesquisaPosicionamentoSelecionado =
        json['IdPesquisaPosicionamentoSelecionado'];
    facing = json['Facing'];
    preco = json['Preco'];
    desconto = json['Desconto'];
    estoqueSistema = json['EstoqueSistema'];
    estoqueFisico = json['EstoqueFisico'];
    informado = json['Informado'];
    enviadoRuptura = json['EnviadoRuptura'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPesquisaProduto'] = idPesquisaProduto;
    data['IdProduto'] = idProduto;
    data['Apresentacao'] = apresentacao;
    data['Descricao'] = descricao;
    data['Foto'] = foto;
    data['CaminhoFoto'] = caminhoFoto;
    data['IdPesquisaProdutoDistribuicaoNumericaSelecionado'] =
        idPesquisaProdutoDistribuicaoNumericaSelecionado;
    data['IdPesquisaPosicionamentoSelecionado'] =
        idPesquisaPosicionamentoSelecionado;
    data['Facing'] = facing;
    data['Preco'] = preco;
    data['Desconto'] = desconto;
    data['EstoqueSistema'] = estoqueSistema;
    data['EstoqueFisico'] = estoqueFisico;
    data['Informado'] = informado;
    data['EnviadoRuptura'] = enviadoRuptura;

    return data;
  }
}
