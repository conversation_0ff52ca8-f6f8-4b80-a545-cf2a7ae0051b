import 'dart:collection';
import 'dart:developer';

import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/core/utils/dynatrace/dynatrace_custom_action.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/stores/enuns/visit_status_enum.dart';
import 'package:pharmalink/modules/stores/models/stores_data_extra_model.dart';

class StoresModel extends SqfLiteBase<StoresModel> {
  int? idLoja;
  int? idEndereco;
  int? idPdv;
  String? cNPJ;
  String? nomeFantasia;
  String? razaoSocial;
  String? estado;
  String? cEP;
  String? logradouro;
  String? bairro;
  String? numero;
  String? cidade;
  String? complemento;
  String? bandeiraNomeFantasia;
  String? bandeiraRazaoSocial;
  String? setor;
  String? telefone;
  String? telefone2;
  String? fax;
  String? celular;
  String? email;
  bool? visita;
  bool? pedido;
  int? metaDeVisita;
  int? visitasRealizadas;
  String? enderecoPdv;
  String? dataUltimoPedido;

  StoresDataExtraModel? dataExtra;

  StoresModel({
    this.idLoja,
    this.idEndereco,
    this.idPdv,
    this.cNPJ,
    this.nomeFantasia,
    this.razaoSocial,
    this.estado,
    this.cEP,
    this.logradouro,
    this.bairro,
    this.numero,
    this.cidade,
    this.complemento,
    this.bandeiraNomeFantasia,
    this.bandeiraRazaoSocial,
    this.setor,
    this.telefone,
    this.telefone2,
    this.fax,
    this.celular,
    this.email,
    this.visita,
    this.pedido,
    this.metaDeVisita,
    this.visitasRealizadas,
    this.enderecoPdv,
    this.dataUltimoPedido,
    this.dataExtra,
  }) : super(DatabaseModels.storesModel);

  StoresModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.storesModel) {
    idLoja = json['IdLoja'];
    idEndereco = json['IdEndereco'];
    idPdv = json['IdPdv'];
    cNPJ = json['CNPJ'];
    nomeFantasia = json['NomeFantasia'];
    razaoSocial = json['RazaoSocial'];
    estado = json['Estado'];
    cEP = json['CEP'];
    logradouro = json['Logradouro'];
    bairro = json['Bairro'];
    numero = json['Numero'];
    cidade = json['Cidade'];
    complemento = json['Complemento'];
    bandeiraNomeFantasia = json['BandeiraNomeFantasia'];
    bandeiraRazaoSocial = json['BandeiraRazaoSocial'];
    setor = json['Setor'];
    telefone = json['Telefone'];
    telefone2 = json['Telefone2'];
    fax = json['Fax'];
    celular = json['Celular'];
    email = json['Email'];
    visita = json['Visita'];
    pedido = json['Pedido'];
    metaDeVisita = json['MetaDeVisita'];
    visitasRealizadas = json['VisitasRealizadas'];
    enderecoPdv = json['EnderecoPdv'];
    dataUltimoPedido = json['DataUltimoPedido'];
    dataExtra = json['DataExtra'] != null
        ? StoresDataExtraModel.fromJson(json['DataExtra'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdLoja'] = idLoja;
    data['IdEndereco'] = idEndereco;
    data['IdPdv'] = idPdv;
    data['CNPJ'] = cNPJ;
    data['NomeFantasia'] = nomeFantasia;
    data['RazaoSocial'] = razaoSocial;
    data['Estado'] = estado;
    data['CEP'] = cEP;
    data['Logradouro'] = logradouro;
    data['Bairro'] = bairro;
    data['Numero'] = numero;
    data['Cidade'] = cidade;
    data['Complemento'] = complemento;
    data['BandeiraNomeFantasia'] = bandeiraNomeFantasia;
    data['BandeiraRazaoSocial'] = bandeiraRazaoSocial;
    data['Setor'] = setor;
    data['Telefone'] = telefone;
    data['Telefone2'] = telefone2;
    data['Fax'] = fax;
    data['Celular'] = celular;
    data['Email'] = email;
    data['Visita'] = visita;
    data['Pedido'] = pedido;
    data['MetaDeVisita'] = metaDeVisita;
    data['VisitasRealizadas'] = visitasRealizadas;
    data['EnderecoPdv'] = enderecoPdv;
    data['DataUltimoPedido'] = dataUltimoPedido;
    data['DataExtra'] = dataExtra?.toJson();
    return data;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! StoresModel) return false;
    return idLoja == other.idLoja;
  }

  @override
  int get hashCode => idLoja.hashCode;

  StoresModel clone() {
    return StoresModel(
      idLoja: idLoja,
      idEndereco: idEndereco,
      idPdv: idPdv,
      cNPJ: cNPJ,
      nomeFantasia: nomeFantasia,
      razaoSocial: razaoSocial,
      estado: estado,
      cEP: cEP,
      logradouro: logradouro,
      bairro: bairro,
      numero: numero,
      cidade: cidade,
      complemento: complemento,
      bandeiraNomeFantasia: bandeiraNomeFantasia,
      bandeiraRazaoSocial: bandeiraRazaoSocial,
      setor: setor,
      telefone: telefone,
      telefone2: telefone2,
      fax: fax,
      celular: celular,
      email: email,
      visita: visita,
      pedido: pedido,
      metaDeVisita: metaDeVisita,
      visitasRealizadas: visitasRealizadas,
      dataUltimoPedido: dataUltimoPedido,
      enderecoPdv: enderecoPdv,
    );
  }

  Future<StoresModel> getFirst({required int workspaceId}) async {
    var list = await getAll<StoresModel>(
        userId: appController.userLogged?.userId!,
        workspaceId: workspaceId,
        StoresModel.fromJson);
    return list.first;
  }

  Future<StoresModel?> getStoreSelected() async {
    try {
      var list = await getAll<StoresModel>(
          key: DatabaseModels.storeSelected,
          userId: appController.userLogged?.userId!,
          workspaceId: appController.workspace!.workspaceId!,
          StoresModel.fromJson);
      return list.isEmpty ? null : list.first;
    } catch (e) {
      return null;
    }
  }

  Future<bool> exists({required int workspaceId}) async {
    var list = await getAll<StoresModel>(
        userId: appController.userLogged?.userId!,
        workspaceId: workspaceId,
        StoresModel.fromJson);
    return list.isNotEmpty;
  }

  Future<List<StoresModel>> getList({String? hashCode}) async {
    var list = await getAll<StoresModel>(
        userId: appController.userLogged?.userId!,
        workspaceId: appController.workspace?.workspaceId!,
        hashCode: hashCode,
        StoresModel.fromJson);
    return list;
  }

  Future<List<StoresModel>> getListTake() async {
    var list = await getAllByKey<StoresModel>(
        DatabaseModels.storesTakeModel,
        appController.workspace!.workspaceId!,
        null,
        appController.userLogged?.userId!,
        null,
        StoresModel.fromJson);
    list = removeDuplicates(list);
    var idLojaToStoreMap =
        Map.fromEntries(list.map((store) => MapEntry(store.idLoja, store)));

    var uniqueList = idLojaToStoreMap.values.toList();

    return uniqueList;
  }

  List<StoresModel> removeDuplicates(List<StoresModel> list) {
    return LinkedHashSet<StoresModel>.from(list).toList();
  }

  Future<List<StoresModel>> getListTakeSync() async {
    var list = await getAllByKey<StoresModel>(
        DatabaseModels.storesTakeModel,
        appController.workspace!.workspaceId!,
        null,
        appController.userLogged?.userId!,
        null,
        StoresModel.fromJson);

    list = removeDuplicates(list);
    var idLojaToStoreMap =
        Map.fromEntries(list.map((store) => MapEntry(store.idLoja, store)));

    var uniqueList = idLojaToStoreMap.values.toList();

    return uniqueList;
  }

  Future<void> resetSyncOffline(DynatraceCustomAction action) async {
    var list = await getAll<StoresModel>(
        userId: appController.userLogged?.userId!,
        workspaceId: appController.workspace!.workspaceId!,
        hashCode: "planned",
        StoresModel.fromJson);

    for (var item in list) {
      item.dataExtra?.offlineDateSync = null;
      item.dataExtra?.offlineId = null;
      item.dataExtra?.offlineIsSync = false;
      item.dataExtra?.offlineProgress = 0;
      item.dataExtra?.offlineStatus = null;
      item.dataExtra?.selected = false;
      item.dataExtra?.isTake = false;
      item.dataExtra?.statusVisit = StatusVisitEnum.notInitilized;
      item.dataExtra?.roteiroId = null;
      item.dataExtra?.routeId = null;
      await item.dataExtra?.updateDataExtra(item.dataExtra!);
    }
    await updateList(list, action);
  }

  Future<void> resetSelecteds(DynatraceCustomAction action) async {
    var list = await getList();
    if (list.isNotEmpty) {
      list.map((e) async {
        if (e.dataExtra?.isTake == true) {
          e.dataExtra?.isTake = false;
          e.dataExtra?.currentDate = null;
          await e.dataExtra?.updateDataExtra(e.dataExtra!);
        }
      }).toList();
      await updateList(list, action);
    }
  }

  Future<void> setTakeList(
      List<StoresModel> listTake, DynatraceCustomAction action) async {
    var list = await getList();
    if (list.isNotEmpty) {
      list.map((e) async {
        if (listTake.any((element) => element.idLoja == e.idLoja)) {
          final take = listTake
              .firstWhereOrNull((element) => element.idLoja == e.idLoja);
          if (take != null) {
            e.dataExtra?.isTake = take.dataExtra?.isTake;
            e.dataExtra?.currentDate = take.dataExtra?.currentDate;
            e.dataExtra?.selected = take.dataExtra?.selected;
            await e.dataExtra?.updateDataExtra(e.dataExtra!);
          }
        }
      }).toList();
      list = removeDuplicates(list);
      await updateList(list, action);
    }
  }

  Future<void> updateList(
      List<StoresModel> list, DynatraceCustomAction action) async {
    list = removeDuplicates(list);

    for (var store in list) {
      await dbContext.withAction(action).addData(
            key: DatabaseModels.storesModel,
            data: store,
            storeId: store.idLoja,
            userId: appController.userLogged?.userId!,
            workspaceId: appController.workspace!.workspaceId,
            clearCurrentData: true,
          );
    }
  }

  String getOfflineStatusDescription() {
    if (dataExtra?.offlineStatus == null) {
      return '-';
    }
    switch (dataExtra?.offlineStatus) {
      case 1:
        return 'Aguardando sincronização';
      case 2:
        return 'Sincronizando';
      case 3:
        return 'Sinconização finalizada';
      default:
        return 'Erro ao sincronizar';
    }
  }

  Future<void> removeOffline() async {
    dataExtra?.offlineIsSync = null;
    dataExtra?.offlineDateSync = null;
    dataExtra?.offlineSyncStatus = null;
    dataExtra?.offlineProgress = null;
    dataExtra?.offlineId = null;
    dataExtra?.offlineStatus = null;
    dataExtra?.offlineTotalPages = null;
    dataExtra?.offlineCurrentPages = null;
    dataExtra?.offlineMessage = null;
    dataExtra?.canOrderOfflineSync = false;
    await dataExtra?.updateDataExtra(dataExtra!);
  }

  Future<void> removeRoutes() async {
    dataExtra?.offlineIsSync = null;
    dataExtra?.offlineDateSync = null;
    dataExtra?.offlineSyncStatus = null;
    dataExtra?.offlineProgress = null;
    dataExtra?.offlineId = null;
    dataExtra?.offlineStatus = null;
    dataExtra?.offlineTotalPages = null;
    dataExtra?.offlineCurrentPages = null;
    dataExtra?.offlineMessage = null;
    dataExtra?.canOrderOfflineSync = false;
    dataExtra?.canVisitaSync = false;
    dataExtra?.canPlanned = false;
    dataExtra?.hasRouteSync = false;
    dataExtra?.currentDate = null;
    await dataExtra?.updateDataExtra(dataExtra!);
  }

  Future<void> resetPlanned() async {
    DateTime now = DateTime.now();
    DateTime targetDate = DateTime(now.year, now.month, now.day);
    try {
      List<StoresModel> list = await getList();
      //list.addAll(await getList(hashCode: "planned"));
      if (list.isNotEmpty) {
        final filteredList = list.where((element) {
          if (element.dataExtra?.canPlanned == false) return false;
          if (element.dataExtra?.currentDate == null) return true;
          DateTime elementDate = DateTime(
              element.dataExtra!.currentDate!.year,
              element.dataExtra!.currentDate!.month,
              element.dataExtra!.currentDate!.day);
          return elementDate.isBefore(targetDate);
        }).toList();

        if (filteredList.isNotEmpty) {
          for (var store in filteredList) {
            store.dataExtra?.canPlanned = false;
            store.dataExtra?.canVisitaSync = false;
            store.dataExtra?.canOrderOfflineSync = false;
            store.dataExtra?.currentDate = null;
            store.dataExtra?.hasRouteSync = false;
            store.dataExtra?.orderLocal = null;
            store.dataExtra?.roteiroId = null;
            store.dataExtra?.isVisita = false;
            store.dataExtra?.isVisitaCanEdit = false;
            store.dataExtra?.isVisitaSync = false;
            store.dataExtra?.statusVisit = StatusVisitEnum.notInitilized;
            store.dataExtra?.routeId = null;
            store.dataExtra?.offlineStatus = 0;
            store.dataExtra?.offlineDateSync = null;
            store.dataExtra?.offlineId = null;
            store.dataExtra?.offlineIsSync = false;
            store.dataExtra?.offlineProgress = 0;
            store.dataExtra?.offlineMessage = null;
            store.dataExtra?.offlineTotalPages = null;
            store.dataExtra?.offlineCurrentPages = null;
            await dbContext.addData(
              key: DatabaseModels.storesModel,
              data: store,
              storeId: store.idLoja,
              userId: appController.userLogged?.userId!,
              workspaceId: appController.workspace!.workspaceId,
              hashCode: store.dataExtra?.canPlanned == true ? "planned" : null,
              clearCurrentData: true,
            );
            await store.dataExtra?.updateDataExtra(store.dataExtra!);
          }
        }
      }
    } catch (e) {
      log(e.toString());
    }
  }

  static Future<(bool, StoresModel)> checkAndUpdate(
      StoresModel currentStore, StoresModel newStore) async {
    bool hasChanges = false;

    final fieldsToCheck = [
      (
        currentStore.idEndereco,
        newStore.idEndereco,
        (value) => currentStore.idEndereco = value
      ),
      (
        currentStore.idPdv,
        newStore.idPdv,
        (value) => currentStore.idPdv = value
      ),
      (currentStore.cNPJ, newStore.cNPJ, (value) => currentStore.cNPJ = value),
      (
        currentStore.nomeFantasia,
        newStore.nomeFantasia,
        (value) => currentStore.nomeFantasia = value
      ),
      (
        currentStore.razaoSocial,
        newStore.razaoSocial,
        (value) => currentStore.razaoSocial = value
      ),
      (
        currentStore.estado,
        newStore.estado,
        (value) => currentStore.estado = value
      ),
      (currentStore.cEP, newStore.cEP, (value) => currentStore.cEP = value),
      (
        currentStore.logradouro,
        newStore.logradouro,
        (value) => currentStore.logradouro = value
      ),
      (
        currentStore.bairro,
        newStore.bairro,
        (value) => currentStore.bairro = value
      ),
      (
        currentStore.numero,
        newStore.numero,
        (value) => currentStore.numero = value
      ),
      (
        currentStore.cidade,
        newStore.cidade,
        (value) => currentStore.cidade = value
      ),
      (
        currentStore.complemento,
        newStore.complemento,
        (value) => currentStore.complemento = value
      ),
      (
        currentStore.bandeiraNomeFantasia,
        newStore.bandeiraNomeFantasia,
        (value) => currentStore.bandeiraNomeFantasia = value
      ),
      (
        currentStore.bandeiraRazaoSocial,
        newStore.bandeiraRazaoSocial,
        (value) => currentStore.bandeiraRazaoSocial = value
      ),
      (
        currentStore.setor,
        newStore.setor,
        (value) => currentStore.setor = value
      ),
      (
        currentStore.telefone,
        newStore.telefone,
        (value) => currentStore.telefone = value
      ),
      (
        currentStore.telefone2,
        newStore.telefone2,
        (value) => currentStore.telefone2 = value
      ),
      (currentStore.fax, newStore.fax, (value) => currentStore.fax = value),
      (
        currentStore.celular,
        newStore.celular,
        (value) => currentStore.celular = value
      ),
      (
        currentStore.email,
        newStore.email,
        (value) => currentStore.email = value
      ),
      (
        currentStore.visita,
        newStore.visita,
        (value) => currentStore.visita = value
      ),
      (
        currentStore.pedido,
        newStore.pedido,
        (value) => currentStore.pedido = value
      ),
      (
        currentStore.metaDeVisita,
        newStore.metaDeVisita,
        (value) => currentStore.metaDeVisita = value
      ),
      (
        currentStore.visitasRealizadas,
        newStore.visitasRealizadas,
        (value) => currentStore.visitasRealizadas = value
      ),
      (
        currentStore.enderecoPdv,
        newStore.enderecoPdv,
        (value) => currentStore.enderecoPdv = value
      ),
      (
        currentStore.dataUltimoPedido,
        newStore.dataUltimoPedido,
        (value) => currentStore.dataUltimoPedido = value
      ),
    ];

    for (var (currentValue, newValue, setter) in fieldsToCheck) {
      if (currentValue != newValue) {
        setter(newValue);
        hasChanges = true;
      }
    }

    return (hasChanges, currentStore);
  }
}
