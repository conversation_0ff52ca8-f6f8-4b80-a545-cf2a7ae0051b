import 'package:flutter/services.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_model.dart';

class ResearchesComplementaryNumberWidget extends StatelessWidget {
  final ResearchesComplementaryPerguntas question;
  final bool isShown;
  const ResearchesComplementaryNumberWidget(
      {super.key, required this.question, this.isShown = true});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ResearchesComplementaryController>(builder: (ctx) {
      return Visibility(
        visible: question.isShown!,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              LabelWidget(
                title: question.titulo!,
                fontSize: DeviceSize.fontSize(20, 24),
                fontWeight: FontWeight.bold,
                textColor: themesController.getPrimaryColor(),
                textAlign: TextAlign.center,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: TextFormField(
                  enabled: ctx.selected!.isSync != SyncEnum.finished,
                  controller: TextEditingController(
                    text: ctx.getTextAnswer(question),
                  ),
                  style: TextStyle(
                    color: themesController.getPrimaryColor(),
                  ),
                  textAlign: TextAlign.start,
                  decoration: InputDecoration(
                    filled: true,
                    fillColor: Colors.white,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9,.]+')),
                  ],
                  onChanged: (value) {
                    ctx.setTextAnswer(value, question);
                  },
                ),
              ),
              if (question.hasDivisor!)
                Divider(
                  color: themesController.getPrimaryColor(),
                  height: 3,
                  thickness: BorderSide.strokeAlignCenter,
                ),
            ],
          ),
        ),
      );
    });
  }
}
