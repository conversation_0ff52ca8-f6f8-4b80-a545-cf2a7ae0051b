import 'dart:convert';
import 'dart:io';

import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/notification/models/notification_model.dart';
import 'package:pharmalink/modules/orders/models/order_attachment_file_request_model.dart';
import 'package:pharmalink/modules/orders_resume/enuns/status_syncronization_enum.dart';
import 'package:pharmalink/modules/orders_resume/models/file_attachment.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';
import 'package:pharmalink/modules/routes/models/list_routes_model.dart';
import 'package:pharmalink/modules/routes/models/pdvs_dia_model.dart';
import 'package:pharmalink/modules/routes/models/route_list_model.dart';
import 'package:pharmalink/modules/routes/models/routes_save_model.dart';
import 'package:pharmalink/modules/stores/enuns/visit_status_enum.dart';
import 'package:pharmalink/modules/synchronizations/models/synchronizations_model.dart';
import 'package:pharmalink/modules/synchronizations/pages/widgets/synchronizations_card_widget.dart';
import 'package:pharmalink/modules/visits/models/visits_by_routes_response_model.dart';

class SynchronizationsController
    extends GetxControllerInstrumentado<SynchronizationsController>
    with TraceableController {
  SynchronizationsController();
  bool showAllItens = true;
  bool syncStart = false;
  DateTime? lastSyncDate;

  String? orderId;

  void setSyncStart(bool? v) {
    syncStart = v ?? false;
    update();
  }

  void setSyncStartRoutes(bool? v) {
    syncStartRoutes = v ?? false;
    update();
  }

  void setSyncStartOrders(bool? v) {
    syncStartOrders = v ?? false;
    update();
  }

  void setSyncStartTheme(bool? v) {
    syncStartTheme = v ?? false;
    update();
  }

  void setSyncStartContents(bool? v) {
    syncStartContents = v ?? false;
    update();
  }

  void setSyncStartVisits(bool? v) {
    syncStartVisits = v ?? false;
    update();
  }

  void setSyncStartVisitsInfo(bool? v) {
    syncStartSalesInfo = v ?? false;
    update();
  }

  void setSyncStartSettings(bool? v) {
    syncStartSettings = v ?? false;
    update();
  }

  void setSyncStartResearches(bool? v) {
    syncStartResearches = v ?? false;
    update();
  }

  double progress = 0.0;
  void setProgress(double v) {
    progress += v;
    update();
  }

  void resetProgress() {
    progress = 0;
    update();
  }

  bool hasStores = false;
  bool hasRoutes = false;
  bool hasSettings = false;
  bool hasResearches = false;
  bool hasOrders = false;
  bool hasTheme = false;
  bool hasSalesInfo = false;
  bool hasVisits = false;
  bool hasContents = false;

  bool syncStartVisits = false;
  bool syncStartContents = false;
  bool syncStartRoutes = false;
  bool syncStartSettings = false;
  bool syncStartResearches = false;
  bool syncStartOrders = false;
  bool syncStartTheme = false;
  bool syncStartSalesInfo = false;

  @override
  Future<void> onReady() async {
    super.onReady();
    resetAll();
    if (appController.userLogged != null) {
      await getDateSync();
    }
  }

  void resetAll() {
    hasStores = false;
    hasRoutes = false;
    hasSettings = false;
    hasResearches = false;
    hasOrders = false;
    hasTheme = false;
    hasSalesInfo = false;
    hasVisits = false;
    hasContents = false;
    syncStart = false;
    syncStartRoutes = false;
    syncStartOrders = false;
    syncStartTheme = false;
    syncStartContents = false;
    syncStartVisits = false;
    syncStartSettings = false;
    syncStartResearches = false;
    syncStartSalesInfo = false;

    update();
  }

  void setHasStores(bool? v) {
    hasStores = v ?? false;
    update();
  }

  void setHasRoutes(bool? v) {
    hasRoutes = v ?? false;
    update();
  }

  void setHasSettings(bool? v) {
    hasSettings = v ?? false;
    update();
  }

  void setHasResearches(bool? v) {
    hasResearches = v ?? false;
    update();
  }

  void setHasOrders(bool? v) {
    hasOrders = v ?? false;
    update();
  }

  void setHasTheme(bool? v) {
    hasTheme = v ?? false;
    update();
  }

  void setHasSalesInfo(bool? v) {
    hasSalesInfo = v ?? false;
    update();
  }

  void setHasVisits(bool? v) {
    hasVisits = v ?? false;
    update();
  }

  void setHasContents(bool? v) {
    hasContents = v ?? false;
    update();
  }

  bool get hasItemSelected =>
      hasStores ||
      hasRoutes ||
      hasTheme ||
      hasContents ||
      hasOrders ||
      hasSalesInfo ||
      hasSettings ||
      hasResearches ||
      hasVisits;

  Future<void> startSyncronizations() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "startSyncronizations",
    );

    try {
      bool isConnected = await appController.checkConnectivity();
      if (!isConnected) {
        subAction.reportEvent(AppStrings.noInternet);
        SnackbarCustom.snackbarError(AppStrings.noInternet);
        return;
      }

      await appController.isValidToken(noMessages: true);
      if (appController.hasErrorRefreshToken == true) {
        subAction.reportEvent(AppStrings.tokenExpired);
        SnackbarCustom.snackbarError(AppStrings.tokenExpired);
        Get.offAndToNamed(RoutesPath.login);
        return;
      }

      if (!hasItemSelected) {
        subAction.reportEvent("Nenhum item selecionado");
        Dialogs.info(
          "Atenção",
          showAllItens
              ? "Selecione ao menos um item para sincronizar!"
              : "Habilite a Loja para poder seguir com a sincronização!",
        );

        return;
      }

      if (hasStores) {
        dynatraceAction.subActionReport("Sincronização - Lojas");

        subAction.reportValue("hasStores", "$hasStores");

        setSyncStart(true);
        await syncStores();
        setSyncStart(false);
        setHasStores(false);

        leaveAction();
      }

      if (hasRoutes) {
        dynatraceAction.subActionReport("Sincronização - Rotas");
        subAction.reportValue("hasRoutes", "$hasRoutes");
        setSyncStartRoutes(true);

        await syncRoutes();

        leaveAction();
      }

      if (hasOrders) {
        dynatraceAction.subActionReport("Sincronização - Pedidos");
        subAction.reportValue("hasOrders", "$hasOrders");

        setSyncStartOrders(true);
        await storeRoutesController
            .withControllerAction(this)
            .syncStoreData(forceUpdate: true, isUpdateList: true);
        // await verifiyMessages();
        await syncOrders();
        await Future.delayed(const Duration(seconds: 5));
        await syncOrdersRetrieve();
        await syncFileOrders();
        setSyncStartOrders(false);
        setHasOrders(false);
      }
      if (hasTheme) {
        dynatraceAction.subActionReport("Sincronização - Tema");
        subAction.reportValue("hasTheme", "$hasTheme");

        setSyncStartTheme(true);

        await syncThemes();
        setSyncStartTheme(false);
        setHasTheme(false);
      }

      if (hasContents) {
        dynatraceAction.subActionReport("Sincronização - Conteúdos");
        subAction.reportValue("hasContents", "$hasContents");

        setSyncStartContents(true);
        await syncContent();
        setSyncStartContents(false);
        setHasContents(false);
      }

      if (hasVisits) {
        dynatraceAction.subActionReport("Sincronização - Visitas");
        subAction.reportValue("hasVisits", "$hasVisits");

        setSyncStartVisits(true);
        await startSyncVisits();
        await startSyncResearches(hideMsg: true);
        await startSyncSalasInfo();
        setSyncStartVisits(false);
        setHasVisits(false);
      }

      if (hasSalesInfo) {
        dynatraceAction.subActionReport(
          "Sincronização - Informações de Visitas",
        );
        subAction.reportValue("hasSalesInfo", "$hasSalesInfo");

        setSyncStartVisitsInfo(true);
        await startSyncSalasInfo();
        setSyncStartVisitsInfo(false);
        setHasSalesInfo(false);
      }

      if (hasSettings) {
        dynatraceAction.subActionReport("Sincronização - Parametrizações");
        subAction.reportValue("hasSettings", "$hasSettings");

        setSyncStartSettings(true);
        await startSyncSettings();
        setSyncStartSettings(false);
        setHasSettings(false);
      }

      if (hasResearches) {
        dynatraceAction.subActionReport("Sincronização - Pesquisas");
        subAction.reportValue("hasResearches", "$hasResearches");

        setSyncStartResearches(true);
        await startSyncResearches();
        setSyncStartResearches(false);
        setHasResearches(false);
      }

      if (!showAllItens) {
        Get.offAllNamed(RoutesPath.navigationPage);
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      SnackbarCustom.snackbarError(e.toString());
      DBLogger.logger(
        'Erro na sincronização:',
        jsonData: jsonEncode({
          "error": e.toString(),
          "stacktrace": s.toString(),
        }),
      );
      resetAll();
      rethrow;
    } finally {
      leaveAction();
    }

    await setDateSync();
  }

  Widget getWidgetList(bool allItens, DateTime? lastSyncDate) {
    final List<Widget> widgets = [];
    showAllItens = allItens;
    if (showAllItens) {
      widgets.add(10.toHeightSpace());
      widgets.add(
        LabelWidget(
          title:
              "Última Sincronização: ${lastSyncDate?.formatDate(formatType: DateFormatType.ddMMyyyyHHmmss) ?? ""}",
        ),
      );
      widgets.add(10.toHeightSpace());
    }

    widgets.add(
      SynchronizationsCardWidget(
        title: "Lojas",
        iconSvg: AppImages.syncShop,
        switchValue: hasStores,
        onChanged: setHasStores,
        syncStart: syncStart,
        child: Column(
          children: [
            5.toHeightSpace(),
            LinearProgressIndicator(
              minHeight: 8,
              value: progress,
              color: themesController.getBackgroundColor(),
            ),
          ],
        ),
      ),
    );

    if (showAllItens) {
      widgets.add(
        SynchronizationsCardWidget(
          title: "Rotas",
          iconSvg: AppImages.syncRotas,
          switchValue: hasRoutes,
          syncStart: syncStartRoutes,
          onChanged: setHasRoutes,
          child: Column(
            children: [
              5.toHeightSpace(),
              LinearProgressIndicator(
                minHeight: 8,
                value: progress,
                color: themesController.getBackgroundColor(),
              ),
            ],
          ),
        ),
      );
      widgets.add(
        SynchronizationsCardWidget(
          title: "Pedidos",
          iconSvg: AppImages.syncPedido,
          switchValue: hasOrders,
          syncStart: syncStartOrders,
          onChanged: setHasOrders,
          child: Column(
            children: [
              5.toHeightSpace(),
              LinearProgressIndicator(
                minHeight: 8,
                value: progress,
                color: themesController.getBackgroundColor(),
              ),
            ],
          ),
        ),
      );
      widgets.add(
        SynchronizationsCardWidget(
          title: "Visitas",
          iconSvg: AppImages.syncVisitas,
          switchValue: hasVisits,
          syncStart: syncStartVisits,
          onChanged: setHasVisits,
          child: Column(
            children: [
              5.toHeightSpace(),
              LinearProgressIndicator(
                minHeight: 8,
                value: progress,
                color: themesController.getBackgroundColor(),
              ),
            ],
          ),
        ),
      );
      widgets.add(
        SynchronizationsCardWidget(
          title: "Pesquisas",
          iconSvg: AppImages.syncFactory,
          switchValue: hasResearches,
          syncStart: syncStartResearches,
          onChanged: setHasResearches,
          child: Column(
            children: [
              5.toHeightSpace(),
              LinearProgressIndicator(
                minHeight: 8,
                value: progress,
                color: themesController.getBackgroundColor(),
              ),
            ],
          ),
        ),
      );

      widgets.add(
        SynchronizationsCardWidget(
          title: "Informações de visita",
          iconSvg: AppImages.syncInfoVisit,
          switchValue: hasSalesInfo,
          syncStart: syncStartSalesInfo,
          onChanged: setHasSalesInfo,
          child: Column(
            children: [
              5.toHeightSpace(),
              LinearProgressIndicator(
                minHeight: 8,
                value: progress,
                color: themesController.getBackgroundColor(),
              ),
            ],
          ),
        ),
      );
      widgets.add(
        SynchronizationsCardWidget(
          title: "Tema",
          iconSvg: AppImages.syncTheme,
          switchValue: hasTheme,
          syncStart: syncStartTheme,
          onChanged: setHasTheme,
          child: Column(
            children: [
              5.toHeightSpace(),
              LinearProgressIndicator(
                minHeight: 8,
                value: progress,
                color: themesController.getBackgroundColor(),
              ),
            ],
          ),
        ),
      );
      widgets.add(
        SynchronizationsCardWidget(
          title: "Conteúdos",
          iconSvg: AppImages.syncConteudo,
          switchValue: hasContents,
          syncStart: syncStartContents,
          onChanged: setHasContents,
          child: Column(
            children: [
              5.toHeightSpace(),
              LinearProgressIndicator(
                minHeight: 8,
                value: progress,
                color: themesController.getBackgroundColor(),
              ),
            ],
          ),
        ),
      );
      widgets.add(100.toHeightSpace());
    }

    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widgets,
      ),
    );
  }

  Future<void> syncStores() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "Sincronização de Lojas",
    );
    return trace('syncStores', () async {
      try {
        // await storeParametersController.getData(forceUpdate: showAllItens);
        if (!showAllItens) await settingsAppController.init();
        //await storesController.getData(forceUpdate: showAllItens);

        await storeRoutesController
            .withControllerAction(this)
            .syncStoreData(forceUpdate: true, isUpdateList: showAllItens);
        await generalParameterizationController
            .withDynatraceAction(dynatraceAction)
            .getData(forceUpdate: true);
        await generalParameterizationController
            .withDynatraceAction(dynatraceAction)
            .getFilterCustom(forceUpdate: true);
        await generalParameterizationController
            .withDynatraceAction(dynatraceAction)
            .getSystemParameterization(forceUpdate: true);
        await generalParameterizationController
            .withDynatraceAction(dynatraceAction)
            .getGeneralSettingsOrderDiscountRegistration(forceUpdate: true);

        await generalParameterizationController.getParameterByKeyOffline(
          forceUpdate: true,
        );

        await dbContext.deleteByKey(
          key: DatabaseModels.storeParametersModel,
          workspaceId: appController.workspace!.workspaceId,
          userId: appController.userLogged?.userId!,
          isOnline: true,
          isLog: true,
        );

        await dbContext.deleteByKey(
          key: DatabaseModels.productsMixModel,
          workspaceId: appController.workspace!.workspaceId,
          isLog: true,
        );

        SnackbarCustom.snackbarSucess("Sincronização", AppStrings.syncStores);
      } catch (e, s) {
        subAction.reportZoneStacktrace(e, s);
        rethrow;
      } finally {
        leaveAction();
      }
    });
  }

  Future<void> syncRoutes() async {
    await VisitsByRoutesResponseModel().removeOldVisits();

    final storeNeedSync =
        storeRoutesPlannedController.storesList
            .where((x) => x.dataExtra!.sync == true)
            .map(
              (e) => PdvsDiaModel(
                idEndereco: e.idEndereco,
                idLoja: e.idLoja,
                idPdv: e.idPdv,
                idSetorVisita: null,
              ),
            )
            .toList();
    if (storeNeedSync.isNotEmpty) {
      final responseSave = await routesApi.saveRoutes(
        model: RoutesSaveModel(
          data: DateTime.now(),
          userId: appController.userLogged!.userId!,
          pdvsDia: storeNeedSync,
        ),
      );
      responseSave.when(
        sucess: (data) async {
          await storeRoutesSyncService.syncRoutesGetList(
            syncVisits: false,
            onNext: () {
              setSyncStartRoutes(false);
              setHasRoutes(false);
            },
          );
        },
        error: (error) {
          SnackbarCustom.snackbarError(error.error);
          setSyncStartRoutes(false);
          setHasRoutes(false);
        },
      );
    } else {
      await storeRoutesSyncService.syncRoutesGetList(
        syncVisits: false,
        onNext: () {
          setSyncStartRoutes(false);
          setHasRoutes(false);
        },
      );
    }
  }

  // Future<void> syncRoutesGetList1() async {
  //   final responseList = await routesApi.getRoutesList(
  //     model: RoutesListRequestModel(
  //       dataInicio: DateTime.now(),
  //       dataTermino: DateTime.now(),
  //       userId: appController.userLooged!.userId!,
  //     ),
  //   );
  //   responseList.when(sucess: (data) async {
  //     await dbContext.withControllerAction(this).addData(
  //         clearCurrentData: true,
  //         workspaceId: appController.workspace?.workspaceId,
  //         data: data,
  //         key: DatabaseModels.routersListModel);

  //     final storeIds = extractAllIdLoja(data);
  //     final storeListRouts = extractAllListaRota(data);
  //     await storesController.setListStores(storeIds, storeListRouts);
  //     SnackbarCustom.snackbarSucess(
  //         "Rotas", "Sincronização das Rotas realizada com sucesso!");
  //     setSyncStartRoutes(false);
  //     setHasRoutes(false);
  //   }, error: (error) {
  //     SnackbarCustom.snackbarError(error.error);
  //     setSyncStartRoutes(false);
  //     setHasRoutes(false);
  //   });
  // }

  Future<void> syncThemes() async {
    await themesController.getData(forceUpdate: true);
  }

  Future<void> syncContent() async {
    reportContentsController = Get.find<ReportContentsController>();
    await reportContentsController.getData(forceUpdate: true);
    if (reportContentsController.dataList == null ||
        reportContentsController.dataList.isEmpty) {
      SnackbarCustom.snackbarWarning(
        "Não foi encontrado nenhum conteúdo a ser sincronizado.",
      );
      return;
    }
    SnackbarCustom.snackbarSucess(
      "Conteúdos",
      "Sincronização dos Conteúdos realizado com sucesso!",
    );
  }

  List<int> extractAllIdLoja(List<ListRoutesModel> data) {
    List<int> idLojas = [];

    for (var routeModel in data) {
      if (routeModel.roteiro?.listaRota != null) {
        for (var listaRota in routeModel.roteiro!.listaRota!) {
          if (listaRota.idLoja != null) {
            idLojas.add(listaRota.idLoja!);
          }
        }
      }
    }

    return idLojas;
  }

  List<ListaRota> extractAllListaRota(List<ListRoutesModel> data) {
    List<ListaRota> idLojas = [];

    for (var routeModel in data) {
      if (routeModel.roteiro?.listaRota != null) {
        for (var listaRota in routeModel.roteiro!.listaRota!) {
          if (listaRota.idLoja != null) {
            idLojas.add(listaRota);
          }
        }
      }
    }

    return idLojas;
  }

  Future<void> syncOrders({List<String>? ordersToSync}) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "syncOrders",
    );

    bool hasError = false;
    String? msgError;
    List<SyncronizationModel>? ordersSync = await SyncronizationModel().getList(
      workspaceId: appController.workspace!.workspaceId!,
    );

    if (orderId != null) {
      ordersSync =
          ordersSync.where((e) => e.transactionKey == orderId).toList();
    }
    if (ordersToSync != null) {
      ordersSync =
          ordersSync
              .where((e) => ordersToSync.contains(e.transactionKey))
              .toList();
    }

    if (ordersSync.isNotEmpty) {
      final ordersList =
          ordersSync
              .where(
                (element) =>
                    element.status ==
                    getStatusSynchronizationValue(
                      StatusSynchronization.notSent,
                    ),
              )
              .toList();

      if (ordersList.isEmpty) {
        SnackbarCustom.snackbarSucess(
          "Pedidos",
          "Não foi encontrado pedidos a serem sincronizados!",
        );
        leaveAction();
        orderId = null;
        return;
      }

      for (var e in ordersList) {
        try {
          final result = await ordersApi.syncOrders(
            model: e.payLoad!.orderInfo!.first,
          );
          if (result.data != null) {
            e.payLoad!.orderStatus = getStatusSynchronizationValue(
              StatusSynchronization.sentForApproval,
            );
            e.status = getStatusSynchronizationValue(
              StatusSynchronization.sentForApproval,
            );

            await dbContext
                .withControllerAction(this)
                .addData(
                  clearCurrentData: true,
                  data: e,
                  hashCode: e.transactionKey,
                  workspaceId: appController.workspace?.workspaceId,
                  storeId: e.payLoad!.pdvId,
                  userId: appController.userLogged!.userId,
                  key: DatabaseModels.syncronization,
                );
          } else {
            hasError = true;
          }
        } catch (e, s) {
          subAction.reportZoneStacktrace(e, s);
          hasError = true;
          msgError = e.toString();
          rethrow;
        } finally {
          leaveAction();
        }
      }
    } else {
      SnackbarCustom.snackbarError(
        "Não foi encontrado pedidos a serem sincronizados!",
      );

      leaveAction();
      return;
    }
    orderId = null;
    leaveAction();

    if (hasError == true) {
      SnackbarCustom.snackbarError(
        msgError ?? "Ocorreu um erro durante a sincronização dos pedidos",
      );
    } else {
      SnackbarCustom.snackbarSucess(
        "Pedidos",
        "Sincronização dos Pedidos realizado com sucesso!",
      );
    }
  }

  Future<void> syncOrdersRetrieve() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "syncOrdersRetrieve",
    );
    bool hasError = false;
    String? msgError;
    // String syncError = "";
    // pegar apenas os itens como SENT e SENT_FOR_APPROVEL
    final ordersSync = await SyncronizationModel().getList(
      workspaceId: appController.workspace!.workspaceId!,
    );

    if (ordersSync.isNotEmpty) {
      final ordersList =
          ordersSync
              .where(
                (element) =>
                    element.status ==
                        getStatusSynchronizationValue(
                          StatusSynchronization.sent,
                        ) ||
                    element.status ==
                        getStatusSynchronizationValue(
                          StatusSynchronization.sentForApproval,
                        ),
              )
              .toList();

      if (ordersList.isEmpty) {
        return;
      }
      for (var e in ordersList) {
        try {
          final result = await ordersApi.syncOrdersRetrieve(
            model: e.payLoad!.orderInfo!.first,
          );

          if (result.data != null) {
            if (result.data!.isNotEmpty) {
              if (result.data!.first.orderStatus! != 2) {
                e.payLoad!.orderStatus = result.data!.first.orderStatus!;
                e.status = result.data!.first.orderStatus!;
              }

              await dbContext
                  .withControllerAction(this)
                  .addData(
                    clearCurrentData: true,
                    data: e,
                    hashCode: e.transactionKey,
                    workspaceId: appController.workspace?.workspaceId,
                    storeId: e.payLoad!.pdvId,
                    userId: appController.userLogged!.userId,
                    key: DatabaseModels.syncronization,
                  );
            } else {
              await dbContext
                  .withControllerAction(this)
                  .deleteByKey(
                    hashCode: e.transactionKey,
                    workspaceId: appController.workspace?.workspaceId,
                    storeId: e.payLoad!.pdvId,
                    userId: appController.userLogged!.userId,
                    key: DatabaseModels.syncronization,
                  );
            }
          } else {
            hasError = true;
          }
        } catch (e, s) {
          hasError = true;
          msgError = e.toString();
          subAction.reportZoneStacktrace(e, s);
        } finally {
          leaveAction();
        }
      }
    } else {
      return;
    }

    if (hasError == true) {
      SnackbarCustom.snackbarError(
        msgError ??
            "Houve um erro ao verificar o status da sincronização dos pedidos.",
      );
    } else {
      SnackbarCustom.snackbarSucess(
        "Pedidos",
        "A verificação do status da sincronização dos pedidos foi concluída com sucesso.",
      );
    }

    // if (syncError.isNotEmpty) {
    //   Dialogs.info(AppStrings.attention,
    //       "Não foi possível obter os status atual da sincronização do pedido.$syncError",
    //       buttonName: "ENTENDI");
    // }
    leaveAction();
  }

  Future<void> syncFileOrders() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "SincronizarArquivos",
    );
    try {
      subAction.reportEvent('Sincronizando arquivos');

      final ordersSync = await SyncronizationModel().getList(
        workspaceId: appController.workspace!.workspaceId!,
      );

      final fileOrders =
          ordersSync
              .where(
                (e) =>
                    e.status == 4 &&
                    e.fileAttachment?.status == FileAttachmentStatus.waiting,
              )
              .map((e) => e)
              .toList();

      for (var fileOrder in fileOrders) {
        final fileAttachment = fileOrder.fileAttachment;
        if (fileAttachment != null) {
          final result = await ordersApi.sendFileOrders(
            model: OrderAttachmentFileRequestModel(
              fileName: fileAttachment.fileName,
              fileBytes:
                  File(fileAttachment.selectedFilePath!).readAsBytesSync(),
              filePath: fileAttachment.selectedFilePath,
              fileType: fileAttachment.fileName?.split('.').last,
              userId: fileOrder.userId,
              orderGroupId: fileOrder.transactionKey,
              orderSentForApproval: false,
              orderId: 0,
            ),
          );
          if (result.data != null) {
            fileOrder.fileAttachment?.status = FileAttachmentStatus.synced;
            fileOrder.fileAttachment?.message = result.data;
          } else {
            fileOrder.fileAttachment?.status = FileAttachmentStatus.error;
            fileOrder.fileAttachment?.message = result.error?.message;
          }

          await dbContext
              .withControllerAction(this)
              .addData(
                clearCurrentData: true,
                data: fileOrder,
                hashCode: fileOrder.transactionKey,
                workspaceId: appController.workspace?.workspaceId,
                storeId: fileOrder.payLoad!.pdvId,
                userId: appController.userLogged!.userId,
                key: DatabaseModels.syncronization,
              );
        }
      }
    } catch (e, stack) {
      subAction.reportZoneStacktrace(e, stack);
    } finally {
      leaveAction();
    }
  }

  Future<void> getDateSync() async {
    final sync = await SynchronizationsModel().getFirst();
    if (sync != null) {
      lastSyncDate = sync.syncDate;
    }
    update();
  }

  Future<void> setDateSync() async {
    var sync = await SynchronizationsModel().getFirst();
    if (sync != null) {
      sync.syncDate = DateTime.now();
    } else {
      sync = SynchronizationsModel(syncDate: DateTime.now());
    }
    await dbContext
        .withControllerAction(this)
        .addData(
          clearCurrentData: true,
          data: sync,
          workspaceId: appController.workspace?.workspaceId,
          userId: appController.userLogged!.userId,
          key: DatabaseModels.synchronizationsModel,
        );

    lastSyncDate = sync.syncDate;
    update();
  }

  Future<void> startSyncSettings() async {
    await generalParameterizationController.getData(forceUpdate: true);
    await generalParameterizationController.getFilterCustom(forceUpdate: true);
    await generalParameterizationController.getSystemParameterization(
      forceUpdate: true,
    );
    await generalParameterizationController
        .getGeneralSettingsOrderDiscountRegistration(forceUpdate: true);
    await generalParameterizationController.getParameterByKeyOffline(
      forceUpdate: true,
    );
    SnackbarCustom.snackbarSucess("Sincronização", AppStrings.syncSettings);
  }

  Future<void> startSyncResearches({bool? hideMsg, bool? sendAnwsers}) async {
    //1) sincronizar as pesquisas respondidas
    if (sendAnwsers != false) {
      await researchesProductIndustryController.syncResearches();
      await researchesProductConcurrentController.syncResearches();
      await researchesMerchanCompetitiveController.syncResearches();
      await researchesMerchanIndustryController.syncResearches();
      await researchesComplementaryController.syncResearches();
      await researchesTradeMarketingController.syncResearches();
      await researchesShareOfShelfController.syncResearches();
    }
    //2) Sincronizar as pesquisas a responder
    final routesIds =
        storeRoutesPlannedController.storesList
            .where(
              (element) =>
                  element.dataExtra!.routeId != null &&
                  (element.dataExtra!.statusVisit ==
                          StatusVisitEnum.notInitilized ||
                      element.dataExtra!.statusVisit ==
                          StatusVisitEnum.synchronized),
            )
            .map((e) => e.dataExtra!.routeId)
            .toList();
    if (routesIds.isNotEmpty) {
      for (var e in routesIds) {
        await researchesProductIndustryController.getDataSync(e!);
        await researchesProductConcurrentController.getDataSync(e);
        await researchesMerchanCompetitiveController.getDataSync(e);
        await researchesMerchanIndustryController.getDataSync(e);
        await researchesComplementaryController.getDataSync(e);
      }
    }

    final storeIds =
        storeRoutesPlannedController.storesList
            .where(
              (element) =>
                  element.idLoja != null &&
                  (element.dataExtra!.statusVisit ==
                          StatusVisitEnum.notInitilized ||
                      element.dataExtra!.statusVisit ==
                          StatusVisitEnum.synchronized),
            )
            .map((e) => e.idLoja)
            .toList();
    if (storeIds.isNotEmpty) {
      for (var e in storeIds) {
        await researchesTradeMarketingController.getDataSync(storeId: e!);
        await researchesShareOfShelfController.getDataSync(storeId: e);
      }
    }

    if (sendAnwsers != false) {
      final visitController = Get.find<VisitsController>();
      visitController.refreshVisitsSearch();
      if (hideMsg != true) {
        SnackbarCustom.snackbarSucess(
          "Sincronização",
          AppStrings.syncResearches,
        );
      }
    }
  }

  Future<void> startSyncVisits() async {
    final visitController = Get.find<VisitsController>();
    final visitIsSync = await visitController.syncVisits();
    await visitController.getSyncVisits();
    if (visitIsSync) {
      SnackbarCustom.snackbarSucess("Sincronização", AppStrings.syncVisits);
    }
  }

  Future<void> startSyncSalasInfo() async {
    final visitController = Get.find<VisitsController>();

    final hasVisitToSync = await visitController.hasVisitToSync();

    if (hasVisitToSync) {
      SnackbarCustom.snackbarWarning(
        "Existem visitas pendentes de sincronização, por favor sincronize as visitas antes de sincronizar as informações das visitas.",
      );
      return;
    }

    await visitController.syncVisitsInfo();
    SnackbarCustom.snackbarSucess("Sincronização", AppStrings.syncVisitsInfo);
  }

  bool syncStarting() {
    return syncStart ||
        syncStartRoutes ||
        syncStartOrders ||
        syncStartTheme ||
        syncStartContents ||
        syncStartVisits ||
        syncStartSettings ||
        syncStartSalesInfo ||
        syncStartResearches;
  }

  Future<void> verifiyMessages() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "VerificaMensagens",
    );
    try {
      subAction.reportEvent('Buscando se há notificação');
      final message = await NotificationModel().getFirstMessage(1);
      if (message != null) {
        subAction.reportEvent('Notificação encontrada para ser exibida');
        message.isShow = true;
        await dbContext.addData(
          clearCurrentData: true,
          key: DatabaseModels.notificationModel,
          userId: message.id,
          data: message,
          workspaceId: appController.workspace!.workspaceId!,
        );
        await Dialogs.info(
          AppStrings.attention,
          message.message!,
          buttonName: "Entendi",
        );
      }
    } catch (e, stack) {
      subAction.reportZoneStacktrace(e, stack);
    } finally {
      leaveAction();
    }
  }

  Future<void> syncProductTypes() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "syncProductTypes",
    );

    try {
      subAction.reportEvent('Iniciando sincronização de tipos de produtos');

      final result = await reportOrdersApi.getProductTypes();

      if (result.data != null) {
        subAction.reportEvent(
          'Tipos de produtos recebidos: ${result.data!.length}',
        );

        await dbContext.deleteByKey(
          key: DatabaseModels.productTypeModel,
          workspaceId: appController.workspace!.workspaceId,
        );

        for (var productType in result.data!) {
          await dbContext
              .withControllerAction(this)
              .addData(
                key: DatabaseModels.productTypeModel,
                data: productType,
                workspaceId: appController.workspace!.workspaceId,
                clearCurrentData: false,
              );
        }

        subAction.reportEvent('Tipos de produtos salvos com sucesso');
      } else {
        subAction.reportError('Falha ao obter tipos de produtos');
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
    } finally {
      leaveAction();
    }
  }
}
