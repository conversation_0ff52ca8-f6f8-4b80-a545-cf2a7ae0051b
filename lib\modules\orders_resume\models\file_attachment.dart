import 'dart:io';

enum FileAttachmentStatus {
  waiting, // aguardando
  synced, // sincronizado
  error // erro
}

class FileAttachment {
  String? selectedFilePath;
  String? fileName;
  String? fileSize;
  FileAttachmentStatus status;
  String? message;

  FileAttachment({
    this.selectedFilePath,
    this.fileName,
    this.fileSize,
    this.status = FileAttachmentStatus.waiting,
    this.message,
  });

  // Optional: Add a copyWith method for easier state updates
  FileAttachment copyWith({
    File? selectedFile,
    String? fileName,
    String? fileSize,
    FileAttachmentStatus? status,
    String? message,
  }) {
    return FileAttachment(
      selectedFilePath: selectedFilePath ?? selectedFilePath,
      fileName: fileName ?? this.fileName,
      fileSize: fileSize ?? this.fileSize,
      status: status ?? this.status,
      message: message ?? this.message,
    );
  }

  FileAttachment.fromJson(Map<String, dynamic> json)
      : status = FileAttachmentStatus.values.firstWhere(
          (e) => e.toString() == json['status'],
          orElse: () => FileAttachmentStatus.waiting,
        ) {
    selectedFilePath = json['selectedFilePath'];
    fileName = json['fileName'];
    fileSize = json['fileSize'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    return {
      'fileName': fileName,
      'fileSize': fileSize,
      'status': status.toString(),
      'message': message,
      'selectedFilePath': selectedFilePath,
    };
  }
}
