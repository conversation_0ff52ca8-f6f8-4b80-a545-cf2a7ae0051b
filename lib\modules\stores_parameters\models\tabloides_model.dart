import 'package:pharmalink/modules/stores_parameters/models/prazo_pagamento_model.dart';

class TabloidesModel {
  String? corTextoBotaoVisualizacao;
  String? corTextoExpiracao;
  DateTime? dataFim;
  DateTime? dataInicio;
  String? descricao;
  String? descricaoTabloide;
  int? idTabloide;
  bool? mostrarBotaoVisualizacao;
  bool? mostrarDataExpiracao;
  int? posicionamento;
  String? urlImagem;
  int? itemMinimumQuantity;
  int? itemMaximumQuantity;
  int? orderMaximumQuantity;
  List<PrazoPagamentoModel>? prazoPagamento;
  List<FamiliasProdutos>? familiasProdutos;
  List<Produtos>? produtos;
  List<CombosOferta>? combosOferta;
  List<TiposProdutos>? tiposProdutos;

  TabloidesModel({
    this.corTextoBotaoVisualizacao,
    this.corTextoExpiracao,
    this.dataFim,
    this.dataInicio,
    this.descricao,
    this.descricaoTabloide,
    this.idTabloide,
    this.mostrarBotaoVisualizacao,
    this.mostrarDataExpiracao,
    this.posicionamento,
    this.urlImagem,
    this.itemMinimumQuantity,
    this.itemMaximumQuantity,
    this.orderMaximumQuantity,
    this.prazoPagamento,
    this.familiasProdutos,
    this.produtos,
    this.combosOferta,
    this.tiposProdutos,
  });

  TabloidesModel.fromJson(Map<String, dynamic> json) {
    corTextoBotaoVisualizacao = json['CorTextoBotaoVisualizacao'];
    corTextoExpiracao = json['CorTextoExpiracao'];
    dataFim = json['DataFim'] != null ? DateTime.parse(json['DataFim']) : null;
    dataInicio =
        json['DataInicio'] != null ? DateTime.parse(json['DataInicio']) : null;
    descricao = json['Descricao'];
    descricaoTabloide = json['DescricaoTabloide'];
    idTabloide = json['IdTabloide'];
    mostrarBotaoVisualizacao = json['MostrarBotaoVisualizacao'];
    mostrarDataExpiracao = json['MostrarDataExpiracao'];
    posicionamento = json['Posicionamento'];
    urlImagem = json['UrlImagem'];
    if (json['PrazoPagamento'] != null) {
      prazoPagamento = <PrazoPagamentoModel>[];
      json['PrazoPagamento'].forEach((v) {
        prazoPagamento!.add(PrazoPagamentoModel.fromJson(v));
      });
    }
    if (json['FamiliasProdutos'] != null) {
      familiasProdutos = <FamiliasProdutos>[];
      json['FamiliasProdutos'].forEach((v) {
        familiasProdutos!.add(FamiliasProdutos.fromJson(v));
      });
    }
    if (json['Produtos'] != null) {
      produtos = <Produtos>[];
      json['Produtos'].forEach((v) {
        produtos!.add(Produtos.fromJson(v));
      });
    }
    if (json['CombosOferta'] != null) {
      combosOferta = <CombosOferta>[];
      json['CombosOferta'].forEach((v) {
        combosOferta!.add(CombosOferta.fromJson(v));
      });
    }
    if (json['TiposProdutos'] != null) {
      tiposProdutos = <TiposProdutos>[];
      json['TiposProdutos'].forEach((v) {
        tiposProdutos!.add(TiposProdutos.fromJson(v));
      });
    }
    itemMinimumQuantity = json['ItemQtdMin'];
    itemMaximumQuantity = json['ItemQtdMax'];
    orderMaximumQuantity = json['PedidoQtdMax'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['CorTextoBotaoVisualizacao'] = corTextoBotaoVisualizacao;
    data['CorTextoExpiracao'] = corTextoExpiracao;
    data['DataFim'] = dataFim?.toIso8601String();
    data['DataInicio'] = dataInicio?.toIso8601String();
    data['Descricao'] = descricao;
    data['DescricaoTabloide'] = descricaoTabloide;
    data['IdTabloide'] = idTabloide;
    data['MostrarBotaoVisualizacao'] = mostrarBotaoVisualizacao;
    data['MostrarDataExpiracao'] = mostrarDataExpiracao;
    data['Posicionamento'] = posicionamento;
    data['UrlImagem'] = urlImagem;
    if (prazoPagamento != null) {
      data['PrazoPagamento'] = prazoPagamento!.map((v) => v.toJson()).toList();
    }
    if (familiasProdutos != null) {
      data['FamiliasProdutos'] =
          familiasProdutos!.map((v) => v.toJson()).toList();
    }
    if (produtos != null) {
      data['Produtos'] = produtos!.map((v) => v.toJson()).toList();
    }
    if (combosOferta != null) {
      data['CombosOferta'] = combosOferta!.map((v) => v.toJson()).toList();
    }
    if (tiposProdutos != null) {
      data['TiposProdutos'] = tiposProdutos!.map((v) => v.toJson()).toList();
    }
    data['ItemQtdMin'] = itemMinimumQuantity;
    data['ItemQtdMax'] = itemMaximumQuantity;
    data['PedidoQtdMax'] = orderMaximumQuantity;
    return data;
  }
}

class FamiliasProdutos {
  int? idFamilia;
  String? descricao;
  String? caminhoFoto;

  FamiliasProdutos({this.idFamilia, this.descricao, this.caminhoFoto});

  FamiliasProdutos.fromJson(Map<String, dynamic> json) {
    idFamilia = json['IdFamilia'];
    descricao = json['Descricao'];
    caminhoFoto = json['CaminhoFoto'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdFamilia'] = idFamilia;
    data['Descricao'] = descricao;
    data['CaminhoFoto'] = caminhoFoto;
    return data;
  }
}

class Produtos {
  int? idProduto;
  String? ean;
  String? descricao;
  String? laboratorio;
  String? familia;
  double? preco;
  bool? destaque;
  String? status;
  List<FaixasDesconto>? faixasDesconto;
  String? menorDataVigencia;
  int? quantidadeEstoque;
  bool? precoDistribuidor;
  String? caminhoFoto;
  int? idFamilia;
  int? idTipoProduto;
  String? apresentacaoDUN;
  String? dUN;
  int? idProdutoDUN;
  bool? isDemonstraGridPedido;
  FiltrosPersonalizados? filtrosPersonalizados;
  MetricaMdtr? metricaMdtr;
  int? quantidade;
  double? desconto;

  Produtos(
      {this.idProduto,
      this.ean,
      this.descricao,
      this.laboratorio,
      this.familia,
      this.preco,
      this.destaque,
      this.status,
      this.faixasDesconto,
      this.menorDataVigencia,
      this.quantidadeEstoque,
      this.precoDistribuidor,
      this.caminhoFoto,
      this.idFamilia,
      this.idTipoProduto,
      this.apresentacaoDUN,
      this.dUN,
      this.idProdutoDUN,
      this.isDemonstraGridPedido,
      this.filtrosPersonalizados,
      this.metricaMdtr,
      this.quantidade,
      this.desconto});

  Produtos.fromJson(Map<String, dynamic> json) {
    idProduto = json['IdProduto'];
    ean = json['Ean'];
    descricao = json['Descricao'];
    laboratorio = json['Laboratorio'];
    familia = json['Familia'];
    preco = json['Preco'];
    destaque = json['Destaque'];
    status = json['Status'];
    if (json['FaixasDesconto'] != null) {
      faixasDesconto = <FaixasDesconto>[];
      json['FaixasDesconto'].forEach((v) {
        faixasDesconto!.add(FaixasDesconto.fromJson(v));
      });
    }
    menorDataVigencia = json['MenorDataVigencia'];
    quantidadeEstoque = json['QuantidadeEstoque'];
    precoDistribuidor = json['PrecoDistribuidor'];
    caminhoFoto = json['CaminhoFoto'];
    idFamilia = json['IdFamilia'];
    idTipoProduto = json['IdTipoProduto'];
    apresentacaoDUN = json['ApresentacaoDUN'];
    dUN = json['DUN'];
    idProdutoDUN = json['IdProdutoDUN'];
    isDemonstraGridPedido = json['IsDemonstraGridPedido'];
    filtrosPersonalizados = json['FiltrosPersonalizados'] != null
        ? FiltrosPersonalizados.fromJson(json['FiltrosPersonalizados'])
        : null;
    metricaMdtr = json['MetricaMdtr'] != null
        ? MetricaMdtr.fromJson(json['MetricaMdtr'])
        : null;
    quantidade = json['Quantidade'];
    desconto = json['Desconto'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdProduto'] = idProduto;
    data['Ean'] = ean;
    data['Descricao'] = descricao;
    data['Laboratorio'] = laboratorio;
    data['Familia'] = familia;
    data['Preco'] = preco;
    data['Destaque'] = destaque;
    data['Status'] = status;
    if (faixasDesconto != null) {
      data['FaixasDesconto'] = faixasDesconto!.map((v) => v.toJson()).toList();
    }
    data['MenorDataVigencia'] = menorDataVigencia;
    data['QuantidadeEstoque'] = quantidadeEstoque;
    data['PrecoDistribuidor'] = precoDistribuidor;
    data['CaminhoFoto'] = caminhoFoto;
    data['IdFamilia'] = idFamilia;
    data['IdTipoProduto'] = idTipoProduto;
    data['ApresentacaoDUN'] = apresentacaoDUN;
    data['DUN'] = dUN;
    data['IdProdutoDUN'] = idProdutoDUN;
    data['IsDemonstraGridPedido'] = isDemonstraGridPedido;
    if (filtrosPersonalizados != null) {
      data['FiltrosPersonalizados'] = filtrosPersonalizados!.toJson();
    }
    if (metricaMdtr != null) {
      data['MetricaMdtr'] = metricaMdtr!.toJson();
    }
    data['Quantidade'] = quantidade;
    data['Desconto'] = desconto;
    return data;
  }
}

class FaixasDesconto {
  int? quantidadeMinima;
  int? quantidadeMaxima;
  double? descontoMinimo;
  double? descontoMaximo;

  FaixasDesconto(
      {this.quantidadeMinima,
      this.quantidadeMaxima,
      this.descontoMinimo,
      this.descontoMaximo});

  FaixasDesconto.fromJson(Map<String, dynamic> json) {
    quantidadeMinima = json['QuantidadeMinima'];
    quantidadeMaxima = json['QuantidadeMaxima'];
    descontoMinimo = json['DescontoMinimo'];
    descontoMaximo = json['DescontoMaximo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['QuantidadeMinima'] = quantidadeMinima;
    data['QuantidadeMaxima'] = quantidadeMaxima;
    data['DescontoMinimo'] = descontoMinimo;
    data['DescontoMaximo'] = descontoMaximo;
    return data;
  }
}

class FiltrosPersonalizados {
  int? idProduto;
  bool? ativo;
  String? codigo;
  String? descricao;
  String? descricaoCategoria;
  String? icone;
  int? idCategoriaFiltroPersonalizado;
  int? idFiltroPersonalizado;

  FiltrosPersonalizados(
      {this.idProduto,
      this.ativo,
      this.codigo,
      this.descricao,
      this.descricaoCategoria,
      this.icone,
      this.idCategoriaFiltroPersonalizado,
      this.idFiltroPersonalizado});

  FiltrosPersonalizados.fromJson(Map<String, dynamic> json) {
    idProduto = json['idProduto'];
    ativo = json['Ativo'];
    codigo = json['Codigo'];
    descricao = json['Descricao'];
    descricaoCategoria = json['DescricaoCategoria'];
    icone = json['Icone'];
    idCategoriaFiltroPersonalizado = json['IdCategoriaFiltroPersonalizado'];
    idFiltroPersonalizado = json['IdFiltroPersonalizado'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idProduto'] = idProduto;
    data['Ativo'] = ativo;
    data['Codigo'] = codigo;
    data['Descricao'] = descricao;
    data['DescricaoCategoria'] = descricaoCategoria;
    data['Icone'] = icone;
    data['IdCategoriaFiltroPersonalizado'] = idCategoriaFiltroPersonalizado;
    data['IdFiltroPersonalizado'] = idFiltroPersonalizado;
    return data;
  }
}

class MetricaMdtr {
  String? cnpj;
  String? ean;
  String? dtAno;
  String? dtMes;
  int? qtdObjetivo;
  int? vlrObjetivo;
  int? qtdRealizado;
  int? vlrRealizado;
  int? vlrMedia;
  int? qtdMedia;
  String? dtObjetivo;
  String? dtRealizado;

  MetricaMdtr(
      {this.cnpj,
      this.ean,
      this.dtAno,
      this.dtMes,
      this.qtdObjetivo,
      this.vlrObjetivo,
      this.qtdRealizado,
      this.vlrRealizado,
      this.vlrMedia,
      this.qtdMedia,
      this.dtObjetivo,
      this.dtRealizado});

  MetricaMdtr.fromJson(Map<String, dynamic> json) {
    cnpj = json['Cnpj'];
    ean = json['Ean'];
    dtAno = json['DtAno'];
    dtMes = json['DtMes'];
    qtdObjetivo = json['QtdObjetivo'];
    vlrObjetivo = json['VlrObjetivo'];
    qtdRealizado = json['QtdRealizado'];
    vlrRealizado = json['VlrRealizado'];
    vlrMedia = json['VlrMedia'];
    qtdMedia = json['QtdMedia'];
    dtObjetivo = json['DtObjetivo'];
    dtRealizado = json['DtRealizado'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Cnpj'] = cnpj;
    data['Ean'] = ean;
    data['DtAno'] = dtAno;
    data['DtMes'] = dtMes;
    data['QtdObjetivo'] = qtdObjetivo;
    data['VlrObjetivo'] = vlrObjetivo;
    data['QtdRealizado'] = qtdRealizado;
    data['VlrRealizado'] = vlrRealizado;
    data['VlrMedia'] = vlrMedia;
    data['QtdMedia'] = qtdMedia;
    data['DtObjetivo'] = dtObjetivo;
    data['DtRealizado'] = dtRealizado;
    return data;
  }
}

class TiposProdutos {
  int? idTipoProduto;
  String? descricao;

  TiposProdutos({this.idTipoProduto, this.descricao});

  TiposProdutos.fromJson(Map<String, dynamic> json) {
    idTipoProduto = json['IdTipoProduto'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdTipoProduto'] = idTipoProduto;
    data['Descricao'] = descricao;
    return data;
  }
}

class CombosOferta {
  int? idComboOferta;
  String? descricao;
  String? status;
  String? menorDataVigencia;
  List<Produtos>? produtos;
  double? precoCombo;
  double? precoComboLiquido;
  String? caminhoFoto;
  int? tipoAgrupamentoLojas;
  bool? locked;
  int? orderComboQuantity; // QtdComboPedido
  int? validityComboQuantity; // QtdComboNaVigencia
  bool? orderComboLimit; // LimitarComboPedido
  bool? validityComboLimit; // LimitarComboNaVigencia

  CombosOferta(
      {this.idComboOferta,
      this.descricao,
      this.status,
      this.menorDataVigencia,
      this.produtos,
      this.precoCombo,
      this.precoComboLiquido,
      this.caminhoFoto,
      this.tipoAgrupamentoLojas,
      this.locked,
      this.orderComboQuantity,
      this.validityComboQuantity,
      this.orderComboLimit,
      this.validityComboLimit});

  CombosOferta.fromJson(Map<String, dynamic> json) {
    idComboOferta = json['IdComboOferta'];
    descricao = json['Descricao'];
    status = json['Status'];
    menorDataVigencia = json['MenorDataVigencia'];
    if (json['Produtos'] != null) {
      produtos = <Produtos>[];
      json['Produtos'].forEach((v) {
        produtos!.add(Produtos.fromJson(v));
      });
    }
    precoCombo = json['PrecoCombo'];
    precoComboLiquido = json['PrecoComboLiquido'];
    caminhoFoto = json['CaminhoFoto'];
    tipoAgrupamentoLojas = json['TipoAgrupamentoLojas'];
    locked = json['Locked'];
    orderComboQuantity = json['QtdComboPedido'];
    validityComboQuantity = json['QtdComboNaVigencia'];
    orderComboLimit = json['LimitarComboPedido'];
    validityComboLimit = json['LimitarComboNaVigencia'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdComboOferta'] = idComboOferta;
    data['Descricao'] = descricao;
    data['Status'] = status;
    data['MenorDataVigencia'] = menorDataVigencia;
    if (produtos != null) {
      data['Produtos'] = produtos!.map((v) => v.toJson()).toList();
    }
    data['PrecoCombo'] = precoCombo;
    data['PrecoComboLiquido'] = precoComboLiquido;
    data['CaminhoFoto'] = caminhoFoto;
    data['TipoAgrupamentoLojas'] = tipoAgrupamentoLojas;
    data['Locked'] = locked;
    data['QtdComboPedido'] = orderComboQuantity;
    data['QtdComboNaVigencia'] = validityComboQuantity;
    data['LimitarComboPedido'] = orderComboLimit;
    data['LimitarComboNaVigencia'] = validityComboLimit;
    return data;
  }
}
