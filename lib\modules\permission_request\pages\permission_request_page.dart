import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class PermissionRequestPage extends StatelessWidget {
  const PermissionRequestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<PermissionRequestController>(builder: (ctrl) {
      return PopScope(
        canPop: false,
        child: Scaffold(
          backgroundColor: whiteColor,
          appBar: AppBar(
            centerTitle: true,
            backgroundColor: themesController.getPrimaryColor(),
            title: LabelWidget(
              title: "Habilitar Permissões",
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              textColor: whiteColor,
            ),
            leading: const SizedBox.shrink(),
          ),
          body: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                FaIcon(
                  FontAwesomeIcons.lock,
                  size: 100,
                  color: themesController.getPrimaryColor(),
                ),
                const SizedBox(height: 20),
                LabelWidget(
                  title:
                      "As permissões de acesso à câmera são indispensáveis para prosseguir.",
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 40),
                PrimaryButtonWidget(
                  titleButtom: "Aceitar",
                  isLoading: ctrl.acceptButtonLoading,
                  onTap: () async {
                    await ctrl.requestPermission();
                  },
                ),
                PrimaryButtonWidget(
                  buttonColor: themesController.getColorC(),
                  titleButtom: "Não permitir",
                  onTap: () async {
                    Get.back(result: false);
                  },
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
