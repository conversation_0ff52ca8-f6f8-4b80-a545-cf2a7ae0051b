import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_model.dart';

class ResearchesComplementarySingleWidget extends StatelessWidget {
  final ResearchesComplementaryPerguntas question;
  const ResearchesComplementarySingleWidget(
      {super.key, required this.question});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ResearchesComplementaryController>(builder: (ctx) {
      return Visibility(
        visible: question.isShown!,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            children: [
              LabelWidget(
                title: question.titulo!,
                fontSize: DeviceSize.fontSize(20, 24),
                fontWeight: FontWeight.bold,
                textColor: themesController.getPrimaryColor(),
                textAlign: TextAlign.center,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Column(
                  children: question.possiveisRespostas!
                      .map(
                        (e) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: CustomInkWell(
                            onTap: () {
                              if (ctx.selected!.isSync != SyncEnum.finished) {
                                if (question.showAnotherAnswer) {
                                  ctx.setAnotherAnswer(question);
                                }
                                ctx.setQuestionSelectAwser(e, question);
                              }
                            },
                            child: Container(
                              decoration: BoxDecoration(
                                border: Border.all(
                                    color:
                                        ctx.hasAnswer(question, e.idResposta!)
                                            ? themesController.getColorButton()
                                            : Colors.white,
                                    width: 2),
                                borderRadius: BorderRadius.circular(10),
                                color: ctx.hasAnswer(question, e.idResposta!)
                                    ? Colors.white
                                    : themesController.getColorButton(),
                              ),
                              child: Padding(
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Flexible(
                                        child: Padding(
                                          padding: const EdgeInsets.only(
                                              left: 10, right: 10),
                                          child: LabelWidget(
                                            title: e.resposta?.toUpperCase() ??
                                                "-",
                                            fontSize: e.idResposta != null
                                                ? e.resposta!.length < 20
                                                    ? DeviceSize.fontSize(
                                                        14, 18)
                                                    : DeviceSize.fontSize(
                                                        10, 12)
                                                : DeviceSize.fontSize(14, 18),
                                            textColor: ctx.hasAnswer(
                                                    question, e.idResposta!)
                                                ? themesController
                                                    .getPrimaryColor()
                                                : Colors.white,
                                            fontWeight: ctx.hasAnswer(
                                                    question, e.idResposta!)
                                                ? FontWeight.bold
                                                : FontWeight.normal,
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 4,
                                          ),
                                        ),
                                      ),
                                      if (ctx.hasAnswer(
                                          question, e.idResposta!))
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(right: 10),
                                          child: Icon(
                                            Icons.check,
                                            color: themesController
                                                .getColorButton(),
                                          ),
                                        )
                                    ]),
                              ),
                            ),
                          ),
                        ),
                      )
                      .toList(),
                ),
              ),
              if (question.incluiOutraResposta != null &&
                  question.incluiOutraResposta!)
                CustomInkWell(
                  onTap: () {
                    if (ctx.selected!.isSync != SyncEnum.finished) {
                      ctx.setAnotherAnswer(question);
                      if (question.answers != null &&
                          question.answers!.isNotEmpty &&
                          question.answers!.any((e) => e.idResposta != null)) {
                        ctx.setQuestionSelectAwser(
                            question.possiveisRespostas![0], question);
                      }
                    }
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Checkbox(
                          activeColor: themesController.getBackgroundColor(),
                          value: question.showAnotherAnswer,
                          onChanged: (_) => {}),
                      LabelWidget(
                        title: "Outros:",
                        fontSize: DeviceSize.fontSize(20, 24),
                        fontWeight: FontWeight.bold,
                        textColor: themesController.getPrimaryColor(),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              if (question.showAnotherAnswer)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: Column(
                    children: [
                      TextFormField(
                        enabled: ctx.selected!.isSync != SyncEnum.finished,
                        keyboardType: TextInputType.text,
                        controller: TextEditingController(
                          text: ctx.getFreeAnswer(question),
                        ),
                        style: TextStyle(
                          color: themesController.getPrimaryColor(),
                        ),
                        textAlign: TextAlign.center,
                        decoration: InputDecoration(
                          filled: true,
                          fillColor: Colors.white,
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(10.0),
                          ),
                        ),
                        inputFormatters: const [],
                        onChanged: (value) {
                          ctx.setFreeAnswer(value, question);
                        },
                      ),
                    ],
                  ),
                ),
              if (question.showAnotherAnswer && !question.hasDivisor!)
                Divider(
                  color: themesController.getPrimaryColor(),
                  height: 3,
                  thickness: BorderSide.strokeAlignCenter,
                ),
              if (question.hasDivisor!)
                Divider(
                  color: themesController.getPrimaryColor(),
                  height: 3,
                  thickness: BorderSide.strokeAlignCenter,
                )
            ],
          ),
        ),
      );
    });
  }
}
