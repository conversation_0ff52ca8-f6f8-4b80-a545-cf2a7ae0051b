import 'package:pharmalink/exports/basic_exports.dart';

enum CameraOptionsEnum { video, photo, gallery }

class CameraOptionsWidgets extends StatelessWidget {
  const CameraOptionsWidgets(
      {super.key, required this.isSelected, required this.title, this.onTap});

  final String title;
  final bool isSelected;
  final Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return CustomInkWell(
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: isSelected ? Colors.grey[800] : Colors.transparent,
          ),
          child: LabelWidget(
            title: title,
          ),
        ));
  }
}
