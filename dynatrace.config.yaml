android:
  config:
    "dynatrace {
      configurations {
        defaultConfig {
          autoStart{
            applicationId '40e18b35-0587-42f7-b3ba-4a1dfcfc7d5e'
            beaconUrl 'https://bf53667gkn.bf.dynatrace.com/mbeacon'
          }
          userOptIn true
          agentBehavior.startupLoadBalancing true
        }
      }
    }"

ios:
  config:
    "<key>DTXApplicationID</key>
    <string>40e18b35-0587-42f7-b3ba-4a1dfcfc7d5e</string>
    <key>DTXBeaconURL</key>
    <string>https://bf53667gkn.bf.dynatrace.com/mbeacon</string>
    <key>DTXUserOptIn</key>
    <true/>
    <key>DTXStartupLoadBalancing</key>
    <true/>
    <key>DTXAutoStart</key> 
    <true/>"
