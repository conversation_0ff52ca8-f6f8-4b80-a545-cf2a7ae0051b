import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/reports/models/reports_types_model.dart';

class ReportsController extends GetxControllerInstrumentado<ReportsController> {
  ReportsController();
  List<ReportsTypesTypesModel> types = [];

  void setTypes() {
    types = [];
    types.add(ReportsTypesTypesModel(
      icon: const Icon(
        FontAwesomeIcons.cartFlatbed,
        color: Colors.black,
      ),
      title: "Status do pedido",
      onTap: () async {
        Get.toNamed(RoutesPath.reportOrders);
      },
    ));
    types.add(ReportsTypesTypesModel(
      icon: const Icon(
        FontAwesomeIcons.download,
        color: Colors.black,
      ),
      title: "Listagem de Conteúdos",
      onTap: () async {
        Get.toNamed(RoutesPath.reportContents);
      },
    ));
    update();
  }
}
