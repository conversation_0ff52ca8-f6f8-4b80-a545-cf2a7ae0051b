import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/core/enuns/product_state_enum.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/orders/models/product_type_model.dart';

class ProductsMixModel extends SqfLiteBase<ProductsMixModel> {
  ProdutosModel? produtos;
  List<FamiliasProdutosModel>? familiasProdutos;
  List<TiposProdutosModel>? tiposProdutos;
  List<ProdutosDistribuidoresModel>? produtosDistribuidores;
  List<ProdutosDUNModel>? produtosDUN;
  List<ProdutosDUNDistribuidorModel>? produtosDUNDistribuidor;

  ProductsMixModel({
    this.produtos,
    this.familiasProdutos,
    this.tiposProdutos,
    this.produtosDistribuidores,
    this.produtosDUN,
    this.produtosDUNDistribuidor,
  }) : super(DatabaseModels.productsMixModel);

  ProductsMixModel.fromJson(Map<String, dynamic> json)
    : super(DatabaseModels.productsMixModel) {
    produtos =
        json['Produtos'] != null
            ? ProdutosModel.fromJson(json['Produtos'])
            : null;
    if (json['FamiliasProdutos'] != null) {
      familiasProdutos = <FamiliasProdutosModel>[];
      json['FamiliasProdutos'].forEach((v) {
        familiasProdutos!.add(FamiliasProdutosModel.fromJson(v));
      });
    }
    if (json['TiposProdutos'] != null) {
      tiposProdutos = <TiposProdutosModel>[];
      json['TiposProdutos'].forEach((v) {
        tiposProdutos!.add(TiposProdutosModel.fromJson(v));
      });
    }
    if (json['ProdutosDistribuidores'] != null) {
      produtosDistribuidores = <ProdutosDistribuidoresModel>[];
      json['ProdutosDistribuidores'].forEach((v) {
        produtosDistribuidores!.add(ProdutosDistribuidoresModel.fromJson(v));
      });
    }
    if (json['ProdutosDUN'] != null) {
      produtosDUN = <ProdutosDUNModel>[];
      json['ProdutosDUN'].forEach((v) {
        produtosDUN!.add(ProdutosDUNModel.fromJson(v));
      });
    }
    if (json['ProdutosDUNDistribuidor'] != null) {
      produtosDUNDistribuidor = <ProdutosDUNDistribuidorModel>[];
      json['ProdutosDUNDistribuidor'].forEach((v) {
        produtosDUNDistribuidor!.add(ProdutosDUNDistribuidorModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (produtos != null) {
      data['Produtos'] = produtos!.toJson();
    }
    if (familiasProdutos != null) {
      data['FamiliasProdutos'] =
          familiasProdutos!.map((v) => v.toJson()).toList();
    }
    if (tiposProdutos != null) {
      data['TiposProdutos'] = tiposProdutos!.map((v) => v.toJson()).toList();
    }
    if (produtosDistribuidores != null) {
      data['ProdutosDistribuidores'] =
          produtosDistribuidores!.map((v) => v.toJson()).toList();
    }
    if (produtosDUN != null) {
      data['ProdutosDUN'] = produtosDUN!.map((v) => v.toJson()).toList();
    }
    if (produtosDUNDistribuidor != null) {
      data['ProdutosDUNDistribuidor'] =
          produtosDUNDistribuidor!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  Future<ProductsMixModel?> getFirst({
    required int workspaceId,
    required int storeId,
    required String hashCode,
  }) async {
    var list = await getAll<ProductsMixModel>(
      workspaceId: workspaceId,
      storeId: storeId,
      hashCode: hashCode,
      ProductsMixModel.fromJson,
    );
    return list.isEmpty ? null : list.first;
  }

  Future<List<ProductsMixModel>> getList({
    required int workspaceId,
    String? hashCode,
  }) async {
    var list = await getAll<ProductsMixModel>(
      workspaceId: workspaceId,
      hashCode: hashCode,
      ProductsMixModel.fromJson,
    );
    return list;
  }
}

class ProdutosModel {
  List<SkusModel>? skus;
  List<TabloideResponseModel>? tabloide;
  List<CombosOfertaModel>? combosOferta;

  ProdutosModel({this.skus, this.tabloide, this.combosOferta});

  ProdutosModel.fromJson(Map<String, dynamic> json) {
    if (json['Skus'] != null) {
      skus = <SkusModel>[];
      json['Skus'].forEach((v) {
        skus!.add(SkusModel.fromJson(v));
      });
    }
    if (json['Tabloide'] != null) {
      tabloide = <TabloideResponseModel>[];
      json['Tabloide'].forEach((v) {
        tabloide!.add(TabloideResponseModel.fromJson(v));
      });
    }
    if (json['CombosOferta'] != null) {
      combosOferta = <CombosOfertaModel>[];
      json['CombosOferta'].forEach((v) {
        combosOferta!.add(CombosOfertaModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (skus != null) {
      data['Skus'] = skus!.map((v) => v.toJson()).toList();
    }
    if (tabloide != null) {
      data['Tabloide'] = tabloide!.map((v) => v.toJson()).toList();
    }
    if (combosOferta != null) {
      data['CombosOferta'] = combosOferta!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SkusModel {
  int? idProduto;
  String? ean;
  double? desconto;
  String? descricao;
  String? laboratorio;
  int? idFamilia;
  String? familia;
  double? preco;
  int? quantidadeMinima;
  List<FiltrosPersonalizadosModel>? filtrosPersonalizados;
  bool? destaque;
  String? status;
  List<FaixasDescontoModel>? faixasDesconto;
  DateTime? menorDataVigencia;
  int? quantidadeEstoque;
  bool? precoDistribuidor;
  int? quantidade;
  String? caminhoFoto;
  int? idTipoProduto;
  bool? isDemonstraGridPedido;
  bool? concedeDesconto;
  bool? recebeDesconto;
  double? precoMinimo;
  List<DescontoPorVolumeBD>? descontoPorVolumeBD;
  MetricaMdtr? metricaMdtr;
  double? substituicaoTributaria;

  SkusModel({
    this.idProduto,
    this.ean,
    this.desconto,
    this.descricao,
    this.laboratorio,
    this.idFamilia,
    this.familia,
    this.preco,
    this.quantidadeMinima,
    this.filtrosPersonalizados,
    this.destaque,
    this.status,
    this.faixasDesconto,
    this.menorDataVigencia,
    this.quantidadeEstoque,
    this.precoDistribuidor,
    this.quantidade,
    this.caminhoFoto,
    this.idTipoProduto,
    this.isDemonstraGridPedido,
    this.concedeDesconto,
    this.recebeDesconto,
    this.precoMinimo,
    this.descontoPorVolumeBD,
    this.metricaMdtr,
    this.substituicaoTributaria,
  });

  SkusModel.fromJson(Map<String, dynamic> json) {
    idProduto = json['IdProduto'];
    ean = json['Ean'];

    desconto = DynamicExtensions.toCastDouble(json['Desconto']);

    descricao = json['Descricao'];
    laboratorio = json['Laboratorio'];
    idFamilia = json['IdFamilia'];
    familia = json['Familia'];
    preco = DynamicExtensions.toCastDouble(json['Preco']); //json['Preco'];
    quantidadeMinima = json['QuantidadeMinima'];
    if (json['FiltrosPersonalizados'] != null) {
      filtrosPersonalizados = <FiltrosPersonalizadosModel>[];
      json['FiltrosPersonalizados'].forEach((v) {
        filtrosPersonalizados!.add(FiltrosPersonalizadosModel.fromJson(v));
      });
    }
    destaque = json['Destaque'];
    status = json['Status'];
    if (json['FaixasDesconto'] != null) {
      faixasDesconto = <FaixasDescontoModel>[];
      json['FaixasDesconto'].forEach((v) {
        faixasDesconto!.add(FaixasDescontoModel.fromJson(v));
      });
    }
    menorDataVigencia =
        json['MenorDataVigencia'] != null
            ? DateTime.parse(json['MenorDataVigencia'])
            : null;
    quantidadeEstoque = json['QuantidadeEstoque'];
    precoDistribuidor = json['PrecoDistribuidor'];
    quantidade = json['Quantidade'];
    caminhoFoto = json['CaminhoFoto'];
    idTipoProduto = json['IdTipoProduto'];
    isDemonstraGridPedido = json['IsDemonstraGridPedido'];
    concedeDesconto = json['ConcedeDesconto'];
    recebeDesconto = json['RecebeDesconto'];
    precoMinimo = DynamicExtensions.toCastDouble(
      json['PrecoMinimo'],
    ); //json['PrecoMinimo'];
    if (json['DescontoPorVolumeBD'] != null) {
      descontoPorVolumeBD = <DescontoPorVolumeBD>[];
      json['DescontoPorVolumeBD'].forEach((v) {
        descontoPorVolumeBD!.add(DescontoPorVolumeBD.fromJson(v));
      });
    }
    metricaMdtr =
        json['MetricaMdtr'] != null
            ? MetricaMdtr.fromJson(json['MetricaMdtr'])
            : null;
    substituicaoTributaria = json['SubstituicaoTributaria'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdProduto'] = idProduto;
    data['Ean'] = ean;
    data['Desconto'] = desconto;
    data['Descricao'] = descricao;
    data['Laboratorio'] = laboratorio;
    data['IdFamilia'] = idFamilia;
    data['Familia'] = familia;
    data['Preco'] = preco;
    data['QuantidadeMinima'] = quantidadeMinima;
    if (filtrosPersonalizados != null) {
      data['FiltrosPersonalizados'] =
          filtrosPersonalizados!.map((v) => v.toJson()).toList();
    }
    data['Destaque'] = destaque;
    data['Status'] = status;
    if (faixasDesconto != null) {
      data['FaixasDesconto'] = faixasDesconto!.map((v) => v.toJson()).toList();
    }
    data['MenorDataVigencia'] = menorDataVigencia?.toIso8601String();
    data['QuantidadeEstoque'] = quantidadeEstoque;
    data['PrecoDistribuidor'] = precoDistribuidor;
    data['Quantidade'] = quantidade;
    data['CaminhoFoto'] = caminhoFoto;
    data['IdTipoProduto'] = idTipoProduto;
    data['IsDemonstraGridPedido'] = isDemonstraGridPedido;
    data['ConcedeDesconto'] = concedeDesconto;
    data['RecebeDesconto'] = recebeDesconto;
    data['PrecoMinimo'] = precoMinimo;
    if (descontoPorVolumeBD != null) {
      data['DescontoPorVolumeBD'] =
          descontoPorVolumeBD!.map((v) => v.toJson()).toList();
    }
    if (metricaMdtr != null) {
      data['MetricaMdtr'] = metricaMdtr!.toJson();
    }
    data['SubstituicaoTributaria'] = substituicaoTributaria;
    return data;
  }
}

class FiltrosPersonalizadosModel {
  int? idFiltroPersonalizado;
  String? descricao;
  String? descricaoCategoria;
  String? codigo;
  int? idCategoriaFiltroPersonalizado;
  bool? ativo;
  String? icone;
  int? idProduto;

  FiltrosPersonalizadosModel({
    this.idFiltroPersonalizado,
    this.descricao,
    this.descricaoCategoria,
    this.codigo,
    this.idCategoriaFiltroPersonalizado,
    this.ativo,
    this.icone,
    this.idProduto,
  });

  FiltrosPersonalizadosModel.fromJson(Map<String, dynamic> json) {
    idFiltroPersonalizado = json['IdFiltroPersonalizado'];
    descricao = json['Descricao'];
    descricaoCategoria = json['DescricaoCategoria'];
    codigo = json['Codigo'];
    idCategoriaFiltroPersonalizado = json['IdCategoriaFiltroPersonalizado'];
    ativo = json['Ativo'];
    icone = json['Icone'];
    idProduto = json['IdProduto'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdFiltroPersonalizado'] = idFiltroPersonalizado;
    data['Descricao'] = descricao;
    data['DescricaoCategoria'] = descricaoCategoria;
    data['Codigo'] = codigo;
    data['IdCategoriaFiltroPersonalizado'] = idCategoriaFiltroPersonalizado;
    data['Ativo'] = ativo;
    data['Icone'] = icone;
    data['IdProduto'] = idProduto;
    return data;
  }
}

class CondicaoComercialModel {
  int? idDescontoBase;
  double? percentualDescontoBase;
  int? idDescontoNegociado;
  double? percentualDescontoNegociado;
  int? idDescontoNegociacao1;
  double? percentualDescontoNegociacao1;
  int? idDescontoNegociacao2;
  double? percentualDescontoNegociacao2;
  int? idDescontoNegociacao3;
  double? percentualDescontoNegociacao3;
  int? idDescontoNegociacao4;
  double? percentualDescontoNegociacao4;
  int? idDescontoCupom;
  double? percentualDescontoCupom;
  double? descontoDisponivel;
  double? descontoGestor;
  double? percentualDescontoOLSpread;

  CondicaoComercialModel({
    this.idDescontoBase,
    this.percentualDescontoBase,
    this.idDescontoNegociado,
    this.percentualDescontoNegociado,
    this.idDescontoNegociacao1,
    this.percentualDescontoNegociacao1,
    this.idDescontoNegociacao2,
    this.percentualDescontoNegociacao2,
    this.idDescontoNegociacao3,
    this.percentualDescontoNegociacao3,
    this.idDescontoNegociacao4,
    this.percentualDescontoNegociacao4,
    this.idDescontoCupom,
    this.percentualDescontoCupom,
    this.descontoDisponivel,
    this.descontoGestor,
    this.percentualDescontoOLSpread,
  });

  CondicaoComercialModel.fromJson(Map<String, dynamic> json) {
    idDescontoBase = json['IdDescontoBase'];
    percentualDescontoBase = DynamicExtensions.toCastDouble(
      json['PercentualDescontoBase'],
    ); //json['PercentualDescontoBase'];
    idDescontoNegociado = json['IdDescontoNegociado'];
    percentualDescontoNegociado = DynamicExtensions.toCastDouble(
      json['PercentualDescontoNegociado'],
    ); //json['PercentualDescontoNegociado'];
    idDescontoNegociacao1 = json['IdDescontoNegociacao1'];
    percentualDescontoNegociacao1 = DynamicExtensions.toCastDouble(
      json['PercentualDescontoNegociacao1'],
    ); //json['PercentualDescontoNegociacao1'];
    idDescontoNegociacao2 = json['IdDescontoNegociacao2'];
    percentualDescontoNegociacao2 = DynamicExtensions.toCastDouble(
      json['PercentualDescontoNegociacao2'],
    ); //json['PercentualDescontoNegociacao2'];
    idDescontoNegociacao3 = json['IdDescontoNegociacao3'];
    percentualDescontoNegociacao3 = DynamicExtensions.toCastDouble(
      json['PercentualDescontoNegociacao3'],
    ); //json['PercentualDescontoNegociacao3'];
    idDescontoNegociacao4 = json['IdDescontoNegociacao4'];
    percentualDescontoNegociacao4 = DynamicExtensions.toCastDouble(
      json['PercentualDescontoNegociacao4'],
    ); //json['PercentualDescontoNegociacao4'];
    idDescontoCupom = json['IdDescontoCupom'];
    percentualDescontoCupom = DynamicExtensions.toCastDouble(
      json['PercentualDescontoCupom'],
    ); // json['PercentualDescontoCupom'];
    descontoDisponivel = DynamicExtensions.toCastDouble(
      json['DescontoDisponivel'],
    ); //json['DescontoDisponivel'];
    descontoGestor = DynamicExtensions.toCastDouble(
      json['DescontoGestor'],
    ); //json['DescontoGestor'];
    percentualDescontoOLSpread = DynamicExtensions.toCastDouble(
      json['PercentualDescontoOLSpread'],
    ); //json['PercentualDescontoOLSpread'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdDescontoBase'] = idDescontoBase;
    data['PercentualDescontoBase'] = percentualDescontoBase;
    data['IdDescontoNegociado'] = idDescontoNegociado;
    data['PercentualDescontoNegociado'] = percentualDescontoNegociado;
    data['IdDescontoNegociacao1'] = idDescontoNegociacao1;
    data['PercentualDescontoNegociacao1'] = percentualDescontoNegociacao1;
    data['IdDescontoNegociacao2'] = idDescontoNegociacao2;
    data['PercentualDescontoNegociacao2'] = percentualDescontoNegociacao2;
    data['IdDescontoNegociacao3'] = idDescontoNegociacao3;
    data['PercentualDescontoNegociacao3'] = percentualDescontoNegociacao3;
    data['IdDescontoNegociacao4'] = idDescontoNegociacao4;
    data['PercentualDescontoNegociacao4'] = percentualDescontoNegociacao4;
    data['IdDescontoCupom'] = idDescontoCupom;
    data['PercentualDescontoCupom'] = percentualDescontoCupom;
    data['DescontoDisponivel'] = descontoDisponivel;
    data['DescontoGestor'] = descontoGestor;
    data['PercentualDescontoOLSpread'] = percentualDescontoOLSpread;
    return data;
  }
}

class DescontoPorVolumeBD {
  double? desconto;
  String? descricao;

  DescontoPorVolumeBD({this.desconto, this.descricao});

  DescontoPorVolumeBD.fromJson(Map<String, dynamic> json) {
    desconto = DynamicExtensions.toCastDouble(
      json['Desconto'],
    ); // json['Desconto'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Desconto'] = desconto;
    data['Descricao'] = descricao;
    return data;
  }
}

class MetricaMdtr {
  String? ean;
  String? dtAno;
  String? dtMes;
  int? qtdObjetivo;
  double? vlrObjetivo;
  int? qtdRealizado;
  double? vlrRealizado;
  double? vlrMedia;
  double? qtdMedia;
  String? dtObjetivo;
  String? dtRealizado;
  String? cnpj;
  MetricaMdtr({
    this.ean,
    this.dtAno,
    this.dtMes,
    this.qtdObjetivo,
    this.vlrObjetivo,
    this.qtdRealizado,
    this.vlrRealizado,
    this.vlrMedia,
    this.qtdMedia,
    this.dtObjetivo,
    this.dtRealizado,
    this.cnpj,
  });

  MetricaMdtr.fromJson(Map<String, dynamic> json) {
    ean = json['Ean'];
    dtAno = json['DtAno'];
    dtMes = json['DtMes'];
    qtdObjetivo = json['QtdObjetivo'];
    vlrObjetivo = DynamicExtensions.toCastDouble(
      json['VlrObjetivo'],
    ); //json['VlrObjetivo'];
    qtdRealizado = json['QtdRealizado'];
    vlrRealizado = DynamicExtensions.toCastDouble(
      json['VlrRealizado'],
    ); //json['VlrRealizado'];
    vlrMedia = DynamicExtensions.toCastDouble(
      json['VlrMedia'],
    ); //json['VlrMedia'];
    qtdMedia = DynamicExtensions.toCastDouble(
      json['QtdMedia'],
    ); //json['QtdMedia'];
    dtObjetivo = json['dtObjetivo'];
    dtRealizado = json['dtRealizado'];
    cnpj = json['cnpj'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Ean'] = ean;
    data['DtAno'] = dtAno;
    data['DtMes'] = dtMes;
    data['QtdObjetivo'] = qtdObjetivo;
    data['VlrObjetivo'] = vlrObjetivo;
    data['QtdRealizado'] = qtdRealizado;
    data['VlrRealizado'] = vlrRealizado;
    data['VlrMedia'] = vlrMedia;
    data['QtdMedia'] = qtdMedia;
    data['dtObjetivo'] = dtObjetivo;
    data['dtRealizado'] = dtRealizado;
    data['cnpj'] = cnpj;
    return data;
  }
}

class TabloideResponseModel {
  int? idProduto;
  String? ean;
  String? descricao;
  String? laboratorio;
  String? familia;
  int? preco;
  bool? destaque;
  String? status;
  List<FaixasDescontoModel>? faixasDesconto;
  String? menorDataVigencia;
  int? quantidadeEstoque;
  bool? precoDistribuidor;
  String? caminhoFoto;
  int? idFamilia;
  int? idTipoProduto;
  String? apresentacaoDUN;
  String? dUN;
  int? idProdutoDUN;
  bool? isDemonstraGridPedido;
  List<FiltrosPersonalizadosModel>? filtrosPersonalizados;
  MetricaMdtr? metricaMdtr;
  int? quantidade;
  int? desconto;

  TabloideResponseModel({
    this.idProduto,
    this.ean,
    this.descricao,
    this.laboratorio,
    this.familia,
    this.preco,
    this.destaque,
    this.status,
    this.faixasDesconto,
    this.menorDataVigencia,
    this.quantidadeEstoque,
    this.precoDistribuidor,
    this.caminhoFoto,
    this.idFamilia,
    this.idTipoProduto,
    this.apresentacaoDUN,
    this.dUN,
    this.idProdutoDUN,
    this.isDemonstraGridPedido,
    this.filtrosPersonalizados,
    this.metricaMdtr,
    this.quantidade,
    this.desconto,
  });

  TabloideResponseModel.fromJson(Map<String, dynamic> json) {
    idProduto = json['IdProduto'];
    ean = json['Ean'];
    descricao = json['Descricao'];
    laboratorio = json['Laboratorio'];
    familia = json['Familia'];
    preco = json['Preco'];
    destaque = json['Destaque'];
    status = json['Status'];
    if (json['FaixasDesconto'] != null) {
      faixasDesconto = <FaixasDescontoModel>[];
      json['FaixasDesconto'].forEach((v) {
        faixasDesconto!.add(FaixasDescontoModel.fromJson(v));
      });
    }
    menorDataVigencia = json['MenorDataVigencia'];
    quantidadeEstoque = json['QuantidadeEstoque'];
    precoDistribuidor = json['PrecoDistribuidor'];
    caminhoFoto = json['CaminhoFoto'];
    idFamilia = json['IdFamilia'];
    idTipoProduto = json['IdTipoProduto'];
    apresentacaoDUN = json['ApresentacaoDUN'];
    dUN = json['DUN'];
    idProdutoDUN = json['IdProdutoDUN'];
    isDemonstraGridPedido = json['IsDemonstraGridPedido'];
    if (json['FiltrosPersonalizados'] != null) {
      filtrosPersonalizados = <FiltrosPersonalizadosModel>[];
      json['FiltrosPersonalizados'].forEach((v) {
        filtrosPersonalizados!.add(FiltrosPersonalizadosModel.fromJson(v));
      });
    }
    metricaMdtr =
        json['MetricaMdtr'] != null
            ? MetricaMdtr.fromJson(json['MetricaMdtr'])
            : null;
    quantidade = json['Quantidade'];
    desconto = json['Desconto'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdProduto'] = idProduto;
    data['Ean'] = ean;
    data['Descricao'] = descricao;
    data['Laboratorio'] = laboratorio;
    data['Familia'] = familia;
    data['Preco'] = preco;
    data['Destaque'] = destaque;
    data['Status'] = status;
    if (faixasDesconto != null) {
      data['FaixasDesconto'] = faixasDesconto!.map((v) => v.toJson()).toList();
    }
    data['MenorDataVigencia'] = menorDataVigencia;
    data['QuantidadeEstoque'] = quantidadeEstoque;
    data['PrecoDistribuidor'] = precoDistribuidor;
    data['CaminhoFoto'] = caminhoFoto;
    data['IdFamilia'] = idFamilia;
    data['IdTipoProduto'] = idTipoProduto;
    data['ApresentacaoDUN'] = apresentacaoDUN;
    data['DUN'] = dUN;
    data['IdProdutoDUN'] = idProdutoDUN;
    data['IsDemonstraGridPedido'] = isDemonstraGridPedido;
    if (filtrosPersonalizados != null) {
      data['FiltrosPersonalizados'] =
          filtrosPersonalizados!.map((v) => v.toJson()).toList();
    }
    if (metricaMdtr != null) {
      data['MetricaMdtr'] = metricaMdtr!.toJson();
    }
    data['Quantidade'] = quantidade;
    data['Desconto'] = desconto;
    return data;
  }
}

class FaixasDescontoModel {
  int? quantidadeMinima;
  int? quantidadeMaxima;
  int? descontoMinimo;
  int? descontoMaximo;
  CondicaoComercialModel? condicaoComercial;
  FaixasDescontoModel({
    this.quantidadeMinima,
    this.quantidadeMaxima,
    this.descontoMinimo,
    this.descontoMaximo,
    this.condicaoComercial,
  });

  FaixasDescontoModel.fromJson(Map<String, dynamic> json) {
    quantidadeMinima = json['QuantidadeMinima'];
    quantidadeMaxima = json['QuantidadeMaxima'];
    descontoMinimo = json['DescontoMinimo'];
    descontoMaximo = json['DescontoMaximo'];

    condicaoComercial =
        json['CondicaoComercial'] != null
            ? CondicaoComercialModel.fromJson(json['CondicaoComercial'])
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['QuantidadeMinima'] = quantidadeMinima;
    data['QuantidadeMaxima'] = quantidadeMaxima;
    data['DescontoMinimo'] = descontoMinimo;
    data['DescontoMaximo'] = descontoMaximo;
    if (condicaoComercial != null) {
      data['CondicaoComercial'] = condicaoComercial!.toJson();
    }
    return data;
  }
}

class CombosOfertaModel {
  int? idComboOferta;
  String? descricao;
  String? status;
  DateTime? menorDataVigencia;
  List<ProdutosOfertasModel>? produtos;
  double? precoCombo;
  double? precoComboLiquido;
  String? caminhoFoto;
  int? tipoAgrupamentoLojas;
  int? orderComboQuantity; // QtdComboPedido
  int? validityComboQuantity; // QtdComboNaVigencia
  bool? locked;
  int? qtdy;
  int? qtdyReal;
  ProductStateEnum? states;
  int? distributorId;
  int? paymentTermId;
  double? priceOrder;
  double? totalOrder;
  TextEditingController? qtdyController;
  FocusNode? focusNode;
  bool? orderComboLimit; // LimitarComboPedido
  bool? validityComboLimit; // LimitarComboNaVigencia

  CombosOfertaModel({
    this.idComboOferta,
    this.descricao,
    this.status,
    this.menorDataVigencia,
    this.produtos,
    this.precoCombo,
    this.precoComboLiquido,
    this.caminhoFoto,
    this.tipoAgrupamentoLojas,
    this.locked,
    this.qtdy,
    this.qtdyReal,
    this.qtdyController,
    this.focusNode,
    this.states,
    this.distributorId,
    this.priceOrder,
    this.totalOrder,
    this.paymentTermId,
    this.orderComboQuantity,
    this.validityComboQuantity,
    this.orderComboLimit,
    this.validityComboLimit,
  });

  CombosOfertaModel.fromJson(Map<String, dynamic> json) {
    idComboOferta = json['IdComboOferta'];
    descricao = json['Descricao'];
    status = json['Status'];
    menorDataVigencia =
        json['MenorDataVigencia'] != null
            ? DateTime.parse(json['MenorDataVigencia'])
            : null;
    if (json['Produtos'] != null) {
      produtos = <ProdutosOfertasModel>[];
      json['Produtos'].forEach((v) {
        produtos!.add(ProdutosOfertasModel.fromJson(v));
      });
    }
    precoCombo = DynamicExtensions.toCastDouble(
      json['PrecoCombo'],
    ); //json['PrecoCombo'];
    precoComboLiquido = DynamicExtensions.toCastDouble(
      json['PrecoComboLiquido'],
    ); //json['PrecoComboLiquido'];
    caminhoFoto = json['CaminhoFoto'];
    // tipoAgrupamentoLojas = json['TipoAgrupamentoLojas'];
    locked = json['Locked'];
    qtdy = json['qtdy'] ?? 0;
    qtdyReal = json['qtdyReal'] ?? 0;
    distributorId = json['distributorId'];
    paymentTermId = json['paymentTermId'];
    priceOrder = json['priceOrder'] ?? 0;
    totalOrder = json['totalOrder'] ?? 0;
    orderComboQuantity = json['QtdComboPedido'];
    validityComboQuantity = json['QtdComboNaVigencia'];
    orderComboLimit = json['LimitarComboPedido'];
    validityComboLimit = json['LimitarComboNaVigencia'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdComboOferta'] = idComboOferta;
    data['Descricao'] = descricao;
    data['Status'] = status;
    data['MenorDataVigencia'] = menorDataVigencia?.toIso8601String();
    if (produtos != null) {
      data['Produtos'] = produtos!.map((v) => v.toJson()).toList();
    }
    data['PrecoCombo'] = precoCombo;
    data['PrecoComboLiquido'] = precoComboLiquido;
    data['CaminhoFoto'] = caminhoFoto;
    // data['TipoAgrupamentoLojas'] = tipoAgrupamentoLojas;
    data['Locked'] = locked;
    data['qtdy'] = qtdy;
    data['qtdyReal'] = qtdyReal;
    data['distributorId'] = distributorId;
    data['paymentTermId'] = paymentTermId;
    data['priceOrder'] = priceOrder;
    data['totalOrder'] = totalOrder;
    data['QtdComboPedido'] = orderComboQuantity;
    data['QtdComboNaVigencia'] = validityComboQuantity;
    data['LimitarComboPedido'] = orderComboLimit;
    data['LimitarComboNaVigencia'] = validityComboLimit;
    return data;
  }
}

class ProdutosOfertasModel {
  int? idProduto;
  int? idProdutoDUN;
  String? ean;
  String? dUN;
  String? descricao;
  String? laboratorio;
  String? familia;
  double? preco;
  int? quantidade;
  int? quantidadeDUN;
  double? desconto;
  bool? bonificado;
  double? precoLiquido;
  bool? precoDistribuidor;
  ProductStateEnum? states;
  int? distributorId;
  int? paymentTermId;
  ProdutosOfertasModel({
    this.idProduto,
    this.idProdutoDUN,
    this.ean,
    this.dUN,
    this.descricao,
    this.laboratorio,
    this.familia,
    this.preco,
    this.quantidade,
    this.quantidadeDUN,
    this.desconto,
    this.bonificado,
    this.precoLiquido,
    this.precoDistribuidor,
    this.states,
    this.distributorId,
    this.paymentTermId,
  });

  ProdutosOfertasModel.fromJson(Map<String, dynamic> json) {
    idProduto = json['IdProduto'];
    idProdutoDUN = json['IdProdutoDUN'];
    ean = json['Ean'];
    dUN = json['DUN'];
    descricao = json['Descricao'];
    laboratorio = json['Laboratorio'];
    familia = json['Familia'];
    preco = DynamicExtensions.toCastDouble(json['Preco']); //json['Preco'];
    quantidade = json['Quantidade'];
    quantidadeDUN = json['QuantidadeDUN'];
    desconto = DynamicExtensions.toCastDouble(
      json['Desconto'],
    ); //json['Desconto'];
    bonificado = json['Bonificado'];
    precoLiquido = DynamicExtensions.toCastDouble(
      json['PrecoLiquido'],
    ); //json['PrecoLiquido'];
    precoDistribuidor = json['PrecoDistribuidor'];
    distributorId = json['distributorId'];
    paymentTermId = json['paymentTermId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdProduto'] = idProduto;
    data['IdProdutoDUN'] = idProdutoDUN;
    data['Ean'] = ean;
    data['DUN'] = dUN;
    data['Descricao'] = descricao;
    data['Laboratorio'] = laboratorio;
    data['Familia'] = familia;
    data['Preco'] = preco;
    data['Quantidade'] = quantidade;
    data['QuantidadeDUN'] = quantidadeDUN;
    data['Desconto'] = desconto;
    data['Bonificado'] = bonificado;
    data['PrecoLiquido'] = precoLiquido;
    data['PrecoDistribuidor'] = precoDistribuidor;
    data['distributorId'] = distributorId;
    data['paymentTermId'] = paymentTermId;
    return data;
  }
}

class FamiliasProdutosModel {
  int? idFamilia;
  String? descricao;
  String? caminhoFoto;

  FamiliasProdutosModel({this.idFamilia, this.descricao, this.caminhoFoto});

  FamiliasProdutosModel.fromJson(Map<String, dynamic> json) {
    idFamilia = json['IdFamilia'];
    descricao = json['Descricao'];
    caminhoFoto = json['CaminhoFoto'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdFamilia'] = idFamilia;
    data['Descricao'] = descricao;
    data['CaminhoFoto'] = caminhoFoto;
    return data;
  }
}

class ProdutosDistribuidoresModel {
  int? idProduto;
  int? idPrazoPagamento;
  List<int>? idsDistribuidores;

  ProdutosDistribuidoresModel({
    this.idProduto,
    this.idPrazoPagamento,
    this.idsDistribuidores,
  });

  ProdutosDistribuidoresModel.fromJson(Map<String, dynamic> json) {
    idProduto = json['idProduto'];
    idPrazoPagamento = json['IdPrazoPagamento'];
    idsDistribuidores = json['idsDistribuidores'].cast<int>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idProduto'] = idProduto;
    data['IdPrazoPagamento'] = idPrazoPagamento;
    data['idsDistribuidores'] = idsDistribuidores;
    return data;
  }
}

class ProdutosDUNModel {
  int? idProdutoDUN;
  int? idProduto;
  String? dUN;
  String? apresentacao;
  int? quantidade;
  String? foto;
  String? arquivo;
  String? dataCadastro;
  String? dataAtualizacao;
  bool? isDeleted;
  bool? isDemonstraGridPedido;
  bool? isPedidoEspecial;
  bool? destaque;
  String? status;
  List<FiltrosPersonalizadosModel>? filtrosPersonalizados;

  ProdutosDUNModel({
    this.idProdutoDUN,
    this.idProduto,
    this.dUN,
    this.apresentacao,
    this.quantidade,
    this.foto,
    this.arquivo,
    this.dataCadastro,
    this.dataAtualizacao,
    this.isDeleted,
    this.isDemonstraGridPedido,
    this.isPedidoEspecial,
    this.destaque,
    this.status,
    this.filtrosPersonalizados,
  });

  ProdutosDUNModel.fromJson(Map<String, dynamic> json) {
    idProdutoDUN = json['idProdutoDUN'];
    idProduto = json['idProduto'];
    dUN = json['DUN'];
    apresentacao = json['Apresentacao'];
    quantidade = json['Quantidade'];
    foto = json['Foto'];
    arquivo = json['Arquivo'];
    dataCadastro = json['DataCadastro'];
    dataAtualizacao = json['DataAtualizacao'];
    isDeleted = json['isDeleted'];
    isDemonstraGridPedido = json['isDemonstraGridPedido'];
    isPedidoEspecial = json['isPedidoEspecial'];
    destaque = json['Destaque'];
    status = json['Status'];
    if (json['FiltrosPersonalizados'] != null) {
      filtrosPersonalizados = <FiltrosPersonalizadosModel>[];
      json['FiltrosPersonalizados'].forEach((v) {
        filtrosPersonalizados!.add(FiltrosPersonalizadosModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idProdutoDUN'] = idProdutoDUN;
    data['idProduto'] = idProduto;
    data['DUN'] = dUN;
    data['Apresentacao'] = apresentacao;
    data['Quantidade'] = quantidade;
    data['Foto'] = foto;
    data['Arquivo'] = arquivo;
    data['DataCadastro'] = dataCadastro;
    data['DataAtualizacao'] = dataAtualizacao;
    data['isDeleted'] = isDeleted;
    data['isDemonstraGridPedido'] = isDemonstraGridPedido;
    data['isPedidoEspecial'] = isPedidoEspecial;
    data['Destaque'] = destaque;
    data['Status'] = status;
    if (filtrosPersonalizados != null) {
      data['FiltrosPersonalizados'] =
          filtrosPersonalizados!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ProdutosDUNDistribuidorModel {
  int? idProdutoDUN;
  List<int>? idsDistribuidores;

  ProdutosDUNDistribuidorModel({this.idProdutoDUN, this.idsDistribuidores});

  ProdutosDUNDistribuidorModel.fromJson(Map<String, dynamic> json) {
    idProdutoDUN = json['idProdutoDUN'];
    idsDistribuidores = json['idsDistribuidores'].cast<int>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idProdutoDUN'] = idProdutoDUN;
    data['idsDistribuidores'] = idsDistribuidores;
    return data;
  }
}
