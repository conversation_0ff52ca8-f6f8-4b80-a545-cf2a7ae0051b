import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:path_provider/path_provider.dart';

class JsonStorage {
  static Future<String> get _localPath async {
    final directory = await getApplicationDocumentsDirectory();
    return directory.path;
  }

  static Future<File> _localFile(String fileName) async {
    final path = await _localPath;
    return File('$path/$fileName.json');
  }

  static Future<void> save<T>(T data, String fileName) async {
    try {
      final file = await _localFile(fileName);
      final jsonString = jsonEncode(data);
      await file.writeAsString(jsonString);
      log('Data saved successfully to $fileName.json');
    } catch (e) {
      log('Error saving data to file: $e');
      rethrow;
    }
  }

  static Future<T> read<T>(
      String fileName, T Function(Map<String, dynamic>) fromJson) async {
    try {
      final file = await _localFile(fileName);
      if (await file.exists()) {
        final jsonString = await file.readAsString();
        final jsonMap = jsonDecode(jsonString);
        return fromJson(jsonMap);
      } else {
        throw FileSystemException('File $fileName.json does not exist');
      }
    } catch (e) {
      log('Error reading data from file: $e');
      rethrow;
    }
  }

  static Future<List<T>> readList<T>(
      String fileName, T Function(Map<String, dynamic>) fromJson) async {
    try {
      final file = await _localFile(fileName);
      if (await file.exists()) {
        final jsonString = await file.readAsString();
        final jsonList = jsonDecode(jsonString) as List;
        return jsonList.map((item) => fromJson(item)).toList();
      } else {
        throw FileSystemException('File $fileName.json does not exist');
      }
    } catch (e) {
      log('Error reading list data from file: $e');
      rethrow;
    }
  }

  static Future<bool> exists(String fileName) async {
    final file = await _localFile(fileName);
    return file.exists();
  }

  static Future<void> delete(String fileName) async {
    try {
      final file = await _localFile(fileName);
      if (await file.exists()) {
        await file.delete();
        log('File $fileName.json deleted successfully');
      }
    } catch (e) {
      log('Error deleting file: $e');
      rethrow;
    }
  }

  static Future<void> cleanupOldFiles() async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final cutoffDate = DateTime.now().subtract(const Duration(hours: 24));

      final entities = directory.listSync();

      for (var entity in entities) {
        if (entity is File && entity.path.toLowerCase().endsWith('.json')) {
          final lastModified = entity.lastModifiedSync();
          if (lastModified.isBefore(cutoffDate)) {
            await entity.delete();
            log('Deleted old file: ${entity.path}');
          }
        }
      }

      log('Cleanup completed successfully');
    } catch (e) {
      log('Error during cleanup: $e');
      rethrow;
    }
  }

  // Método auxiliar para obter a data de criação de um arquivo
  static DateTime getFileCreationDate(File file) {
    return file.statSync().changed;
  }
}
