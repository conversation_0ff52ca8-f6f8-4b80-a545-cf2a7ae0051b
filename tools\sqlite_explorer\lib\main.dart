import 'dart:io';

import 'package:flutter/material.dart';
import 'package:sqlite_explorer/app/app_exports.dart';
import 'package:sqlite_explorer/config/databases/database_init.dart';
import 'package:sqlite_explorer/config/dependency_injection.dart';
import 'package:window_manager/window_manager.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (Platform.isWindows || Platform.isLinux || Platform.isMacOS) {
    // Verifique se é desktop
    await windowManager.ensureInitialized();
    windowManager.setTitle('SQLite Explorer - Pharmalink');
  }

  DependencyInjection.init();
  await initialConfig();

  SqfLiteHub.init();
  runApp(const MyAppPL());
}
