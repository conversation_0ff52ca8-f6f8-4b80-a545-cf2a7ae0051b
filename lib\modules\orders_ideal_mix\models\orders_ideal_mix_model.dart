import 'package:pharmalink/exports/basic_exports.dart';

class MixIdealModel {
  bool? descontoCumulativo;
  List<MixIdealCondicoesModel>? condicoes;

  MixIdealModel({this.descontoCumulativo, this.condicoes});

  MixIdealModel.fromJson(Map<String, dynamic> json) {
    descontoCumulativo = json['DescontoCumulativo'];
    if (json['Condicoes'] != null) {
      condicoes = <MixIdealCondicoesModel>[];
      json['Condicoes'].forEach((v) {
        condicoes!.add(MixIdealCondicoesModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['DescontoCumulativo'] = descontoCumulativo;
    if (condicoes != null) {
      data['Condicoes'] = condicoes!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class MixIdealCondicoesModel {
  int? idCondicao;
  String? codigo;
  String? descricao;
  String? dataInicioVigencia;
  String? dataFimVigencia;
  int? tipoAgrupamentoLojas;
  bool? possuiQuantidadeMaximaMix;
  bool? possuiQuantidadeMaximaCondicao;
  int? quantidadeMaximaMix;
  int? quantidadeMaximaCondicao;

  int? qtdySelected;
  int? qtdyCondicaoSelected;

  List<MixIdealProdutosMixModel>? produtosMix;
  List<MixIdealProdutosCondicaoModel>? produtosCondicao;

  MixIdealCondicoesModel({
    this.idCondicao,
    this.codigo,
    this.descricao,
    this.dataInicioVigencia,
    this.dataFimVigencia,
    this.tipoAgrupamentoLojas,
    this.possuiQuantidadeMaximaMix,
    this.possuiQuantidadeMaximaCondicao,
    this.quantidadeMaximaMix,
    this.quantidadeMaximaCondicao,
    this.produtosMix,
    this.produtosCondicao,
    this.qtdySelected,
    this.qtdyCondicaoSelected,
  });

  MixIdealCondicoesModel.fromJson(Map<String, dynamic> json) {
    idCondicao = json['IdCondicao'];
    codigo = json['Codigo'];
    descricao = json['Descricao'];
    dataInicioVigencia = json['DataInicioVigencia'];
    dataFimVigencia = json['DataFimVigencia'];
    // tipoAgrupamentoLojas = json['TipoAgrupamentoLojas'];
    possuiQuantidadeMaximaMix = json['PossuiQuantidadeMaximaMix'];
    possuiQuantidadeMaximaCondicao = json['PossuiQuantidadeMaximaCondicao'];
    quantidadeMaximaMix = json['QuantidadeMaximaMix'];
    quantidadeMaximaCondicao = json['QuantidadeMaximaCondicao'];
    if (json['ProdutosMix'] != null) {
      produtosMix = <MixIdealProdutosMixModel>[];
      json['ProdutosMix'].forEach((v) {
        produtosMix!.add(MixIdealProdutosMixModel.fromJson(v));
      });
    }
    if (json['ProdutosCondicao'] != null) {
      produtosCondicao = <MixIdealProdutosCondicaoModel>[];
      json['ProdutosCondicao'].forEach((v) {
        produtosCondicao!.add(MixIdealProdutosCondicaoModel.fromJson(v));
      });
    }
    //Not Mapped
    qtdySelected = json['qtdySelected1'] ?? 0;
    qtdyCondicaoSelected = json['qtdyCondicaoSelected'] ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdCondicao'] = idCondicao;
    data['Codigo'] = codigo;
    data['Descricao'] = descricao;
    data['DataInicioVigencia'] = dataInicioVigencia;
    data['DataFimVigencia'] = dataFimVigencia;
    // data['TipoAgrupamentoLojas'] = tipoAgrupamentoLojas;
    data['PossuiQuantidadeMaximaMix'] = possuiQuantidadeMaximaMix;
    data['PossuiQuantidadeMaximaCondicao'] = possuiQuantidadeMaximaCondicao;
    data['QuantidadeMaximaMix'] = quantidadeMaximaMix;
    data['QuantidadeMaximaCondicao'] = quantidadeMaximaCondicao;
    if (produtosMix != null) {
      data['ProdutosMix'] = produtosMix!.map((v) => v.toJson()).toList();
    }
    if (produtosCondicao != null) {
      data['ProdutosCondicao'] =
          produtosCondicao!.map((v) => v.toJson()).toList();
    }

    data['qtdySelected'] = qtdySelected;
    data['qtdyCondicaoSelected'] = qtdyCondicaoSelected;
    return data;
  }
}

class MixIdealProdutosMixModel {
  int? idFamilia;
  String? descricaoFamilia;
  double? desconto;
  int? quantidadeMinima;
  int? quantidadeMaxima;
  int? qtdySelected;
  bool? porFamilia;
  List<MixIdealProdutosModel>? produtos;

  MixIdealProdutosMixModel({
    this.idFamilia,
    this.descricaoFamilia,
    this.desconto,
    this.quantidadeMinima,
    this.quantidadeMaxima,
    this.qtdySelected,
    this.porFamilia,
    this.produtos,
  });

  MixIdealProdutosMixModel.fromJson(Map<String, dynamic> json) {
    idFamilia = json['IdFamilia'];
    descricaoFamilia = json['DescricaoFamilia'];
    desconto = json['Desconto'];
    quantidadeMinima = json['QuantidadeMinima'];
    quantidadeMaxima = json['QuantidadeMaxima'];
    qtdySelected = json['qtdySelected'] ?? 0;

    porFamilia = json['PorFamilia'];
    if (json['Produtos'] != null) {
      produtos = <MixIdealProdutosModel>[];
      json['Produtos'].forEach((v) {
        produtos!.add(MixIdealProdutosModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdFamilia'] = idFamilia;
    data['DescricaoFamilia'] = descricaoFamilia;
    data['Desconto'] = desconto;
    data['qtdySelected'] = qtdySelected;
    data['QuantidadeMinima'] = quantidadeMinima;
    data['QuantidadeMaxima'] = quantidadeMaxima;

    data['PorFamilia'] = porFamilia;
    if (produtos != null) {
      data['Produtos'] = produtos!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class MixIdealProdutosModel {
  int? idProduto;
  String? descricaoProduto;
  String? eAN;
  double? desconto;
  int? quantidadeMinima;
  int? quantidadeMaxima;
  String? dUN;
  int? quantidadeDUN;
  bool? usandoDUN;
  String? familyName;

  TextEditingController? qtdyController;
  FocusNode? focusNode;
  bool? isEditing;

  int? qtdySelected;
  MixIdealProdutosModel({
    this.idProduto,
    this.descricaoProduto,
    this.eAN,
    this.desconto,
    this.quantidadeMinima,
    this.quantidadeMaxima,
    this.dUN,
    this.quantidadeDUN,
    this.usandoDUN,
    this.qtdySelected,
    this.familyName,
    this.focusNode,
    this.qtdyController,
    this.isEditing,
  });

  MixIdealProdutosModel.fromJson(Map<String, dynamic> json) {
    idProduto = json['IdProduto'];
    descricaoProduto = json['DescricaoProduto'];
    eAN = json['EAN'];
    desconto = json['Desconto'];
    quantidadeMinima = json['QuantidadeMinima'];
    quantidadeMaxima = json['QuantidadeMaxima'];
    dUN = json['DUN'];
    quantidadeDUN = json['QuantidadeDUN'];
    usandoDUN = json['UsandoDUN'];
    qtdySelected = json['qtdySelected'] ?? 0;
    familyName = json['familyName'];
    focusNode = FocusNode();
    qtdyController =
        TextEditingController(text: json['qtdySelected']?.toString() ?? "0");
    isEditing = json['isEditing'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdProduto'] = idProduto;
    data['DescricaoProduto'] = descricaoProduto;
    data['EAN'] = eAN;
    data['Desconto'] = desconto;
    data['QuantidadeMinima'] = quantidadeMinima;
    data['QuantidadeMaxima'] = quantidadeMaxima;
    data['DUN'] = dUN;
    data['QuantidadeDUN'] = quantidadeDUN;
    data['UsandoDUN'] = usandoDUN;
    data['qtdySelected'] = qtdySelected;
    data['familyName'] = familyName;

    return data;
  }

  MixIdealProdutosModel copyWith({
    int? idProduto,
    String? descricaoProduto,
    String? eAN,
    double? desconto,
    int? quantidadeMinima,
    int? quantidadeMaxima,
    String? dUN,
    int? quantidadeDUN,
    bool? usandoDUN,
    int? qtdySelected,
    String? familyName,
  }) {
    return MixIdealProdutosModel(
        idProduto: idProduto ?? this.idProduto,
        descricaoProduto: descricaoProduto ?? this.descricaoProduto,
        eAN: eAN ?? this.eAN,
        desconto: desconto ?? this.desconto,
        quantidadeMinima: quantidadeMinima ?? this.quantidadeMinima,
        quantidadeMaxima: quantidadeMaxima ?? this.quantidadeMaxima,
        dUN: dUN ?? this.dUN,
        quantidadeDUN: quantidadeDUN ?? this.quantidadeDUN,
        usandoDUN: usandoDUN ?? this.usandoDUN,
        qtdySelected: qtdySelected ?? this.qtdySelected,
        familyName: familyName ?? this.familyName);
  }
}

class MixIdealProdutosCondicaoModel {
  int? idFamilia;
  String? descricaoFamilia;
  double? desconto;
  bool? porFamilia;
  int? quantidadeMaxima;
  int? qtdySelected;
  List<MixIdealProdutosModel>? produtos;

  MixIdealProdutosCondicaoModel(
      {this.idFamilia,
      this.descricaoFamilia,
      this.desconto,
      this.porFamilia,
      this.quantidadeMaxima,
      this.qtdySelected,
      this.produtos});

  MixIdealProdutosCondicaoModel.fromJson(Map<String, dynamic> json) {
    idFamilia = json['IdFamilia'];
    descricaoFamilia = json['DescricaoFamilia'];
    desconto = json['Desconto'];
    porFamilia = json['PorFamilia'];
    quantidadeMaxima = json['QuantidadeMaxima'];
    qtdySelected = json['qtdySelected'] ?? 0;
    if (json['Produtos'] != null) {
      produtos = <MixIdealProdutosModel>[];
      json['Produtos'].forEach((v) {
        produtos!.add(MixIdealProdutosModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdFamilia'] = idFamilia;
    data['DescricaoFamilia'] = descricaoFamilia;
    data['Desconto'] = desconto;
    data['PorFamilia'] = porFamilia;
    data['QuantidadeMaxima'] = quantidadeMaxima;
    data['qtdySelected'] = qtdySelected;
    if (produtos != null) {
      data['Produtos'] = produtos!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
