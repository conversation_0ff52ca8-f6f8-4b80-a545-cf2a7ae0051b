import 'package:flutter/widgets.dart';
import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';

class ResearchesTradeMarketingDataModel
    extends SqfLiteBase<ResearchesTradeMarketingDataModel> {
  int? workspaceId;
  int? storeId;
  int? isSync;
  String? cnpj;
  int? idLoja;
  String? idSurvey;
  String? surveyVersion;
  List<ResearchesTradeMarketingSteps>? steps;
  List<ResearchesTradeMarketingImages>? images;

  ResearchesTradeMarketingDataModel(
      {this.workspaceId,
      this.storeId,
      this.isSync,
      this.cnpj,
      this.idLoja,
      this.idSurvey,
      this.surveyVersion,
      this.steps,
      this.images})
      : super(DatabaseModels.researchesTradeMarketingModel);

  ResearchesTradeMarketingDataModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.researchesTradeMarketingModel) {
    workspaceId = json['workspaceId'];
    storeId = json['storeId'];
    isSync = json['isSync'] ?? SyncEnum.none;
    cnpj = json['cnpj'];
    idLoja = json['idLoja'];
    idSurvey = json['idSurvey'];
    surveyVersion = json['surveyVersion'];
    if (json['steps'] != null) {
      steps = <ResearchesTradeMarketingSteps>[];
      json['steps'].forEach((v) {
        steps!.add(ResearchesTradeMarketingSteps.fromJson(v));
      });
    }
    if (json['images'] != null) {
      images = <ResearchesTradeMarketingImages>[];
      json['images'].forEach((v) {
        images!.add(ResearchesTradeMarketingImages.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workspaceId'] = workspaceId;
    data['storeId'] = storeId;
    data['isSync'] = isSync;
    data['cnpj'] = cnpj;
    data['idLoja'] = idLoja;
    data['idSurvey'] = idSurvey;
    data['surveyVersion'] = surveyVersion;
    if (steps != null) {
      data['steps'] = steps!.map((v) => v.toJson()).toList();
    }
    if (images != null) {
      data['images'] = images!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  Future<ResearchesTradeMarketingDataModel> getFirst() async {
    var list = await getAll<ResearchesTradeMarketingDataModel>(
        workspaceId: appController.workspace!.workspaceId!,
        ResearchesTradeMarketingDataModel.fromJson);
    return list.first;
  }

  Future<List<ResearchesTradeMarketingDataModel>> getList() async {
    var list = await getAll<ResearchesTradeMarketingDataModel>(
        workspaceId: appController.workspace!.workspaceId!,
        ResearchesTradeMarketingDataModel.fromJson);
    return list;
  }

  Future<List<ResearchesTradeMarketingDataModel>> getListByStore(
      {required int storeId}) async {
    var list = await getAll<ResearchesTradeMarketingDataModel>(
        workspaceId: appController.workspace!.workspaceId!,
        storeId: storeId,
        ResearchesTradeMarketingDataModel.fromJson);
    return list;
  }

  Future<List<ResearchesTradeMarketingDataModel>> getListToSync(
      {required int workspaceId}) async {
    var list = await getAll<ResearchesTradeMarketingDataModel>(
        workspaceId: workspaceId, ResearchesTradeMarketingDataModel.fromJson);
    return (list.isNotEmpty &&
            list.any((element) =>
                element.isSync != null && element.isSync == SyncEnum.awaited))
        ? list
            .where((element) =>
                element.isSync != null && element.isSync == SyncEnum.awaited)
            .toList()
        : [];
  }
}

class ResearchesTradeMarketingSteps {
  String? stepIdentifier;
  int? idRota;
  String? stepName;
  int? stepIndex;
  bool? readonly;
  int? imageNumMin;
  int? imageNumMax;
  List<ResearchesTradeMarketingQuestions>? questions;
  List<ResearchesTradeMarketingAnswersSubmitted>? answersSubmitted;

  ResearchesTradeMarketingSteps(
      {this.stepIdentifier,
      this.idRota,
      this.stepName,
      this.stepIndex,
      this.readonly,
      this.imageNumMin,
      this.imageNumMax,
      this.questions,
      this.answersSubmitted});

  ResearchesTradeMarketingSteps.fromJson(Map<String, dynamic> json) {
    stepIdentifier = json['stepIdentifier'];
    idRota = json['idRota'];
    stepName = json['stepName'];
    stepIndex = json['stepIndex'];
    readonly = json['readonly'];
    imageNumMin = json['imageNumMin'];
    imageNumMax = json['imageNumMax'];
    if (json['questions'] != null) {
      questions = <ResearchesTradeMarketingQuestions>[];
      json['questions'].forEach((v) {
        questions!.add(ResearchesTradeMarketingQuestions.fromJson(v));
      });
    }
    if (json['answersSubmitted'] != null) {
      answersSubmitted = <ResearchesTradeMarketingAnswersSubmitted>[];
      json['answersSubmitted'].forEach((v) {
        answersSubmitted!
            .add(ResearchesTradeMarketingAnswersSubmitted.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['stepIdentifier'] = stepIdentifier;
    data['idRota'] = idRota;
    data['stepName'] = stepName;
    data['stepIndex'] = stepIndex;
    data['readonly'] = readonly;
    data['imageNumMin'] = imageNumMin;
    data['imageNumMax'] = imageNumMax;
    if (questions != null) {
      data['questions'] = questions!.map((v) => v.toJson()).toList();
    }
    if (answersSubmitted != null) {
      data['answersSubmitted'] =
          answersSubmitted!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResearchesTradeMarketingQuestions {
  int? questionId;
  int? step;
  String? question;
  String? dataType;
  dynamic answers;
  ResearchesTradeMarketingMetadata? metadata;
  bool? explain;
  String? explainLabel;
  String? explainText;

  ResearchesTradeMarketingQuestions(
      {this.questionId,
      this.step,
      this.question,
      this.dataType,
      this.answers,
      this.metadata,
      this.explain,
      this.explainLabel,
      this.explainText});

  ResearchesTradeMarketingQuestions.fromJson(Map<String, dynamic> json) {
    questionId = json['questionId'];
    step = json['step'];
    question = json['question'];
    dataType = json['dataType'];
    if (json['answers'] != null) {
      var value = json['answers'];
      if (value is List) {
        if (value.isNotEmpty) {
          if (value[0] is int) {
            answers = value.cast<int>();
          } else if (value[0] == null) {
            answers = [];
          } else if (value[0] is String) {
            answers = value.cast<String>();
          } else if (value[0] is double) {
            answers = value.cast<double>();
          } else if (value[0] is Map) {
            answers = value
                .map((v) => ResearchesTradeMarketingAnswers.fromJson(v))
                .toList();
          }
        } else {
          answers = [];
        }
      } else {
        answers = value;
      }
    }
    metadata = json['metadata'] != null
        ? ResearchesTradeMarketingMetadata.fromJson(json['metadata'])
        : null;
    explain = json['explain'];
    explainLabel = json['explainLabel'];
    explainText = json['explainText'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['questionId'] = questionId;
    data['step'] = step;
    data['question'] = question;
    data['dataType'] = dataType;
    if (answers != null) {
      if (answers is List<ResearchesTradeMarketingAnswers>) {
        data['answers'] = (answers as List<ResearchesTradeMarketingAnswers>)
            .map((v) => v.toJson())
            .toList();
      } else {
        data['answers'] = answers;
      }
    } else {
      data['answers'] = [];
    }
    if (metadata != null) {
      data['metadata'] = metadata!.toJson();
    }
    data['explain'] = explain;
    data['explainLabel'] = explainLabel;
    data['explainText'] = explainText;
    return data;
  }
}

class ResearchesTradeMarketingAnswers {
  String? qtd1;
  String? qtd2;
  String? qtd3;
  String? qtd4;

  ResearchesTradeMarketingAnswers({this.qtd1, this.qtd2, this.qtd3, this.qtd4});

  ResearchesTradeMarketingAnswers.fromJson(Map<String, dynamic> json) {
    qtd1 = json['qtd1'];
    qtd2 = json['qtd2'];
    qtd3 = json['qtd3'];
    qtd4 = json['qtd4'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['qtd1'] = qtd1;
    data['qtd2'] = qtd2;
    data['qtd3'] = qtd3;
    data['qtd4'] = qtd4;
    return data;
  }
}

class ResearchesTradeMarketingMetadata {
  bool? required;
  bool? multiple;
  String? placeholder;
  String? trueLabel;
  String? falseLabel;
  List<ResearchesTradeMarketingOptions>? options;
  String? maxLength;
  String? minLength;
  bool? isRequired;
  ResearchesTradeMarketingMetadata(
      {this.required,
      this.isRequired,
      this.multiple,
      this.placeholder,
      this.trueLabel,
      this.falseLabel,
      this.options,
      this.maxLength,
      this.minLength});

  ResearchesTradeMarketingMetadata.fromJson(Map<String, dynamic> json) {
    required = json['required'];
    isRequired = json['isRequired'];
    multiple = json['multiple'];
    placeholder = json['placeholder'];
    trueLabel = json['trueLabel'];
    falseLabel = json['falseLabel'];
    if (json['options'] != null) {
      options = <ResearchesTradeMarketingOptions>[];
      json['options'].forEach((v) {
        options!.add(ResearchesTradeMarketingOptions.fromJson(v));
      });
    }
    maxLength = json['maxLength'];
    minLength = json['minLength'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['required'] = required;
    data['isRequired'] = isRequired;
    data['multiple'] = multiple;
    data['placeholder'] = placeholder;
    data['trueLabel'] = trueLabel;
    data['falseLabel'] = falseLabel;
    if (options != null) {
      data['options'] = options!.map((v) => v.toJson()).toList();
    }
    data['maxLength'] = maxLength;
    data['minLength'] = minLength;
    return data;
  }
}

class ResearchesTradeMarketingOptions {
  String? text;
  int? value;

  ResearchesTradeMarketingOptions({this.text, this.value});

  ResearchesTradeMarketingOptions.fromJson(Map<String, dynamic> json) {
    text = json['text'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['text'] = text;
    data['value'] = value;
    return data;
  }
}

class ResearchesTradeMarketingAnswersSubmitted {
  int? id;
  String? stepId;
  String? question;
  String? dataType;
  List<String>? answers;
  DateTime? date;
  DateTime? dateSystem;
  bool? explain;
  String? explainText;
  String? explainLabel;

  ResearchesTradeMarketingAnswersSubmitted(
      {this.id,
      this.stepId,
      this.question,
      this.dataType,
      this.answers,
      this.date,
      this.explain,
      this.explainText,
      this.explainLabel});

  ResearchesTradeMarketingAnswersSubmitted.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    stepId = json['stepId'];
    question = json['question'];
    dataType = json['dataType'];
    answers = json['answers'] != null
        ? List<String>.from(json['answers'].map((x) => x ?? ''))
        : [];

    date = json['date'] != null ? DateTime.parse(json['date']) : null;
    if (json['date'] != null) {
      final x = DateTime.parse(json['date']);
      dateSystem = DateTime(x.year, x.month, x.day);
    }

    explain = json['explain'];
    explainText = json['explainText'];
    explainLabel = json['explainLabel'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['stepId'] = stepId;
    data['question'] = question;
    data['dataType'] = dataType;
    data['answers'] = answers;
    data['date'] = date?.toIso8601String();
    data['explain'] = explain;
    data['explainText'] = explainText;
    data['explainLabel'] = explainLabel;
    return data;
  }
}

class ResearchesTradeMarketingImages {
  String? imageId;
  String? imageName;
  String? stepIdentifier;
  int? stepIndex;
  int? categoryId;
  int? storeId;
  String? cnpj;
  String? imageVirtualPath;
  String? contentType;
  Orientation? orientation;
  ResearchesTradeMarketingImages({
    this.imageId,
    this.imageName,
    this.stepIdentifier,
    this.stepIndex,
    this.categoryId,
    this.storeId,
    this.cnpj,
    this.imageVirtualPath,
    this.contentType,
    this.orientation,
  });

  ResearchesTradeMarketingImages.fromJson(Map<String, dynamic> json) {
    imageId = json['imageId'];
    imageName = json['imageName'];
    stepIdentifier = json['stepIdentifier'];
    stepIndex = json['stepIndex'];
    categoryId = json['categoryId'];
    storeId = json['storeId'];
    cnpj = json['cnpj'];
    imageVirtualPath = json['imageVirtualPath'];
    contentType = json['contentType'];
    orientation = json['Orientation'] != null
        ? Orientation.values.firstWhere(
            (element) => element.name == json['Orientation'],
            orElse: () => Orientation.portrait)
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['imageId'] = imageId;
    data['imageName'] = imageName;
    data['stepIdentifier'] = stepIdentifier;
    data['stepIndex'] = stepIndex;
    data['categoryId'] = categoryId;
    data['storeId'] = storeId;
    data['cnpj'] = cnpj;
    data['imageVirtualPath'] = imageVirtualPath;
    data['contentType'] = contentType;
    data['Orientation'] = orientation?.name;
    return data;
  }
}

class ResearchesTradeMarketingMaintenanceDateList {
  DateTime? date;
  int? index;
  ResearchesTradeMarketingMaintenanceDateList({this.date, this.index});
}
