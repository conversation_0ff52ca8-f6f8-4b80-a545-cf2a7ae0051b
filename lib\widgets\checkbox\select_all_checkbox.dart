import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SelectAll<PERSON>heck<PERSON> extends StatelessWidget {
  final Function(bool)? onChanged;
  final RxBool isChecked;
  final String? title;

  const SelectAllCheckbox({
    super.key,
    required this.isChecked,
    this.onChanged,
    this.title,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() => Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            Checkbox(
              value: isChecked.value,
              onChanged: (bool? value) {
                isChecked.value = value ?? false;
                onChanged?.call(isChecked.value);
              },
            ),
            GestureDetector(
              onTap: () {
                isChecked.value = !isChecked.value;
                onChanged?.call(isChecked.value);
              },
              child: Text(
                title ?? 'Selecionar todos',
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ));
  }
}
