import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:camera/camera.dart';
import 'package:flutter/services.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:image_picker/image_picker.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/camera_picker/widgets/camera_options.dart';
import 'package:path_provider/path_provider.dart' as path_provider;
import 'package:path/path.dart' as path;
import 'package:pharmalink/modules/camera_picker/models/camera_picker_model.dart';

class CameraPickerController
    extends GetxControllerInstrumentado<CameraPickerController> {
  CameraPickerController();

  TextEditingController messageTextController = TextEditingController();
  List<CameraDescription>? cameras;
  int currentCamera = 0;
  CameraController? cameraController;
  Future<void>? initializeControllerFuture;
  CameraOptionsEnum selectedOption = CameraOptionsEnum.photo;

  FlashMode flashMode = FlashMode.auto;

  IconData flashIcon = Icons.flash_auto;
  String? imageFileName;
  bool fileIsVideo = false;
  List<int>? imageFileBytes;
  XFile? fileCurrent;
  Orientation orientation = Orientation.portrait;

  bool hasSend = false;
  Stopwatch stopwatch = Stopwatch();
  Timer? timerVideo;

  bool isProcessingPicture = false;

  final _picker = ImagePicker();
  final selectedImage = Rxn<CameraPickerModel>();

  @override
  Future<void> onInit() async {
    super.onInit();
    cameras = await availableCameras();

    currentCamera = 0;
    cameraController = CameraController(
      cameras![currentCamera],
      ResolutionPreset.high,
    );

    initializeControllerFuture = cameraController?.initialize().catchError((
      Object e,
    ) {
      if (e is CameraException) {
        dynatraceAction.reportError(e.code);
        switch (e.code) {
          case 'CameraAccessDenied':
            break;
          default:
            break;
        }
      }
    });

    update();
  }

  void setProcessingPicture(bool value) {
    isProcessingPicture = value;
    update();
  }

  void setCameraOption(CameraOptionsEnum option) {
    selectedOption = option;
    update();
  }

  @override
  Future<void> onClose() async {
    cameraController?.dispose();
    timerVideo?.cancel();
    stopwatch.stop();
    super.onClose();
  }

  void turnLight() {
    switch (flashMode) {
      case FlashMode.auto:
        flashMode = FlashMode.always;
        break;
      case FlashMode.always:
        flashMode = FlashMode.off;
        break;
      case FlashMode.off:
        flashMode = FlashMode.torch;
        break;
      case FlashMode.torch:
        flashMode = FlashMode.auto;
        break;
    }
    getFlashIcon();
    cameraController?.setFlashMode(flashMode);
    update();
  }

  void getFlashIcon() {
    switch (flashMode) {
      case FlashMode.off:
        flashIcon = Icons.flash_off;
        break;
      case FlashMode.auto:
        flashIcon = Icons.flash_auto;
        break;
      case FlashMode.always:
        flashIcon = Icons.flash_on;
        break;
      case FlashMode.torch:
        flashIcon = Icons.highlight;
        break;
      default:
        flashIcon = Icons.flash_off;
    }
  }

  Future<void> changeCamera() async {
    if (currentCamera == 0 && cameras!.length == 1) return;

    currentCamera = currentCamera == 0 ? 1 : 0;
    await cameraController?.dispose();
    cameraController = CameraController(
      cameras![currentCamera],
      ResolutionPreset.high,
    );
    flashMode = FlashMode.auto;
    getFlashIcon();
    cameraController?.setFlashMode(flashMode);
    initializeControllerFuture = cameraController?.initialize().catchError((
      Object e,
    ) {
      if (e is CameraException) {
        switch (e.code) {
          case 'CameraAccessDenied':
            break;
          default:
            break;
        }
      }
    });
    update();
  }

  Future<void> openGalery() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "openGalery",
    );

    try {
      final picker = ImagePicker();
      final pickedFile = await picker.pickImage(source: ImageSource.gallery);

      if (pickedFile != null) {
        fileCurrent = pickedFile;
        await convertImageWebp(pickedFile);
        Get.toNamed(RoutesPath.cameraPickerPreview)?.then((value) {
          if (hasSend) Get.back();
        });
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
    } finally {
      leaveAction();
    }
  }

  Future<void> takePicture2() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "takePicture",
    );

    try {
      await initializeControllerFuture;
      // Capturar a orientação atual do dispositivo
      orientation = MediaQuery.of(Get.context!).orientation;
      final XFile file = await cameraController!.takePicture();
      log("Foto tirada: ${file.path}");
      subAction.reportEvent("Foto tirada");
      fileCurrent = file;
      await convertImageWebp(file);
      Get.toNamed(RoutesPath.cameraPickerPreview)?.then((value) {
        if (hasSend) Get.back();
      });
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
    } finally {
      leaveAction();
    }
  }

  Future<void> capture(
    CameraOptionsEnum mode,
    Orientation orientationType,
  ) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport("capture");

    try {
      orientation = orientationType;
      log('orientationType: $orientationType');
      await initializeControllerFuture;
      subAction.reportValue("CameraOptionsEnum", mode.name);
      if (mode == CameraOptionsEnum.photo) {
        fileIsVideo = false;
        final XFile file = await cameraController!.takePicture();
        log("Foto tirada: ${file.path}");
        subAction.reportEvent("Foto tirada");
        subAction.reportValue("Orientation", orientation.toString());

        fileCurrent = file;
        await convertImageWebp(file);
        Get.toNamed(RoutesPath.cameraPickerPreview)?.then((value) {
          if (hasSend) Get.back();
        });
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
    } finally {
      leaveAction();
    }
  }

  void startTimer() {
    stopwatch.reset();
    stopwatch.start();
    timerVideo = Timer.periodic(const Duration(seconds: 1), (timer) {
      update();
    });
  }

  void stopTimer() {
    timerVideo?.cancel();
    stopwatch.stop();
  }

  Future<void> convertImageWebp(XFile file) async {
    String inputPath = file.path;
    String outputPath = file.path
        .replaceAll(".png", ".webp")
        .replaceAll(".jpg", ".webp")
        .replaceAll(".jpeg", ".webp");

    final dir = await path_provider.getTemporaryDirectory();
    final targetPath = path.join(
      dir.path,
      '${DateTime.now().millisecondsSinceEpoch}.webp',
    );

    final result = await FlutterImageCompress.compressAndGetFile(
      inputPath,
      targetPath,
      quality: 70,
      format: CompressFormat.webp,
    );

    if (result != null) {
      imageFileName = result.path;
      imageFileBytes = await result.readAsBytes();
      fileCurrent = result;
      await File(inputPath).delete();
    }
  }

  Future<void> pickImage() async {
    try {
      final XFile? image = await _picker.pickImage(source: ImageSource.camera);
      if (image != null) {
        final compressedImage = await compressImage(image);
        selectedImage.value = CameraPickerModel.fromXFile(
          XFile(compressedImage.path),
          orientation: orientation,
        );
      }
    } on PlatformException catch (e) {
      print('Failed to pick image: $e');
    }
  }

  Future<File> compressImage(XFile file) async {
    final dir = await path_provider.getTemporaryDirectory();
    final targetPath = path.join(
      dir.path,
      '${DateTime.now().millisecondsSinceEpoch}.jpg',
    );

    final result = await FlutterImageCompress.compressAndGetFile(
      file.path,
      targetPath,
      quality: 70,
      format: CompressFormat.jpeg,
    );

    return File(result?.path ?? file.path);
  }
}
