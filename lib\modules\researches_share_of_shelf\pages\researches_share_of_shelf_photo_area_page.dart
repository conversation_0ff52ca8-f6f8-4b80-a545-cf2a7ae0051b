import 'dart:io';
import 'dart:ui';

import 'package:crop_image/crop_image.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart' as img;
import 'package:path_provider/path_provider.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class ResearchesShareOfShelfPhotoAreaPage extends StatefulWidget {
  const ResearchesShareOfShelfPhotoAreaPage({super.key});

  @override
  State<ResearchesShareOfShelfPhotoAreaPage> createState() =>
      _ResearchesShareOfShelfPhotoAreaPageState();
}

class _ResearchesShareOfShelfPhotoAreaPageState
    extends State<ResearchesShareOfShelfPhotoAreaPage> {
  final _globalKey = GlobalKey();
  bool isDone = false;
  Rect? area;
  Size? imageSize;
  File? _image;
  double percentage = 0;
  final controller = CropController(
    aspectRatio: null,
    defaultCrop: const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9),
  );

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ResearchesShareOfShelfController>(
        "ResearchesShareOfShelfPhotoAreaPage", builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelWidget(
                title: globalParams.getCurrentStore()?.razaoSocial ?? "-",
                fontSize: DeviceSize.fontSize(14, 18),
                fontWeight: FontWeight.w600,
                textColor: whiteColor,
              ),
              LabelWidget(
                title: globalParams.getCurrentStore()?.cNPJ ?? "-",
                fontSize: DeviceSize.fontSize(11, 13),
                textColor: whiteColor,
              ),
            ],
          ),
          actions: [
            IconButton(
                onPressed: () async {
                  _recreate();
                },
                icon: const Icon(
                  Icons.refresh,
                  color: whiteColor,
                )),
            IconButton(
                onPressed: () async {
                  if (!isDone) {
                    await _finished(ctrl);
                  } else {
                    _close();
                  }
                },
                icon: const Icon(
                  Icons.send,
                  color: whiteColor,
                ))
          ],
        ),
        body: !isDone
            ? RepaintBoundary(
                key: _globalKey,
                child: CropImage(
                  controller: controller,
                  gridCornerColor: Colors.red,
                  gridInnerColor: Colors.red,
                  gridColor: Colors.red,
                  gridThickWidth: 5,
                  gridThinWidth: 3,
                  image: Image.file(
                    File(ctrl.photoPictureCropPath!),
                  ),
                  paddingSize: 0.0,
                  alwaysMove: true,
                ),
              )
            : Image.file(_image!),
      );
    });
  }

  void _recreate() {
    setState(() {
      controller.crop = const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9);
      isDone = false;
    });
  }

  Future<void> _finished(ResearchesShareOfShelfController ctrl) async {
    final loading = PlkLoading();
    loading.show();
    RenderRepaintBoundary boundary =
        _globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
    final image = await boundary.toImage();
    ByteData? byteData = await image.toByteData(format: ImageByteFormat.png);
    Uint8List pngBytes = byteData!.buffer.asUint8List();

    // Agora você tem uma imagem PNG na memória. Você pode, por exemplo, escrevê-la em um arquivo:
    final directory = await getApplicationDocumentsDirectory();
    final file =
        File('${directory.path}/${DateTime.now().toIso8601String()}.png');
    await file.writeAsBytes(pngBytes);
    img.Image? imageDecoded = img.decodeImage(file.readAsBytesSync());
    if (imageDecoded != null) {
      Size imageSize =
          Size(imageDecoded.width.toDouble(), imageDecoded.height.toDouble());

      percentage = calculateCropAreaPercentage(controller.crop, imageSize);
    }
    ctrl.photoPictureCroppedPath = file.path;
    ctrl.currentPhoto?.image = file.path;
    ctrl.currentPhoto?.shareApurado = percentage;
    ctrl.update();
    loading.hide();

    Get.back();
  }

  void _close() {
    Get.back();
  }

  double calculateCropAreaPercentage(Rect cropArea, Size imageSize) {
    // Escala o Rect para corresponder ao tamanho da imagem
    final scaledCropArea = Rect.fromLTRB(
      cropArea.left * imageSize.width,
      cropArea.top * imageSize.height,
      cropArea.right * imageSize.width,
      cropArea.bottom * imageSize.height,
    );

    double cropAreaSize = scaledCropArea.width * scaledCropArea.height;
    double imageSizeSize = imageSize.width * imageSize.height;

    return (cropAreaSize / imageSizeSize) * 100;
  }
}
