class OrdersDataRetrieveModel {
  String? userId;
  String? orderId;
  String? workSpace;
  OrdersSyncAuthorizationRetrieveModel? authorization;
  String? authorizationUri;
  String? timeStamp;
  DateTime? createAt;
  int? orderStatus;
  String? verb;
  String? payLoadUrl;
  List<OrdersSyncPayLoadRetrieveModel>? payLoad;
  OrdersSyncPushNotificationRetrieveModel? pushNotification;
  bool? forApproval;
  bool? hasSeparatedOrders;
  OrdersDataResponseRetrieveModel? response;
  OrdersDataRetrieveModel({
    this.userId,
    this.orderId,
    this.workSpace,
    this.authorization,
    this.authorizationUri,
    this.timeStamp,
    this.orderStatus,
    this.verb,
    this.createAt,
    this.payLoadUrl,
    this.payLoad,
    this.pushNotification,
    this.forApproval,
    this.hasSeparatedOrders,
    this.response,
  });

  OrdersDataRetrieveModel.fromJson(Map<String, dynamic> json) {
    userId = json['UserId'];
    orderId = json['OrderId'];
    workSpace = json['WorkSpace'];
    authorization = json['Authorization'] != null
        ? OrdersSyncAuthorizationRetrieveModel.fromJson(json['Authorization'])
        : null;
    createAt =
        json['CreateAt'] != null ? DateTime.parse(json['CreateAt']) : null;
    authorizationUri = json['AuthorizationUri'];
    timeStamp = json['TimeStamp'];
    if (json['OrderStatus'] is String) {
      orderStatus = int.parse(json['OrderStatus']);
    } else if (json['OrderStatus'] is int) {
      orderStatus = json['OrderStatus'];
    }
    verb = json['Verb'];
    payLoadUrl = json['PayLoadUrl'];
    if (json['PayLoad'] != null) {
      payLoad = <OrdersSyncPayLoadRetrieveModel>[];
      json['PayLoad'].forEach((v) {
        payLoad!.add(OrdersSyncPayLoadRetrieveModel.fromJson(v));
      });
    }
    pushNotification = json['PushNotification'] != null
        ? OrdersSyncPushNotificationRetrieveModel.fromJson(
            json['PushNotification'])
        : null;
    forApproval = json['ForApproval'];
    hasSeparatedOrders = json['HasSeparatedOrders'];
    response = json['Response'] != null
        ? OrdersDataResponseRetrieveModel.fromJson(json['Response'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['UserId'] = userId;
    data['OrderId'] = orderId;
    data['WorkSpace'] = workSpace;
    if (authorization != null) {
      data['Authorization'] = authorization!.toJson();
    }
    data['AuthorizationUri'] = authorizationUri;
    data['TimeStamp'] = timeStamp;
    data['OrderStatus'] = orderStatus;
    data['Verb'] = verb;
    data['PayLoadUrl'] = payLoadUrl;
    if (payLoad != null) {
      data['PayLoad'] = payLoad!.map((v) => v.toJson()).toList();
    }
    if (pushNotification != null) {
      data['PushNotification'] = pushNotification!.toJson();
    }
    data['ForApproval'] = forApproval;
    data['HasSeparatedOrders'] = hasSeparatedOrders;
    data['CreateAt'] = createAt?.toIso8601String();
    if (response != null) {
      data['Response'] = response!.toJson();
    }
    return data;
  }
}

class OrdersDataResponseRetrieveModel {
  String? mensagemErro;

  OrdersDataResponseRetrieveModel({this.mensagemErro});

  OrdersDataResponseRetrieveModel.fromJson(Map<String, dynamic> json) {
    mensagemErro = json['MensagemErro'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['MensagemErro'] = mensagemErro;
    return data;
  }
}

class OrdersSyncAuthorizationRetrieveModel {
  String? user;
  String? password;

  OrdersSyncAuthorizationRetrieveModel({this.user, this.password});

  OrdersSyncAuthorizationRetrieveModel.fromJson(Map<String, dynamic> json) {
    user = json['User'];
    password = json['Password'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['User'] = user;
    data['Password'] = password;
    return data;
  }
}

class OrdersSyncPayLoadRetrieveModel {
  bool? isPedidoComboOferta;
  String? agrupadorPedido;
  String? cNPJ;
  OrdersSyncDistribuidoresRetrieveModel? distribuidor;
  List<OrdersSyncDistribuidoresRetrieveModel>? distribuidores;
  int? distribuidorId;
  List<OrdersSyncItensDoPedidoRetrieveModel>? itensDoPedido;
  List<OrdersSyncCombosDoPedidoRetrieveModel>? combosDoPedido;
  int? condicaoComercialBaseId;
  int? idTabloide;
  List<String>? datasProgramadas;
  bool? forcarGerenciamento;
  int? idPrazoPagamento;
  String? origem;
  int? pdvId;
  String? prazo;
  String? somenteValidar;
  String? tipoLooping;
  String? tipoPedido;
  String? userId;
  String? chaveUnicaPedido;
  String? referenciaPedido;
  String? referenciaDePedido;
  List<OrdersSyncProdutosComboRetrieveModel>? produtosCombo;
  int? totalApresentacoesSKU;
  int? totalUnidadesSKU;
  int? descontoTotalSKU;
  int? descontoRecebidoSKU;
  double? totalBrutoSKU;
  double? totalLiquidoSKU;
  String? observacao;
  String? codigoPrazo;
  String? descricaoPrazo;
  String? razaoSocialPDV;

  OrdersSyncPayLoadRetrieveModel(
      {this.isPedidoComboOferta,
      this.agrupadorPedido,
      this.cNPJ,
      this.distribuidor,
      this.distribuidores,
      this.distribuidorId,
      this.itensDoPedido,
      this.combosDoPedido,
      this.condicaoComercialBaseId,
      this.idTabloide,
      this.datasProgramadas,
      this.forcarGerenciamento,
      this.idPrazoPagamento,
      this.origem,
      this.pdvId,
      this.prazo,
      this.somenteValidar,
      this.tipoLooping,
      this.tipoPedido,
      this.userId,
      this.chaveUnicaPedido,
      this.referenciaPedido,
      this.referenciaDePedido,
      this.produtosCombo,
      this.totalApresentacoesSKU,
      this.totalUnidadesSKU,
      this.descontoTotalSKU,
      this.descontoRecebidoSKU,
      this.totalBrutoSKU,
      this.totalLiquidoSKU,
      this.observacao,
      this.codigoPrazo,
      this.descricaoPrazo,
      this.razaoSocialPDV});

  OrdersSyncPayLoadRetrieveModel.fromJson(Map<String, dynamic> json) {
    isPedidoComboOferta = json['IsPedidoComboOferta'];
    agrupadorPedido = json['AgrupadorPedido'];
    cNPJ = json['CNPJ'];
    distribuidor = json['Distribuidor'] != null
        ? OrdersSyncDistribuidoresRetrieveModel.fromJson(json['Distribuidor'])
        : null;
    if (json['Distribuidores'] != null) {
      distribuidores = <OrdersSyncDistribuidoresRetrieveModel>[];
      json['Distribuidores'].forEach((v) {
        distribuidores!.add(OrdersSyncDistribuidoresRetrieveModel.fromJson(v));
      });
    }
    distribuidorId = json['DistribuidorId'];
    if (json['ItensDoPedido'] != null) {
      itensDoPedido = <OrdersSyncItensDoPedidoRetrieveModel>[];
      json['ItensDoPedido'].forEach((v) {
        itensDoPedido!.add(OrdersSyncItensDoPedidoRetrieveModel.fromJson(v));
      });
    }
    if (json['CombosDoPedido'] != null) {
      combosDoPedido = <OrdersSyncCombosDoPedidoRetrieveModel>[];
      json['CombosDoPedido'].forEach((v) {
        combosDoPedido!.add(OrdersSyncCombosDoPedidoRetrieveModel.fromJson(v));
      });
    }
    condicaoComercialBaseId = json['CondicaoComercialBaseId'];
    idTabloide = json['IdTabloide'];
    datasProgramadas = json['DatasProgramadas'].cast<String>();
    forcarGerenciamento = json['ForcarGerenciamento'];
    idPrazoPagamento = json['IdPrazoPagamento'];
    origem = json['Origem'];
    pdvId = json['PdvId'];
    prazo = json['Prazo'];
    somenteValidar = json['SomenteValidar'];
    tipoLooping = json['TipoLooping'];
    tipoPedido = json['TipoPedido'];
    userId = json['UserId'];
    chaveUnicaPedido = json['ChaveUnicaPedido'];
    referenciaPedido = json['ReferenciaPedido'];
    referenciaDePedido = json['ReferenciaDePedido'];
    if (json['ProdutosCombo'] != null) {
      produtosCombo = <OrdersSyncProdutosComboRetrieveModel>[];
      json['ProdutosCombo'].forEach((v) {
        produtosCombo!.add(OrdersSyncProdutosComboRetrieveModel.fromJson(v));
      });
    }
    totalApresentacoesSKU = json['TotalApresentacoesSKU'];
    totalUnidadesSKU = json['TotalUnidadesSKU'];
    descontoTotalSKU = json['DescontoTotalSKU'];
    descontoRecebidoSKU = json['DescontoRecebidoSKU'];
    totalBrutoSKU = json['TotalBrutoSKU'];
    totalLiquidoSKU = json['TotalLiquidoSKU'];
    observacao = json['Observacao'];
    codigoPrazo = json['Codigo'];
    descricaoPrazo = json['DescricaoPrazo'];
    razaoSocialPDV = json['RazaoSocial'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IsPedidoComboOferta'] = isPedidoComboOferta;
    data['AgrupadorPedido'] = agrupadorPedido;
    data['CNPJ'] = cNPJ;
    if (distribuidor != null) {
      data['Distribuidor'] = distribuidor!.toJson();
    }
    if (distribuidores != null) {
      data['Distribuidores'] = distribuidores!.map((v) => v.toJson()).toList();
    }
    data['DistribuidorId'] = distribuidorId;
    if (itensDoPedido != null) {
      data['ItensDoPedido'] = itensDoPedido!.map((v) => v.toJson()).toList();
    }
    if (combosDoPedido != null) {
      data['CombosDoPedido'] = combosDoPedido!.map((v) => v.toJson()).toList();
    } else {
      data['CombosDoPedido'] = [];
    }
    data['CondicaoComercialBaseId'] = condicaoComercialBaseId;
    data['IdTabloide'] = idTabloide;
    data['DatasProgramadas'] = datasProgramadas;

    data['ForcarGerenciamento'] = forcarGerenciamento;
    data['IdPrazoPagamento'] = idPrazoPagamento;
    data['Origem'] = origem;
    data['PdvId'] = pdvId;
    data['Prazo'] = prazo;
    data['SomenteValidar'] = somenteValidar;
    data['TipoLooping'] = tipoLooping;
    data['TipoPedido'] = tipoPedido;
    data['UserId'] = userId;
    data['ChaveUnicaPedido'] = chaveUnicaPedido;
    data['ReferenciaPedido'] = referenciaPedido;
    data['ReferenciaDePedido'] = referenciaDePedido;
    if (produtosCombo != null) {
      data['ProdutosCombo'] = produtosCombo!.map((v) => v.toJson()).toList();
    }
    data['TotalApresentacoesSKU'] = totalApresentacoesSKU;
    data['TotalUnidadesSKU'] = totalUnidadesSKU;
    data['DescontoTotalSKU'] = descontoTotalSKU ?? 0;
    data['DescontoRecebidoSKU'] = descontoRecebidoSKU ?? 0;
    data['TotalBrutoSKU'] = totalBrutoSKU;
    data['TotalLiquidoSKU'] = totalLiquidoSKU;
    data['Observacao'] = observacao;
    data['Codigo'] = codigoPrazo;
    data['Descricao'] = descricaoPrazo;
    data['RazaoSocial'] = razaoSocialPDV;
    return data;
  }
}

class OrdersSyncDistribuidorRetrieveModel {
  int? id;
  bool? ativo;
  double? valorMinimoDePedido;
  String? nomeFantasia;
  String? razaoSocial;
  bool? habilitaContaCorrente;

  OrdersSyncDistribuidorRetrieveModel(
      {this.id,
      this.ativo,
      this.valorMinimoDePedido,
      this.nomeFantasia,
      this.razaoSocial,
      this.habilitaContaCorrente});

  OrdersSyncDistribuidorRetrieveModel.fromJson(Map<String, dynamic> json) {
    id = json['Id'];
    ativo = json['Ativo'];
    valorMinimoDePedido = json['ValorMinimoDePedido'];
    nomeFantasia = json['NomeFantasia'];
    razaoSocial = json['RazaoSocial'];
    habilitaContaCorrente = json['HabilitaContaCorrente'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Id'] = id;
    data['Ativo'] = ativo;
    data['ValorMinimoDePedido'] = valorMinimoDePedido;
    data['NomeFantasia'] = nomeFantasia;
    data['RazaoSocial'] = razaoSocial;
    data['HabilitaContaCorrente'] = habilitaContaCorrente;
    return data;
  }
}

class OrdersSyncItensDoPedidoRetrieveModel {
  double? descontoAdicional;
  double? descontoTotal;
  int? idProduto;
  int? quantidade;
  double? preco;
  bool? precoDistribuidor;
  String? dUN;
  int? qtdeDUN;
  int? qtdeProdutoTotal;
  int? idProdutoDUN;
  bool? isDUN;

  OrdersSyncItensDoPedidoRetrieveModel(
      {this.descontoAdicional,
      this.descontoTotal,
      this.idProduto,
      this.quantidade,
      this.preco,
      this.precoDistribuidor,
      this.dUN,
      this.qtdeDUN,
      this.qtdeProdutoTotal,
      this.idProdutoDUN,
      this.isDUN});

  OrdersSyncItensDoPedidoRetrieveModel.fromJson(Map<String, dynamic> json) {
    descontoAdicional = json['DescontoAdicional'];
    descontoTotal = json['DescontoTotal'];
    idProduto = json['IdProduto'];
    quantidade = json['Quantidade'];
    preco = json['Preco'];
    precoDistribuidor = json['PrecoDistribuidor'];
    dUN = json['DUN'];
    qtdeDUN = json['QtdeDUN'];
    qtdeProdutoTotal = json['QtdeProdutoTotal'];
    idProdutoDUN = json['idProdutoDUN'];
    isDUN = json['isDUN'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['DescontoAdicional'] = descontoAdicional;
    data['DescontoTotal'] = descontoTotal;
    data['IdProduto'] = idProduto;
    data['Quantidade'] = quantidade;
    data['Preco'] = preco;
    data['PrecoDistribuidor'] = precoDistribuidor;
    data['DUN'] = dUN;
    data['QtdeDUN'] = qtdeDUN;
    data['QtdeProdutoTotal'] = qtdeProdutoTotal;
    data['idProdutoDUN'] = idProdutoDUN;
    data['isDUN'] = isDUN;
    return data;
  }
}

class OrdersSyncPushNotificationRetrieveModel {
  String? token;
  OrdersSyncSuccessRetrieveModel? success;
  OrdersSyncErrorRetrieveModel? error;

  OrdersSyncPushNotificationRetrieveModel(
      {this.token, this.success, this.error});

  OrdersSyncPushNotificationRetrieveModel.fromJson(Map<String, dynamic> json) {
    token = json['Token'];
    success = json['Success'] != null
        ? OrdersSyncSuccessRetrieveModel.fromJson(json['Success'])
        : null;
    error = json['Error'] != null
        ? OrdersSyncErrorRetrieveModel.fromJson(json['Error'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Token'] = token;
    if (success != null) {
      data['Success'] = success!.toJson();
    }
    if (error != null) {
      data['Error'] = error!.toJson();
    }
    return data;
  }
}

class OrdersSyncSuccessRetrieveModel {
  String? titleYes;
  String? messageYes;
  OrdersSyncDataYesRetrieveModel? dataYes;

  OrdersSyncSuccessRetrieveModel(
      {this.titleYes, this.messageYes, this.dataYes});

  OrdersSyncSuccessRetrieveModel.fromJson(Map<String, dynamic> json) {
    titleYes = json['TitleYes'];
    messageYes = json['MessageYes'];
    dataYes = json['DataYes'] != null
        ? OrdersSyncDataYesRetrieveModel.fromJson(json['DataYes'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['TitleYes'] = titleYes;
    data['MessageYes'] = messageYes;
    if (dataYes != null) {
      data['DataYes'] = dataYes!.toJson();
    }
    return data;
  }
}

class OrdersSyncDataYesRetrieveModel {
  String? orderId;
  String? page;
  OrdersSyncParamsRetrieveModel? params;

  OrdersSyncDataYesRetrieveModel({this.orderId, this.page, this.params});

  OrdersSyncDataYesRetrieveModel.fromJson(Map<String, dynamic> json) {
    orderId = json['OrderId'];
    page = json['Page'];
    params = json['Params'] != null
        ? OrdersSyncParamsRetrieveModel.fromJson(json['Params'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['OrderId'] = orderId;
    data['Page'] = page;
    if (params != null) {
      data['Params'] = params!.toJson();
    }
    return data;
  }
}

class OrdersSyncParamsRetrieveModel {
  bool? openFromPush;
  String? orderId;

  OrdersSyncParamsRetrieveModel({this.openFromPush, this.orderId});

  OrdersSyncParamsRetrieveModel.fromJson(Map<String, dynamic> json) {
    openFromPush = json['OpenFromPush'];
    orderId = json['OrderId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['OpenFromPush'] = openFromPush;
    data['OrderId'] = orderId;
    return data;
  }
}

class OrdersSyncErrorRetrieveModel {
  String? titleNo;
  String? messageNo;
  //DataNo? dataNo;

  OrdersSyncErrorRetrieveModel({
    this.titleNo,
    this.messageNo,
  });

  OrdersSyncErrorRetrieveModel.fromJson(Map<String, dynamic> json) {
    titleNo = json['TitleNo'];
    messageNo = json['MessageNo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['TitleNo'] = titleNo;
    data['MessageNo'] = messageNo;

    return data;
  }
}

class OrdersSyncDistribuidoresRetrieveModel {
  int? pdvId;
  int? distribuidorId;
  int? ordemDePreferencia;
  int? ordemMelhorAtendimento;
  OrdersSyncDistribuidorRetrieveModel? distribuidor;
  bool? selected;
  int? oldOrdemDePreferencia;

  OrdersSyncDistribuidoresRetrieveModel(
      {this.pdvId,
      this.distribuidorId,
      this.ordemDePreferencia,
      this.ordemMelhorAtendimento,
      this.distribuidor,
      this.selected,
      this.oldOrdemDePreferencia});

  OrdersSyncDistribuidoresRetrieveModel.fromJson(Map<String, dynamic> json) {
    pdvId = json['PdvId'];
    distribuidorId = json['DistribuidorId'];
    ordemDePreferencia = json['OrdemDePreferencia'];
    ordemMelhorAtendimento = json['OrdemMelhorAtendimento'];
    distribuidor = json['Distribuidor'] != null
        ? OrdersSyncDistribuidorRetrieveModel.fromJson(json['Distribuidor'])
        : null;
    selected = json['Selected'];
    oldOrdemDePreferencia = json['OldOrdemDePreferencia'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['PdvId'] = pdvId;
    data['DistribuidorId'] = distribuidorId;
    data['OrdemDePreferencia'] = ordemDePreferencia;
    data['OrdemMelhorAtendimento'] = ordemMelhorAtendimento;
    if (distribuidor != null) {
      data['Distribuidor'] = distribuidor!.toJson();
    }
    data['Selected'] = selected;
    data['OldOrdemDePreferencia'] = oldOrdemDePreferencia;
    return data;
  }
}

class OrdersSyncCombosDoPedidoRetrieveModel {
  int? idComboOferta;
  int? quantidade;

  OrdersSyncCombosDoPedidoRetrieveModel({this.idComboOferta, this.quantidade});

  OrdersSyncCombosDoPedidoRetrieveModel.fromJson(Map<String, dynamic> json) {
    idComboOferta = json['IdComboOferta'];
    quantidade = json['Quantidade'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdComboOferta'] = idComboOferta;
    data['Quantidade'] = quantidade;
    return data;
  }
}

class OrdersSyncProdutosComboRetrieveModel {
  int? idProduto;
  String? ean;
  String? descricao;
  String? laboratorio;
  String? familia;
  int? preco;
  int? quantidade;
  int? desconto;
  bool? bonificado;
  int? precoLiquido;
  List<int>? distribuidores;
  List<int>? distribuidoresFlat;
  int? distribuidorQueAtende;
  String? statusDistribuidor;

  OrdersSyncProdutosComboRetrieveModel(
      {this.idProduto,
      this.ean,
      this.descricao,
      this.laboratorio,
      this.familia,
      this.preco,
      this.quantidade,
      this.desconto,
      this.bonificado,
      this.precoLiquido,
      this.distribuidores,
      this.distribuidoresFlat,
      this.distribuidorQueAtende,
      this.statusDistribuidor});

  OrdersSyncProdutosComboRetrieveModel.fromJson(Map<String, dynamic> json) {
    idProduto = json['IdProduto'];
    ean = json['Ean'];
    descricao = json['Descricao'];
    laboratorio = json['Laboratorio'];
    familia = json['Familia'];
    preco = json['Preco'];
    quantidade = json['Quantidade'];
    desconto = json['Desconto'];
    bonificado = json['Bonificado'];
    precoLiquido = json['PrecoLiquido'];
    distribuidores = json['Distribuidores'].cast<int>();
    distribuidoresFlat = json['DistribuidoresFlat'].cast<int>();
    distribuidorQueAtende = json['DistribuidorQueAtende'];
    statusDistribuidor = json['StatusDistribuidor'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdProduto'] = idProduto;
    data['Ean'] = ean;
    data['Descricao'] = descricao;
    data['Laboratorio'] = laboratorio;
    data['Familia'] = familia;
    data['Preco'] = preco;
    data['Quantidade'] = quantidade;
    data['Desconto'] = desconto;
    data['Bonificado'] = bonificado;
    data['PrecoLiquido'] = precoLiquido;
    data['Distribuidores'] = distribuidores;
    data['DistribuidoresFlat'] = distribuidoresFlat;
    data['DistribuidorQueAtende'] = distribuidorQueAtende;
    data['StatusDistribuidor'] = statusDistribuidor;
    return data;
  }
}
