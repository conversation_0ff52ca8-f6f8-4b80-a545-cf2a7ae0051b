import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class ReportOrdersSearchDetailWidget extends StatelessWidget {
  const ReportOrdersSearchDetailWidget(
      {super.key,
      required this.title,
      required this.value,
      this.fontSizeLabel,
      this.fontSizeValue});
  final String title;
  final String value;
  final double? fontSizeLabel;
  final double? fontSizeValue;
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        LabelWidget(
          title: title,
          fontSize: fontSizeLabel ?? DeviceSize.fontSize(12, 15),
          fontWeight: FontWeight.bold,
        ),
        5.toHeightSpace(),
        LabelWidget(
          title: value,
          fontSize: fontSizeValue ?? DeviceSize.fontSize(13, 16),
        ),
      ],
    );
  }
}
