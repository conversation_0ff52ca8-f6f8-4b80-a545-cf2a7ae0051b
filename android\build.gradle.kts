plugins {
    id("com.dynatrace.instrumentation") version "8.315.1.1005"
}
extra["dynatrace.instrumentationFlavor"] = "flutter"
dynatrace {
    configurations {
        create("defaultConfig") {
            autoStart{
                applicationId("40e18b35-0587-42f7-b3ba-4a1dfcfc7d5e")
                beaconUrl("https://bf53667gkn.bf.dynatrace.com/mbeacon")
            }
            userOptIn(true)
            agentBehavior.startupLoadBalancing(true)
        }
    }
}
allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

val newBuildDir: Directory = rootProject.layout.buildDirectory.dir("../../build").get()
rootProject.layout.buildDirectory.value(newBuildDir)

subprojects {
    val newSubprojectBuildDir: Directory = newBuildDir.dir(project.name)
    project.layout.buildDirectory.value(newSubprojectBuildDir)
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register<Delete>("clean") {
    delete(rootProject.layout.buildDirectory)
}