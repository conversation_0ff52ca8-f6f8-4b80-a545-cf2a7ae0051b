import 'dart:io';

import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/camera_picker/widgets/rotated_image_preview.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_model.dart';
import 'package:pharmalink/modules/researches_complementary/models/researches_complementary_send_model.dart';
import 'package:pharmalink/widgets/buttons/button_with_icon.dart';

class ResearchesComplementaryCameraWidget extends StatelessWidget {
  const ResearchesComplementaryCameraWidget(
      {super.key, required this.onPressed, required this.question});

  final GestureTapCallback onPressed;
  final ResearchesComplementaryPerguntas question;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ResearchesComplementaryController>(builder: (ctx) {
      return Visibility(
        visible: question.isShown!,
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              LabelWidget(
                title: question.titulo!,
                fontSize: DeviceSize.fontSize(20, 24),
                fontWeight: FontWeight.bold,
                textColor: themesController.getPrimaryColor(),
                textAlign: TextAlign.center,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 16),
                child: Container(
                  color: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (ctx.selected!.isSync != SyncEnum.finished)
                        Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            ButtonWithIcon(
                              onPressed: onPressed,
                              fontSize: 12.sp,
                              title: "Adicionar".toUpperCase(),
                              icon: Icon(
                                FontAwesomeIcons.solidImage,
                                size: 14.w,
                                color: Colors.white,
                              ),
                            )
                          ],
                        ),
                      if (ctx.selected!.isSync != SyncEnum.finished)
                        const Divider(thickness: 2),
                      SizedBox(
                          width: MediaQuery.of(context).size.width,
                          child: _buildImage(ctx))
                    ],
                  ),
                ),
              ),
              if (question.hasDivisor!)
                Divider(
                  color: themesController.getBackgroundColor(),
                  height: 2,
                  thickness: BorderSide.strokeAlignCenter,
                )
            ],
          ),
        ),
      );
    });
  }

  Widget _buildImage(ResearchesComplementaryController ctrl) {
    question;

    if (question.answers is List &&
        (question.answers as List).every(
            (item) => item is ResearchesComplementarySendRespostasUsuario)) {
      // Seu código aqui

      List<ResearchesComplementarySendRespostasUsuario> answers = (question
              .answers as List)
          .map((item) => item as ResearchesComplementarySendRespostasUsuario)
          .toList();

      if (answers.isNotEmpty) {
        return SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: answers.map((e) {
              return Padding(
                padding: const EdgeInsets.only(left: 5, right: 5),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomInkWell(
                      onTap: () {
                        showDialog(
                          context: Get.context!,
                          builder: (ctx) => AlertDialog(
                            title: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                LabelWidget(
                                  title: "Previsualização foto",
                                  fontSize: DeviceSize.fontSize(14, 18),
                                  fontWeight: FontWeight.bold,
                                ),
                                IconButton(
                                  onPressed: () => Navigator.pop(ctx),
                                  icon: const Icon(Icons.close),
                                )
                              ],
                            ),
                            content: SingleChildScrollView(
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    height:
                                        MediaQuery.of(ctx).size.height * 0.7,
                                    width: MediaQuery.of(ctx).size.width * 0.7,
                                    child: PhotoViewPreview(
                                      imagePath: e.resposta!,
                                      orientation:
                                          e.orientation ?? Orientation.portrait,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                      child: SizedBox(
                        width: 72.w, // Defina a altura se necessário
                        child: Stack(
                          children: [
                            Image.file(
                              File(e.resposta!),
                              width: 72.w,
                            ),
                            SizedBox(
                              width: 72.w,
                              height: 100.w,
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Icon(
                                    size: 30.w,
                                    Icons.remove_red_eye_outlined,
                                    color: Colors.white,
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    if (ctrl.selected!.isSync != SyncEnum.finished)
                      IconButtonWidget(
                        colorButton: Colors.red,
                        width: 72.w,
                        icon: const Icon(
                          FontAwesomeIcons.trash,
                          color: Colors.white,
                          size: 16,
                        ),
                        onTap: () async {
                          await ctrl.removePicture(question, e);
                        },
                      ),
                  ],
                ),
              );
            }).toList(),
          ),
        );
      }
    }
    return const SizedBox();
  }
}
