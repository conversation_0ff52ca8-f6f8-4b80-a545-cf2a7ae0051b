import 'package:dynatrace_flutter_plugin/dynatrace_flutter_plugin.dart';

import 'dynatrace_custom_action.dart';

class DynatraceCustomSubAction {
  final DynatraceCustomAction dynatraceCustomAction;
  final DynatraceAction action;

  DynatraceCustomSubAction(this.dynatraceCustomAction, this.action);

  void reportValue<T>(String? valueName, T? value) {
    if (valueName != null) {
      action.reportStringValue(valueName, value?.toString());
    }
  }

  void reportEvent(String eventName) {
    action.reportEvent(eventName);
  }

  void reportError(String? errorName, {int? errorCode}) {
    action.reportError(errorName, errorCode);
  }

  Future<void> reportZoneStacktrace(
      dynamic error, StackTrace stacktrace) async {
    await dynatraceCustomAction.reportZoneStacktrace(error, stacktrace);
  }
}
