import 'package:pharmalink/modules/stores_parameters/models/condicao_comercial_base_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/distribuidores_prazo_pagamento_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/parametrizacao_looping_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/prazo_pagamento_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/tabloides_model.dart';

class ParametrizacaoModel {
  ParametrizacaoLoopingModel? parametrizacaoLooping;
  List<DistribuidoresPrazoPagamentoModel>? distribuidoresPrazoPagamento;

  List<PrazoPagamentoModel>? prazoPagamento;
  List<TabloidesModel>? tabloides;
  CondicaoComercialBaseModel? condicaoComercialBase;
  String? chaveUnicaPedido;

  ParametrizacaoModel({
    this.parametrizacaoLooping,
    this.distribuidoresPrazoPagamento,
    this.prazoPagamento,
    this.tabloides,
    this.condicaoComercialBase,
    this.chaveUnicaPedido,
  });

  ParametrizacaoModel.fromJson(Map<String, dynamic> json) {
    parametrizacaoLooping = json['ParametrizacaoLooping'] != null
        ? ParametrizacaoLoopingModel.fromJson(json['ParametrizacaoLooping'])
        : null;
    if (json['DistribuidoresPrazoPagamento'] != null) {
      distribuidoresPrazoPagamento = <DistribuidoresPrazoPagamentoModel>[];
      json['DistribuidoresPrazoPagamento'].forEach((v) {
        distribuidoresPrazoPagamento!
            .add(DistribuidoresPrazoPagamentoModel.fromJson(v));
      });
    }

    if (json['PrazoPagamento'] != null) {
      prazoPagamento = <PrazoPagamentoModel>[];
      json['PrazoPagamento'].forEach((v) {
        prazoPagamento!.add(PrazoPagamentoModel.fromJson(v));
      });
    }

    if (json['Tabloides'] != null) {
      tabloides = <TabloidesModel>[];
      json['Tabloides'].forEach((v) {
        tabloides!.add(TabloidesModel.fromJson(v));
      });
    }
    condicaoComercialBase = json['CondicaoComercialBase'] != null
        ? CondicaoComercialBaseModel.fromJson(json['CondicaoComercialBase'])
        : null;
    chaveUnicaPedido = json['ChaveUnicaPedido'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (parametrizacaoLooping != null) {
      data['ParametrizacaoLooping'] = parametrizacaoLooping!.toJson();
    }
    data['DistribuidoresPrazoPagamento'] = distribuidoresPrazoPagamento;

    data['PrazoPagamento'] = prazoPagamento;
    if (tabloides != null) {
      data['Tabloides'] = tabloides!.map((v) => v.toJson()).toList();
    }
    if (condicaoComercialBase != null) {
      data['CondicaoComercialBase'] = condicaoComercialBase!.toJson();
    }
    data['ChaveUnicaPedido'] = chaveUnicaPedido;

    return data;
  }
}
