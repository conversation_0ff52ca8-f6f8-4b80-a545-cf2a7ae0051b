import 'dart:convert';

import 'package:dynatrace_flutter_plugin/dynatrace_flutter_plugin.dart';
import 'package:pharmalink/config/databases/call_context.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/order_types/models/order_parameters_model.dart';
import 'package:pharmalink/modules/order_types/models/order_types_types_model.dart';
import 'package:pharmalink/modules/orders/models/orders_local_data_model.dart';
import 'package:pharmalink/modules/stores_parameters/enuns/type_order_enum.dart';

class OrderTypesController
    extends GetxControllerInstrumentado<OrderTypesController>
    with TraceableController {
  List<OrderTypesTypesModel> types = [];

  PlkLoading loading = PlkLoading();

  @override
  Future<void> onReady() async {
    setTypes();
    await initialize();
    super.onReady();
  }

  Future<void> initialize() async {}

  Future<void> verifyLocalOrder() async {
    if (globalParams.order.orderLocal != null) {
      globalParams.order
          .setOrderParameters(globalParams.order.orderLocal!.parameters!);
      if (globalParams.getCurrentStore()!.dataExtra?.offlineDateSync == null) {
        bool isConnected = await appController.checkConnectivity();
        if (!isConnected) {
          SnackbarCustom.snackbarError(AppStrings.noInternet);
          return;
        }
        await appController.isValidToken(noMessages: true);
      }
      if (appController.hasErrorRefreshToken == true) {
        SnackbarCustom.snackbarError(AppStrings.tokenExpired);
        return;
      }
      final typeOrderStrEnum =
          getTyperOrderText(globalParams.order.orderParameters.typeOrderId!);
      final typeOrderEnum = globalParams.order.orderParameters.typeOrderId!;
      globalParams.order.setTypeOrderStr(typeOrderStrEnum);
      globalParams.order
          .setTypeOrderId(globalParams.order.orderParameters.typeOrderId!);
      globalParams.order.orderParameters.typeOrderId = typeOrderEnum;

      if (globalParams.getCurrentStore()!.dataExtra?.offlineDateSync != null) {
        await storeParametersController.loadOfflineData(
            storeId: globalParams.getCurrentStore()!.idLoja!,
            typeOrder: typeOrderStrEnum);
      } else {
        await storeParametersController.getDataByStoreIdAndType(
          storeId: globalParams.getCurrentStore()!.idLoja!,
          typeOrder: typeOrderStrEnum,
          isOffline: false,
        );
      }

      if (!storeParametersController.dataList.any((element) =>
          element.idLoja == globalParams.getCurrentStore()!.idLoja!)) {
        await Dialogs.info("Atenção", "Parametros da loja não encontrado");
        return;
      }

      globalParams.order.setParametrization(storeParametersController
          .storeSelected
          .firstWhere((element) => element.tipoPedido == typeOrderStrEnum)
          .parametrizacao);

      if (typeOrderEnum == TyperOrderEnum.especial) {
        await ordersTabloidController.initialize();
      } else {
        await orderPaymentTypeController.initialize();
      }
    }
  }

  void addTypeIfAvailable(bool Function() hasType, Widget icon, String title,
      String typeOrderStrEnum, int typeOrderEnum) {
    if (hasType()) {
      types.add(OrderTypesTypesModel(
        icon: icon,
        title: title,
        typeOrderEnum: typeOrderEnum,
        typeOrderStrEnum: typeOrderStrEnum,
      ));
    }
  }

  Future<void> advance(String typeOrderStrEnum, int typeOrderEnum) async {
    return trace('advance', () async {
      var (leaveAction, subAction) = dynatraceAction.subActionReport("advance");

      try {
        subAction.reportEvent(
            "typeOrderStrEnum: $typeOrderStrEnum - typeOrderEnum: $typeOrderEnum");

        globalParams.clearData();
        final storeId = globalParams.getCurrentStore()!.idLoja!;
        bool shouldExecuteBlock2 = true;
        //currentStoreId = storeId;

        if (typeOrderEnum != TyperOrderEnum.especial &&
            await OrdersLocalDataModel()
                .exists(storeId: storeId, typeOrder: typeOrderEnum)) {
          await Dialogs.confirm(
            AppStrings.orderUnfinished,
            AppStrings.orderUnfinishedMessage(getTypeOrderName(typeOrderEnum)),
            buttonNameCancel: "Continuar Pedido".toUpperCase(),
            buttonNameOk: "Descartar Pedido".toUpperCase(),
            onPressedOk: () async {
              shouldExecuteBlock2 = true;
              GetC.close();
              appLogWithDynatrace(subAction, "Excluir pedido não finalizado",
                  data: jsonEncode({
                    "typeOrder": typeOrderStrEnum.toString(),
                    "storeId": storeId,
                    "workspaceId":
                        appController.workspace!.workspaceId.toString(),
                  }));
              await OrdersLocalDataModel().deleteOrder(
                storeId: storeId,
                typeOrder: typeOrderEnum,
              );

              subAction.reportEvent("Descartar Pedido");
            },
            onPressedCancel: () async {
              globalParams.order.orderLocal = await OrdersLocalDataModel()
                  .getFirst(
                      storeId: globalParams.getCurrentStore()!.idLoja!,
                      typeOrder: typeOrderEnum);
              subAction.reportEvent("Continuar Pedido");
              shouldExecuteBlock2 = false;
              GetC.close();
              loading.hide();
              await verifyLocalOrder();
            },
          );
        }

        if (shouldExecuteBlock2) {
          if (globalParams.getCurrentStore()!.dataExtra?.offlineDateSync ==
              null) {
            bool isConnected = await appController.checkConnectivity();
            if (!isConnected) {
              SnackbarCustom.snackbarError(AppStrings.noInternet);
              appLogWithDynatrace(subAction,
                  "Ocorreu um erro ao verificar a conexão: ${AppStrings.noInternet}");
              return;
            }
            await appController
                .withDynatraceAction(dynatraceAction)
                .isValidToken(noMessages: true);
            if (appController.hasErrorRefreshToken == true) {
              SnackbarCustom.snackbarError(AppStrings.tokenExpired);
              appLogWithDynatrace(subAction,
                  "Ocorreu um erro ao atualizar o token: ${AppStrings.tokenExpired}");
              return;
            }
          }

          globalParams.order.setTypeOrderStr(typeOrderStrEnum);
          globalParams.order.setTypeOrderId(typeOrderEnum);
          globalParams.order.orderParameters.typeOrderId = typeOrderEnum;

          await reportEventByAdvance(typeOrderStrEnum, typeOrderEnum);

          if (globalParams.getCurrentStore()!.dataExtra?.offlineDateSync !=
              null) {
            appLogWithDynatrace(
                subAction, "Buscar dados offline: $typeOrderStrEnum",
                data: typeOrderStrEnum);
            await setDataOffline(typeOrderStrEnum);
          } else {
            appLogWithDynatrace(
                subAction, "Buscar dados online: $typeOrderStrEnum",
                data: typeOrderStrEnum);
            await setDataOnline(typeOrderStrEnum);
          }

          if (typeOrderEnum == TyperOrderEnum.especial) {
            appLogWithDynatrace(subAction, "Chamar o initialize do Tabloide",
                data: typeOrderStrEnum);
            await ordersTabloidController.initialize();
            // Get.toNamed(RoutesPath.ordersTabloid);
          } else {
            appLogWithDynatrace(
                subAction, "Chamar o initialize do Tipo de Pagamento",
                data: typeOrderStrEnum);
            await orderPaymentTypeController.initialize();
            // Get.toNamed(RoutesPath.orderPaymentType);
          }
        }
      } catch (e, s) {
        loading.hide();
        SnackbarCustom.snackbarError(e.toString());
        appErrorWithDynatrace(subAction, e, s);
        rethrow;
      } finally {
        leaveAction();
      }
    });
  }

  void setTypes() {
    types = [];

    globalParams.order.setOrderParameters(OrderParametersModel());

    addTypeIfAvailable(
        generalParameterizationController.hasOrderDefault,
        const Icon(FontAwesomeIcons.computer),
        "Pedido Padrão",
        TyperOrderStringEnum.padrao,
        TyperOrderEnum.padrao);

    addTypeIfAvailable(
        generalParameterizationController.hasOrderRep,
        const Icon(FontAwesomeIcons.tabletScreenButton),
        "Pedido Representante",
        TyperOrderStringEnum.rep,
        TyperOrderEnum.rep);

    addTypeIfAvailable(
        generalParameterizationController.hasOrderEspecial,
        SvgPicture.asset(
          AppImages.orderSpecial,
          width: 28,
          height: 28,
        ),
        "Pedido Especial",
        TyperOrderStringEnum.especial,
        TyperOrderEnum.especial);

    update();
  }

  Future<void> setDataOnline(String typeOrderStrEnum) async {
    loading.show(title: AppStrings.load);

    await storeParametersController.getDataByStoreIdAndType(
      storeId: globalParams.getCurrentStore()!.idLoja!,
      typeOrder: typeOrderStrEnum,
      isOffline:
          globalParams.getCurrentStore()!.dataExtra?.offlineDateSync != null,
    );

    if (!storeParametersController.dataList.any((element) =>
        element.idLoja == globalParams.getCurrentStore()!.idLoja!)) {
      loading.hide();
      //await Dialogs.info("Atenção", "Parametros da loja não encontrado");
      throw Exception("Parametros da loja não encontrado");
    }
    if (storeParametersController.storeSelected
        .any((element) => element.tipoPedido == typeOrderStrEnum)) {
      globalParams.order.setParametrization(storeParametersController
          .storeSelected
          .firstWhere((element) => element.tipoPedido == typeOrderStrEnum)
          .parametrizacao);
    } else {
      throw Exception("Parametros da loja não encontrado");
    }

    loading.hide();
  }

  Future<void> setDataOffline(String typeOrderStrEnum) async {
    return trace('setDataOffline', () async {
      loading.show(title: AppStrings.load);

      var (leaveAction, _) = dynatraceAction.subActionReport("setDataOffline");

      try {
        await storeParametersController.loadOfflineData(
            storeId: globalParams.getCurrentStore()!.idLoja!,
            typeOrder: typeOrderStrEnum);

        if (!storeParametersController.dataList.any((element) =>
            element.idLoja == globalParams.getCurrentStore()!.idLoja!)) {
          loading.hide();
          await Dialogs.info("Atenção", "Parametros da loja não encontrado");
          appLog(
              "Parametros > Load Offline Data (Parametros da loja não encontrado)",
              data: {
                "storeId": globalParams.getCurrentStore()!.idLoja!,
                "typeOrder": typeOrderStrEnum,
                "message": "Parametros da loja não encontrado",
              });
          return;
        }

        globalParams.order.setParametrization(storeParametersController
            .storeSelected
            .firstWhere((element) => element.tipoPedido == typeOrderStrEnum)
            .parametrizacao);
        appLog("Parametros > Load Offline Data", data: {
          "storeId": globalParams.getCurrentStore()!.idLoja!,
          "typeOrder": typeOrderStrEnum,
          "orderParam": globalParams.getParametrization(),
        });
      } finally {
        loading.hide();
        leaveAction();
      }
    });
  }

  String getTypeOrderName(int? id) {
    switch (id) {
      case 2:
        return "Pedido Especial";
      case 4:
        return "Pedido Representante";
      case 1:
        return "Pedido Padrão";
      default:
        return "";
    }
  }

  Future<void> reportEventByAdvance(
      String typeOrderStrEnum, int typeOrderEnum) async {
    final reportAll = Dynatrace().enterAction(
        "Advance - Order Types Controller: ${globalParams.order.currentStore?.cNPJ ?? "-"}");
    try {
      final cnpj = globalParams.getCurrentStore()?.cNPJ ?? "-";
      final razaoSocial = globalParams.getCurrentStore()?.razaoSocial ?? "-";
      final idPdv = globalParams.getCurrentStore()?.idPdv.toString() ?? "-";
      final idLoja = globalParams.getCurrentStore()?.idLoja.toString() ?? "-";
      final orderType = getTyperOrderText(typeOrderEnum);
      final temPedidoLocal =
          globalParams.order.orderLocal != null ? "Sim" : "Não";
      final idTipoPedidoLocal =
          globalParams.order.orderLocal?.parameters?.typeOrderId?.toString() ??
              "-";
      final modoPedido =
          globalParams.getCurrentStore()?.dataExtra?.offlineDateSync != null
              ? "Offline"
              : "Online";

      reportAll.reportStringValue("CNPJ do PDV", cnpj);
      reportAll.reportStringValue("Razão Social do PDV", razaoSocial);
      reportAll.reportStringValue("ID do PDV", idPdv);
      reportAll.reportStringValue("ID da Loja do PDV", idLoja);
      reportAll.reportStringValue("Tipo de Pedido", orderType);
      reportAll.reportStringValue("Tem Pedido Local", temPedidoLocal);
      reportAll.reportStringValue(
          "ID do Tipo de Pedido Local", idTipoPedidoLocal);
      reportAll.reportStringValue("Modo de Pedido", modoPedido);

      appLog("Parametros > Advance", data: {
        "cnpj": cnpj,
        "razaoSocial": razaoSocial,
        "idPdv": idPdv,
        "idLoja": idLoja,
        "tipoPedido": orderType,
        "temPedidoLocal": temPedidoLocal,
        "idTipoPedidoLocal": idTipoPedidoLocal,
        "modoPedido": modoPedido,
      });
    } catch (e) {
      reportAll.reportStringValue("Erro ao Reportar Eventos", e.toString());
    }
  }
}
