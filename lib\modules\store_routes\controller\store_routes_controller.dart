import 'package:maps_launcher/maps_launcher.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/notification/models/notification_model.dart';
import 'package:pharmalink/modules/orders_resume/enuns/status_syncronization_enum.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';
import 'package:pharmalink/modules/stores/models/stores_data_extra_model.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';
import 'package:uuid/uuid.dart';

class StoreRoutesController
    extends GetxControllerInstrumentado<StoreRoutesController>
    with TraceableController {
  StoreRoutesController();

  bool hasSyncOffline = false;
  int selectedTab = 0;
  bool hasVisitToSync = false;

  List<StoresModel> storeListFull = [];

  int? cnpjPanel;
  int? cnpjPlanned;

  @override
  void onInit() {
    super.onInit();
    ever(storeRoutesPanelController.rx, (_) => update());
  }

  @override
  Future<void> onReady() async {
    super.onReady();
    await loadStores();
  }

  void changeTab(int index) {
    selectedTab = index;
    _resetAndRefetchController(index);
    update();
  }

  Future<void> loadStores() async {
    storeListFull = await StoresModel().getList();
    update();
  }

  void _resetAndRefetchController(int index) {
    if (index == 0) {
      storeRoutesPanelController.resetAndRefetch();
    } else if (index == 1) {
      storeRoutesPlannedController.resetAndRefetch().then((value) {
        setHasVisitToSync();
      });
    }
    loadStores();
  }

  Future<void> verifyHasVisit() async {
    final allStores = await StoresModel().getList();
    globalParams.hasVisit = allStores.any((element) => element.visita == true);
    globalParams.hasStoreInitialize = true;
    update();
  }

  Future<void> updateData() async {
    hasSyncOffline =
        generalParameterizationController.parameterByKey?.enabled ?? false;

    await storeRoutesPanelController.resetAndRefetch();
    update();
  }

  Future<void> updateRoutesSync() async {
    return trace('updateRoutesSync', () async {
      bool isMyPanel = selectedTab == 0;
      List<StoresModel> storesToUpdate = _getStoresToUpdate(isMyPanel);

      if (storesToUpdate.isNotEmpty) {
        for (var item in storesToUpdate) {
          item.dataExtra?.canPlanned = true;
          item.dataExtra?.currentDate = DateTime.now();
          item.dataExtra?.order ??= 999;
        }

        await Future.wait(storesToUpdate.map(updateStore));

        await resetAllControllers();

        if (isMyPanel) {
          selectedTab = 1;
          update();
        }

        await _syncStoreRoutes(
            storesToUpdate.where((e) => e.dataExtra!.canVisitaSync!).toList());
        await _syncStoreRoutesOffline(storesToUpdate
            .where((e) =>
                e.dataExtra!.canOrderOfflineSync! &&
                e.dataExtra!.offlineDateSync == null)
            .toList());
      } else {
        await storeRoutesSyncService.syncRoutesGetList(syncVisits: true);
      }
    });
  }

  List<StoresModel> _getStoresToUpdate(bool isMyPanel) {
    if (isMyPanel) {
      return storeRoutesPanelController.storesFullList
          .where((element) =>
              (element.dataExtra!.canVisitaSync! ||
                  element.dataExtra!.canOrderOfflineSync!) &&
              !element.dataExtra!.canPlanned!)
          .toList();
    } else {
      return storeRoutesPlannedController.storesList
          .where((element) =>
              (element.dataExtra!.canVisitaSync! ||
                  element.dataExtra!.canOrderOfflineSync!) &&
              element.dataExtra!.canPlanned!)
          .toList();
    }
  }

  Future<void> resetAllControllers() async {
    await storeRoutesPanelController.resetAndRefetch();
    await storeRoutesPlannedController.resetAndRefetch();
    setHasVisitToSync();
  }

  void setHasVisitToSync() async {
    hasVisitToSync = storeRoutesPlannedController.hasVisitToSync();
    update();
  }

  Future<void> _syncStoreRoutes(List<StoresModel> storesToUpdate) async {
    if (storesToUpdate.isEmpty) return;
    await storeRoutesSyncService.syncStoreRoutes(storesToUpdate
        .where(
            (e) => e.dataExtra!.canVisitaSync! && !e.dataExtra!.hasRouteSync!)
        .toList());
  }

  Future<void> _syncStoreRoutesOffline(List<StoresModel> storesToUpdate) async {
    if (storesToUpdate.isEmpty) return;
    await storeRoutesOfflineSyncService.startSyncOffline(storesToUpdate
        .where((e) => e.dataExtra!.canOrderOfflineSync!)
        .toList());
  }

  Future<void> removeStore(StoresModel item) async {
    return trace('removeStore', () async {
      await dbContext.deleteByKey(
        key: DatabaseModels.storesModel,
        storeId: item.idLoja,
        userId: appController.userLogged?.userId!,
        workspaceId: appController.workspace!.workspaceId,
        isLog: true,
      );
    });
  }

  Future<void> updateStore(StoresModel item) async {
    return trace('updateStore', () async {
      await dbContext.deleteByKey(
        key: DatabaseModels.storesModel,
        storeId: item.idLoja,
        userId: appController.userLogged?.userId!,
        workspaceId: appController.workspace!.workspaceId,
        isLog: true,
      );

      await dbContext.addData(
        key: DatabaseModels.storesModel,
        data: item,
        storeId: item.idLoja,
        userId: appController.userLogged?.userId!,
        workspaceId: appController.workspace!.workspaceId,
        hashCode: item.dataExtra?.canPlanned == true ? "planned" : null,
        clearCurrentData: false,
      );
      await item.dataExtra?.updateDataExtra(item.dataExtra!);
    });
  }

  void setStoreDetail(StoresModel? v) {
    Get.toNamed(RoutesPath.storeRoutesDetails, arguments: {'store': v});
  }

  void openAddress(StoresModel data) async {
    if (data.logradouro == null) {
      SnackbarCustom.snackbarError("Endereço inválido!");
      return;
    }

    String address =
        "${data.logradouro!}, ${data.numero ?? "S/N"},${data.bairro ?? ""}, ${data.cidade ?? ""},${data.estado ?? ""}, ${data.cEP ?? ""}";
    MapsLauncher.launchQuery(address);
  }

  Future<void> orderClick(StoresModel item) async {
    final loading = PlkLoading();
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("orderClick");
    try {
      globalParams.order.startFlow(item);

      await _validateToken(subAction);
      if (appController.hasErrorRefreshToken) {
        return;
      }

      if (await _checkExistingOrder(item)) {
        await _handleExistingOrder(item, loading, subAction);
      } else {
        await _startNewOrder(item, loading);
      }
    } catch (e, s) {
      SnackbarCustom.snackbarError(e.toString());
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      loading.hide();
      leaveAction();
    }
  }

  Future<void> _validateToken(subAction) async {
    await appController
        .withDynatraceAction(dynatraceAction)
        .isValidToken(noMessages: true);
    if (appController.hasErrorRefreshToken) {
      SnackbarCustom.snackbarError(AppStrings.tokenExpired);
      subAction.reportEvent(AppStrings.tokenExpired);
      Get.offAndToNamed(RoutesPath.login);
    }
  }

  Future<bool> _checkExistingOrder(StoresModel item) async {
    return await SyncronizationModel().exists(
        workspaceId: appController.workspace!.workspaceId!,
        storeId: item.idLoja,
        today: DateTime.now());
  }

  Future<void> _handleExistingOrder(
      StoresModel item, PlkLoading loading, subAction) async {
    await Dialogs.confirm(
      AppStrings.attention,
      AppStrings.storeOrderMsg(item.nomeFantasia ?? ""),
      buttonNameCancel: "Selecionar".toUpperCase(),
      buttonNameOk: "Continuar".toUpperCase(),
      onPressedOk: () async {
        GetC.close();
        await _startNewOrder(item, loading);
        subAction.reportEvent("Continuar");
      },
      onPressedCancel: () async {
        GetC.close();
        //_showStoreModal();
        subAction.reportEvent("Selecionar");
      },
    );
  }

  Future<void> _startNewOrder(StoresModel item, PlkLoading loading) async {
    loading.show(title: AppStrings.load);
    await generalParameterizationController.getData();
    await generalParameterizationController.getFilterCustom();
    globalParams.order.startFlow(item);
    loading.hide();
    Get.toNamed(RoutesPath.orderTypes);
  }

  // void _showStoreModal() {
  //   showDialog(
  //     context: Get.context!,
  //     barrierDismissible: true,
  //     barrierColor: Colors.transparent,
  //     builder: (context) {
  //       return Dialog(
  //         shape:
  //             RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
  //         backgroundColor: themesController.getPrimaryColor(),
  //         child: const StoreModelPdvWidget(),
  //       );
  //     },
  //   );
  // }

  Future<void> updateStoreCommercialConditions(StoresModel item) async {
    return trace('updateStoreCommercialConditions', () async {
      final loading = PlkLoading();
      loading.show(title: AppStrings.load);
      if (!item.dataExtra!.canPlanned!) {
        item.dataExtra!.canPlanned = true;
        item.dataExtra!.currentDate = DateTime.now();
        item.dataExtra!.canOrderOfflineSync = true;
      }
      await updateStore(item);
      await storeRoutesOfflineSyncService.startSyncOffline([item]);

      loading.hide();
      await resetAllControllers();
      selectedTab = 1;
      update();
    });
  }

  String getButtonOrderTitle(bool isOffline) {
    if (!hasSyncOffline) {
      return "Fazer Pedido";
    }
    return isOffline
        ? "Fazer Pedido sem Internet"
        : "Fazer Pedido com Internet";
  }

  Future<void> syncStoreData({bool? forceUpdate, bool? isUpdateList}) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("Stores - GetData");
    try {
      if (forceUpdate != true) {
        await StoresModel().resetPlanned();
        return;
      }
      final result = await storesApi.getStores();
      if (result.error != null) {
        SnackbarCustom.snackbarError(
            result.error?.message ?? "Ocorreu um erro ao sincronizar as lojas");
      } else {
        await dbContext.deleteByKey(
          key: DatabaseModels.storesModel,
          userId: appController.userLogged?.userId!,
          workspaceId: appController.workspace!.workspaceId,
        );
        for (var store in result.data!) {
          store.dataExtra =
              await StoresDataExtraModel().getFirst(storeId: store.idLoja!);

          await dbContext.withControllerAction(this).addData(
                key: DatabaseModels.storesModel,
                data: store,
                storeId: store.idLoja,
                userId: appController.userLogged?.userId!,
                workspaceId: appController.workspace!.workspaceId,
                hashCode:
                    store.dataExtra!.canPlanned == true ? "planned" : null,
                clearCurrentData: false,
              );
        }
        await resetAllControllers();

        // //Limpa a tabela de lojas
        // if (isUpdateList != true) {
        // } else {
        //   final currentStoreList = await StoresModel().getList();
        //   final newStoreList = result.data!;

        //   final exclusionList = currentStoreList
        //       .where((currentStore) => !newStoreList
        //           .any((newStore) => newStore.idLoja == currentStore.idLoja))
        //       .toList();

        //   final inclusionList = newStoreList
        //       .where((newStore) => !currentStoreList.any(
        //           (currentStore) => currentStore.idLoja == newStore.idLoja))
        //       .toList();

        //   if (exclusionList.isNotEmpty) {
        //     await removeStoreAfterSync(exclusionList, newStoreList);
        //     for (var item in exclusionList) {
        //       await removeStore(item);
        //     }
        //   }
        //   if (inclusionList.isNotEmpty) {
        //     for (var item in inclusionList) {
        //       await updateStore(item);
        //     }
        //   }

        //   for (var newStore in newStoreList) {
        //     var currentStore = currentStoreList
        //         .firstWhereOrNull((store) => store.idLoja == newStore.idLoja);
        //     if (currentStore != null) {
        //       (bool, StoresModel) check =
        //           await StoresModel.checkAndUpdate(currentStore, newStore);
        //       if (check.$1) {
        //         await updateStore(check.$2);
        //       }
        //     }
        //   }
        //   await resetAllControllers();
        // }
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> removeStoreAfterSync(
      List<StoresModel> currentStoreList, List<StoresModel> storesFull) async {
    String storeStrMessage = "";

    var uniqueIdLoja = Set.from(currentStoreList.map((store) => store.idLoja));
    var uniqueIdLojaNotInDataListFull = uniqueIdLoja
        .where((id) => !storesFull.any((store) => store.idLoja == id))
        .toSet();

    final ordersSync = await SyncronizationModel()
        .getList(workspaceId: appController.workspace!.workspaceId!);

    if (ordersSync.isNotEmpty) {
      var orderStoresId =
          Set.from(ordersSync.map((store) => store.payLoad!.pdvId));
      uniqueIdLojaNotInDataListFull = orderStoresId
          .where((id) => !storesFull.any((store) => store.idLoja == id))
          .toSet();

      final ordersRemove = ordersSync
          .where((element) =>
              uniqueIdLojaNotInDataListFull.contains(element.payLoad!.pdvId) &&
              element.status ==
                  getStatusSynchronizationValue(StatusSynchronization.notSent))
          .toList();

      if (ordersRemove.isNotEmpty) {
        for (var e in ordersRemove) {
          await dbContext.deleteByKey(
            hashCode: e.transactionKey,
            workspaceId: appController.workspace?.workspaceId,
            storeId: e.payLoad!.pdvId,
            userId: appController.userLogged!.userId,
            key: DatabaseModels.syncronization,
          );

          storeStrMessage +=
              "\n${e.payLoad!.pdvCnpj} - ${e.payLoad!.pdvName}\n";
        }
        if (storeStrMessage.isNotEmpty) {
          final id = const Uuid().v4().toString();
          await dbContext.withControllerAction(this).addData(
                clearCurrentData: true,
                key: DatabaseModels.notificationModel,
                userId: id,
                data: NotificationModel(
                  isShow: false,
                  type: 1,
                  id: id,
                  message:
                      "Os pedidos referentes as lojas a seguir, não podem ser concluídos, devido os PDV's não estarem mais disponiveis.\n  $storeStrMessage",
                ),
                workspaceId: appController.workspace!.workspaceId!,
              );
        }
      }
    }
  }

  Future<void> sendSyncVisits() async {
    GetC.close();
    var pkLoading = PlkLoading();
    try {
      pkLoading.show(title: AppStrings.load);
      final visitController = Get.find<VisitsController>();
      final visitIsSync = await visitController.syncVisits();
      await visitController.getSyncVisits();
      await resetAllControllers();
      pkLoading.hide();
      if (visitIsSync) {
        SnackbarCustom.snackbarSucess("Sincronização", AppStrings.syncVisits);
      }
    } catch (e) {
      pkLoading.hide();
      SnackbarCustom.snackbarError(e.toString());
    }
  }

  void setCnpjPanel(int? value) {
    cnpjPanel = value;
    update();
  }

  void setCnpjPlanned(int? value) {
    cnpjPlanned = value;
    update();
  }
}
