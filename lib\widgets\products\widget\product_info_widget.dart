import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class ProductDetailInfoWidget extends StatelessWidget {
  const ProductDetailInfoWidget(
      {super.key,
      required this.title,
      required this.value,
      this.valueFontSize});
  final String title;
  final String value;
  final double? valueFontSize;
  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return SizedBox(
      width: size.width * 0.5,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            LabelWidget(
              title: title,
              fontSize: 13.sp,
              fontWeight: FontWeight.normal,
              textColor: Colors.grey,
            ),
            Center(
              child: LabelWidget(
                title: value,
                fontSize: valueFontSize ?? DeviceSize.fontSize(18, 21),
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
