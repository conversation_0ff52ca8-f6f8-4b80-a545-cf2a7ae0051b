import 'dart:convert';

import 'package:pharmalink/exports/basic_exports.dart';

class MixProdutoRequest {
  int? pdvId;
  int? condicaoComercialBaseId;
  double? valorMinimoDePedido;
  List<IdsDistribuidores>? idsDistribuidores;
  int? tipoDePedido;
  bool? utilizaComboOferta;
  String? cNPJ;
  int? idPrazoPagamento;
  String? currentDate;

  MixProdutoRequest({
    this.pdvId,
    this.condicaoComercialBaseId,
    this.valorMinimoDePedido,
    this.idsDistribuidores,
    this.tipoDePedido,
    this.utilizaComboOferta,
    this.cNPJ,
    this.idPrazoPagamento,
    this.currentDate,
  });

  MixProdutoRequest.fromJson(Map<String, dynamic> json) {
    pdvId = json['PdvId'];
    valorMinimoDePedido = json['ValorMinimoDePedido'];
    condicaoComercialBaseId = json['CondicaoComercialBaseId'];
    if (json['IdsDistribuidores'] != null) {
      idsDistribuidores = <IdsDistribuidores>[];
      json['IdsDistribuidores'].forEach((v) {
        idsDistribuidores!.add(IdsDistribuidores.fromJson(v));
      });
    }
    tipoDePedido = json['TipoDePedido'];
    utilizaComboOferta = json['UtilizaComboOferta'];
    cNPJ = json['CNPJ'];
    idPrazoPagamento = json['idPrazoPagamento'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['PdvId'] = pdvId;
    data['ValorMinimoDePedido'] = valorMinimoDePedido;
    if (condicaoComercialBaseId != null) {
      data['CondicaoComercialBaseId'] = condicaoComercialBaseId;
    }
    if (idsDistribuidores != null) {
      data['IdsDistribuidores'] =
          idsDistribuidores!.map((v) => v.toJson()).toList();
    }
    data['TipoDePedido'] = tipoDePedido;
    data['UtilizaComboOferta'] = utilizaComboOferta;
    data['CNPJ'] = cNPJ;
    data['idPrazoPagamento'] = idPrazoPagamento;
    data['currentDate'] = currentDate;
    return data;
  }

  String toJsonHash() {
    return HashCreator.generateHash(jsonEncode(this));
  }
}

class IdsDistribuidores {
  int? idDistribuidor;
  int? ordem;

  IdsDistribuidores({this.idDistribuidor, this.ordem});

  IdsDistribuidores.fromJson(Map<String, dynamic> json) {
    idDistribuidor = json['IdDistribuidor'];
    ordem = json['Ordem'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdDistribuidor'] = idDistribuidor;
    data['Ordem'] = ordem;
    return data;
  }
}
