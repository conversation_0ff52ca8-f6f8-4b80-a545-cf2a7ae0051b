import 'package:get/get_state_manager/src/simple/get_state.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/orders_resume/controller/orders_resume_controller.dart';
import 'package:pharmalink/modules/orders_resume/pages/widgets/orders_resume_schedule.dart';

class OrdersResumeScheduleWidget extends StatelessWidget {
  const OrdersResumeScheduleWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrdersResumeController>(builder: (ctrl) {
      return ctrl.enableScheduleDate(globalParams.getTypeOrderId()!)
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                15.toHeightSpace(),
                LabelWidget(
                  title: "Programação de pedidos",
                  fontSize: DeviceSize.fontSize(16, 19),
                  fontWeight: FontWeight.bold,
                ),
                15.toHeightSpace(),
                CheckBoxHorizontalWidget(
                  //disable: ctrl.disableScheduleDate(),
                  items: [
                    CheckboxItem(
                      type: 2,
                      label: 'Programar pedido',
                      labelSize: DeviceSize.fontSize(16, 19),
                      value: ctrl.resume.isOrderSchedule ?? false,
                      onChanged: ctrl.setOrderSchedule,
                    ),
                  ],
                ),
                const Divider(
                  thickness: 1.2,
                ),
                Visibility(
                    visible: ctrl.resume.isOrderSchedule!,
                    child: const OrdersResumeSchedulePage()),
              ],
            )
          : 15.toHeightSpace();
    });
  }
}
