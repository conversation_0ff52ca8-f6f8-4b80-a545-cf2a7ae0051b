import 'package:pharmalink/modules/routes/models/destiny_model.dart';
import 'package:pharmalink/modules/routes/models/route_list_model.dart';

class Roteiro {
  String? dataAlteracao;
  String? dataInclusao;
  Destino? destino;
  int? idDestino;
  int? idOrigem;
  int? idRoteiro;
  bool? isDeleted;
  double? kMFinalInformado;
  double? kMInicialInformado;
  double? kMRodadoDestino;
  double? kMSugeridoDestino;
  double? kMTotalInformado;
  List<ListaRota>? listaRota;
  Destino? origem;

  Roteiro(
      {this.dataAlteracao,
      this.dataInclusao,
      this.destino,
      this.idDestino,
      this.idOrigem,
      this.idRoteiro,
      this.isDeleted,
      this.kMFinalInformado,
      this.kMInicialInformado,
      this.kMRodadoDestino,
      this.kMSugeridoDestino,
      this.kMTotalInformado,
      this.listaRota,
      this.origem});

  Roteiro.fromJson(Map<String, dynamic> json) {
    dataAlteracao = json['DataAlteracao'];
    dataInclusao = json['DataInclusao'];
    destino =
        json['Destino'] != null ? Destino.fromJson(json['Destino']) : null;
    idDestino = json['IdDestino'];
    idOrigem = json['IdOrigem'];
    idRoteiro = json['IdRoteiro'];
    isDeleted = json['IsDeleted'];
    kMFinalInformado = json['KMFinalInformado'];
    kMInicialInformado = json['KMInicialInformado'];
    kMRodadoDestino = json['KMRodadoDestino'];
    kMSugeridoDestino = json['KMSugeridoDestino'];
    kMTotalInformado = json['KMTotalInformado'];
    if (json['ListaRota'] != null) {
      listaRota = <ListaRota>[];
      json['ListaRota'].forEach((v) {
        listaRota!.add(ListaRota.fromJson(v));
      });
    }
    origem = json['Origem'] != null ? Destino.fromJson(json['Origem']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['DataAlteracao'] = dataAlteracao;
    data['DataInclusao'] = dataInclusao;
    if (destino != null) {
      data['Destino'] = destino!.toJson();
    }
    data['IdDestino'] = idDestino;
    data['IdOrigem'] = idOrigem;
    data['IdRoteiro'] = idRoteiro;
    data['IsDeleted'] = isDeleted;
    data['KMFinalInformado'] =  kMFinalInformado;
    data['KMInicialInformado'] = kMInicialInformado;
    data['KMRodadoDestino'] = kMRodadoDestino;
    data['KMSugeridoDestino'] = kMSugeridoDestino;
    data['KMTotalInformado'] = kMTotalInformado;
    if (listaRota != null) {
      data['ListaRota'] = listaRota!.map((v) => v.toJson()).toList();
    }
    if (origem != null) {
      data['Origem'] = origem!.toJson();
    }
    return data;
  }
}
