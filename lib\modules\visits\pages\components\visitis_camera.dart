import 'package:pharmalink/exports/basic_exports.dart';

class VisitisCameraWidget extends StatefulWidget {
  const VisitisCameraWidget({super.key});

  @override
  State<VisitisCameraWidget> createState() => _VisitisCameraWidgetState();
}

class _VisitisCameraWidgetState extends State<VisitisCameraWidget> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
