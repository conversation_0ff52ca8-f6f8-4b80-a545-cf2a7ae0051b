import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class StartSyncResponseModel extends SqfLiteBase<StartSyncResponseModel> {
  String? id;
  int? storeId;
  bool? isSynced;

  StartSyncResponseModel({
    this.id,
    this.storeId,
    this.isSynced,
  }) : super(DatabaseModels.startSyncResponseModel);

  StartSyncResponseModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.startSyncResponseModel) {
    id = json['id'];
    storeId = json['storeId'];
    isSynced = json['isSynced'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['storeId'] = storeId;
    data['isSynced'] = isSynced;
    return data;
  }

  Future<StartSyncResponseModel> getFirst() async {
    var list =
        await getAll<StartSyncResponseModel>(StartSyncResponseModel.fromJson);
    return list.first;
  }

  Future<List<StartSyncResponseModel>> getList() async {
    var list =
        await getAll<StartSyncResponseModel>(StartSyncResponseModel.fromJson);
    return list;
  }
}
