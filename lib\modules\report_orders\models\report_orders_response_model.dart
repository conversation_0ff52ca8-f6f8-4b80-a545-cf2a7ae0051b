class ReportOrdersListResponse {
  ReportOrdersListPaginacao? paginacao;
  List<ReportOrdersListPedidos>? pedidos;

  ReportOrdersListResponse({this.paginacao, this.pedidos});

  ReportOrdersListResponse.fromJson(Map<String, dynamic> json) {
    paginacao =
        json['Paginacao'] != null
            ? ReportOrdersListPaginacao.fromJson(json['Paginacao'])
            : null;
    if (json['Pedidos'] != null) {
      pedidos = <ReportOrdersListPedidos>[];
      json['Pedidos'].forEach((v) {
        pedidos!.add(ReportOrdersListPedidos.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (paginacao != null) {
      data['Paginacao'] = paginacao!.toJson();
    }
    if (pedidos != null) {
      data['Pedidos'] = pedidos!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ReportOrdersListPaginacao {
  int? pagina;
  int? tamanho;
  int? totalPaginas;
  int? proximaPagina;
  int? paginaAnterior;

  ReportOrdersListPaginacao({
    this.pagina,
    this.tamanho,
    this.totalPaginas,
    this.proximaPagina,
    this.paginaAnterior,
  });

  ReportOrdersListPaginacao.fromJson(Map<String, dynamic> json) {
    pagina = json['Pagina'];
    tamanho = json['Tamanho'];
    totalPaginas = json['TotalPaginas'];
    proximaPagina = json['ProximaPagina'];
    paginaAnterior = json['PaginaAnterior'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Pagina'] = pagina;
    data['Tamanho'] = tamanho;
    data['TotalPaginas'] = totalPaginas;
    data['ProximaPagina'] = proximaPagina;
    data['PaginaAnterior'] = paginaAnterior;
    return data;
  }
}

class ReportOrdersListPedidos {
  DateTime? datapedido;
  String? setor;
  String? representante;
  String? gdn;
  String? gcr;
  String? diretor;
  String? cnpj;
  String? bandeira;
  String? cidade;
  String? uf;
  String? nrPedido;
  String? nrPedidoPrincipal;
  String? origemPedido;
  String? tipoPedido;
  String? prazoPagamento;
  String? statusRetornoPedido;
  String? motivoDeNaoAtendimento;
  String? notaFiscal;
  String? dataNotaFiscal;
  String? cnpjCentroDistribuicao;
  String? centroDistribuidor;
  String? matrizDistribuidor;
  String? classificacao;
  String? faturamentoStatus;
  List<ReportOrdersListItens>? itens;
  List<ReportOrdersListPedidos>? pedidosSecundarios;
  bool? isExpanded;

  ReportOrdersListPedidos({
    this.datapedido,
    this.setor,
    this.representante,
    this.gdn,
    this.gcr,
    this.diretor,
    this.cnpj,
    this.bandeira,
    this.cidade,
    this.uf,
    this.nrPedido,
    this.nrPedidoPrincipal,
    this.origemPedido,
    this.tipoPedido,
    this.prazoPagamento,
    this.statusRetornoPedido,
    this.motivoDeNaoAtendimento,
    this.notaFiscal,
    this.dataNotaFiscal,
    this.cnpjCentroDistribuicao,
    this.centroDistribuidor,
    this.matrizDistribuidor,
    this.classificacao,
    this.faturamentoStatus,
    this.itens,
    this.pedidosSecundarios,
    this.isExpanded,
  });

  ReportOrdersListPedidos.fromJson(Map<String, dynamic> json) {
    datapedido =
        json['Datapedido'] != null ? DateTime.parse(json['Datapedido']) : null;
    setor = json['Setor'];
    representante = json['Representante'];
    gdn = json['Gdn'];
    gcr = json['Gcr'];
    diretor = json['Diretor'];
    cnpj = json['Cnpj'];
    bandeira = json['Bandeira'];
    cidade = json['Cidade'];
    uf = json['Uf'];
    nrPedido = json['NrPedido'];
    nrPedidoPrincipal = json['NrPedidoPrincipal'];
    origemPedido = json['OrigemPedido'];
    tipoPedido = json['TipoPedido'];
    prazoPagamento = json['PrazoPagamento'];
    statusRetornoPedido = json['StatusRetornoPedido'];
    motivoDeNaoAtendimento = json['MotivoDeNaoAtendimento'];
    notaFiscal = json['NotaFiscal'];
    dataNotaFiscal = json['DataNotaFiscal'];
    cnpjCentroDistribuicao = json['CnpjCentroDistribuicao'];
    centroDistribuidor = json['CentroDistribuidor'];
    matrizDistribuidor = json['MatrizDistribuidor'];
    classificacao = json['Classificacao'];
    faturamentoStatus = json['FaturamentoStatus'];
    if (json['Itens'] != null) {
      itens = <ReportOrdersListItens>[];
      json['Itens'].forEach((v) {
        itens!.add(ReportOrdersListItens.fromJson(v));
      });
    }
    if (json['PedidosSecundarios'] != null) {
      pedidosSecundarios = <ReportOrdersListPedidos>[];
      json['PedidosSecundarios'].forEach((v) {
        pedidosSecundarios!.add(ReportOrdersListPedidos.fromJson(v));
      });
    }
    isExpanded = json['isExpanded'] ?? false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Datapedido'] = datapedido;
    data['Setor'] = setor;
    data['Representante'] = representante;
    data['Gdn'] = gdn;
    data['Gcr'] = gcr;
    data['Diretor'] = diretor;
    data['Cnpj'] = cnpj;
    data['Bandeira'] = bandeira;
    data['Cidade'] = cidade;
    data['Uf'] = uf;
    data['NrPedido'] = nrPedido;
    data['NrPedidoPrincipal'] = nrPedidoPrincipal;
    data['OrigemPedido'] = origemPedido;
    data['TipoPedido'] = tipoPedido;
    data['PrazoPagamento'] = prazoPagamento;
    data['StatusRetornoPedido'] = statusRetornoPedido;
    data['MotivoDeNaoAtendimento'] = motivoDeNaoAtendimento;
    data['NotaFiscal'] = notaFiscal;
    data['DataNotaFiscal'] = dataNotaFiscal;
    data['CnpjCentroDistribuicao'] = cnpjCentroDistribuicao;
    data['CentroDistribuidor'] = centroDistribuidor;
    data['MatrizDistribuidor'] = matrizDistribuidor;
    data['Classificacao'] = classificacao;
    data['FaturamentoStatus'] = faturamentoStatus;
    if (itens != null) {
      data['Itens'] = itens!.map((v) => v.toJson()).toList();
    }
    if (pedidosSecundarios != null) {
      data['PedidosSecundarios'] =
          pedidosSecundarios!.map((v) => v.toJson()).toList();
    }
    data['isExpanded'] = isExpanded;
    return data;
  }
}

class ReportOrdersListItens {
  String? familia;
  String? ean;
  String? produto;
  String? statusRetornoItem;
  int? quantidadeSolicitada;
  int? quantidadeFaturada;
  int? quantidadeNaoFaturada;
  int? quantidadeRespondida;
  double? valorBrutoSolicitado;
  double? valorBrutoFaturado;
  double? valorBrutoNaoFaturado;
  double? valorLiquidoSolicitado;
  double? descontoPrograma;
  double? descontoForaPrograma;
  double? descontoNotaFiscal;
  double? valorLiquidoFaturado;
  double? valorLiquidoNaoFaturado;
  String? dUN;
  int? qtdeDUN;
  int? qtdeReal;
  String? motivoDeNaoAtendimento;
  String? codigo;
  String? categoria;

  ReportOrdersListItens({
    this.familia,
    this.ean,
    this.produto,
    this.statusRetornoItem,
    this.quantidadeSolicitada,
    this.quantidadeFaturada,
    this.quantidadeNaoFaturada,
    this.quantidadeRespondida,
    this.valorBrutoSolicitado,
    this.valorBrutoFaturado,
    this.valorBrutoNaoFaturado,
    this.valorLiquidoSolicitado,
    this.descontoPrograma,
    this.descontoForaPrograma,
    this.descontoNotaFiscal,
    this.valorLiquidoFaturado,
    this.valorLiquidoNaoFaturado,
    this.dUN,
    this.qtdeDUN,
    this.qtdeReal,
    this.motivoDeNaoAtendimento,
    this.codigo,
    this.categoria,
  });

  ReportOrdersListItens.fromJson(Map<String, dynamic> json) {
    familia = json['Familia'];
    ean = json['Ean'];
    produto = json['Produto'];
    statusRetornoItem = json['StatusRetornoItem'];
    quantidadeSolicitada = json['QuantidadeSolicitada'];
    quantidadeFaturada = json['QuantidadeFaturada'];
    quantidadeNaoFaturada = json['QuantidadeNaoFaturada'];
    quantidadeRespondida = json['QuantidadeRespondida'];
    valorBrutoSolicitado = double.parse(
      json['ValorBrutoSolicitado'].replaceAll(',', '.'),
    );

    valorBrutoFaturado = double.parse(
      json['ValorBrutoFaturado'].replaceAll(',', '.'),
    );
    valorBrutoNaoFaturado = double.parse(
      json['ValorBrutoNaoFaturado'].replaceAll(',', '.'),
    );
    valorLiquidoSolicitado = double.parse(
      json['ValorLiquidoSolicitado'].replaceAll(',', '.'),
    );
    valorLiquidoFaturado = double.parse(
      json['ValorLiquidoFaturado'].replaceAll(',', '.'),
    );
    valorLiquidoNaoFaturado = double.parse(
      json['ValorLiquidoNaoFaturado'].replaceAll(',', '.'),
    );

    descontoPrograma =
        json['DescontoPrograma'] != null
            ? double.parse(json['DescontoPrograma'].replaceAll(',', '.'))
            : null;
    descontoForaPrograma =
        json['DescontoForaPrograma'] != null
            ? double.parse(json['DescontoForaPrograma'].replaceAll(',', '.'))
            : null;
    descontoNotaFiscal =
        json['DescontoNotaFiscal'] != null
            ? double.parse(json['DescontoNotaFiscal'].replaceAll(',', '.'))
            : null;

    dUN = json['DUN'];
    qtdeDUN = json['QtdeDUN'];
    qtdeReal = json['QtdeReal'];
    motivoDeNaoAtendimento = json['MotivoDeNaoAtendimento'];
    codigo = json['CodigoProduto'];
    categoria = json['CategoriaProduto'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Familia'] = familia;
    data['Ean'] = ean;
    data['Produto'] = produto;
    data['StatusRetornoItem'] = statusRetornoItem;
    data['QuantidadeSolicitada'] = quantidadeSolicitada;
    data['QuantidadeFaturada'] = quantidadeFaturada;
    data['QuantidadeNaoFaturada'] = quantidadeNaoFaturada;
    data['QuantidadeRespondida'] = quantidadeRespondida;
    data['ValorBrutoSolicitado'] = valorBrutoSolicitado;
    data['ValorBrutoFaturado'] = valorBrutoFaturado;
    data['ValorBrutoNaoFaturado'] = valorBrutoNaoFaturado;
    data['ValorLiquidoSolicitado'] = valorLiquidoSolicitado;
    data['DescontoPrograma'] = descontoPrograma;
    data['DescontoForaPrograma'] = descontoForaPrograma;
    data['DescontoNotaFiscal'] = descontoNotaFiscal;
    data['ValorLiquidoFaturado'] = valorLiquidoFaturado;
    data['ValorLiquidoNaoFaturado'] = valorLiquidoNaoFaturado;
    data['DUN'] = dUN;
    data['QtdeDUN'] = qtdeDUN;
    data['QtdeReal'] = qtdeReal;
    data['MotivoDeNaoAtendimento'] = motivoDeNaoAtendimento;
    data['CodigoProduto'] = codigo;
    data['CategoriaProduto'] = categoria;
    return data;
  }
}
