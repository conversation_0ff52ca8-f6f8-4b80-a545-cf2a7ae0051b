import 'package:sqlite_explorer/exports/basic_exports.dart';
import 'package:sqlite_explorer/exports/get_exports.dart';
import 'package:sqlite_explorer/modules/home/<USER>/home_controller.dart';
import 'package:sqlite_explorer/widgets/label/formatted_json_widget.dart';
import 'package:sqlite_explorer/widgets/tiles/tiles_widget.dart';

class HomeContent extends StatelessWidget {
  const HomeContent({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(builder: (ctrl) {
      return Align(
          alignment: Alignment.topLeft,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: double.infinity,
                  child: Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                // LabelWidget(
                                //   title:
                                //       "Usuário: ${ctrl.selectedDatabaseItem?.userName ?? ""}",
                                //   fontSize: 14,
                                // ),
                                // LabelWidget(
                                //   title:
                                //       "Database: ${ctrl.selectedDatabaseItem?.fileName ?? ""}",
                                //   fontSize: 14,
                                // ),
                                // LabelWidget(
                                //   title:
                                //       "Data: ${ctrl.selectedDatabaseItem?.createdAt.formatDateTime() ?? ""}",
                                //   fontSize: 14,
                                // ),
                              ],
                            ),
                          ),
                          const Gap(16),
                          Flexible(
                            child: Wrap(
                              children: [
                                TilesWidget(
                                  title: "Nº de registros (total)",
                                  value: "${ctrl.totalCount}",
                                ),
                                TilesWidget(
                                  title: "Nº de registros (filtro)",
                                  value: "${ctrl.totalCountFiltered}",
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                const Divider(
                  thickness: 1,
                ),
                Expanded(
                    child: Padding(
                  padding: const EdgeInsets.all(24.0),
                  child: FormattedJsonWidget(jsonString: ctrl.jsonData ?? ""),
                )),
              ],
            ),
          ));
    });
  }
}
