import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';
import 'package:sqlite_explorer/app_constants.dart';
import 'package:sqlite_explorer/core/http_result/http_response.dart';
import 'package:sqlite_explorer/core/models/result_error.dart';

abstract class HttpMethods {
  static const String get = 'GET';
  static const String post = 'POST';
  static const String delete = 'DELETE';
  static const String patch = 'PATCH';
  static const String put = 'PUT';
}

typedef ParserError<T> = T Function(dynamic errorData);

class HttpManager {
  final Dio _dio;

  HttpManager(this._dio) {
    final myStatuses = {
      408,
      429,
      503,
      504,
      460,
      499,
      520,
      521,
      522,
      523,
      524,
      525,
      598,
      599
    };
    _dio.interceptors.add(
      RetryInterceptor(
        dio: _dio,
        logPrint: log,
        retries: 4,
        retryEvaluator: DefaultRetryEvaluator(myStatuses).evaluate,
        retryDelays: const [
          Duration(milliseconds: 500),
          Duration(milliseconds: 500),
          Duration(seconds: 1),
          Duration(seconds: 1),
        ],
      ),
    );
  }

  Future<Response<dynamic>> restRequest({
    required String url,
    required String method,
    Map? headers,
    String? baseUrl,
    String? contentType,
    Map? body,
    Object? bodyObject,
    Map<String, dynamic>? formData,
  }) async {
    final defaultHeaders = _buildHeaders(headers);
    final options = Options(
      method: method,
      contentType: contentType,
      headers: defaultHeaders,
    );

    try {
      return await _dio.request(
        baseUrl ?? '$appController.apiUrlAzure$url',
        options: options,
        data:
            formData != null ? FormData.fromMap(formData) : body ?? bodyObject,
      );
    } on DioException catch (error) {
      return _handleDioException(error, method, defaultHeaders);
    } catch (error) {
      return Response(
        statusCode: 500,
        statusMessage: error.toString(),
        requestOptions: RequestOptions(method: method, headers: defaultHeaders),
      );
    }
  }

  Future<HttpResponse<T>> request<T>({
    required String path,
    String method = 'GET',
    Map? headers,
    String? baseUrl,
    String? contentType,
    Map? body,
    Object? bodyObject,
    Map<String, dynamic>? formData,
    T Function(dynamic data)? parser,
    ParserError<T>? parserError,
  }) async {
    final defaultHeaders = _buildHeaders(headers);
    final options = Options(
      method: method,
      contentType: contentType,
      headers: defaultHeaders,
    );

    try {
      final response = await _dio.request(
        baseUrl ?? '${appController.apiUrlAzure}$path',
        options: options,
        data:
            formData != null ? FormData.fromMap(formData) : body ?? bodyObject,
      );

      return parser != null
          ? HttpResponse.success<T>(parser(response.data))
          : HttpResponse.success<T>(response.data);
    } on DioException catch (error) {
      return _handleHttpResponseError<T>(error, parserError);
    } catch (error) {
      return HttpResponse.fail<T>(
        statusCode: 400,
        message: error.toString(),
        data: ResultError(code: 500, error: error.toString()),
      );
    }
  }

  Map<String, String> _buildHeaders(Map? headers) {
    return headers?.cast<String, String>() ?? {}
      ..addAll({
        'Content-type': 'application/json',
        'Content-Encoding': 'gzip',
      });
  }

  Response<dynamic> _handleDioException(
      DioException error, String method, Map<String, String> headers) {
    if (error.response?.statusCode == 401) {
      return Response(
        statusCode: 401,
        statusMessage: 'Sem autorização, autentique-se novamente',
        data: {
          'error': 'Sem autorização, autentique-se novamente',
          'code': 401
        },
        requestOptions: RequestOptions(method: method, headers: headers),
      );
    }

    if (error.type == DioExceptionType.connectionError &&
        error.error is SocketException) {
      final errorSocket = error.error as SocketException;
      return Response(
        statusCode: errorSocket.osError?.errorCode ?? 7,
        statusMessage:
            "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
        data: {
          'error':
              "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
          'code': errorSocket.osError?.errorCode ?? 7
        },
        requestOptions: RequestOptions(method: method, headers: headers),
      );
    }

    return Response(
      statusCode: error.response?.statusCode ?? 500,
      statusMessage: error.response?.statusMessage ?? 'Atenção ocorreu um erro',
      data: {
        'error': error.error != null ? error.error.toString() : error.message,
        'code': error.response?.statusCode ?? 500
      },
      requestOptions: RequestOptions(method: method, headers: headers),
    );
  }

  HttpResponse<T> _handleHttpResponseError<T>(
      DioException error, ParserError<T>? parserError) {
    if (error.type == DioExceptionType.connectionError &&
        error.error is SocketException) {
      return HttpResponse.fail<T>(
        statusCode: 7,
        message:
            "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
        data: ResultError(
          code: 7,
          error:
              "Não foi possível estabelecer uma conexão com o servidor. Tente novamente, por favor.",
        ),
      );
    }

    if (error.response?.statusCode == 401) {
      return HttpResponse.fail<T>(
        statusCode: 401,
        message: 'Sem autorização, autentique-se novamente',
        data: ResultError(
          code: 401,
          error: 'Sem autorização, autentique-se novamente',
        ),
      );
    }

    if (parserError != null) {
      dynamic parsedError = error.response?.data ?? {};
      parsedError = parserError(error.response?.data);

      return HttpResponse.fail<T>(
        statusCode: error.response?.statusCode ?? 500,
        message: error.message,
        data: parsedError,
      );
    }

    return HttpResponse.fail<T>(
      statusCode: error.response?.statusCode ?? 500,
      message: error.toString(),
      data: ResultError(
        code: 500,
        error: error.error.toString(),
      ),
    );
  }
}
