import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/login/widgets/trimmed_lower_case_formatter.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
bool _isFormValid = false;


@override
void initState(){
  super.initState();

  loginController.userNameController.addListener(_validateForm);
  loginController.passwordController.addListener(_validateForm);
}

@override
void dispose (){
  loginController.userNameController.removeListener(_validateForm);
  loginController.passwordController.removeListener(_validateForm);
  super.dispose();
}

void _validateForm(){
  setState(() {
    _isFormValid = loginController.userNameController.text.isNotEmpty && loginController.passwordController.text.isNotEmpty;
  });
}

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return SafeArea(
      child: Scaffold(
        backgroundColor: whiteColor,
        body: GetBuilderInstrumentado<LoginController>(
          "LoginController",
          builder: (ctrl) {
            return Form(
              key: _formKey,
              child: Container(
                width: size.width,
                height: size.height,
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                child: SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height - 56,
                    ),
                    child:
                        appController.workspace?.authenticationType ==
                                "AzureB2C"
                            ? _buildB2C(context)
                            : !ctrl.userLogged
                            ? _buildLoginButton(context)
                            : _buildUserLogged(context),
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildLoginButton(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Align(
          alignment: Alignment.topRight,
          child: CustomInkWell(
            onTap: () {
              Get.offAllNamed(
                RoutesPath.workspaces,
                arguments: {'settings': false},
              );
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: SvgPicture.asset(
                colorFilter: const ColorFilter.mode(
                  greenColor,
                  BlendMode.srcIn,
                ),
                AppImages.workspace,
              ), // Seu ícone no topo
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(F.logo),
              30.toHeightSpace(),
              CustomTextField(
                leadingIcon: Icons.person,
                isPassword: false,
                hint: 'Usuário',
                controller: loginController.userNameController,
                onChanged: loginController.setUserName,
                inputFormatters: [TrimmedLowerCaseFormatter()],
                keyboardType: TextInputType.emailAddress,
               
              ),
              10.toHeightSpace(),
              CustomTextField(
                leadingIcon: Icons.lock,
                trailingIcon: Icons.visibility,
                isPassword: loginController.passwordObscure,
                hint: 'Senha',
                controller: loginController.passwordController,
                onChanged: loginController.setPassword,
                trailingTap: loginController.setPasswordObscure,
                
              ),
              15.toHeightSpace(),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 25),
                child: PrimaryButtonWidget(
                  titleButtom: 'Login',
                 buttonColor: _isFormValid ? themesController.getColorButton() : Colors.grey,
                  isLoading: loginController.loginButtonLoad,
                  onTap: _isFormValid ?  () async {
                    if (_formKey.currentState!.validate()) {
                      await loginController.sendLogin();
                    }
                  } : null,
                ),
              ),
              15.toHeightSpace(),
              CustomInkWell(
                onTap: () async {
                  await loginController.forgetPassword();
                },
                child: const LabelWidget(title: "Esqueci minha senha"),
              ),
            ],
          ),
        ),
        const Column(children: [VersionWidget(showWorkspace: true)]),
      ],
    );
  }

  Widget _buildUserLogged(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Align(
          alignment: Alignment.topRight,
          child: CustomInkWell(
            onTap: () {
              if (appController.hasInitialize) {
                GetC.close();
              } else {
                Get.offAllNamed(
                  RoutesPath.workspaces,
                  arguments: {'settings': false},
                );
              }
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: SvgPicture.asset(
                colorFilter: const ColorFilter.mode(
                  greenColor,
                  BlendMode.srcIn,
                ),
                AppImages.workspace,
              ), // Seu ícone no topo
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              80.toHeightSpace(),
              Image.asset(F.logo),
              30.toHeightSpace(),
              CustomInkWell(
                onTap: loginController.continueLogged,
                child: Container(
                  color: greenColor,
                  width: size.width.w,
                  padding: const EdgeInsets.all(8),
                  child: Row(
                    children: [
                      Icon(
                        FontAwesomeIcons.circleUser,
                        size: 60.w,
                        color: whiteColor,
                      ),
                      Flexible(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 15,
                            vertical: 10,
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              LabelWidget(
                                title: "Voltar como...",
                                fontSize: DeviceSize.fontSize(13, 16),
                                textColor: whiteColor,
                              ),
                              5.toHeightSpace(),
                              LabelWidget(
                                title: appController.userLogged?.nome ?? "-",
                                fontSize: DeviceSize.fontSize(20, 23),
                                fontWeight: FontWeight.bold,
                                textColor: whiteColor,
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              15.toHeightSpace(),
              CustomInkWell(
                onTap: loginController.changeUser,
                child: LabelWidget(
                  title: "Realize login com outro usuário",
                  fontSize: DeviceSize.fontSize(15, 18),
                ),
              ),
            ],
          ),
        ),
        Visibility(
          visible: !loginController.userLogged,
          child: const Column(children: [VersionWidget(showWorkspace: true)]),
        ),
        Visibility(
          visible: loginController.userLogged,
          child: Column(
            children: [
              LabelWidget(
                title: "Esqueci minha senha",
                fontSize: DeviceSize.fontSize(14, 17),
              ),
              5.toHeightSpace(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildB2C(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    return Column(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Align(
          alignment: Alignment.topRight,
          child: CustomInkWell(
            onTap: () {
              if (appController.hasInitialize) {
                GetC.close();
              } else {
                Get.offAllNamed(
                  RoutesPath.workspaces,
                  arguments: {'settings': false},
                );
              }
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 20.0),
              child: SvgPicture.asset(
                colorFilter: const ColorFilter.mode(
                  greenColor,
                  BlendMode.srcIn,
                ),
                AppImages.workspace,
              ), // Seu ícone no topo
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              80.toHeightSpace(),
              Image.asset(F.logo),
              30.toHeightSpace(),
              CustomInkWell(
                onTap: loginController.loginB2c,
                child: Container(
                  color: Colors.blue.shade700,
                  width: size.width.w,
                  padding: const EdgeInsets.all(8),
                  child: Row(
                    children: [
                      Icon(
                        FontAwesomeIcons.circleUser,
                        size: 60.w,
                        color: whiteColor,
                      ),
                      Flexible(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 15,
                            vertical: 10,
                          ),
                          child: LabelWidget(
                            title: "Login com B2C",
                            fontSize: DeviceSize.fontSize(20, 23),
                            fontWeight: FontWeight.bold,
                            textColor: whiteColor,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              15.toHeightSpace(),
              CustomInkWell(
                onTap: loginController.logoutB2c,
                child: LabelWidget(
                  title: "Realize login com outro usuário",
                  fontSize: DeviceSize.fontSize(15, 18),
                ),
              ),
            ],
          ),
        ),
        Visibility(
          visible: !loginController.userLogged,
          child: const Column(children: [VersionWidget(showWorkspace: true)]),
        ),
        Visibility(
          visible: loginController.userLogged,
          child: Column(
            children: [
              LabelWidget(
                title: "Esqueci minha senha",
                fontSize: DeviceSize.fontSize(14, 17),
              ),
              5.toHeightSpace(),
            ],
          ),
        ),
      ],
    );
  }
}
