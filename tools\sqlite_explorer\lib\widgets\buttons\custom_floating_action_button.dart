import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class CustomFloatingActionButton extends StatefulWidget {
  final Color backgroundColor;
  final Future<void> Function() onPressed;
  final Widget? child;
  final int badgeCount;

  const CustomFloatingActionButton({
    super.key,
    required this.backgroundColor,
    required this.onPressed,
    this.child,
    this.badgeCount = 0,
  });

  @override
  CustomFloatingActionButtonState createState() =>
      CustomFloatingActionButtonState();
}

class CustomFloatingActionButtonState extends State<CustomFloatingActionButton>
    with SingleTickerProviderStateMixin {
  bool _isProcessing = false;
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _handlePress() async {
    if (_isProcessing) return;
    setState(() {
      _isProcessing = true;
    });
    _animationController.repeat();
    try {
      await widget.onPressed();
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
        _animationController.stop();
        _animationController.reset();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        FloatingActionButton(
          backgroundColor: _isProcessing ? Colors.grey : widget.backgroundColor,
          onPressed: _isProcessing ? null : _handlePress,
          child: RotationTransition(
            turns: _animationController,
            child: widget.child ??
                const Icon(
                  FontAwesomeIcons.arrowsRotate,
                  color: Colors.white,
                ),
          ),
        ),
        if (widget.badgeCount > 0)
          Positioned(
            right: -5,
            top: -5,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(10),
              ),
              constraints: const BoxConstraints(
                minWidth: 20,
                minHeight: 20,
              ),
              child: Text(
                widget.badgeCount.toString(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),
      ],
    );
  }
}
