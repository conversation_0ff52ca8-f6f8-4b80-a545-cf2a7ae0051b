import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/modules/routes/models/planning_visit_status_model.dart';
import 'package:pharmalink/modules/routes/models/project_pdv_setor_model.dart';
import 'package:pharmalink/modules/routes/models/road_map_model.dart';

class ListRoutesModel extends SqfLiteBase<ListRoutesModel> {
  String? data;
  String? dataAlteracao;
  String? dataInclusao;
  String? dataInicio;
  String? dataTermino;
  bool? diaUtil;
  int? idCicloPeriodo;
  String? descricaoCicloPeriodo;
  int? idPdv;
  int? idPlanejamentoVisita;
  int? idPlanejamentoVisitaStatus;
  int? idProjeto;
  int? idSetor;
  int? idRoteiro;
  bool? isDeleted;
  PlanejamentoVisitaStatus? planejamentoVisitaStatus;
  ProjetoPdvSetor? projetoPdvSetor;
  Roteiro? roteiro;
  String? userId;
  String? nomeUsuario;
  bool? rdvPermiteAlteracaoPlanejamento;
  bool? status;

  ListRoutesModel(
      {this.data,
      this.dataAlteracao,
      this.dataInclusao,
      this.dataInicio,
      this.dataTermino,
      this.diaUtil,
      this.idCicloPeriodo,
      this.descricaoCicloPeriodo,
      this.idPdv,
      this.idPlanejamentoVisita,
      this.idPlanejamentoVisitaStatus,
      this.idProjeto,
      this.idSetor,
      this.idRoteiro,
      this.isDeleted,
      this.planejamentoVisitaStatus,
      this.projetoPdvSetor,
      this.roteiro,
      this.userId,
      this.nomeUsuario,
      this.rdvPermiteAlteracaoPlanejamento,
      this.status})
      : super(DatabaseModels.routersListModel);

  ListRoutesModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.routersListModel) {
    data = json['Data'];
    dataAlteracao = json['DataAlteracao'];
    dataInclusao = json['DataInclusao'];
    dataInicio = json['DataInicio'];
    dataTermino = json['DataTermino'];
    diaUtil = json['DiaUtil'];
    idCicloPeriodo = json['IdCicloPeriodo'];
    descricaoCicloPeriodo = json['DescricaoCicloPeriodo'];
    idPdv = json['IdPdv'];
    idPlanejamentoVisita = json['IdPlanejamentoVisita'];
    idPlanejamentoVisitaStatus = json['IdPlanejamentoVisitaStatus'];
    idProjeto = json['IdProjeto'];
    idSetor = json['IdSetor'];
    idRoteiro = json['IdRoteiro'];
    isDeleted = json['IsDeleted'];
    planejamentoVisitaStatus = json['PlanejamentoVisitaStatus'] != null
        ? PlanejamentoVisitaStatus.fromJson(json['PlanejamentoVisitaStatus'])
        : null;
    projetoPdvSetor = json['ProjetoPdvSetor'] != null
        ? ProjetoPdvSetor.fromJson(json['ProjetoPdvSetor'])
        : null;
    roteiro =
        json['Roteiro'] != null ? Roteiro.fromJson(json['Roteiro']) : null;
    userId = json['UserId'];
    nomeUsuario = json['NomeUsuario'];

    rdvPermiteAlteracaoPlanejamento = json['RdvPermiteAlteracaoPlanejamento'];

    status = json['Status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Data'] = this.data;
    data['DataAlteracao'] = dataAlteracao;
    data['DataInclusao'] = dataInclusao;
    data['DataInicio'] = dataInicio;
    data['DataTermino'] = dataTermino;
    data['DiaUtil'] = diaUtil;
    data['IdCicloPeriodo'] = idCicloPeriodo;
    data['DescricaoCicloPeriodo'] = descricaoCicloPeriodo;
    data['IdPdv'] = idPdv;
    data['IdPlanejamentoVisita'] = idPlanejamentoVisita;
    data['IdPlanejamentoVisitaStatus'] = idPlanejamentoVisitaStatus;
    data['IdProjeto'] = idProjeto;
    data['IdSetor'] = idSetor;
    data['IdRoteiro'] = idRoteiro;
    data['IsDeleted'] = isDeleted;
    if (planejamentoVisitaStatus != null) {
      data['PlanejamentoVisitaStatus'] = planejamentoVisitaStatus!.toJson();
    }
    if (projetoPdvSetor != null) {
      data['ProjetoPdvSetor'] = projetoPdvSetor!.toJson();
    }
    if (roteiro != null) {
      data['Roteiro'] = roteiro!.toJson();
    }
    data['UserId'] = userId;
    data['NomeUsuario'] = nomeUsuario;

    data['RdvPermiteAlteracaoPlanejamento'] = rdvPermiteAlteracaoPlanejamento;

    data['Status'] = status;
    return data;
  }

  Future<ListRoutesModel> getFirst({required int workspaceId}) async {
    var list = await getAll<ListRoutesModel>(
        workspaceId: workspaceId, ListRoutesModel.fromJson);
    return list.first;
  }

  Future<bool> exists({required int workspaceId}) async {
    var list = await getAll<ListRoutesModel>(
        workspaceId: workspaceId, ListRoutesModel.fromJson);
    return list.isNotEmpty;
  }

  Future<List<ListRoutesModel>> getList({required int workspaceId}) async {
    var list = await getAll<ListRoutesModel>(
        workspaceId: workspaceId, ListRoutesModel.fromJson);
    return list;
  }
}
