import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class ReportContentsModel extends SqfLiteBase<ReportContentsModel> {
  String? fileId;
  String? fileName;
  String? description;
  String? roleId;
  List<ReportContentsRoles>? roles;
  String? startDate;
  String? endDate;
  String? fileVirtualPath;
  String? contentType;
  bool? validity;
  bool? isDeleted;

  ReportContentsModel(
      {this.fileId,
      this.fileName,
      this.description,
      this.roleId,
      this.roles,
      this.startDate,
      this.endDate,
      this.fileVirtualPath,
      this.contentType,
      this.validity,
      this.isDeleted})
      : super(DatabaseModels.contentModel);

  ReportContentsModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.contentModel) {
    fileId = json['FileId'];
    fileName = json['FileName'];
    description = json['Description'];
    roleId = json['RoleId'];
    if (json['Roles'] != null) {
      roles = <ReportContentsRoles>[];
      json['Roles'].forEach((v) {
        roles!.add(ReportContentsRoles.fromJson(v));
      });
    }
    startDate = json['StartDate'];
    endDate = json['EndDate'];
    fileVirtualPath = json['FileVirtualPath'];
    contentType = json['ContentType'];
    validity = json['Validity'];
    isDeleted = json['IsDeleted'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['FileId'] = fileId;
    data['FileName'] = fileName;
    data['Description'] = description;
    data['RoleId'] = roleId;
    if (roles != null) {
      data['Roles'] = roles!.map((v) => v.toJson()).toList();
    }
    data['StartDate'] = startDate;
    data['EndDate'] = endDate;
    data['FileVirtualPath'] = fileVirtualPath;
    data['ContentType'] = contentType;
    data['Validity'] = validity;
    data['IsDeleted'] = isDeleted;
    return data;
  }

  Future<ReportContentsModel> getFirst() async {
    var list = await getAll<ReportContentsModel>(ReportContentsModel.fromJson);
    return list.first;
  }

  Future<List<ReportContentsModel>> getList() async {
    var list = await getAll<ReportContentsModel>(ReportContentsModel.fromJson);
    return list;
  }
}

class ReportContentsRoles {
  String? roleId;
  String? roleName;

  ReportContentsRoles({this.roleId, this.roleName});

  ReportContentsRoles.fromJson(Map<String, dynamic> json) {
    roleId = json['RoleId'];
    roleName = json['RoleName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['RoleId'] = roleId;
    data['RoleName'] = roleName;
    return data;
  }
}
