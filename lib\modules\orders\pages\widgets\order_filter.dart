import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class OrdersFilterWidget extends StatelessWidget {
  const OrdersFilterWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrdersController>(builder: (ctrl) {
      return CustomInkWell(
        onTap: () => ctrl.showFilters(),
        child: Container(
          padding:
              const EdgeInsets.only(right: 16, left: 16, top: 2, bottom: 2),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(10),
            border:
                Border.all(color: themesController.getIconColor(), width: 1.0),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                    child: LabelWidget(
                  title: ctrl.filterSelectedString.isNotEmpty
                      ? ctrl.filterSelectedString
                      : "Filtrar por categorias",
                  textColor: ctrl.filterSelectedString.isNotEmpty
                      ? Colors.black
                      : Colors.grey,
                  fontSize: DeviceSize.fontSize(13, 16),
                )),
                Icon(
                  FontAwesomeIcons.chevronDown,
                  size: 14,
                  color: themesController.getIconColor(),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
