class Visita {
  String? comentario;
  String? dataAlteracao;
  String? dataInclusao;
  bool? enviado;
  int? idRota;
  int? idVisita;
  int? idVisitaAcompanhamento;
  int? idVisitaMotivo;
  int? idVisitaPeriodo;
  int? idVisitaStatus;
  bool? isDeleted;
  String? objetivo;
  bool? visita;
  String? visitaStatusDescricao;

  Visita(
      {this.comentario,
      this.dataAlteracao,
      this.dataInclusao,
      this.enviado,
      this.idRota,
      this.idVisita,
      this.idVisitaAcompanhamento,
      this.idVisitaMotivo,
      this.idVisitaPeriodo,
      this.idVisitaStatus,
      this.isDeleted,
      this.objetivo,
      this.visita,
      this.visitaStatusDescricao});

  Visita.fromJson(Map<String, dynamic> json) {
    comentario = json['Comentario'];
    dataAlteracao = json['DataAlteracao'];
    dataInclusao = json['DataInclusao'];
    enviado = json['Enviado'];
    idRota = json['IdRota'];
    idVisita = json['IdVisita'];
    idVisitaAcompanhamento = json['IdVisitaAcompanhamento'];
    idVisitaMotivo = json['IdVisitaMotivo'];
    idVisitaPeriodo = json['IdVisitaPeriodo'];
    idVisitaStatus = json['IdVisitaStatus'];
    isDeleted = json['IsDeleted'];
    objetivo = json['Objetivo'];
    visita = json['Visita'];
    visitaStatusDescricao = json['VisitaStatusDescricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Comentario'] = comentario;
    data['DataAlteracao'] = dataAlteracao;
    data['DataInclusao'] = dataInclusao;
    data['Enviado'] = enviado;
    data['IdRota'] = idRota;
    data['IdVisita'] = idVisita;
    data['IdVisitaAcompanhamento'] = idVisitaAcompanhamento;
    data['IdVisitaMotivo'] = idVisitaMotivo;
    data['IdVisitaPeriodo'] = idVisitaPeriodo;
    data['IdVisitaStatus'] = idVisitaStatus;
    data['IsDeleted'] = isDeleted;
    data['Objetivo'] = objetivo;
    data['Visita'] = visita;
    data['VisitaStatusDescricao'] = visitaStatusDescricao;
    return data;
  }
}

class VisitaStatus {
  int? idVisitaStatus;
  String? descricao;

  VisitaStatus({this.idVisitaStatus, this.descricao});

  VisitaStatus.fromJson(Map<String, dynamic> json) {
    idVisitaStatus = json['IdVisitaStatus'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdVisitaStatus'] = idVisitaStatus;
    data['Descricao'] = descricao;
    return data;
  }
}
