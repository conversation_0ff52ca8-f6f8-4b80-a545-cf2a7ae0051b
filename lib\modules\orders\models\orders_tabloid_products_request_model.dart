import 'package:pharmalink/modules/stores_parameters/models/prazo_pagamento_model.dart';

class TabloidProductsRequest {
  int? paymentTermId;
  List<PrazoPagamentoModel>? deadlinePayment;
  int? distributorId;
  List<int>? distributorsIds;
  String? userId;
  int? tabloidId;
  int? storeId;
  String? cNPJ;

  TabloidProductsRequest(
      {this.distributorId,
      this.paymentTermId,
      this.deadlinePayment,
      this.distributorsIds,
      this.userId,
      this.tabloidId,
      this.storeId,
      this.cNPJ});

  TabloidProductsRequest.fromJson(Map<String, dynamic> json) {
    distributorId = json['DistributorId'];
    paymentTermId = json['PaymentTermId'];
    deadlinePayment = (json['DeadlinePayment'] as List<dynamic>?)
        ?.map((e) => PrazoPagamentoModel.fromJson(e))
        .toList();
    distributorsIds = json['DistributorsIds'].cast<int>();
    userId = json['userId'];
    tabloidId = json['TabloidId'];
    storeId = json['StoreId'];
    cNPJ = json['CNPJ'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['DistributorId'] = distributorId;
    data['PaymentTermId'] = paymentTermId;
    data['DistributorsIds'] = distributorsIds;
    data['DeadlinePayment'] = deadlinePayment?.map((e) => e.toJson()).toList();
    data['userId'] = userId;
    data['TabloidId'] = tabloidId;
    data['StoreId'] = storeId;
    data['CNPJ'] = cNPJ;
    return data;
  }
}
