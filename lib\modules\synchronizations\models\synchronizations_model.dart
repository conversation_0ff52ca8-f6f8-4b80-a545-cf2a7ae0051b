import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class SynchronizationsModel extends SqfLiteBase<SynchronizationsModel> {
  DateTime? syncDate;

  SynchronizationsModel({
    this.syncDate,
  }) : super(DatabaseModels.synchronizationsModel);

  SynchronizationsModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.synchronizationsModel) {
    syncDate = DateTime.tryParse(json['syncDate']);
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['syncDate'] = syncDate?.toIso8601String();

    return data;
  }

  Future<SynchronizationsModel?> getFirst() async {
    var list = await getAll<SynchronizationsModel>(
        userId: appController.userLogged?.userId!,
        workspaceId: appController.workspace!.workspaceId,
        SynchronizationsModel.fromJson);
    return list.firstOrNull;
  }
}
