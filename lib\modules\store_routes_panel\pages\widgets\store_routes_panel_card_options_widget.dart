import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';

class StoreRoutesPanelCardOptionsWidget extends StatelessWidget {
  const StoreRoutesPanelCardOptionsWidget({super.key, required this.data});
  final StoresModel data;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StoreRoutesPanelController>(builder: (ctrl) {
      return Row(
        children: [
          Expanded(
            child: CheckboxListTile(
              title: LabelWidget(
                title: 'Agendar Visita',
                fontSize: DeviceSize.fontSize(16, 20),
                textColor:
                    (data.visita == true || data.dataExtra!.isVisita == true)
                        ? Colors.grey.shade900
                        : Colors.grey[350],
              ),
              value: data.dataExtra!.canVisitaSync ?? false,
              onChanged:
                  (data.visita == true || data.dataExtra!.isVisita == true)
                      ? (bool? value) async {
                          await ctrl.setCanVisitaSync(data, value);
                        }
                      : null,
              controlAffinity: ListTileControlAffinity.leading,
              contentPadding: EdgeInsets.zero,
            ),
          ),
          //Se o parametro de sincronia offline está ativo, exibir o checkbox
          if (storeRoutesController.hasSyncOffline)
            Expanded(
              child: CheckboxListTile(
                title: LabelWidget(
                  title: 'Pedido Offline',
                  fontSize: DeviceSize.fontSize(16, 20),
                  textColor: Colors.grey.shade900,
                ),
                value: data.dataExtra!.canOrderOfflineSync ?? false,
                onChanged: (bool? value) async {
                  await ctrl.setCanOrderOfflineSync(data, value);
                },
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
              ),
            ),
        ],
      );
    });
  }
}
