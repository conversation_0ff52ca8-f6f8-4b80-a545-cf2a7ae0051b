import 'package:pharmalink/exports/basic_exports.dart';

class TilesInfoWidget extends StatefulWidget {
  const TilesInfoWidget({
    super.key,
    required this.title,
    this.value,
    this.isRitchText,
    this.titleFontSize,
    this.valueFontSize,
    this.hasDivider,
    this.valueFontWeight,
    this.crossAxisAlignment,
    this.valueList,
  });
  final String title;
  final String? value;
  final bool? isRitchText;
  final double? titleFontSize;
  final double? valueFontSize;
  final bool? hasDivider;
  final FontWeight? valueFontWeight;
  final CrossAxisAlignment? crossAxisAlignment;
  final List<String>? valueList;

  @override
  State<TilesInfoWidget> createState() => _TilesInfoWidgetState();
}

class _TilesInfoWidgetState extends State<TilesInfoWidget> {
  bool showAll = false;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 10),
      child: Column(
        crossAxisAlignment:
            widget.crossAxisAlignment ?? CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          LabelWidget(
            title: widget.title,
            fontWeight: FontWeight.normal,
            textColor: Colors.grey,
            fontSize: widget.titleFontSize ?? 12.sp,
          ),
          if (widget.value != null)
            LabelWidget(
              title: widget.value ?? "",
              fontSize: widget.valueFontSize ?? 16.sp,
              fontWeight: widget.valueFontWeight,
            ),
          if (widget.value == null &&
              widget.valueList != null &&
              widget.valueList!.isNotEmpty)
            _buildValueList(),
          Visibility(
            visible: widget.hasDivider == null || widget.hasDivider == true,
            child: Divider(
              color: Colors.grey.shade300,
            ),
          )
        ],
      ),
    );
  }

  Widget _buildValueList() {
    return Column(
      children: [
        ...widget.valueList!
            .take(showAll ? widget.valueList!.length : 3)
            .map((item) => LabelWidget(
                  title: item,
                  fontSize: widget.valueFontSize ?? 16.sp,
                  fontWeight: widget.valueFontWeight,
                )),
        if (widget.valueList!.length > 3)
          GestureDetector(
            onTap: () {
              setState(() {
                showAll = !showAll;
              });
            },
            child: LabelWidget(
              title: showAll ? 'Voltar' : 'Ver Todos',
              fontSize: 14,
              fontWeight: widget.valueFontWeight,
              textColor: themesController.getBackgroundColor(),
            ),
          ),
      ],
    );
  }
}
