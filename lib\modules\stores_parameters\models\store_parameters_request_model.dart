class StoreParametersRequestModel {
  String? userId;
  List<String>? tipoPedidoLista;
  List<int>? idLojaLista;

  StoreParametersRequestModel(
      {this.userId, this.tipoPedidoLista, this.idLojaLista});

  StoreParametersRequestModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    tipoPedidoLista = json['tipoPedidoLista'].cast<String>();
    idLojaLista = json['idLojaLista'].cast<int>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['userId'] = userId;
    data['tipoPedidoLista'] = tipoPedidoLista;
    data['idLojaLista'] = idLojaLista;
    return data;
  }
}
