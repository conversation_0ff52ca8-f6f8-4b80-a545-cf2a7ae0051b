class ProjetoPdvSetor {
  int? idPdv;
  int? idProjeto;
  int? idSetor;

  ProjetoPdvSetor({this.idPdv, this.idProjeto, this.idSetor});

  ProjetoPdvSetor.fromJson(Map<String, dynamic> json) {
    idPdv = json['IdPdv'];
    idProjeto = json['IdProjeto'];
    idSetor = json['IdSetor'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPdv'] = idPdv;
    data['IdProjeto'] = idProjeto;
    data['IdSetor'] = idSetor;
    return data;
  }
}
