import 'dart:developer';

import 'package:pharmalink/config/databases/call_context.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/general_parameterization/models/general_parameterization_model.dart';
import 'package:pharmalink/modules/log_trace_monitor/models/log_trace_monitor_model.dart';
import 'package:pharmalink/modules/login/models/login_response_model.dart';
import 'package:pharmalink/modules/orders/models/orders_filter_default_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';
import 'package:pharmalink/modules/q_atester/models/q_atester_model.dart';
import 'package:pharmalink/modules/researches_share_of_shelf/models/researches_share_of_shelf_model.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/store_parameters_model.dart';
import 'package:pharmalink/modules/themes/models/themes_model.dart';
import 'package:pharmalink/modules/workspaces/models/workspaces_model.dart';

class QATesterController extends GetxControllerInstrumentado<QATesterController>
    with TraceableController {
  QATesterController();

  QATesterModel info = QATesterModel();
  bool loginButtonLoading = false;
  final loading = PlkLoading();
  @override
  Future<void> onReady() async {
    super.onReady();
    await initialize();
  }

  Future<void> initialize() async {
    final loginResponseModel = await LoginResponseModel().getList();
    info.loginResponseModel = loginResponseModel.length;

    final workspacesModel = await WorkspacesModel().getList();
    info.workspacesModel = workspacesModel.length;

    final themesModel = await ThemesModel()
        .getList(workspaceId: appController.workspace!.workspaceId!);
    info.themesModel = themesModel.length;

    final storesModel = await StoresModel().getList();
    info.storesModel = storesModel.length;

    final storesTakeModel = await StoresModel().getListTake();
    info.storesTakeModel = storesTakeModel.length;

    final logTraceMonitorModel = await LogTraceMonitorModel().getList();
    info.logTraceMonitorModel = logTraceMonitorModel.length;

    final storeParametersModel = await StoreParametersModel()
        .getList(appController.workspace!.workspaceId!, null, null, null);
    info.storeParametersModel = storeParametersModel.length;

    final productParameterModel = await ProductParameterModel()
        .getList(workspaceId: appController.workspace!.workspaceId!);
    info.productParameterModel = productParameterModel.length;

    final productsMixModel = await ProductsMixModel()
        .getList(workspaceId: appController.workspace!.workspaceId!);
    info.productsMixModel = productsMixModel.length;

    final productFilterDefaultModel = await ProductFilterDefaultModel()
        .getList(workspaceId: appController.workspace!.workspaceId!);
    info.productFilterDefaultModel = productFilterDefaultModel.length;

    final storeOrdersModel = await SyncronizationModel()
        .getList(workspaceId: appController.workspace!.workspaceId!);
    info.storeOrders = storeOrdersModel.length;

    final shareOfShelfModel = await ResearchesShareOfShelfModel().getList();
    info.shareOfShelf = shareOfShelfModel.length;
    update();
  }

  void setButtonLoading(bool value) {
    loginButtonLoading = value;
    update();
  }

  Future<void> clearOrders() async {
    setButtonLoading(true);
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      key: DatabaseModels.notificationModel,
    );
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      key: DatabaseModels.logsHttpModel,
    );
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      key: DatabaseModels.syncronization,
    );
    await initialize();
    SnackbarCustom.snackbarSucess("Limpar Pedidos", "Pedidos resetados");
    setButtonLoading(false);
  }

  Future<void> clearVisits() async {
    setButtonLoading(true);
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      userId: appController.userLogged!.userId,
      key: DatabaseModels.visitsByRoutesResponseModel,
    );
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      key: DatabaseModels.researchesProductIndustryDataModel,
    );
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      key: DatabaseModels.researchesProductConcurrentModel,
    );
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      key: DatabaseModels.researchesMerchanIndustryDataModel,
    );
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      key: DatabaseModels.researchesMerchanCompetitiveDataModel,
    );
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      key: DatabaseModels.researchesTradeMarketingModel,
    );
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      key: DatabaseModels.researchesComplementaryModel,
    );

    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      key: DatabaseModels.researchesShareOfShelfModel,
    );
    await initialize();
    SnackbarCustom.snackbarSucess("Limpar Pedidos", "Pedidos resetados");
    setButtonLoading(false);
  }

  Future<void> clearLogTraceMonitor() async {
    setButtonLoading(true);
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      userId: appController.userLogged?.userId!,
      key: DatabaseModels.logTraceMonitorModel,
    );
    await initialize();
    SnackbarCustom.snackbarSucess(
        "Limpar Logs", "Logs do Monitoramento resetados");
    setButtonLoading(false);
  }

  Future<void> clearStores() async {
    setButtonLoading(true);
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      userId: appController.userLogged?.userId!,
      key: DatabaseModels.storesTakeModel,
    );
    await dbContext.deleteByKey(
      workspaceId: appController.workspace?.workspaceId,
      userId: appController.userLogged?.userId!,
      key: DatabaseModels.storesModel,
    );
    //await StoresModel().resetSyncOffline(dynatraceAction);

    await initialize();
    SnackbarCustom.snackbarSucess(
        "Limpar Lojas", "Lojas selecionadas resetadas");
    setButtonLoading(false);
  }

  void showPopup() {
    Get.defaultDialog(
      title: "Popup",
      content: Column(
        children: [
          const Text("This is a popup"),
          ElevatedButton(
            onPressed: () async {
              if (Get.isSnackbarOpen) {
                await Get.closeCurrentSnackbar();
                Future.delayed(const Duration(milliseconds: 100), () {
                  Get.back();
                });
              } else {
                Get.back();
              }
            },
            child: const Text("Close Popup"),
          ),
        ],
      ),
    );
  }

  void showSnackbar(BuildContext context) {
    // Usar um Builder para criar um novo contexto
    Get.snackbar(
      "Title",
      "This is a snackbar message",
      duration: const Duration(seconds: 10),
      snackPosition: SnackPosition.TOP,
      snackStyle: SnackStyle.FLOATING,
      margin: const EdgeInsets.all(10),
      onTap: (_) {
        // Ação ao clicar no snackbar
      },
      overlayBlur: 0.0,
      overlayColor: Colors.black54,
    );
  }

  Future<void> generateLogTraceMonitor() async {
    return trace('generateLogTraceMonitor', () async {
      var loading = PlkLoading();
      loading.show(delayDuration: 10000);
      for (var i = 0; i < 10000; i++) {
        appLog("Log de Carga $i", data: {
          "id": i,
          "name": "Log de Carga",
          "createAt": DateTime.now().toIso8601String()
        });
        Future.delayed(const Duration(milliseconds: 350));
      }

      await initialize();
      loading.hide();
    });
  }

  Future<void> simulateRoutesPreviousDay() async {
    final response = await StoresModel().getList(
      hashCode: "planned",
    );

    final storesList =
        response.where((x) => x.dataExtra?.canPlanned == true).toList();
    storesList
        .map((e) => {
              e.dataExtra?.currentDate =
                  DateTime.now().subtract(const Duration(days: 1)),
              e.dataExtra?.offlineDateSync =
                  DateTime.now().subtract(const Duration(days: 1)),
            })
        .toList();
    for (var store in storesList) {
      log('============================================');
      log('store new data: ${store.dataExtra?.currentDate?.toIso8601String()}');
      await storeRoutesController.updateStore(store);
    }
    SnackbarCustom.snackbarSucess(
        "Simular Rotas", "Rotas simuladas com sucesso");
  }
}
