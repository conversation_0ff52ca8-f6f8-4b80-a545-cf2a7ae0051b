import 'dart:convert';

import 'package:intl/intl.dart';

extension StringExtensions on String {
  double toDoubleByStr() {
    return double.parse(replaceAll(',', '.'));
  }

  String toStringDoubleFormat() {
    return replaceAll(',', '.');
  }

  String formatDate() {
    return DateFormat('dd/MM/yyyy').format(DateTime.parse(this));
  }

  String formatReal() {
    return NumberFormat.currency(locale: 'pt_BR', symbol: "R\$")
        .format(DateTime.parse(this));
  }

  double toDouble() {
    var value = this;
    if (value.contains(",") && value.contains(".")) {
      value = value.replaceAll(".", "");
      value = value.replaceAll(",", ".");
    } else if (value.contains(",") && !value.contains(".")) {
      value = value.replaceAll(",", ".");
    }
    return double.parse(value);
  }

  DateTime? formatDateByGmt() {
    DateTime? parsedDate;
    String inputDate = this;
    try {
      return DateTime.parse(inputDate);
    } catch (e) {
      // Use uma expressão regular para extrair partes da data
      RegExp regExp =
          RegExp(r'(\d{2}) (\w{3}) (\d{4}) (\d{2}):(\d{2}):(\d{2}) GMT');
      Match? match = regExp.firstMatch(inputDate);
      if (match != null) {
        String day = match[1]!;
        String monthString = match[2]!;
        String year = match[3]!;
        String hour = match[4]!;
        String minute = match[5]!;
        String second = match[6]!;

        // Mapeie o nome do mês para o número do mês
        Map<String, int> monthMap = {
          'Jan': 1,
          'Feb': 2,
          'Mar': 3,
          'Apr': 4,
          'May': 5,
          'Jun': 6,
          'Jul': 7,
          'Aug': 8,
          'Sep': 9,
          'Oct': 10,
          'Nov': 11,
          'Dec': 12,
        };

        int month = monthMap[monthString]!;
        parsedDate = DateTime(
          int.parse(year),
          month,
          int.parse(day),
          int.parse(hour),
          int.parse(minute),
          int.parse(second),
        );
      }
      return parsedDate;
    }
  }

  String toBase64() {
    return base64.encode(utf8.encode(this));
  }
}
