# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 11ms
    create-module-model completed in 20ms
    create-module-model
      create-cmake-model 11ms
    create-module-model completed in 19ms
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 18ms
    [gap of 28ms]
  create-initial-cxx-model completed in 143ms
  [gap of 21ms]
create_cxx_tasks completed in 164ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model 16ms
    [gap of 26ms]
    create-module-model 16ms
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 17ms
    [gap of 21ms]
  create-initial-cxx-model completed in 119ms
  [gap of 11ms]
create_cxx_tasks completed in 130ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 14ms
    create-module-model completed in 26ms
    create-module-model
      create-cmake-model 14ms
    create-module-model completed in 20ms
    create-module-model
      create-cmake-model 16ms
    create-module-model completed in 23ms
    [gap of 27ms]
  create-initial-cxx-model completed in 162ms
  [gap of 19ms]
create_cxx_tasks completed in 181ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 15ms
    create-module-model completed in 22ms
    create-module-model
      create-cmake-model 14ms
    create-module-model completed in 20ms
    [gap of 25ms]
    create-module-model 17ms
    [gap of 29ms]
  create-initial-cxx-model completed in 136ms
  [gap of 15ms]
create_cxx_tasks completed in 152ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 14ms
    create-module-model completed in 22ms
    create-module-model
      create-cmake-model 16ms
    create-module-model completed in 24ms
    create-module-model
      create-cmake-model 14ms
    create-module-model completed in 24ms
    [gap of 35ms]
  create-initial-cxx-model completed in 168ms
  [gap of 21ms]
create_cxx_tasks completed in 190ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 11ms
    create-module-model completed in 20ms
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 18ms
    create-module-model
      create-cmake-model 10ms
    create-module-model completed in 17ms
    [gap of 31ms]
  create-initial-cxx-model completed in 145ms
  [gap of 21ms]
create_cxx_tasks completed in 166ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 11ms
    create-module-model completed in 21ms
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 19ms
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 19ms
    [gap of 27ms]
  create-initial-cxx-model completed in 149ms
  [gap of 24ms]
create_cxx_tasks completed in 174ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 22ms
    create-module-model
      create-cmake-model 10ms
    create-module-model completed in 17ms
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 20ms
    [gap of 26ms]
  create-initial-cxx-model completed in 132ms
create_cxx_tasks completed in 135ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 10ms
    create-module-model completed in 18ms
    create-module-model
      create-cmake-model 10ms
    create-module-model completed in 18ms
    [gap of 27ms]
    create-module-model 14ms
    [gap of 24ms]
  create-initial-cxx-model completed in 128ms
  [gap of 16ms]
create_cxx_tasks completed in 146ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 14ms
    create-module-model completed in 27ms
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 20ms
    create-module-model
      create-cmake-model 11ms
    create-module-model completed in 19ms
    [gap of 30ms]
  create-initial-cxx-model completed in 155ms
create_cxx_tasks completed in 158ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 22ms
    [gap of 24ms]
    create-X86_64-model 10ms
    create-module-model
      create-cmake-model 17ms
    create-module-model completed in 27ms
    create-module-model
      create-cmake-model 11ms
    create-module-model completed in 18ms
    [gap of 29ms]
  create-initial-cxx-model completed in 163ms
  [gap of 22ms]
create_cxx_tasks completed in 185ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 11ms
    create-module-model completed in 19ms
    create-module-model
      create-cmake-model 10ms
    create-module-model completed in 17ms
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 19ms
    [gap of 23ms]
  create-initial-cxx-model completed in 125ms
create_cxx_tasks completed in 127ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      [gap of 10ms]
      create-cmake-model 16ms
    create-module-model completed in 30ms
    create-module-model
      create-cmake-model 15ms
    create-module-model completed in 26ms
    create-module-model
      create-cmake-model 14ms
    create-module-model completed in 25ms
    [gap of 34ms]
  create-initial-cxx-model completed in 186ms
  [gap of 22ms]
create_cxx_tasks completed in 208ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 19ms
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 19ms
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 21ms
    [gap of 25ms]
  create-initial-cxx-model completed in 135ms
create_cxx_tasks completed in 140ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 23ms
    create-module-model
      create-cmake-model 15ms
    create-module-model completed in 23ms
    create-module-model
      create-cmake-model 14ms
    create-module-model completed in 23ms
    [gap of 14ms]
    create-ARM64_V8A-model 13ms
    create-X86-model 11ms
  create-initial-cxx-model completed in 187ms
  [gap of 26ms]
create_cxx_tasks completed in 214ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 11ms
    create-module-model completed in 18ms
    create-module-model
      create-cmake-model 11ms
    create-module-model completed in 19ms
    create-module-model
      create-cmake-model 10ms
    create-module-model completed in 17ms
    [gap of 22ms]
  create-initial-cxx-model completed in 134ms
create_cxx_tasks completed in 138ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 22ms
    create-module-model
      [gap of 23ms]
      create-cmake-model 12ms
    create-module-model completed in 38ms
    [gap of 12ms]
    create-ARM64_V8A-model 12ms
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 24ms
    [gap of 33ms]
  create-initial-cxx-model completed in 190ms
  [gap of 22ms]
create_cxx_tasks completed in 212ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 15ms
    create-module-model completed in 30ms
    create-module-model
      [gap of 11ms]
      create-cmake-model 20ms
    create-module-model completed in 35ms
    create-module-model
      create-cmake-model 14ms
    create-module-model completed in 23ms
    [gap of 26ms]
    create-X86_64-model 11ms
  create-initial-cxx-model completed in 216ms
create_cxx_tasks completed in 218ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 23ms
    create-module-model
      create-cmake-model 15ms
    create-module-model completed in 24ms
    create-module-model
      create-cmake-model 11ms
    create-module-model completed in 19ms
    create-ARMEABI_V7A-model 10ms
    [gap of 20ms]
  create-initial-cxx-model completed in 166ms
  [gap of 25ms]
create_cxx_tasks completed in 192ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 22ms
    [gap of 10ms]
    create-ARM64_V8A-model 15ms
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 20ms
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 20ms
    [gap of 24ms]
  create-initial-cxx-model completed in 151ms
  [gap of 17ms]
create_cxx_tasks completed in 169ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 20ms
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 17ms
    create-module-model
      create-cmake-model 11ms
    create-module-model completed in 18ms
    [gap of 25ms]
  create-initial-cxx-model completed in 131ms
  [gap of 14ms]
create_cxx_tasks completed in 145ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 15ms
    create-module-model completed in 24ms
    create-module-model
      create-cmake-model 14ms
    create-module-model completed in 23ms
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 20ms
    [gap of 29ms]
  create-initial-cxx-model completed in 162ms
  [gap of 22ms]
create_cxx_tasks completed in 185ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 25ms
    create-module-model
      create-cmake-model 15ms
    create-module-model completed in 23ms
    [gap of 24ms]
    create-X86_64-model 11ms
    create-module-model
      create-cmake-model 15ms
    create-module-model completed in 25ms
    [gap of 40ms]
  create-initial-cxx-model completed in 187ms
  [gap of 20ms]
create_cxx_tasks completed in 207ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 10ms
    create-module-model completed in 19ms
    create-module-model
      create-cmake-model 10ms
    create-module-model completed in 16ms
    create-module-model
      create-cmake-model 15ms
    create-module-model completed in 28ms
    [gap of 37ms]
  create-initial-cxx-model completed in 147ms
  [gap of 28ms]
create_cxx_tasks completed in 175ms

# C/C++ build system timings
create_cxx_tasks
  create-initial-cxx-model
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 22ms
    create-module-model
      create-cmake-model 13ms
    create-module-model completed in 19ms
    create-module-model
      create-cmake-model 12ms
    create-module-model completed in 17ms
    [gap of 22ms]
  create-initial-cxx-model completed in 129ms
  [gap of 17ms]
create_cxx_tasks completed in 146ms

