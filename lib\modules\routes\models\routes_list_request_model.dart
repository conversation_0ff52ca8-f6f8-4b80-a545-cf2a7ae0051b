import 'package:intl/intl.dart';

class RoutesListRequestModel {
  String? userId;
  DateTime? dataInicio;
  DateTime? dataTermino;

  RoutesListRequestModel({
    this.userId,
    this.dataInicio,
    this.dataTermino,
  });

  RoutesListRequestModel.fromJson(Map<String, dynamic> json) {
    userId = json['userId'];
    dataInicio =
        json['dataInicio'] != null ? DateTime.parse(json['dataInicio']) : null;

    dataTermino = json['dataTermino'] != null
        ? DateTime.parse(json['dataTermino'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['userId'] = userId;
    final dateFormat = DateFormat('yyyy-MM-dd');
    data['dataInicio'] =
        dataInicio != null ? dateFormat.format(dataInicio!) : null;
    data['dataTermino'] =
        dataTermino != null ? dateFormat.format(dataTermino!) : null;

    return data;
  }
}
