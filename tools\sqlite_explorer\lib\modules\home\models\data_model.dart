class DataModel {
  final int? id;
  final String key;
  final int? workspaceId;
  final int? storeId;
  final String? userId;
  final String? hashCodes;
  final String? value;
  final bool? isOnline;
  final DateTime? createdAt;

  DataModel({
    this.id,
    required this.key,
    this.workspaceId,
    this.storeId,
    this.userId,
    this.hashCodes,
    required this.value,
    this.isOnline,
    this.createdAt,
  });

  factory DataModel.fromJson(Map<String, dynamic> json) {
    return DataModel(
        id: json['id'],
        key: json['KEY'],
        workspaceId: json['WORKSPACE_ID'],
        storeId: json['STORE_ID'],
        userId: json['USERID'],
        hashCodes: json['HASHCODE'],
        value: json['VALUE'],
        createdAt:
            json['CREATEAT'] != null ? DateTime.parse(json['CREATEAT']) : null,
        isOnline: json['IS_ONLINE'] == 1 ? true : false);
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'KEY': key,
      'WORKSPACE_ID': workspaceId,
      'STORE_ID': storeId,
      'USERID': userId,
      'HASHCODE': hashCodes,
      'VALUE': value,
      'CREATEAT': createdAt?.toIso8601String(),
      'IS_ONLINE': isOnline == true ? 1 : 0,
    };
  }
}
