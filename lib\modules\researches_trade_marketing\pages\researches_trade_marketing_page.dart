import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/researches_trade_marketing/pages/components/researches_trade_camera_widget.dart';

class ResearchesTradeMarketingPage extends StatefulWidget {
  const ResearchesTradeMarketingPage({super.key});

  @override
  State<ResearchesTradeMarketingPage> createState() =>
      _ResearchesTradeMarketingPageState();
}

class _ResearchesTradeMarketingPageState
    extends State<ResearchesTradeMarketingPage> {
  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ResearchesTradeMarketingController>(
        "ResearchesTradeMarketingPage", builder: (ctrl) {
      return Scaffold(
          backgroundColor: whiteColor,
          appBar: AppBar(
            backgroundColor: themesController.getPrimaryColor(),
            title: LabelWidget(
              title: ctrl.titlePage,
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              textColor: whiteColor,
            ),
            leading: IconButton(
              icon: const Icon(
                Icons.arrow_back,
                color: whiteColor,
              ),
              onPressed: () {
                GetC.close();
              },
            ),
            actions: [
              IconButton(
                  onPressed: () async {
                    await ctrl.save();
                  },
                  icon: const Icon(
                    Icons.send,
                    color: whiteColor,
                  ))
            ],
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                15.toHeightSpace(),
                StepsWidget(
                  steps: ctrl.stepsPage,
                  currentStep: ctrl.tabIndex,
                ),
                15.toHeightSpace(),
                _buildBody(ctrl)
              ],
            ),
          ),
          bottomNavigationBar: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            child: Row(
              mainAxisAlignment: ctrl.tabIndex > 0
                  ? MainAxisAlignment.spaceBetween
                  : MainAxisAlignment.end,
              children: [
                if (ctrl.tabIndex > 0)
                  FilledButton(
                    onPressed: () {
                      ctrl.setTabIndexBack();
                    },
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.all<Color>(
                        Colors.grey,
                      ), // Sua cor aqui
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.arrow_back),
                        LabelWidget(
                          title: "Voltar".toUpperCase(),
                          fontSize: DeviceSize.fontSize(14, 15),
                        ),
                      ],
                    ),
                  ),
                if (ctrl.tabIndex < 2)
                  FilledButton(
                    onPressed: () {
                      ctrl.setTabIndexForward();
                    },
                    style: ButtonStyle(
                      backgroundColor: WidgetStateProperty.all<Color>(
                        themesController.getColorButton(),
                      ), // Sua cor aqui
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        LabelWidget(
                          title: "Próximo".toUpperCase(),
                          fontSize: DeviceSize.fontSize(14, 15),
                        ),
                        const Icon(Icons.arrow_forward),
                      ],
                    ),
                  ),
              ],
            ),
          ));
    });
  }

  Widget _buildBody(ResearchesTradeMarketingController ctrl) {
    List<Widget> children = [];
    final currentStep = ctrl.awsersTypes!.steps![ctrl.tabIndex];
    for (var item in currentStep.questions!) {
      if (item.dataType == "List" || item.dataType == "Boolean") {
        children.add(QuestionListWidget(
          title: item.question ?? "-",
          readonly: currentStep.readonly ?? false,
          selectedAwser: ctrl.hasAnswer(item) ? ctrl.getAnswerInt(item) : null,
          isRequired: item.metadata?.isRequired ?? false,
          awsers: item.metadata!.options!
              .map(
                (e) => QuestionListModel(
                    text: e.text, value: e.value, isSelected: false),
              )
              .toList(),
          onAnswerSelected: (p0) {
            ctrl.setQuestionListRadioAwser(p0, item);
          },
        ));
      } else if (item.dataType == "Text") {
        children.add(QuestionTextWidget(
          title: item.question ?? "-",
          readonly: currentStep.readonly ?? false,
          isRequired: item.metadata?.isRequired ?? false,
          hint: item.metadata!.placeholder ?? "-",
          selectedAwser: ctrl.hasAnswer(item) ? ctrl.getAnswerText(item) : null,
          onAnswerSelected: (p0) {
            ctrl.setQuestionTextAwser(p0, item);
          },
        ));
      } else if (item.dataType == "Number") {
        children.add(QuestionNumberWidget(
          title: item.question ?? "-",
          readonly: currentStep.readonly ?? false,
          hint: item.metadata!.placeholder ?? "-",
          isRequired: item.metadata?.isRequired ?? false,
          selectedAwser:
              ctrl.hasAnswer(item) ? ctrl.getAnswerDouble(item) : null,
          onAnswerSelected: (p0) {
            ctrl.setQuestionNumberAwser(p0, item);
          },
        ));
      } else if (item.dataType == "InputGroup") {
        children.add(QuestionInputGroupWidget(
          title: item.question ?? "-",
          readonly: currentStep.readonly ?? false,
          isRequired: item.metadata?.isRequired ?? false,
          onAnswerSelected: (ky, p0) {
            ctrl.setQuestionInputGroupAwser(ky, p0, item);
          },
          value1: ctrl.getValueInputGroupAnswer(item, "1"),
          value2: ctrl.getValueInputGroupAnswer(item, "2"),
          value3: ctrl.getValueInputGroupAnswer(item, "3"),
          value4: ctrl.getValueInputGroupAnswer(item, "4"),
        ));
      }

      if (item.explain == true) {
        children.add(QuestionTextWidget(
          title: item.explainLabel ?? "-",
          readonly: currentStep.readonly ?? false,
          isRequired: item.metadata?.isRequired ?? false,
          hint: "Escreva aqui",
          selectedAwser: item.explainText,
          onAnswerSelected: (p0) {
            ctrl.setExplainTextAwser(p0, item);
          },
        ));
      }
    }
    children.add(
      ResearchesTradeCameraWidget(
        title: "Antes",
        categoryId: 1,
        onPressed: () {
          ctrl.openCameraOption(1);
        },
      ),
    );
    if (ctrl.tabIndex > 0) {
      children.add(
        ResearchesTradeCameraWidget(
          title: "Depois",
          categoryId: 2,
          onPressed: () {
            ctrl.openCameraOption(2);
          },
        ),
      );
    }
    if (currentStep.answersSubmitted != null ||
        currentStep.answersSubmitted!.isNotEmpty) {
      children.add(
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: PrimaryButtonWidget(
            titleButtom: "MANUTENÇÕES ENVIADAS",
            buttonColor: Colors.grey.shade700,
            width: double.infinity,
            borderRadius: 0,
            onTap: () async {
              ctrl.setAnswersSubmittedSelected(currentStep.answersSubmitted!);
            },
          ),
        ),
      );
    }
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: children,
    );
  }
}
