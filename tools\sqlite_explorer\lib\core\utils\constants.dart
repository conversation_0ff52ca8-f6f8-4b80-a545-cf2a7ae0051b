import 'package:dio/dio.dart' as dio;
import 'package:sqlite_explorer/core/extensions/export.dart';
import 'package:sqlite_explorer/core/models/result_error.dart';

const error404 = {'error': 'Não foi possível buscar os dados', 'code': 404};

ResultError getOthersStatusCodes(dio.Response result) {
  if (result.statusCode!.isStatus500()) {
    return ResultError.fromJson(result.data);
  } else if (result.statusCode! == 999) {
    return ResultError.fromJson(result.data);
  } else {
    final error = result.getOthers();
    return error;
  }
}
