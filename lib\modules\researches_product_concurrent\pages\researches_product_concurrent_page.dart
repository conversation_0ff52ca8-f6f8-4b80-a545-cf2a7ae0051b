import 'package:flutter/foundation.dart';
import 'package:pharmalink/core/formatters/number_formatter.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class ResearchesProductConcurrentPage extends StatelessWidget {
  const ResearchesProductConcurrentPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ResearchesProductConcurrentController>(builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              ctrl.back();
            },
          ),
          actions: [
            Visibility(
              visible: kDebugMode,
              child: IconButton(
                  onPressed: () async {
                    await ctrl.saveDebugTest();
                  },
                  icon: const Icon(
                    Icons.save,
                    color: whiteColor,
                  )),
            ),
            IconButton(
                onPressed: () {
                  ctrl.next();
                },
                icon: const Icon(
                  Icons.arrow_forward,
                  color: whiteColor,
                ))
          ],
          elevation: 0,
        ),
        body: Container(
          color: themesController.getPrimaryColor(),
          width: double.infinity,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              LabelWidget(
                title: ctrl.productConcurrentDataModel!
                        .produtos![ctrl.productIndex].apresentacao ??
                    "-",
                fontSize: DeviceSize.fontSize(16, 19),
                textColor: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              5.toHeightSpace(),
              LabelWidget(
                title: ctrl
                        .productConcurrentDataModel!
                        .parametrizacaoCampos![ctrl.questionIndex]
                        .descricaoCampo ??
                    "-",
                fontSize: DeviceSize.fontSize(20, 23),
                textColor: Colors.white,
                fontWeight: FontWeight.bold,
              ),
              10.toHeightSpace(),
              _buildQuestion(ctrl),
            ],
          ),
        ),
      );
    });
  }

  Widget _buildQuestion(ResearchesProductConcurrentController ctrl) {
    final typeQuestion = ctrl.productConcurrentDataModel!
        .parametrizacaoCampos![ctrl.questionIndex].descricaoCampo;
    if (typeQuestion == "Distribuição Numérica") {
      final parameters =
          ctrl.productConcurrentDataModel!.distribuicoesNumericas!;
      return Column(
        children: parameters.map((e) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomInkWell(
              onTap: () {
                ctrl.setDistribuicoesNumericas(e);
              },
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white, width: 2),
                  color: ctrl.isDistribuicoesNumericasSelected(e)
                      ? Colors.white
                      : Colors.transparent,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Center(
                    child: LabelWidget(
                      title: e.descricao ?? "-",
                      fontSize: DeviceSize.fontSize(14, 17),
                      textColor: ctrl.isDistribuicoesNumericasSelected(e)
                          ? themesController.getPrimaryColor()
                          : Colors.white,
                      fontWeight: ctrl.isDistribuicoesNumericasSelected(e)
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      );
    } else if (typeQuestion == "Posicionamento") {
      final parameters = ctrl.productConcurrentDataModel!.posicionamentos!;
      return Column(
        children: parameters.map((e) {
          return Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomInkWell(
              onTap: () {
                ctrl.setPosicionamentos(e);
              },
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.white, width: 2),
                  color: ctrl.isPosicionamentosSelected(e)
                      ? Colors.white
                      : Colors.transparent,
                ),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Center(
                    child: LabelWidget(
                      title: e.descricao ?? "-",
                      fontSize: DeviceSize.fontSize(14, 17),
                      textColor: ctrl.isPosicionamentosSelected(e)
                          ? themesController.getPrimaryColor()
                          : Colors.white,
                      fontWeight: ctrl.isPosicionamentosSelected(e)
                          ? FontWeight.bold
                          : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          );
        }).toList(),
      );
    } else if (typeQuestion == "Estoque Sistema" ||
        typeQuestion == "Estoque Físico" ||
        typeQuestion == "Facing") {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: TextFormField(
          keyboardType: TextInputType.number,
          controller: TextEditingController(text: ctrl.getValue(typeQuestion!)),
          textAlign: TextAlign.center,
          decoration: const InputDecoration(
            filled: true,
            fillColor: Colors.white,
          ),
          inputFormatters: [NumberTextInputFormatter.withSeparator()],
          onChanged: (value) {
            ctrl.setValue(value, typeQuestion);
          },
        ),
      );
    } else if (typeQuestion == "Preço" || typeQuestion == "Desconto") {
      ctrl.getValue(typeQuestion!);
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: TextFormField(
          keyboardType: TextInputType.number,
          controller: ctrl.currencyController,
          textAlign: TextAlign.center,
          decoration: const InputDecoration(
            filled: true,
            fillColor: Colors.white,
          ),
          onChanged: (value) {
            ctrl.setValue(value, typeQuestion);
          },
        ),
      );
    } else if (typeQuestion == "Não Informado Pela Loja") {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Transform.scale(
          scale:
              1.5, // Ajuste este valor para aumentar ou diminuir o tamanho do checkbox
          child: Checkbox(
            onChanged: (value) {
              ctrl.setNaoInformado(value!);
            },
            value: ctrl.isNaoInformado(),
          ),
        ),
      );
    }
    return Container(
      color: themesController.getPrimaryColor(),
      width: double.infinity,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          LabelWidget(
            title: ctrl.productConcurrentDataModel!.produtos![ctrl.productIndex]
                    .apresentacao ??
                "-",
            fontSize: DeviceSize.fontSize(16, 19),
            textColor: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          5.toHeightSpace(),
          LabelWidget(
            title: ctrl.productConcurrentDataModel!
                    .parametrizacaoCampos![ctrl.questionIndex].descricaoCampo ??
                "-",
            fontSize: DeviceSize.fontSize(20, 23),
            textColor: Colors.white,
            fontWeight: FontWeight.bold,
          ),
          10.toHeightSpace(),
        ],
      ),
    );
  }
}
