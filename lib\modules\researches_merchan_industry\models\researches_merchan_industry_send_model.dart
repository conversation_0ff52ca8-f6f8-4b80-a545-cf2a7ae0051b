class ResearchesMerchanIndustrySendModel {
  int? idRota;
  List<ResearchesMerchanIndustrySendPesquisaMerchandisingItens>?
      pesquisaMerchandisingItens;
  List<ResearchesMerchanIndustrySendPesquisaMerchandisingItens>?
      pesquisaMerchandisingItensRemovidos;

  ResearchesMerchanIndustrySendModel(
      {this.idRota,
      this.pesquisaMerchandisingItens,
      this.pesquisaMerchandisingItensRemovidos});

  ResearchesMerchanIndustrySendModel.fromJson(Map<String, dynamic> json) {
    idRota = json['IdRota'];
    if (json['PesquisaMerchandisingItens'] != null) {
      pesquisaMerchandisingItens =
          <ResearchesMerchanIndustrySendPesquisaMerchandisingItens>[];
      json['PesquisaMerchandisingItens'].forEach((v) {
        pesquisaMerchandisingItens!.add(
            ResearchesMerchanIndustrySendPesquisaMerchandisingItens.fromJson(
                v));
      });
    }
    if (json['PesquisaMerchandisingItensRemovidos'] != null) {
      pesquisaMerchandisingItensRemovidos =
          <ResearchesMerchanIndustrySendPesquisaMerchandisingItens>[];
      json['PesquisaMerchandisingItensRemovidos'].forEach((v) {
        pesquisaMerchandisingItensRemovidos!.add(
            ResearchesMerchanIndustrySendPesquisaMerchandisingItens.fromJson(
                v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdRota'] = idRota;
    if (pesquisaMerchandisingItens != null) {
      data['PesquisaMerchandisingItens'] =
          pesquisaMerchandisingItens!.map((v) => v.toJson()).toList();
    }
    if (pesquisaMerchandisingItensRemovidos != null) {
      data['PesquisaMerchandisingItensRemovidos'] =
          pesquisaMerchandisingItensRemovidos!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResearchesMerchanIndustrySendPesquisaMerchandisingItens {
  int? idPesquisaMerchandising;
  int? idMerchandising;
  String? merchandisingDescricao;
  int? idFamilia;
  String? familiaDescricao;
  int? idTipoAtivacao;
  String? tipoAtivacaoDescricao;
  bool? enviado;
  int? quantidade;
  String? dataInclusao;

  ResearchesMerchanIndustrySendPesquisaMerchandisingItens(
      {this.idPesquisaMerchandising,
      this.idMerchandising,
      this.merchandisingDescricao,
      this.idFamilia,
      this.familiaDescricao,
      this.idTipoAtivacao,
      this.tipoAtivacaoDescricao,
      this.enviado,
      this.quantidade,
      this.dataInclusao});

  ResearchesMerchanIndustrySendPesquisaMerchandisingItens.fromJson(
      Map<String, dynamic> json) {
    idPesquisaMerchandising = json['IdPesquisaMerchandising'];
    idMerchandising = json['IdMerchandising'];
    merchandisingDescricao = json['MerchandisingDescricao'];
    idFamilia = json['IdFamilia'];
    familiaDescricao = json['FamiliaDescricao'];
    idTipoAtivacao = json['IdTipoAtivacao'];
    tipoAtivacaoDescricao = json['TipoAtivacaoDescricao'];
    enviado = json['Enviado'];
    quantidade = json['Quantidade'];
    dataInclusao = json['DataInclusao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPesquisaMerchandising'] = idPesquisaMerchandising;
    data['IdMerchandising'] = idMerchandising;
    data['MerchandisingDescricao'] = merchandisingDescricao;
    data['IdFamilia'] = idFamilia;
    data['FamiliaDescricao'] = familiaDescricao;
    data['IdTipoAtivacao'] = idTipoAtivacao;
    data['TipoAtivacaoDescricao'] = tipoAtivacaoDescricao;
    data['Enviado'] = enviado;
    data['Quantidade'] = quantidade;
    data['DataInclusao'] = dataInclusao;
    return data;
  }
}
