import 'package:pharmalink/app/routes/logging_middleware.dart';
import 'package:pharmalink/modules/app_modules.dart';

import '../../exports/get_exports.dart';

abstract class AppPages {
  static final pages = <GetPage>[
    GetPage(
      name: RoutesPath.home,
      page: () => const HomePage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.navigationPage,
      page: () => const NavigationPagePage(),
      bindings: [
        VisitsBinding(),
        StoreRoutesBinding(),
        StoreRoutesPanelBinding(),
        StoreRoutesPlannedBinding(),
        // SynchronizationsOfflineBinding(),
      ],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
        name: RoutesPath.initialize,
        page: () => const InitializePage(),
        bindings: [InitializeBinding()],
        transition: Transition.cupertino),
    GetPage(
      name: RoutesPath.splashPage,
      page: () => const SplashScreenPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.begin,
      page: () => const BeginPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.workspaces,
      page: () => WorkspacesPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.login,
      page: () => const LoginPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.logoutB2c,
      page: () => const LogoutB2cPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.synchronizations,
      bindings: [
        OrdersBinding(),
        ReportContentsBinding(),
        VisitsBinding(),
        ResearchesComplementaryBinding(),
        ResearchesShareOfShelfBinding(),
        ResearchesProductIndustryBinding(),
        ResearchesProductConcurrentBinding(),
        ResearchesMerchanIndustryBinding(),
        ResearchesTradeMarketingBinding(),
        ResearchesMerchanCompetitiveBinding(),
      ],
      page: () => SynchronizationsPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    // GetPage(
    //   name: RoutesPath.stores,
    //   page: () => const StoresPage(),
    //   bindings: [VisitsBinding()],
    //   transition: Transition.fadeIn,
    //   middlewares: [LoggingMiddleware()],
    // ),
    GetPage(
      name: RoutesPath.settings,
      page: () => const SettingsPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.settingsApp,
      page: () => const SettingsAppPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.settingsAppDashboard,
      page: () => const SettingsAppDashboardPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.settingsAppOrders,
      page: () => const SettingsAppOrdersPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.reports,
      page: () => const ReportsPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    // GetPage(
    //   name: RoutesPath.storesPdv,
    //   page: () => const StoresPdvPage(),
    //   transition: Transition.fadeIn,
    //   middlewares: [LoggingMiddleware()],
    // ),
    // GetPage(
    //   name: RoutesPath.storeDetail,
    //   page: () => const StoreDetailPage(),
    //   transition: Transition.fadeIn,
    //   middlewares: [LoggingMiddleware()],
    // ),
    GetPage(
      name: RoutesPath.orderTypes,
      page: () => const OrderTypesPage(),
      bindings: [
        OrderTypesBinding(),
        OrderPaymentTypeBinding(),
        OrdersTabloidBinding(),
        OrdersIdealMixBinding(),
        OrdersBinding(),
        OrdersComboBinding(),
        OrdersResumeBinding()
      ],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.orderPaymentType,
      bindings: [
        OrderPaymentTypeBinding(),
        OrdersIdealMixBinding(),
        OrdersBinding(),
        OrdersResumeBinding(),
      ],
      page: () => const OrderPaymentTypePage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.orders,
      page: () => const OrdersPage(),
      bindings: [OrdersBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.ordersIdealMix,
      page: () => const OrdersIdealMixPage(),
      bindings: [OrdersIdealMixBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.ordersCombo,
      page: () => const OrdersComboPage(),
      bindings: [OrdersComboBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.ordersResume,
      page: () => const OrdersResumePage(),
      bindings: [OrdersResumeBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.ordersTabloid,
      page: () => const OrdersTabloidPage(),
      bindings: [
        OrdersTabloidBinding(),
        OrderTypesBinding(),
        OrderPaymentTypeBinding(),
        OrdersIdealMixBinding(),
        OrdersBinding(),
        OrdersComboBinding(),
        OrdersResumeBinding(),
      ],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.qATester,
      page: () => const QATesterPage(),
      bindings: [QATesterBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.reportContents,
      page: () => const ReportContentsPage(),
      bindings: [
        ReportContentsBinding(),
      ],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.reportOrders,
      page: () => const ReportOrdersPage(),
      bindings: [
        OrderTypesBinding(),
        OrdersTabloidBinding(),
        OrderPaymentTypeBinding(),
        OrdersIdealMixBinding(),
        OrdersBinding(),
        OrdersComboBinding(),
        OrdersResumeBinding(),
        ReportOrdersBinding()
      ],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.reportOrdersDetail,
      page: () => const ReportOrdersDetailPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.visits,
      page: () => const VisitsPage(),
      bindings: [
        VisitsBinding(),
        ResearchesComplementaryBinding(),
        ResearchesShareOfShelfBinding(),
        ResearchesProductIndustryBinding(),
        ResearchesProductConcurrentBinding(),
        ResearchesMerchanIndustryBinding(),
        ResearchesMerchanCompetitiveBinding(),
        ResearchesTradeMarketingBinding(),
      ],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.visitsCamera,
      page: () => const VisitisCameraWidget(),
      bindings: [VisitsBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesMerchanIndustryQuestion,
      page: () => const ResearchesMerchanIndustryQuestionPage(),
      bindings: const [],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesMerchanIndustry,
      page: () => const ResearchesMerchanIndustryPage(),
      bindings: [ResearchesMerchanIndustryBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesMerchanCompetitive,
      page: () => const ResearchesMerchanCompetitivePage(),
      bindings: [ResearchesMerchanCompetitiveBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesMerchanCompetitiveQuestion,
      page: () => const ResearchesMerchanCompetitiveQuestionPage(),
      bindings: const [],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesProductIndustry,
      page: () => const ResearchesProductIndustryPage(),
      bindings: [ResearchesProductIndustryBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesProductConcurrent,
      page: () => const ResearchesProductConcurrentPage(),
      bindings: [ResearchesProductConcurrentBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesTradeMarketing,
      page: () => const ResearchesTradeMarketingPage(),
      bindings: [ResearchesTradeMarketingBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesTradeMaintenanceList,
      page: () => ResearchesTradeMaintenanceListPage(),
      bindings: [ResearchesTradeMarketingBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesTradeMaintenanceDetail,
      page: () => const ResearchesTradeMaintenanceDetailPage(),
      bindings: [ResearchesTradeMarketingBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesComplementary,
      page: () => const ResearchesComplementaryPage(),
      bindings: [ResearchesComplementaryBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesComplementaryQuestion,
      page: () => const ResearchesComplementaryQuestionPage(),
      bindings: const [],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesShareOfShelf,
      page: () => const ResearchesShareOfShelfPage(),
      bindings: [ResearchesShareOfShelfBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesShareOfShelfPhotoCrop,
      page: () => const ResearchesShareOfShelfPhotoCropPage(),
      bindings: [ResearchesShareOfShelfBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.researchesShareOfShelfPhotoArea,
      page: () => const ResearchesShareOfShelfPhotoAreaPage(),
      bindings: [ResearchesShareOfShelfBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.logsHttp,
      page: () => const LogsHttpPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.cameraPicker,
      bindings: [CameraPickerBinding()],
      page: () => const CameraPickerPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.cameraPickerPreview,
      page: () => const CameraPickerPreviewPage(),
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.permissionRequest,
      page: () => const PermissionRequestPage(),
      bindings: [PermissionRequestBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.reportOrdersSync,
      page: () => const ReportOrdersSyncPage(),
      bindings: [ReportOrdersSyncBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.logTraceMonitor,
      page: () => const LogTraceMonitorPage(),
      bindings: [LogTraceMonitorBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.storeRoutes,
      page: () => const StoreRoutesPage(),
      bindings: [
        StoreRoutesBinding(),
        VisitsBinding(),
        ResearchesComplementaryBinding(),
        ResearchesShareOfShelfBinding(),
        ResearchesProductIndustryBinding(),
        ResearchesProductConcurrentBinding(),
        ResearchesMerchanIndustryBinding(),
        ResearchesTradeMarketingBinding(),
        ResearchesMerchanCompetitiveBinding(),
      ],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.storeRoutesPanel,
      page: () => const StoreRoutesPanelPage(),
      bindings: [StoreRoutesPanelBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.storeRoutesPlanned,
      page: () => const StoreRoutesPlannedPage(),
      bindings: [StoreRoutesPlannedBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.storeRoutesDetails,
      page: () => const StoreRoutesDetailsPage(),
      bindings: [StoreRoutesDetailsBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
      name: RoutesPath.permissionGlobal,
      page: () => const PermissionGlobalPage(),
      bindings: [PermissionGlobalBinding()],
      transition: Transition.fadeIn,
      middlewares: [LoggingMiddleware()],
    ),
    GetPage(
        name: RoutesPath.cacheConfig,
        page: () => const CacheConfigPage(),
        bindings: [CacheConfigBinding()],
        transition: Transition.fadeIn),
    GetPage(
        name: RoutesPath.reportOrdersUpload,
        page: () => const ReportOrdersUploadPage(),
        bindings: [ReportOrdersUploadBinding()],
        transition: Transition.fadeIn),
  ];
}
