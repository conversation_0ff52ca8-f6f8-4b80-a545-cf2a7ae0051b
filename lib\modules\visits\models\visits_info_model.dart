import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/exports/get_exports.dart';

class VisitisInfoModel extends SqfLiteBase<VisitisInfoModel> {
  String? chave;
  String? valor;

  VisitisInfoModel({
    this.chave,
    this.valor,
  }) : super(DatabaseModels.visitisInfoModel);

  VisitisInfoModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.visitisInfoModel) {
    chave = json['Chave'];
    valor = json['Valor'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Chave'] = chave;
    data['Valor'] = valor;

    return data;
  }

  Future<VisitisInfoModel?> getFirst({required int workspaceId}) async {
    var list = await getAll<VisitisInfoModel>(
        workspaceId: workspaceId, VisitisInfoModel.fromJson);
    return list.isNotEmpty ? list.first : null;
  }

  Future<VisitisInfoModel?> getFirstByType(
      {required int workspaceId, required String type}) async {
    var list = await getAll<VisitisInfoModel>(
        workspaceId: workspaceId, VisitisInfoModel.fromJson);
    return list.firstWhereOrNull((element) => element.chave == type);
  }

  Future<bool> exists({required int workspaceId}) async {
    var list = await getAll<VisitisInfoModel>(
        workspaceId: workspaceId, VisitisInfoModel.fromJson);
    return list.isNotEmpty;
  }

  Future<List<VisitisInfoModel>> getList({required int workspaceId}) async {
    var list = await getAll<VisitisInfoModel>(
        workspaceId: workspaceId, VisitisInfoModel.fromJson);
    return list;
  }
}
