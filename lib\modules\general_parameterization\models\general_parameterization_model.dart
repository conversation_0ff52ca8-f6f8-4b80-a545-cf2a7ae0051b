// ignore: file_names
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class ProductParameterModel extends SqfLiteBase<ProductParameterModel> {
  ParametrizacaoRelatorioMapaVendas? parametrizacaoRelatorioMapaVendas;
  ParametrizacaoRelatorioMapaVendasValorExibido?
      parametrizacaoRelatorioMapaVendasValorExibido;
  bool? parametrizacaoPedidoRepDiferencaEstoquePesquisado;
  bool? parametrizacaoPermitirCadastroDiretoDePDV;
  bool? permitirCadastroDiretoDePDVPorConectividade;
  int? parametrizacaoDiasDemonstracaoEstoque;
  int? parametrizacaoTempoSessao;
  bool? parametrizacaoDestaquePesquisaProdutoIndustria;
  bool? parametrizacaoSomenteProdutosDestaque;
  ParametrizacaoPerfilAcompanhaEmailPedido?
      parametrizacaoPerfilAcompanhaEmailPedido;
  bool? parametrizacaoHabilitaPreco;
  bool? parametrizacaoHabilitaEstoque;
  bool? parametrizacaoAutenticacaoExterna;
  String? parametrizacaoAutenticacaoExternaEndereco;
  List<ParametrizacaoAutenticacaoExternaPerfis>?
      parametrizacaoAutenticacaoExternaPerfis;
  ParametrizacaoTipoCliente? parametrizacaoTipoCliente;
  bool? trocaBandeiraNecessitaAprovacao;
  bool? parametrizacaoPedidoFlexPrecoMinimo;
  bool? parametrizacaoProjetoIraTrabalharComModeloDUN;
  int? parametrizacaoFormatoDeEnvio;
  String? caminhoArquivosLogico;
  ParametrizacaoExibeFiltroCategoria? parametrizacaoExibeFiltroCategoria;
  ParametrizacaoExibeFiltroCategoria? parametrizacaoExibeFiltroFamilia;
  ParametrizacaoExibeFiltroCategoria? parametrizacaoExibeImagemProduto;
  ParametrizacaoExibeFiltroCategoria? parametrizacaoExibirFiltrosPersonalizados;
  ParametrizacaoExibirFiltrosPersonalizadosPedido?
      parametrizacaoExibirFiltrosPersonalizadosPedido;
  bool? parametrizacaoFiltroDestaqueAtivo;
  bool? habilitaMultiSetor;
  bool? parametrizacaoUtilizarProgramaLancamento;
  String? parametrizacaoNomeProgramaLancamento;
  List<ParametrizacaoCargaProgramada>? parametrizacaoCargaProgramada;
  ParametrizacaoAprovaPrecoProduto? parametrizacaoAprovaPrecoProduto;

  ProductParameterModel(
      {this.parametrizacaoRelatorioMapaVendas,
      this.parametrizacaoRelatorioMapaVendasValorExibido,
      this.parametrizacaoPedidoRepDiferencaEstoquePesquisado,
      this.parametrizacaoPermitirCadastroDiretoDePDV,
      this.permitirCadastroDiretoDePDVPorConectividade,
      this.parametrizacaoDiasDemonstracaoEstoque,
      this.parametrizacaoTempoSessao,
      this.parametrizacaoDestaquePesquisaProdutoIndustria,
      this.parametrizacaoSomenteProdutosDestaque,
      this.parametrizacaoPerfilAcompanhaEmailPedido,
      this.parametrizacaoHabilitaPreco,
      this.parametrizacaoHabilitaEstoque,
      this.parametrizacaoAutenticacaoExterna,
      this.parametrizacaoAutenticacaoExternaEndereco,
      this.parametrizacaoAutenticacaoExternaPerfis,
      this.parametrizacaoTipoCliente,
      this.trocaBandeiraNecessitaAprovacao,
      this.parametrizacaoPedidoFlexPrecoMinimo,
      this.parametrizacaoProjetoIraTrabalharComModeloDUN,
      this.parametrizacaoFormatoDeEnvio,
      this.caminhoArquivosLogico,
      this.parametrizacaoExibeFiltroCategoria,
      this.parametrizacaoExibeFiltroFamilia,
      this.parametrizacaoExibeImagemProduto,
      this.parametrizacaoExibirFiltrosPersonalizados,
      this.parametrizacaoExibirFiltrosPersonalizadosPedido,
      this.parametrizacaoFiltroDestaqueAtivo,
      this.habilitaMultiSetor,
      this.parametrizacaoUtilizarProgramaLancamento,
      this.parametrizacaoNomeProgramaLancamento,
      this.parametrizacaoCargaProgramada,
      this.parametrizacaoAprovaPrecoProduto})
      : super(DatabaseModels.productParameterModel);

  ProductParameterModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.productParameterModel) {
    parametrizacaoRelatorioMapaVendas =
        json['ParametrizacaoRelatorioMapaVendas'] != null
            ? ParametrizacaoRelatorioMapaVendas.fromJson(
                json['ParametrizacaoRelatorioMapaVendas'])
            : null;
    parametrizacaoRelatorioMapaVendasValorExibido =
        json['ParametrizacaoRelatorioMapaVendasValorExibido'] != null
            ? ParametrizacaoRelatorioMapaVendasValorExibido.fromJson(
                json['ParametrizacaoRelatorioMapaVendasValorExibido'])
            : null;
    parametrizacaoPedidoRepDiferencaEstoquePesquisado =
        json['ParametrizacaoPedidoRepDiferencaEstoquePesquisado'];
    parametrizacaoPermitirCadastroDiretoDePDV =
        json['ParametrizacaoPermitirCadastroDiretoDePDV'];
    permitirCadastroDiretoDePDVPorConectividade =
        json['PermitirCadastroDiretoDePDVPorConectividade'];
    parametrizacaoDiasDemonstracaoEstoque =
        json['ParametrizacaoDiasDemonstracaoEstoque'];
    parametrizacaoTempoSessao = json['ParametrizacaoTempoSessao'];
    parametrizacaoDestaquePesquisaProdutoIndustria =
        json['ParametrizacaoDestaquePesquisaProdutoIndustria'];
    parametrizacaoSomenteProdutosDestaque =
        json['ParametrizacaoSomenteProdutosDestaque'];
    parametrizacaoPerfilAcompanhaEmailPedido =
        json['ParametrizacaoPerfilAcompanhaEmailPedido'] != null
            ? ParametrizacaoPerfilAcompanhaEmailPedido.fromJson(
                json['ParametrizacaoPerfilAcompanhaEmailPedido'])
            : null;
    parametrizacaoHabilitaPreco = json['ParametrizacaoHabilitaPreco'];
    parametrizacaoHabilitaEstoque = json['ParametrizacaoHabilitaEstoque'];
    parametrizacaoAutenticacaoExterna =
        json['ParametrizacaoAutenticacaoExterna'];
    parametrizacaoAutenticacaoExternaEndereco =
        json['ParametrizacaoAutenticacaoExternaEndereco'];
    if (json['ParametrizacaoAutenticacaoExternaPerfis'] != null) {
      parametrizacaoAutenticacaoExternaPerfis =
          <ParametrizacaoAutenticacaoExternaPerfis>[];
      json['ParametrizacaoAutenticacaoExternaPerfis'].forEach((v) {
        parametrizacaoAutenticacaoExternaPerfis!
            .add(ParametrizacaoAutenticacaoExternaPerfis.fromJson(v));
      });
    }
    parametrizacaoTipoCliente = json['ParametrizacaoTipoCliente'] != null
        ? ParametrizacaoTipoCliente.fromJson(json['ParametrizacaoTipoCliente'])
        : null;
    trocaBandeiraNecessitaAprovacao = json['TrocaBandeiraNecessitaAprovacao'];
    parametrizacaoPedidoFlexPrecoMinimo =
        json['ParametrizacaoPedidoFlexPrecoMinimo'];
    parametrizacaoProjetoIraTrabalharComModeloDUN =
        json['ParametrizacaoProjetoIraTrabalharComModeloDUN'];
    parametrizacaoFormatoDeEnvio = json['ParametrizacaoFormatoDeEnvio'];
    caminhoArquivosLogico = json['CaminhoArquivosLogico'];
    parametrizacaoExibeFiltroCategoria =
        json['ParametrizacaoExibeFiltroCategoria'] != null
            ? ParametrizacaoExibeFiltroCategoria.fromJson(
                json['ParametrizacaoExibeFiltroCategoria'])
            : null;
    parametrizacaoExibeFiltroFamilia =
        json['ParametrizacaoExibeFiltroFamilia'] != null
            ? ParametrizacaoExibeFiltroCategoria.fromJson(
                json['ParametrizacaoExibeFiltroFamilia'])
            : null;
    parametrizacaoExibeImagemProduto =
        json['ParametrizacaoExibeImagemProduto'] != null
            ? ParametrizacaoExibeFiltroCategoria.fromJson(
                json['ParametrizacaoExibeImagemProduto'])
            : null;
    parametrizacaoExibirFiltrosPersonalizados =
        json['ParametrizacaoExibirFiltrosPersonalizados'] != null
            ? ParametrizacaoExibeFiltroCategoria.fromJson(
                json['ParametrizacaoExibirFiltrosPersonalizados'])
            : null;
    parametrizacaoExibirFiltrosPersonalizadosPedido =
        json['ParametrizacaoExibirFiltrosPersonalizadosPedido'] != null
            ? ParametrizacaoExibirFiltrosPersonalizadosPedido.fromJson(
                json['ParametrizacaoExibirFiltrosPersonalizadosPedido'])
            : null;
    parametrizacaoFiltroDestaqueAtivo =
        json['ParametrizacaoFiltroDestaqueAtivo'];
    habilitaMultiSetor = json['HabilitaMultiSetor'];
    parametrizacaoUtilizarProgramaLancamento =
        json['ParametrizacaoUtilizarProgramaLancamento'];
    parametrizacaoNomeProgramaLancamento =
        json['ParametrizacaoNomeProgramaLancamento'];
    if (json['ParametrizacaoCargaProgramada'] != null) {
      parametrizacaoCargaProgramada = <ParametrizacaoCargaProgramada>[];
      json['ParametrizacaoCargaProgramada'].forEach((v) {
        parametrizacaoCargaProgramada!
            .add(ParametrizacaoCargaProgramada.fromJson(v));
      });
    }
    parametrizacaoAprovaPrecoProduto =
        json['ParametrizacaoAprovaPrecoProduto'] != null
            ? ParametrizacaoAprovaPrecoProduto.fromJson(
                json['ParametrizacaoAprovaPrecoProduto'])
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (parametrizacaoRelatorioMapaVendas != null) {
      data['ParametrizacaoRelatorioMapaVendas'] =
          parametrizacaoRelatorioMapaVendas!.toJson();
    }
    if (parametrizacaoRelatorioMapaVendasValorExibido != null) {
      data['ParametrizacaoRelatorioMapaVendasValorExibido'] =
          parametrizacaoRelatorioMapaVendasValorExibido!.toJson();
    }
    data['ParametrizacaoPedidoRepDiferencaEstoquePesquisado'] =
        parametrizacaoPedidoRepDiferencaEstoquePesquisado;
    data['ParametrizacaoPermitirCadastroDiretoDePDV'] =
        parametrizacaoPermitirCadastroDiretoDePDV;
    data['PermitirCadastroDiretoDePDVPorConectividade'] =
        permitirCadastroDiretoDePDVPorConectividade;
    data['ParametrizacaoDiasDemonstracaoEstoque'] =
        parametrizacaoDiasDemonstracaoEstoque;
    data['ParametrizacaoTempoSessao'] = parametrizacaoTempoSessao;
    data['ParametrizacaoDestaquePesquisaProdutoIndustria'] =
        parametrizacaoDestaquePesquisaProdutoIndustria;
    data['ParametrizacaoSomenteProdutosDestaque'] =
        parametrizacaoSomenteProdutosDestaque;
    if (parametrizacaoPerfilAcompanhaEmailPedido != null) {
      data['ParametrizacaoPerfilAcompanhaEmailPedido'] =
          parametrizacaoPerfilAcompanhaEmailPedido!.toJson();
    }
    data['ParametrizacaoHabilitaPreco'] = parametrizacaoHabilitaPreco;
    data['ParametrizacaoHabilitaEstoque'] = parametrizacaoHabilitaEstoque;
    data['ParametrizacaoAutenticacaoExterna'] =
        parametrizacaoAutenticacaoExterna;
    data['ParametrizacaoAutenticacaoExternaEndereco'] =
        parametrizacaoAutenticacaoExternaEndereco;
    if (parametrizacaoAutenticacaoExternaPerfis != null) {
      data['ParametrizacaoAutenticacaoExternaPerfis'] =
          parametrizacaoAutenticacaoExternaPerfis!
              .map((v) => v.toJson())
              .toList();
    }
    if (parametrizacaoTipoCliente != null) {
      data['ParametrizacaoTipoCliente'] = parametrizacaoTipoCliente!.toJson();
    }
    data['TrocaBandeiraNecessitaAprovacao'] = trocaBandeiraNecessitaAprovacao;
    data['ParametrizacaoPedidoFlexPrecoMinimo'] =
        parametrizacaoPedidoFlexPrecoMinimo;
    data['ParametrizacaoProjetoIraTrabalharComModeloDUN'] =
        parametrizacaoProjetoIraTrabalharComModeloDUN;
    data['ParametrizacaoFormatoDeEnvio'] = parametrizacaoFormatoDeEnvio;
    data['CaminhoArquivosLogico'] = caminhoArquivosLogico;
    if (parametrizacaoExibeFiltroCategoria != null) {
      data['ParametrizacaoExibeFiltroCategoria'] =
          parametrizacaoExibeFiltroCategoria!.toJson();
    }
    if (parametrizacaoExibeFiltroFamilia != null) {
      data['ParametrizacaoExibeFiltroFamilia'] =
          parametrizacaoExibeFiltroFamilia!.toJson();
    }
    if (parametrizacaoExibeImagemProduto != null) {
      data['ParametrizacaoExibeImagemProduto'] =
          parametrizacaoExibeImagemProduto!.toJson();
    }
    if (parametrizacaoExibirFiltrosPersonalizados != null) {
      data['ParametrizacaoExibirFiltrosPersonalizados'] =
          parametrizacaoExibirFiltrosPersonalizados!.toJson();
    }
    if (parametrizacaoExibirFiltrosPersonalizadosPedido != null) {
      data['ParametrizacaoExibirFiltrosPersonalizadosPedido'] =
          parametrizacaoExibirFiltrosPersonalizadosPedido!.toJson();
    }
    data['ParametrizacaoFiltroDestaqueAtivo'] =
        parametrizacaoFiltroDestaqueAtivo;
    data['HabilitaMultiSetor'] = habilitaMultiSetor;
    data['ParametrizacaoUtilizarProgramaLancamento'] =
        parametrizacaoUtilizarProgramaLancamento;
    data['ParametrizacaoNomeProgramaLancamento'] =
        parametrizacaoNomeProgramaLancamento;
    if (parametrizacaoCargaProgramada != null) {
      data['ParametrizacaoCargaProgramada'] =
          parametrizacaoCargaProgramada!.map((v) => v.toJson()).toList();
    }
    if (parametrizacaoAprovaPrecoProduto != null) {
      data['ParametrizacaoAprovaPrecoProduto'] =
          parametrizacaoAprovaPrecoProduto!.toJson();
    }
    return data;
  }

  Future<ProductParameterModel?> getFirst({required int workspaceId}) async {
    var list = await getAll<ProductParameterModel>(
        workspaceId: workspaceId, ProductParameterModel.fromJson);
    return list.isEmpty ? null : list.first;
  }

  Future<bool> exists({required int workspaceId}) async {
    var list = await getAll<ProductParameterModel>(
        workspaceId: workspaceId, ProductParameterModel.fromJson);
    return list.isNotEmpty;
  }

  Future<List<ProductParameterModel>> getList(
      {required int workspaceId}) async {
    var list = await getAll<ProductParameterModel>(
        workspaceId: workspaceId, ProductParameterModel.fromJson);
    return list;
  }
}

class ParametrizacaoRelatorioMapaVendas {
  bool? familia;
  bool? produto;

  ParametrizacaoRelatorioMapaVendas({this.familia, this.produto});

  ParametrizacaoRelatorioMapaVendas.fromJson(Map<String, dynamic> json) {
    familia = json['Familia'];
    produto = json['Produto'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Familia'] = familia;
    data['Produto'] = produto;
    return data;
  }
}

class ParametrizacaoRelatorioMapaVendasValorExibido {
  bool? valorBruto;
  bool? valorLiquido;
  bool? valorBrutoLiquido;

  ParametrizacaoRelatorioMapaVendasValorExibido(
      {this.valorBruto, this.valorLiquido, this.valorBrutoLiquido});

  ParametrizacaoRelatorioMapaVendasValorExibido.fromJson(
      Map<String, dynamic> json) {
    valorBruto = json['ValorBruto'];
    valorLiquido = json['ValorLiquido'];
    valorBrutoLiquido = json['ValorBrutoLiquido'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ValorBruto'] = valorBruto;
    data['ValorLiquido'] = valorLiquido;
    data['ValorBrutoLiquido'] = valorBrutoLiquido;
    return data;
  }
}

class ParametrizacaoPerfilAcompanhaEmailPedido {
  bool? keyAccount;
  bool? gerenteCampo;
  bool? gerenteRegional;
  bool? diretorComercial;

  ParametrizacaoPerfilAcompanhaEmailPedido(
      {this.keyAccount,
      this.gerenteCampo,
      this.gerenteRegional,
      this.diretorComercial});

  ParametrizacaoPerfilAcompanhaEmailPedido.fromJson(Map<String, dynamic> json) {
    keyAccount = json['KeyAccount'];
    gerenteCampo = json['GerenteCampo'];
    gerenteRegional = json['GerenteRegional'];
    diretorComercial = json['DiretorComercial'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['KeyAccount'] = keyAccount;
    data['GerenteCampo'] = gerenteCampo;
    data['GerenteRegional'] = gerenteRegional;
    data['DiretorComercial'] = diretorComercial;
    return data;
  }
}

class ParametrizacaoAutenticacaoExternaPerfis {
  String? perfilId;
  String? perfilNome;
  bool? selecionado;

  ParametrizacaoAutenticacaoExternaPerfis(
      {this.perfilId, this.perfilNome, this.selecionado});

  ParametrizacaoAutenticacaoExternaPerfis.fromJson(Map<String, dynamic> json) {
    perfilId = json['PerfilId'];
    perfilNome = json['PerfilNome'];
    selecionado = json['Selecionado'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['PerfilId'] = perfilId;
    data['PerfilNome'] = perfilNome;
    data['Selecionado'] = selecionado;
    return data;
  }
}

class ParametrizacaoTipoCliente {
  String? singular;
  String? plural;
  String? artigo;

  ParametrizacaoTipoCliente({this.singular, this.plural, this.artigo});

  ParametrizacaoTipoCliente.fromJson(Map<String, dynamic> json) {
    singular = json['Singular'];
    plural = json['Plural'];
    artigo = json['Artigo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Singular'] = singular;
    data['Plural'] = plural;
    data['Artigo'] = artigo;
    return data;
  }
}

class ParametrizacaoExibeFiltroCategoria {
  bool? pedidoPadrao;
  bool? pedidoEspecial;
  bool? pedidoREP;
  bool? pedidoFlex;

  ParametrizacaoExibeFiltroCategoria(
      {this.pedidoPadrao,
      this.pedidoEspecial,
      this.pedidoREP,
      this.pedidoFlex});

  ParametrizacaoExibeFiltroCategoria.fromJson(Map<String, dynamic> json) {
    pedidoPadrao = json['PedidoPadrao'];
    pedidoEspecial = json['PedidoEspecial'];
    pedidoREP = json['PedidoREP'];
    pedidoFlex = json['PedidoFlex'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['PedidoPadrao'] = pedidoPadrao;
    data['PedidoEspecial'] = pedidoEspecial;
    data['PedidoREP'] = pedidoREP;
    data['PedidoFlex'] = pedidoFlex;
    return data;
  }
}

class ParametrizacaoExibirFiltrosPersonalizadosPedido {
  bool? manterPadrao;
  bool? ocultarPadrao;
  bool? desabilitar;

  ParametrizacaoExibirFiltrosPersonalizadosPedido(
      {this.manterPadrao, this.ocultarPadrao, this.desabilitar});

  ParametrizacaoExibirFiltrosPersonalizadosPedido.fromJson(
      Map<String, dynamic> json) {
    manterPadrao = json['ManterPadrao'];
    ocultarPadrao = json['OcultarPadrao'];
    desabilitar = json['Desabilitar'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['ManterPadrao'] = manterPadrao;
    data['OcultarPadrao'] = ocultarPadrao;
    data['Desabilitar'] = desabilitar;
    return data;
  }
}

class ParametrizacaoCargaProgramada {
  String? roleId;
  bool? flag;
  String? roleName;

  ParametrizacaoCargaProgramada({this.roleId, this.flag, this.roleName});

  ParametrizacaoCargaProgramada.fromJson(Map<String, dynamic> json) {
    roleId = json['RoleId'];
    flag = json['Flag'];
    roleName = json['RoleName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['RoleId'] = roleId;
    data['Flag'] = flag;
    data['RoleName'] = roleName;
    return data;
  }
}

class ParametrizacaoAprovaPrecoProduto {
  bool? aprovaPreco;
  bool? aprovaProduto;

  ParametrizacaoAprovaPrecoProduto({this.aprovaPreco, this.aprovaProduto});

  ParametrizacaoAprovaPrecoProduto.fromJson(Map<String, dynamic> json) {
    aprovaPreco = json['AprovaPreco'];
    aprovaProduto = json['AprovaProduto'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['AprovaPreco'] = aprovaPreco;
    data['AprovaProduto'] = aprovaProduto;
    return data;
  }
}
