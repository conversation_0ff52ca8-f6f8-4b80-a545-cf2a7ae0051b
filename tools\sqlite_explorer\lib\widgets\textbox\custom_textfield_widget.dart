import 'package:flutter/services.dart';
import 'package:sqlite_explorer/core/utils/export.dart';
import 'package:sqlite_explorer/exports/basic_exports.dart';
import 'package:sqlite_explorer/widgets/widgets_exports.dart';

class CustomTextField extends StatelessWidget {
  final IconData? leadingIcon;
  final IconData? trailingIcon;
  final bool? isPassword;
  final String? hint;
  final String? labelText;
  final TextEditingController controller;
  final ValueChanged<String>? onChanged;
  final GestureTapCallback? trailingTap;
  final String? label;
  final TextInputType? keyboardType;
  final bool? readOnly;
  final int? maxLength;
  final MaxLengthEnforcement? maxLengthEnforcement;
  final int? minLines;
  final int? maxLines;
  final List<TextInputFormatter>? inputFormatters;
  const CustomTextField(
      {super.key,
      this.leadingIcon,
      this.trailingIcon,
      this.isPassword,
      this.hint,
      required this.controller,
      this.onChanged,
      this.trailingTap,
      this.label,
      this.labelText,
      this.keyboardType,
      this.readOnly,
      this.maxLength,
      this.maxLengthEnforcement,
      this.minLines,
      this.maxLines,
      this.inputFormatters});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey,
            width: 1.0,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (label != null)
            LabelWidget(
              title: label!,
              textColor: Colors.grey,
              fontSize: DeviceSize.fontSize(12, 14),
            ),
          Row(
            children: <Widget>[
              if (leadingIcon != null) Icon(leadingIcon, color: Colors.grey),
              Expanded(
                child: TextFormField(
                  minLines: minLines,
                  maxLines: maxLines ?? 1,
                  maxLength: maxLength,
                  inputFormatters: inputFormatters,
                  maxLengthEnforcement:
                      maxLengthEnforcement ?? MaxLengthEnforcement.none,
                  controller: controller,
                  obscureText: isPassword ?? false,
                  readOnly: readOnly ?? false,
                  decoration: InputDecoration(
                    fillColor: Colors.transparent,
                    contentPadding: const EdgeInsets.only(left: 10.0),
                    labelText: labelText,
                    hintText: hint,
                    hintStyle: const TextStyle(
                      color: Colors.grey,
                    ),
                    border: InputBorder.none,
                  ),
                  onChanged: onChanged,
                  keyboardType: keyboardType ?? TextInputType.text,
                  style: TextStyle(
                    color:
                        readOnly != true ? Colors.black : Colors.grey.shade400,
                  ),
                ),
              ),
              if (trailingIcon != null)
                CustomInkWell(
                  onTap: trailingTap,
                  child: Icon(trailingIcon, color: Colors.grey),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
