class Destino {
  String? bairro;
  String? cep;
  String? cidade;
  int? idCidade;
  String? complemento;
  String? dataAlteracao;
  String? dataInclusao;
  int? idOrigemDestino;
  bool? isDeleted;
  double? kMRodado;
  double? kMSugerido;
  String? latitude;
  String? logradouro;
  String? longitude;
  String? numero;
  String? statusEndereco;
  String? descricaoEnderecoStatus;
  int? idEnderecoStatus;
  String? uF;

  Destino(
      {this.bairro,
      this.cep,
      this.cidade,
      this.idCidade,
      this.complemento,
      this.dataAlteracao,
      this.dataInclusao,
      this.idOrigemDestino,
      this.isDeleted,
      this.kMRodado,
      this.kMSugerido,
      this.latitude,
      this.logradouro,
      this.longitude,
      this.numero,
      this.statusEndereco,
      this.descricaoEnderecoStatus,
      this.idEnderecoStatus,
      this.uF});

  Destino.fromJson(Map<String, dynamic> json) {
    bairro = json['Bairro'];
    cep = json['Cep'];
    cidade = json['Cidade'];
    idCidade = json['IdCidade'];
    complemento = json['Complemento'];
    dataAlteracao = json['DataAlteracao'];
    dataInclusao = json['DataInclusao'];
    idOrigemDestino = json['IdOrigemDestino'];
    isDeleted = json['IsDeleted'];
    kMRodado = json['KMRodado'];
    kMSugerido = json['KMSugerido'];
    latitude = json['Latitude'];
    logradouro = json['Logradouro'];
    longitude = json['Longitude'];
    numero = json['Numero'];
    statusEndereco = json['StatusEndereco'];
    descricaoEnderecoStatus = json['DescricaoEnderecoStatus'];
    idEnderecoStatus = json['IdEnderecoStatus'];
    uF = json['UF'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Bairro'] = bairro;
    data['Cep'] = cep;
    data['Cidade'] = cidade;
    data['IdCidade'] = idCidade;
    data['Complemento'] = complemento;
    data['DataAlteracao'] = dataAlteracao;
    data['DataInclusao'] = dataInclusao;
    data['IdOrigemDestino'] = idOrigemDestino;
    data['IsDeleted'] = isDeleted;
    data['KMRodado'] = kMRodado;
    data['KMSugerido'] = kMSugerido;
    data['Latitude'] = latitude;
    data['Logradouro'] = logradouro;
    data['Longitude'] = longitude;
    data['Numero'] = numero;
    data['StatusEndereco'] = statusEndereco;
    data['DescricaoEnderecoStatus'] = descricaoEnderecoStatus;
    data['IdEnderecoStatus'] = idEnderecoStatus;
    data['UF'] = uF;
    return data;
  }
}
