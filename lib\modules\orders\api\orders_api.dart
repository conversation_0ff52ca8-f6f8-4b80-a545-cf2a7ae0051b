import 'dart:convert';
import 'dart:developer';

import 'package:pharmalink/app/controller/app_controller.dart';
import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/core/models/result_error_model.dart';
import 'package:pharmalink/core/utils/constants.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/orders/models/order_attachment_file_request_model.dart';
import 'package:pharmalink/modules/orders/models/orders_mix_product_special_request.dart';
import 'package:pharmalink/modules/orders/models/orders_mix_product_special_response.dart';
import 'package:pharmalink/modules/orders/models/orders_mix_produto_request_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';
import 'package:pharmalink/modules/orders/models/orders_tabloid_products_request_model.dart';
import 'package:pharmalink/modules/orders/models/orders_tabloid_products_response_model.dart';
import 'package:pharmalink/modules/orders/models/orders_tabloid_update_response.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_retrieve_model.dart';

abstract class IOrdersApi {
  Future<HttpResult<ProductsMixModel>> getMixProducts(
      {required MixProdutoRequest model});

  Future<HttpResult<TabloidProductsResponse>> getTabloidProducts(
      {required TabloidProductsRequest model});

  Future<HttpResponse<bool>> syncOrders({required OrdersDataModel model});

  Future<HttpResponse<List<OrdersDataRetrieveModel>>> syncOrdersRetrieve(
      {required OrdersDataModel model});

  Future<HttpResponse<GetMixSpecialProductsResponse>> getTabloidProductsSpecial(
      {required GetMixSpecialProductsRequest model});

  Future<HttpResponse<OrdersTabloidUpdateResponse>> getTabloidUpdate(
      {required int tablodId, required int storeId});

  Future<HttpResponse<String>> sendFileOrders(
      {required OrderAttachmentFileRequestModel model});
}

class OrdersApi extends IOrdersApi {
  final HttpManager _httpManager;
  OrdersApi(this._httpManager);

  @override
  Future<HttpResult<ProductsMixModel>> getMixProducts(
      {required MixProdutoRequest model}) async {
    final result = await _httpManager.restRequest(
        url: 'produtos/obterMixProdutos',
        method: HttpMethods.post,
        body: model.toJson());

    if (result.data.isNotEmpty && result.statusCode!.isStatusOk()) {
      return HttpResult.sucess(ProductsMixModel.fromJson(result.data));
    } else {
      return HttpResult.error(getOthersStatusCodes(result));
    }
  }

  @override
  Future<HttpResult<TabloidProductsResponse>> getTabloidProducts(
      {required TabloidProductsRequest model}) async {
    log(jsonEncode(model));
    final result = await _httpManager.restRequest(
        url: 'lojas/obterTabloide',
        method: HttpMethods.post,
        body: model.toJson());

    if (result.data.isNotEmpty && result.statusCode!.isStatusOk()) {
      return HttpResult.sucess(TabloidProductsResponse.fromJson(result.data));
    } else {
      return HttpResult.error(getOthersStatusCodes(result));
    }
  }

  @override
  Future<HttpResponse<GetMixSpecialProductsResponse>> getTabloidProductsSpecial(
      {required GetMixSpecialProductsRequest model}) async {
    return await _httpManager.request<GetMixSpecialProductsResponse>(
      path: 'produtos/obterMixProdutosEspecial',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        return GetMixSpecialProductsResponse.fromJson(data);
      },
    );
  }

  @override
  Future<HttpResponse<OrdersTabloidUpdateResponse>> getTabloidUpdate(
      {required int tablodId, required int storeId}) async {
    return await _httpManager
        .requestFull<OrdersTabloidUpdateResponse, ResultErrorModel>(
      path: 'tabloides/buscarTabloideAtualizado/$tablodId/$storeId',
      method: HttpMethods.get,
      parser: (data) {
        return OrdersTabloidUpdateResponse.fromJson(data);
      },
      parserError: (dynamic errorData) {
        return ResultErrorModel.fromJson(errorData);
      },
    );
  }

  @override
  Future<HttpResponse<bool>> syncOrders(
      {required OrdersDataModel model}) async {
    String versionApi = model.authorizationWithToken == null ? "v1" : "v2";

    if (appController.currentEnvironment == Environment.production) {
      versionApi = "";
    }

    log(jsonEncode(model));

    return await _httpManager.request<bool>(
      path: '',
      baseUrl:
          "${appController.apiUrlAzure}$versionApi/pedidos/registrar-multiplos",
      method: HttpMethods.post,
      body: model.toJsonApi(),
      parser: (data) {
        return true;
      },
    );
  }

  @override
  Future<HttpResponse<List<OrdersDataRetrieveModel>>> syncOrdersRetrieve(
      {required OrdersDataModel model}) async {
    String versionApi = "v1";

    if (appController.currentEnvironment == Environment.production) {
      versionApi = "";
    }

    return await _httpManager.request<List<OrdersDataRetrieveModel>>(
      path: '',
      baseUrl: "${appController.apiUrlAzure}$versionApi/pedidos/buscar-status",
      method: HttpMethods.post,
      body: {
        "orderId": model.orderId,
      },
      parser: (data) {
        if (data is List) {
          return data
              .map((item) => OrdersDataRetrieveModel.fromJson(item))
              .toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }

  @override
  Future<HttpResponse<String>> sendFileOrders(
      {required OrderAttachmentFileRequestModel model}) async {
    log(jsonEncode(model));
    return await _httpManager.request<String>(
      path: 'anexoPedido/Salvar',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        return data;
      },
    );
  }
}
