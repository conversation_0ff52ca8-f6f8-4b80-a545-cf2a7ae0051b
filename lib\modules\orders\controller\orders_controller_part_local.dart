import 'dart:developer';

import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/enuns/product_state_enum.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/orders/controller/orders_controller.dart';
import 'package:pharmalink/modules/orders/models/orders_local_data_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';
import 'package:pharmalink/modules/orders/models/orders_tabloid_products_response_model.dart';
import 'package:pharmalink/modules/stores_parameters/enuns/type_order_enum.dart';

class OrdersControllerPartLocal {
  static Future<void> loadOrderLocal(OrdersController ctrl) async {
    var (leaveAction, _) = ctrl.dynatraceAction.subActionReport(
      "loadOrderLocal",
    );
    try {
      if (!ctrl.isEditOrder) {
        if (globalParams.order.orderLocal != null &&
            globalParams.order.orderLocal!.productListFull != null) {
          for (var product in globalParams.order.orderLocal!.productListFull!) {
            final targetProduct =
                ctrl.productListFull
                    .where(
                      (p) =>
                          p.productId == product.productId &&
                          p.productDunId == product.productDunId,
                    )
                    .firstOrNull;
            if (targetProduct != null &&
                targetProduct.states != ProductStateEnum.block) {
              targetProduct.discountAdditional =
                  product.discountAdditional ?? 0;
              targetProduct.discountChange = product.discountChange ?? 0;
              if (globalParams.order.orderLocal!.parameters!.typeOrderId ==
                  TyperOrderEnum.especial) {
                targetProduct.discountChangeController!.text =
                    targetProduct.discountChange?.formatarDouble() ?? "0";
              } else {
                targetProduct.discountChangeController!.text =
                    targetProduct.discountAdditional?.formatarDouble() ?? "0";
              }

              await ctrl.setQtdyByLoading(targetProduct, product.qtdy!);
            }
          }

          for (var item in ctrl.productListFull) {
            await ctrl.calculateDiscountByItem(item);
            ctrl.setFiltersItens(item);
          }
          ctrl.updateFooter();

          if (globalParams.getProducts() != null &&
              globalParams.getProducts()!.produtos!.combosOferta != null &&
              globalParams.getProducts()!.produtos!.combosOferta!.isNotEmpty &&
              globalParams.order.orderLocal!.combosOferta != null) {
            for (var product in globalParams.order.orderLocal!.combosOferta!) {
              final targetProduct =
                  globalParams
                      .getProducts()!
                      .produtos!
                      .combosOferta!
                      .where((p) => p.idComboOferta == product.idComboOferta)
                      .firstOrNull;
              if (targetProduct != null &&
                  targetProduct.states != ProductStateEnum.block) {
                await setQtdyCombo(targetProduct, product.qtdy!);
              }
            }
          }

          if (ctrl.productsTabloid != null &&
              ctrl.productsTabloid!.combosOferta != null &&
              ctrl.productsTabloid!.combosOferta!.isNotEmpty) {
            for (var product in globalParams.order.orderLocal!.combosOferta!) {
              final targetProduct =
                  ctrl.productsTabloid!.combosOferta!
                      .where((p) => p.idComboOferta == product.idComboOferta)
                      .firstOrNull;
              if (targetProduct != null &&
                  targetProduct.states != ProductStateEnum.block) {
                await setQtdyComboEspecial(targetProduct, product.qtdy!);
              }
            }
          }
        }
      }
    } finally {
      leaveAction();
    }
  }

  static Future<void> setQtdyCombo(CombosOfertaModel item, int value) async {
    item.qtdy = value;

    item.qtdyReal =
        item.produtos!.isNotEmpty
            ? item.produtos!
                .map((product) {
                  if (product.idProdutoDUN != null &&
                      product.quantidadeDUN != null &&
                      product.quantidadeDUN! > 0) {
                    return product.quantidade! *
                        item.qtdy! *
                        product.quantidadeDUN!;
                  } else {
                    return product.quantidade! * item.qtdy!;
                  }
                })
                .reduce((a, b) => a + b)
            : 0;

    item.qtdyController ??= TextEditingController();
    item.qtdyController!.text = item.qtdy.toString();
    item.priceOrder = item.precoCombo! * item.qtdy!;
    item.totalOrder = item.precoComboLiquido! * item.qtdy!;
  }

  static Future<void> setQtdyComboEspecial(
    TabloidProductsCombosOfertaResponse item,
    int value,
  ) async {
    item.qtdy = value;

    item.priceOrder = item.precoCombo! * item.qtdy!;
    item.totalOrder = item.precoComboLiquido! * item.qtdy!;
  }

  static Future<void> saveOrderLocal(OrdersController ctrl) async {
    var (leaveAction, _) = ctrl.dynatraceAction.subActionReport(
      "saveOrderLocal",
    );
    try {
      if (!ctrl.isEditOrder && !globalParams.order.orderLocalFinish) {
        globalParams.order.orderLocal = OrdersLocalDataModel(
          storeId: globalParams.getCurrentStore()!.idLoja!,
          productListFull:
              ctrl.productListFull
                  .where((element) => element.qtdy! > 0)
                  .toList(),
          parameters: globalParams.order.orderParameters,
          combosOferta: globalParams.order.orderLocal?.combosOferta,
          clientNumber: globalParams.order.orderLocal?.clientNumber,
          observation: globalParams.order.orderLocal?.observation,
        );
        await globalParams.order.orderLocal!.saveOrder(
          storeId: globalParams.getCurrentStore()!.idLoja!,
          typeOrder: globalParams.order.orderParameters.typeOrderId!,
          hashCode:
              globalParams.order.orderParameters.typeOrderId! ==
                      TyperOrderEnum.especial
                  ? "2:${ordersTabloidController.tabloidId}"
                  : null,
        );
      }
    } catch (e) {
      log(e.toString());
    } finally {
      leaveAction();
    }
  }

  static Future<void> deleteOrder(OrdersController ctrl) async {
    var (leaveAction, subAction) = ctrl.dynatraceAction.subActionReport(
      "deleteOrder",
    );
    try {
      if (ctrl.isEditOrder) {
        await dbContext.deleteByKey(
          hashCode: ctrl.currendEditOrderId,
          workspaceId: appController.workspace?.workspaceId,
          storeId: ctrl.syncOrderEdit!.payLoad!.pdvId,
          userId: appController.userLogged!.userId,
          key: DatabaseModels.syncronization,
        );
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }
  }
}
