import 'package:pharmalink/exports/basic_exports.dart';

class ResearchesComplementarySendModel {
  int? idPesquisa;
  int? idRota;
  List<ResearchesComplementarySendRespostas>? respostas;

  ResearchesComplementarySendModel(
      {this.idPesquisa, this.idRota, this.respostas});

  ResearchesComplementarySendModel.fromJson(Map<String, dynamic> json) {
    idPesquisa = json['idPesquisa'];
    idRota = json['idRota'];
    if (json['respostas'] != null) {
      respostas = <ResearchesComplementarySendRespostas>[];
      json['respostas'].forEach((v) {
        respostas!.add(ResearchesComplementarySendRespostas.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idPesquisa'] = idPesquisa;
    data['idRota'] = idRota;
    if (respostas != null) {
      data['respostas'] = respostas!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResearchesComplementarySendRespostas {
  int? idPergunta;
  int? tipo;
  List<ResearchesComplementarySendRespostasUsuario>? respostasUsuario;

  ResearchesComplementarySendRespostas(
      {this.idPergunta, this.tipo, this.respostasUsuario});

  ResearchesComplementarySendRespostas.fromJson(Map<String, dynamic> json) {
    idPergunta = json['idPergunta'];
    tipo = json['tipo'];
    if (json['respostasUsuario'] != null) {
      respostasUsuario = <ResearchesComplementarySendRespostasUsuario>[];
      json['respostasUsuario'].forEach((v) {
        respostasUsuario!
            .add(ResearchesComplementarySendRespostasUsuario.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idPergunta'] = idPergunta;
    data['tipo'] = tipo;
    if (respostasUsuario != null) {
      data['respostasUsuario'] =
          respostasUsuario!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResearchesComplementarySendRespostasUsuario {
  int? idResposta;
  String? extensaoImagem;
  String? resposta;
  Orientation? orientation;

  ResearchesComplementarySendRespostasUsuario({
    this.idResposta,
    this.extensaoImagem,
    this.resposta,
    this.orientation,
  });

  ResearchesComplementarySendRespostasUsuario.fromJson(
      Map<String, dynamic> json) {
    idResposta = json['idResposta'];
    extensaoImagem = json['extensaoImagem'];
    resposta = json['resposta'];
    orientation = json['Orientation'] != null
        ? Orientation.values.firstWhere(
            (element) => element.name == json['Orientation'],
            orElse: () => Orientation.portrait)
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idResposta'] = idResposta;
    data['extensaoImagem'] = extensaoImagem;
    data['resposta'] = resposta;
    data['Orientation'] = orientation?.name;
    return data;
  }
}
