// ignore: file_names
import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class SystemParameterizationModel {
  SystemParameterizationTipoClienteModel? tipoCliente;
  SystemParameterizationProgramaLancamentoDescricaoModel?
      programaLancamentoDescricao;
  SystemParameterizationProjetoModel? projeto;
  List<SystemParameterizationPermissoesAcessoModel>? permissoesAcesso;

  SystemParameterizationModel({
    this.tipoCliente,
    this.programaLancamentoDescricao,
    this.projeto,
    this.permissoesAcesso,
  });

  SystemParameterizationModel.fromJson(Map<String, dynamic> json) {
    tipoCliente = json['TipoCliente'] != null
        ? SystemParameterizationTipoClienteModel.fromJson(json['TipoCliente'])
        : null;
    programaLancamentoDescricao = json['ProgramaLancamentoDescricao'] != null
        ? SystemParameterizationProgramaLancamentoDescricaoModel.fromJson(
            json['ProgramaLancamentoDescricao'])
        : null;
    projeto = json['Projeto'] != null
        ? SystemParameterizationProjetoModel.fromJson(json['Projeto'])
        : null;
    if (json['permissoesAcesso'] != null) {
      permissoesAcesso = <SystemParameterizationPermissoesAcessoModel>[];
      json['permissoesAcesso'].forEach((v) {
        permissoesAcesso!
            .add(SystemParameterizationPermissoesAcessoModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (tipoCliente != null) {
      data['TipoCliente'] = tipoCliente!.toJson();
    }
    if (programaLancamentoDescricao != null) {
      data['ProgramaLancamentoDescricao'] =
          programaLancamentoDescricao!.toJson();
    }
    if (projeto != null) {
      data['Projeto'] = projeto!.toJson();
    }
    if (permissoesAcesso != null) {
      data['permissoesAcesso'] =
          permissoesAcesso!.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

class SystemParameterizationTipoClienteModel {
  String? descricao;
  String? artigo;
  String? plural;

  SystemParameterizationTipoClienteModel(
      {this.descricao, this.artigo, this.plural});

  SystemParameterizationTipoClienteModel.fromJson(Map<String, dynamic> json) {
    descricao = json['Descricao'];
    artigo = json['Artigo'];
    plural = json['Plural'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Descricao'] = descricao;
    data['Artigo'] = artigo;
    data['Plural'] = plural;
    return data;
  }
}

class SystemParameterizationProgramaLancamentoDescricaoModel {
  String? descricao;

  SystemParameterizationProgramaLancamentoDescricaoModel({this.descricao});

  SystemParameterizationProgramaLancamentoDescricaoModel.fromJson(
      Map<String, dynamic> json) {
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Descricao'] = descricao;
    return data;
  }
}

class SystemParameterizationProjetoModel {
  int? idProjeto;
  String? nome;
  String? descricao;
  String? observacao;
  String? dataInicio;
  String? dataFim;
  String? dataAprovacao;
  int? numeroSupervisores;
  int? numeroPromotores;
  int? numeroPdv;
  int? numeroCoordenadores;
  bool? calculoMes;
  bool? sistematicaCiclo;
  double? valorKM;
  bool? isDeleted;
  String? dataInclusao;
  String? dataAlteracao;
  bool? ativado;
  String? proprietario;
  bool? requerAprovacaoPlanejamentoVisita;

  SystemParameterizationProjetoModel(
      {this.idProjeto,
      this.nome,
      this.descricao,
      this.observacao,
      this.dataInicio,
      this.dataFim,
      this.dataAprovacao,
      this.numeroSupervisores,
      this.numeroPromotores,
      this.numeroPdv,
      this.numeroCoordenadores,
      this.calculoMes,
      this.sistematicaCiclo,
      this.valorKM,
      this.isDeleted,
      this.dataInclusao,
      this.dataAlteracao,
      this.ativado,
      this.proprietario,
      this.requerAprovacaoPlanejamentoVisita});

  SystemParameterizationProjetoModel.fromJson(Map<String, dynamic> json) {
    idProjeto = json['IdProjeto'];
    nome = json['Nome'];
    descricao = json['Descricao'];
    observacao = json['Observacao'];
    dataInicio = json['DataInicio'];
    dataFim = json['DataFim'];
    dataAprovacao = json['DataAprovacao'];
    numeroSupervisores = json['NumeroSupervisores'];
    numeroPromotores = json['NumeroPromotores'];
    numeroPdv = json['NumeroPdv'];
    numeroCoordenadores = json['NumeroCoordenadores'];
    calculoMes = json['CalculoMes'];
    sistematicaCiclo = json['SistematicaCiclo'];
    valorKM = json['ValorKM'];
    isDeleted = json['IsDeleted'];
    dataInclusao = json['DataInclusao'];
    dataAlteracao = json['DataAlteracao'];
    ativado = json['Ativado'];
    proprietario = json['Proprietario'];
    requerAprovacaoPlanejamentoVisita =
        json['RequerAprovacaoPlanejamentoVisita'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdProjeto'] = idProjeto;
    data['Nome'] = nome;
    data['Descricao'] = descricao;
    data['Observacao'] = observacao;
    data['DataInicio'] = dataInicio;
    data['DataFim'] = dataFim;
    data['DataAprovacao'] = dataAprovacao;
    data['NumeroSupervisores'] = numeroSupervisores;
    data['NumeroPromotores'] = numeroPromotores;
    data['NumeroPdv'] = numeroPdv;
    data['NumeroCoordenadores'] = numeroCoordenadores;
    data['CalculoMes'] = calculoMes;
    data['SistematicaCiclo'] = sistematicaCiclo;
    data['ValorKM'] = valorKM;
    data['IsDeleted'] = isDeleted;
    data['DataInclusao'] = dataInclusao;
    data['DataAlteracao'] = dataAlteracao;
    data['Ativado'] = ativado;
    data['Proprietario'] = proprietario;
    data['RequerAprovacaoPlanejamentoVisita'] =
        requerAprovacaoPlanejamentoVisita;
    return data;
  }
}

class SystemParameterizationPermissoesAcessoModel
    extends SqfLiteBase<SystemParameterizationPermissoesAcessoModel> {
  String? diretorio;
  String? codigo;
  bool? visualizar;
  bool? editar;
  bool? excluir;
  bool? incluir;

  SystemParameterizationPermissoesAcessoModel({
    this.diretorio,
    this.codigo,
    this.visualizar,
    this.editar,
    this.excluir,
    this.incluir,
  }) : super(DatabaseModels.systemParameterizationPermissoesAcessoModel);

  SystemParameterizationPermissoesAcessoModel.fromJson(
      Map<String, dynamic> json)
      : super(DatabaseModels.systemParameterizationPermissoesAcessoModel) {
    diretorio = json['Diretorio'];
    codigo = json['Codigo'];
    visualizar = json['Visualizar'];
    editar = json['Editar'];
    excluir = json['Excluir'];
    incluir = json['Incluir'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Diretorio'] = diretorio;
    data['Codigo'] = codigo;
    data['Visualizar'] = visualizar;
    data['Editar'] = editar;
    data['Excluir'] = excluir;
    data['Incluir'] = incluir;
    return data;
  }

  Future<SystemParameterizationPermissoesAcessoModel> getFirst() async {
    var list = await getAll<SystemParameterizationPermissoesAcessoModel>(
        workspaceId: appController.workspace!.workspaceId!,
        userId: appController.userLogged!.userId!,
        SystemParameterizationPermissoesAcessoModel.fromJson);
    return list.first;
  }

  Future<List<SystemParameterizationPermissoesAcessoModel>> getList() async {
    var list = await getAll<SystemParameterizationPermissoesAcessoModel>(
        workspaceId: appController.workspace!.workspaceId!,
        userId: appController.userLogged!.userId!,
        SystemParameterizationPermissoesAcessoModel.fromJson);
    return list;
  }
}
