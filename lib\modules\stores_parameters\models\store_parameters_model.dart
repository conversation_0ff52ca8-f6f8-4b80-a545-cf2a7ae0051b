import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/modules/stores_parameters/models/parametrizacao_model.dart';

class StoreParametersModel extends SqfLiteBase<StoreParametersModel> {
  String? userId;
  int? idLoja;
  String? tipoPedido;

  ParametrizacaoModel? parametrizacao;

  StoreParametersModel({
    this.userId,
    this.idLoja,
    this.tipoPedido,
    this.parametrizacao,
  }) : super(DatabaseModels.storeParametersModel);

  StoreParametersModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.storeParametersModel) {
    userId = json['userId'] ?? json['UserId'];
    idLoja = json['idLoja'] ?? json['IdLoja'];
    tipoPedido = json['tipoPedido'] ?? json['TipoPedido'];

    parametrizacao = json['parametrizacao'] != null
        ? ParametrizacaoModel.fromJson(json['parametrizacao'])
        : json['Parametrizacao'] != null
            ? ParametrizacaoModel.fromJson(json['Parametrizacao'])
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['userId'] = userId;
    data['idLoja'] = idLoja;
    data['tipoPedido'] = tipoPedido;

    if (parametrizacao != null) {
      data['parametrizacao'] = parametrizacao!.toJson();
    }

    return data;
  }

  Future<StoreParametersModel> getFirst(int workspaceId) async {
    var list = await getAll<StoreParametersModel>(
        workspaceId: workspaceId,
        userId: appController.userLogged!.userId,
        StoreParametersModel.fromJson);
    return list.first;
  }

  Future<bool> exists(int workspaceId) async {
    var list = await getAll<StoreParametersModel>(
        workspaceId: workspaceId,
        userId: appController.userLogged!.userId,
        StoreParametersModel.fromJson);
    return list.isNotEmpty;
  }

  Future<List<StoreParametersModel>> getList(
      int workspaceId, int? storeId, String? typeOrder, bool? isOnline) async {
    var list = await getAll<StoreParametersModel>(
      workspaceId: workspaceId,
      storeId: storeId,
      userId: appController.userLogged!.userId,
      hashCode: typeOrder,
      isOnline: isOnline,
      StoreParametersModel.fromJson,
    );
    return list;
  }
}
