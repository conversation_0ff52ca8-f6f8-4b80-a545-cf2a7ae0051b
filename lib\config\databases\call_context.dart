import 'dart:async';

import 'package:pharmalink/modules/log_trace_monitor/models/log_trace_monitor_model.dart';

class CallContext {
  final String controllerName;
  final String methodName;

  CallContext(this.controllerName, this.methodName);

  static CallContext? current() {
    return Zone.current[#callContext] as CallContext?;
  }

  static R run<R>(CallContext context, R Function() body) {
    return runZoned(
      body,
      zoneValues: {#callContext: context},
    );
  }
}

mixin TraceableController {
  R trace<R>(String methodName, R Function() body) {
    return CallContext.run(
      CallContext(runtimeType.toString(), methodName),
      body,
    );
  }
}

class DBLogger {
  static void log(String type, {String? textData, String? jsonData}) {
    final context = CallContext.current();
    if (context != null) {
      try {
        LogTraceMonitorModel(
          controllerName: context.controllerName,
          methodName: context.methodName,
          type: type,
          jsonData: jsonData,
          textData: textData,
          createAt: DateTime.now(),
        ).logger();
      } catch (e) {
        log(e.toString());
      }
    }
  }

  static void logger(String type, {String? textData, String? jsonData}) {
    try {
      LogTraceMonitorModel(
        controllerName: null,
        methodName: null,
        type: type,
        jsonData: jsonData,
        textData: textData,
        createAt: DateTime.now(),
      ).logger();
    } catch (e) {
      log(e.toString());
    }
  }
}
