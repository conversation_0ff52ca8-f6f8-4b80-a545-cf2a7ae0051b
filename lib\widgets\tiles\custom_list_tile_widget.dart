import 'package:flutter/material.dart';

class CustomListTile extends StatefulWidget {
  final Widget leading;
  final Widget title;
  final Widget trailing;
  final Future<void> Function() onTap;

  const CustomListTile({
    super.key,
    required this.leading,
    required this.title,
    required this.trailing,
    required this.onTap,
  });

  @override
  CustomListTileState createState() => CustomListTileState();
}

class CustomListTileState extends State<CustomListTile> {
  bool _isProcessing = false;

  Future<void> _handleTap() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      await widget.onTap();
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: widget.leading,
      title: widget.title,
      trailing: _isProcessing
          ? const SizedBox(
              width: 24,
              height: 24,
              child: CircularProgressIndicator(
                strokeWidth: 2,
              ),
            )
          : widget.trailing,
      onTap: _handleTap,
      enabled: !_isProcessing,
    );
  }
}
