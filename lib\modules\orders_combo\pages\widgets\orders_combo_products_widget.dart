import 'package:cached_network_image/cached_network_image.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';

class OrdersComboProductsWidget extends StatelessWidget {
  const OrdersComboProductsWidget({
    super.key,
    required this.data,
    required this.comboQtdy,
  });

  final ProdutosOfertasModel data;
  final int comboQtdy;
  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<OrdersComboController>("Orders Combo",
        builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          children: [
            Row(
              children: [
                SizedBox(
                  width: 60,
                  child: CachedNetworkImage(
                      imageUrl: AppImages.productGeneric,
                      placeholder: (context, url) => Image.asset(
                            AppImages.productGeneric,
                          ),
                      errorWidget: (context, url, error) => Image.asset(
                            AppImages.productGeneric,
                          )),
                ),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      LabelWidget(
                        title: data.descricao ?? "-",
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                      ),
                      5.toHeightSpace(),
                      LabelWidget(
                        title: data.idProdutoDUN! > 0
                            ? "DUN: ${data.dUN!}"
                            : "EAN: ${data.ean!}",
                        fontSize: 12.sp,
                        textColor: Colors.grey.shade600,
                      ),
                    ],
                  ),
                )
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(10.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TilesInfoWidget(
                    title: "Qtd",
                    value: ((data.quantidade ?? 0) * comboQtdy).toString(),
                    titleFontSize: 10.sp,
                    valueFontSize: 12.sp,
                  ),
                  TilesInfoWidget(
                    title: "Desc. disp.",
                    value: data.desconto?.formatPercent() ?? "0,00",
                    titleFontSize: 10.sp,
                    valueFontSize: 12.sp,
                  ),
                  TilesInfoWidget(
                    title: "De",
                    value: ordersComboController
                        .getComboProductPrice(data, comboQtdy, false)
                        .formatReal(),
                    titleFontSize: 10.sp,
                    valueFontSize: 12.sp,
                    isRitchText: true,
                  ),
                  TilesInfoWidget(
                    title: "Por",
                    value: ordersComboController
                        .getComboProductPrice(data, comboQtdy, true)
                        .formatReal(),
                    titleFontSize: 10.sp,
                    valueFontSize: 12.sp,
                  ),
                ],
              ),
            ),
            const Divider(
              thickness: 1.5,
            ),
          ],
        ),
      );
    });
  }
}
