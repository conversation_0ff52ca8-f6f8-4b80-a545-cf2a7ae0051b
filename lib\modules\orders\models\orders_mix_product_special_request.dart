class GetMixSpecialProductsRequest {
  int? idPrazoPagamento;
  List<int>? idsDistribuidores;
  List<int>? idsProdutos;
  List<int>? idsProdutosDUN;

  GetMixSpecialProductsRequest(
      {this.idPrazoPagamento,
      this.idsDistribuidores,
      this.idsProdutos,
      this.idsProdutosDUN});

  GetMixSpecialProductsRequest.fromJson(Map<String, dynamic> json) {
    idPrazoPagamento = json['IdPrazoPagamento'];
    idsDistribuidores = json['IdsDistribuidores'].cast<int>();
    idsProdutos = json['IdsProdutos'].cast<int>();
    idsProdutosDUN = json['idsProdutosDUN'].cast<int>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPrazoPagamento'] = idPrazoPagamento;
    data['IdsDistribuidores'] = idsDistribuidores;
    data['IdsProdutos'] = idsProdutos;
    data['idsProdutosDUN'] = idsProdutosDUN;
    return data;
  }
}
