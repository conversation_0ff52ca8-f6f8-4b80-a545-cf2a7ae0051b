import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/report_contents/models/report_contents_model.dart';

abstract class IReportContentsApi {
  Future<HttpResponse<List<ReportContentsModel>>> getReportContents();
}

class ReportContentsApi extends IReportContentsApi {
  final HttpManager _httpManager;
  ReportContentsApi(this._httpManager);

  @override
  Future<HttpResponse<List<ReportContentsModel>>> getReportContents() async {
    return await _httpManager.request<List<ReportContentsModel>>(
      path:
          'file/merchandising/getSyncByUser/${appController.userLogged!.perfilId}',
      method: HttpMethods.get,
      parser: (data) {
        if (data is List) {
          return data
              .map((item) => ReportContentsModel.fromJson(item))
              .toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }
}
