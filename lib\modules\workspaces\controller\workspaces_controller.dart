import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/workspaces/models/workspaces_model.dart';

class WorkspacesController
    extends GetxControllerInstrumentado<WorkspacesController> {
  WorkspacesController();

  WorkspacesModel? selected;
  List<WorkspacesModel> dataList = [];
  TextEditingController controllerSearch = TextEditingController();
  String search = '';

  Future<void> setWorkspace(WorkspacesModel item) async {
    selected = item;
    appController.workspace = item;
    await dbContext.withControllerAction(this).addData(
        key: DatabaseModels.currentWorkspace,
        data: item,
        clearCurrentData: true);
    loginController.reset();
    Get.toNamed(RoutesPath.login);
  }

  void setDataList(List<WorkspacesModel> data) {
    dataList = data;
    dataList.sort((a, b) => a.name!.compareTo(b.name!));
  }

  void setSearch(String value) {
    search = value;
    update();
  }

  Future<void> refreshData() async {
    final loading = PlkLoading();
    loading.show(title: AppStrings.refresh);

    search = '';
    controllerSearch.text = '';
    try {
      await dbContext.deleteByKey(key: DatabaseModels.workspacesModel);
      final result = await workspacesApi.getWorkspaces();
      result.when(
        sucess: (data) async {
          await dbContext.withControllerAction(this).addData(
                key: DatabaseModels.workspacesModel,
                data: data,
                clearCurrentData: true,
              );
          setDataList(data);
          loading.hide();
          update();
        },
        error: (error) {
          loading.hide();
          SnackbarCustom.snackbarError(error.error);
        },
      );
    } catch (e) {
      loading.hide();
      SnackbarCustom.snackbarError(e.toString());
      rethrow;
    }
  }
}
