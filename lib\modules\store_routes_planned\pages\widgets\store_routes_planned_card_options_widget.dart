import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';

class StoreRoutesPlannedCardOptionsWidget extends StatelessWidget {
  const StoreRoutesPlannedCardOptionsWidget({super.key, required this.data});
  final StoresModel data;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StoreRoutesPlannedController>(builder: (ctrl) {
      return data.dataExtra!.offlineStatus != 4
          ? Row(
              children: [
                Expanded(
                  child: _buildAgendarVisitaCheckbox(ctrl),
                ),
                //Se o parametro de sincronia offline está ativo, exibir o checkbox
                if (storeRoutesController.hasSyncOffline)
                  Expanded(
                    child: _buildOfflineOrderCheckbox(ctrl),
                  ),
              ],
            )
          : Row(children: [
              Flexible(
                child: PrimaryButtonWidget(
                  // ignore: unrelated_type_equality_checks
                  titleButtom: appController.deviceType == DeviceType.mobile
                      ? "Remover\nda Lista"
                      : "Remover da Lista",
                  borderColor: themesController.getColorButton(),
                  buttonColor: Colors.white,
                  titleColor: themesController.getColorButton(),
                  textPadding: const EdgeInsets.symmetric(vertical: 4),
                  textFontWeight: FontWeight.bold,
                  onTap: () async {
                    await ctrl.removeStoreFromRoute(data, onError: true);
                  },
                ),
              ),
              const Gap(10),
              Flexible(
                child: PrimaryButtonWidget(
                  // ignore: unrelated_type_equality_checks
                  titleButtom: appController.deviceType == DeviceType.mobile
                      ? "Sincronizar\nnovamente"
                      : "Sincronizar novamente",
                  textPadding: const EdgeInsets.symmetric(vertical: 4),
                  textFontWeight: FontWeight.bold,
                  onTap: () async {
                    await storeRoutesController
                        .updateStoreCommercialConditions(data);
                  },
                ),
              ),
            ]);
    });
  }

  Widget _buildOfflineOrderCheckbox(StoreRoutesPlannedController ctrl) {
    if (data.dataExtra!.offlineDateSync == null &&
        data.dataExtra!.offlineStatus != 4) {
      return CheckboxListTile(
        title: LabelWidget(
          title: 'Pedido Offline',
          fontSize: DeviceSize.fontSize(16, 20),
          textColor: Colors.grey.shade900,
        ),
        value: data.dataExtra!.canOrderOfflineSync ?? false,
        onChanged: (bool? value) async {
          await ctrl.setCanOrderOfflineSync(data, value);
        },
        controlAffinity: ListTileControlAffinity.leading,
        contentPadding: EdgeInsets.zero,
      );
    } else if (data.dataExtra!.offlineDateSync != null &&
        (data.dataExtra!.offlineStatus == 1 ||
            data.dataExtra!.offlineStatus == 2)) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const SyncLoading(),
          const Gap(10),
          Flexible(
            child: LabelWidget(
              title: "Sincronizando...",
              fontSize: DeviceSize.fontSize(16, 20),
              textColor: Colors.grey,
            ),
          ),
        ],
      );
    } else if (data.dataExtra!.offlineDateSync != null &&
        data.dataExtra!.offlineStatus == 3) {
      //sucesso
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(FontAwesomeIcons.checkDouble,
              color: themesController.getBackgroundColor()),
          const Gap(10),
          Flexible(
            child: LabelWidget(
              // ignore: unrelated_type_equality_checks
              title: appController.deviceType == DeviceType.mobile
                  ? "Condições\ndisponíveis"
                  : "Condições disponíveis",
              fontSize: DeviceSize.fontSize(16, 20),
              textColor: themesController.getBackgroundColor(),
            ),
          ),
        ],
      );
    }
    return Container();
  }

  Widget _buildAgendarVisitaCheckbox(StoreRoutesPlannedController ctrl) {
    if (data.visita == false && !data.dataExtra!.isVisita!) {
      return CheckboxListTile(
        title: LabelWidget(
          title: 'Agendar Visita',
          fontSize: DeviceSize.fontSize(16, 20),
          textColor: Colors.grey[350],
        ),
        value: data.dataExtra!.canVisitaSync ?? false,
        onChanged: null,
        controlAffinity: ListTileControlAffinity.leading,
        contentPadding: EdgeInsets.zero,
      );
    } else if (data.visita == true && !data.dataExtra!.isVisita!) {
      return CheckboxListTile(
        title: LabelWidget(
          title: 'Agendar Visita',
          fontSize: DeviceSize.fontSize(16, 20),
          textColor: Colors.grey.shade900,
        ),
        value: data.dataExtra!.canVisitaSync ?? false,
        onChanged: (bool? value) async {
          await ctrl.setCanVisitaSync(data, value);
        },
        controlAffinity: ListTileControlAffinity.leading,
        contentPadding: EdgeInsets.zero,
      );
    } else if (data.visita == true && data.dataExtra!.isVisita!) {
      return Row(
        children: [
          Icon(ctrl.getVisitStatusIcon(data),
              color: ctrl.getVisitStatusColor(data)),
          const SizedBox(width: 8),
          Text(
            ctrl.getVisitStatusLabel(data),
            style: TextStyle(
              fontSize: DeviceSize.fontSize(16, 20),
              color: ctrl.getVisitStatusColor(data),
            ),
          ),
        ],
      );
    }
    return Container(); // Default case, should not reach here
  }
}
