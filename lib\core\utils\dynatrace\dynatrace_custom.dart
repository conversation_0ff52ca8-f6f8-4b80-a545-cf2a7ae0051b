import 'package:dynatrace_flutter_plugin/dynatrace_flutter_plugin.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:pharmalink/app_constants.dart';

import 'dynatrace_custom_action.dart';

class DynatraceCustom {
  static const String _actionFlowPrefix = "ActionFlow";

  Future<void> init() async {
    await Dynatrace().startWithoutWidget();
    Dynatrace().applyUserPrivacyOptions(
        UserPrivacyOptions(DataCollectionLevel.UserBehavior, true));

    informaAmbiente();
  }

  void informaAmbiente({DynatraceAction? action}) {
    var key = "Ambiente";

    if (action == null) {
      Dynatrace().reportStringValue(
          key, appController.currentEnvironment.name.capitalize);
    } else {
      action.reportStringValue(
          key, appController.currentEnvironment.name.capitalize);
    }
  }

  Future<void> collectAndReportSessionData() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    var username = appController.userLogged?.userName?.toLowerCase().trim();
    if (username != null) {
      Dynatrace().identifyUser(username);
    }

    DynatraceRootAction reportAll = Dynatrace().enterAction("Session Data");
    try {
      informaAmbiente(action: reportAll);

      reportAll.reportStringValue(
          "Package Name", packageInfo.packageName.toString());

      reportAll.reportStringValue("Version", packageInfo.version.toString());

      if (username != null) {
        reportAll.reportStringValue("Workspace",
            appController.workspace?.name ?? "Workspace not selected");

        reportAll.reportStringValue("User", username);
      }
    } finally {
      reportAll.leaveAction();
    }
  }

  Future<void> reportZoneStacktrace(
      dynamic error, StackTrace stacktrace) async {
    await Dynatrace().reportZoneStacktrace(error, stacktrace);
  }

  DynatraceCustomAction actionReport(Type runtimeType, String tagIdentifier,
      {String? valueName, String? value}) {
    var storeData = globalParams.getCurrentStore();

    var actionName =
        "$_actionFlowPrefix - ${runtimeType.toString()} -> $tagIdentifier";

    if (storeData != null) {
      actionName = "$actionName - ${storeData.cNPJ}";
    }
    var action = Dynatrace().enterAction(actionName);
    if (valueName != null) {
      action.reportStringValue(valueName, value);
    }
    var customAction = DynatraceCustomAction(action, actionName);

    return customAction;
  }

  Future<void> reportErrorStacktrace(
      String tagIdentifier, String error, StackTrace stacktrace) async {
    Dynatrace().reportErrorStacktrace(
        tagIdentifier, error, error, stacktrace.toString());
  }
}
