import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders_ideal_mix/models/orders_ideal_mix_model.dart';

import '../../../../exports/basic_exports.dart';

class OrdersIdealMixProductItemWidget extends StatelessWidget {
  const OrdersIdealMixProductItemWidget({
    super.key,
    required this.familyName,
    required this.item,
    this.discount,
    this.minQtdy,
    required this.showQtdyMin,
    required this.showQtdyMax,
    required this.mixIdealCondicoes,
    this.familyMix,
    this.familyConditionalMix,
  });

  final String familyName;
  final MixIdealProdutosModel item;
  final double? discount;
  final int? minQtdy;
  final bool showQtdyMin;
  final bool showQtdyMax;
  final MixIdealCondicoesModel mixIdealCondicoes;
  final MixIdealProdutosMixModel? familyMix;
  final MixIdealProdutosCondicaoModel? familyConditionalMix;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrdersIdealMixController>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                SizedBox(
                  width: 80,
                  child: CachedNetworkImage(
                    imageUrl: AppImages.productGeneric,
                    placeholder: (context, url) => Image.asset(
                      AppImages.productGeneric,
                    ),
                    errorWidget: (context, url, error) => Image.asset(
                      AppImages.productGeneric,
                    ),
                  ),
                ),
                Flexible(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      LabelWidget(
                        title: item.descricaoProduto!.toString(),
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        overflow: TextOverflow.ellipsis,
                        maxLines: 5,
                      ),
                      3.toHeightSpace(),
                      LabelWidget(
                          title: item.usandoDUN! == true
                              ? "DUN: ${item.dUN!}"
                              : "EAN: ${item.eAN!}"),
                    ],
                  ),
                ),
              ],
            ),
            Row(
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    LabelWidget(
                      title: "Desc. Mínimo",
                      fontSize: 11.sp,
                    ),
                    LabelWidget(
                      title: discount?.formatPercent() ??
                          item.desconto?.formatPercent() ??
                          0.0.formatPercent(),
                      fontSize: 14.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ],
                ),
                Visibility(visible: showQtdyMin, child: const Spacer()),
                Visibility(
                  visible: showQtdyMin,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LabelWidget(
                        title: "Qtd Mínima",
                        fontSize: 11.sp,
                      ),
                      LabelWidget(
                        title: item.quantidadeMinima?.toString() ??
                            item.quantidadeMaxima?.toString() ??
                            "---",
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ],
                  ),
                ),
                Visibility(visible: showQtdyMax, child: const Spacer()),
                Visibility(
                  visible: showQtdyMax,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LabelWidget(
                        title: "Qtd Máxima",
                        fontSize: 11.sp,
                      ),
                      LabelWidget(
                        title: item.quantidadeMaxima?.toString() ?? "-",
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                if (!item.isEditing!) ...[
                  IconButton(
                      onPressed: () {
                        item.isEditing = false;
                        ctrl.update();
                        ctrl.setQtdyDown(
                          item,
                          minQtdy,
                          familyName,
                          mixIdealCondicoes,
                          familyMix,
                          familyConditionalMix,
                        );
                      },
                      icon: const Icon(FontAwesomeIcons.minus)),
                  GestureDetector(
                    onTap: () {
                      item.focusNode ??= FocusNode();
                      item.qtdyController ??= TextEditingController(
                          text: item.qtdySelected.toString());

                      item.isEditing = true;
                      ctrl.update();
                      Future.delayed(const Duration(milliseconds: 250), () {
                        item.focusNode!.requestFocus();
                        item.qtdyController!.selection = TextSelection(
                          baseOffset: 0,
                          extentOffset: item.qtdyController!.text.length,
                        );
                      });
                    },
                    child: LabelWidget(
                      title: item.qtdySelected.toString(),
                      fontSize: 18.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                      onPressed: () async {
                        item.isEditing = false;
                        ctrl.update();
                        await ctrl.setQtdyUp(
                          item,
                          minQtdy,
                          familyName,
                          mixIdealCondicoes,
                          familyMix,
                          familyConditionalMix,
                        );
                      },
                      icon: const Icon(FontAwesomeIcons.plus)),
                ] else ...[
                  SizedBox(
                    width: 100,
                    child: Row(
                      children: [
                        Expanded(
                          child: TextField(
                            focusNode: item.focusNode!,
                            keyboardType: TextInputType.number,
                            controller: item.qtdyController,
                            maxLength: 5,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly
                            ],
                            style: TextStyle(
                                fontSize: DeviceSize.fontSize(18, 21)),
                            textAlign: TextAlign.center,
                            decoration: const InputDecoration(
                              border: InputBorder.none,
                              counterText: "",
                            ),
                          ),
                        ),
                        IconButton(
                          icon: const Icon(
                            Icons.check,
                            size: 24,
                            color: Colors.green,
                          ),
                          onPressed: () async {
                            String value = item.qtdyController!.text;
                            if (value.isEmpty) {
                              value = '0';
                            }
                            await ctrl.setQtdyEdit(
                              item,
                              int.parse(value),
                              minQtdy,
                              familyName,
                              mixIdealCondicoes,
                              familyMix,
                              familyConditionalMix,
                            );
                            item.isEditing = false;
                            ctrl.update();
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            ),
            const Divider(thickness: 1.5),
          ],
        ),
      );
    });
  }
}
