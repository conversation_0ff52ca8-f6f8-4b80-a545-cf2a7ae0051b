import 'dart:collection';

import 'package:intl/intl.dart';
import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/home/<USER>/dashboard_request_model.dart';
import 'package:pharmalink/modules/home/<USER>/dashboard_response_model.dart';

class HomeController extends GetxControllerInstrumentado<HomeController> {
  int totalSentOrdersThisMonth = 0;
  double totalInvoicedThisMonth = 0.00;
  double totalValueRequestedThisMonth = 0.00;
  int totalPDVOrdersSentThisMonth = 0;
  double totalValuePDVRequestedThisMonth = 0.00;
  String? totalInvoicedThisMonthParsed = "";
  String? totalValueRequestedThisMonthParsed = "";
  String? totalValuePDVRequestedThisMonthParsed = "";
  final rx = 0.obs;
  HomeController();

  Future<void> setDashboard() async {
    if (settingsAppController.settings.hasSentOrdersThisMonth! ||
        settingsAppController.settings.hasTotalInvoicedThisMonth! ||
        settingsAppController.settings.hasTotalRequestedThisMonth! ||
        settingsAppController.settings.hasPDVOrdersSentThisMonth! ||
        settingsAppController.settings.hasPDVOrdersRequestedThisMonth!) {
      var dashboardRequest = _buildDashboardRequest();
      var result = await homeApi.getDashboardReport(dashboardRequest);

      result.when(
          sucess: (dashboardsResponse) {
            totalInvoicedThisMonth = 0.00;
            totalValuePDVRequestedThisMonth = 0.00;
            totalValueRequestedThisMonth = 0.00;
            totalPDVOrdersSentThisMonth = 0;
            totalSentOrdersThisMonth = 0;
            _contarPedidosEnviados(dashboardsResponse);
            _sumValues(dashboardsResponse);
            update();
          },
          error: (error) => SnackbarCustom.snackbarError(error.error));
    }
  }

  DashboardRequest _buildDashboardRequest() {
    var actualDate = DateTime.now();
    return DashboardRequest(
        dataInicio: DateTime(actualDate.year, actualDate.month, 1),
        dataTermino: DateTime(actualDate.year, actualDate.month,
            DateTime(actualDate.year, actualDate.month + 1, 0).day),
        representante: appController.userLogged!.perfil == 'Representante'
            ? appController.userLogged!.userName
            : null,
        gcr: appController.userLogged!.perfil == 'GerenteRegional'
            ? appController.userLogged!.userName
            : null,
        gdn: appController.userLogged!.perfil == 'GerenteCampo'
            ? appController.userLogged!.userName
            : null,
        cd: null,
        distribuidor: null,
        familia: null,
        origemPedido: null,
        pdv: appController.userLogged!.perfil == 'Loja'
            ? appController.userLogged!.userName
            : null,
        setor: null,
        statusRetornoPedido: null,
        statusRetornoProduto: null,
        tipoPedido: null,
        bandeira: null,
        dun: null,
        idPessoa: appController.userLogged?.userId,
        idUsuario: appController.userLogged?.userId,
        laboratorio: null,
        produto: null,
        pagina: 0,
        /*valor maximo de um inteiro é necessario para paginação dashboard não usa e precisa do maximo de info*/
        totalItens: 2147483647);
  }

  void _contarPedidosEnviados(List<DashboardResponse> dashboardsResponse) {
    final dataSet = HashSet<DashboardResponse>(
      // or LinkedHashSet
      equals: (a, b) => a.nrPedido == b.nrPedido,
      hashCode: (a) => a.nrPedido.hashCode,
    )..addAll(dashboardsResponse);

    totalSentOrdersThisMonth = dataSet.length;
    if (globalParams.getCurrentStore() != null) {
      totalPDVOrdersSentThisMonth = dataSet
          .where((element) =>
              element.cnpj == globalParams.getCurrentStore()!.cNPJ!)
          .length;
    }
  }

  void _sumValues(List<DashboardResponse> dashboardsResponse) {
    for (var element in dashboardsResponse) {
      totalInvoicedThisMonth = totalInvoicedThisMonth +
          (element.valorLiquidoFaturado != null
              ? double.parse(element.valorLiquidoFaturado!)
              : 0.00);
      if (globalParams.getCurrentStore() != null) {
        totalValuePDVRequestedThisMonth = totalValuePDVRequestedThisMonth +
            (element.valorLiquidoSolicitado != null &&
                    element.cnpj == globalParams.getCurrentStore()!.cNPJ!
                ? double.parse(element.valorLiquidoSolicitado!)
                : 0.00);
      } else {
        totalValuePDVRequestedThisMonth = 0.00;
      }

      totalValueRequestedThisMonth = totalValueRequestedThisMonth +
          (element.valorLiquidoSolicitado != null
              ? double.parse(element.valorLiquidoSolicitado!)
              : 0.00);
    }
    var formater = NumberFormat.currency(
        locale: 'pt_BR', name: 'BRL', symbol: 'R\$', decimalDigits: 2);
    totalInvoicedThisMonthParsed = formater.format(totalInvoicedThisMonth);
    totalValuePDVRequestedThisMonthParsed =
        formater.format(totalValuePDVRequestedThisMonth);
    totalValueRequestedThisMonthParsed =
        formater.format(totalValueRequestedThisMonth);
  }

  Future<void> updateDashBoardPdv() async {
    await setDashboard();
    rx.value++;
    update();
  }

  Future<void> openStoreDetail() async {
    Get.toNamed(RoutesPath.storeRoutesDetails,
        arguments: {'store': globalParams.getCurrentStore()!});
  }
}
