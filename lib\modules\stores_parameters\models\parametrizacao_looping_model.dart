class ParametrizacaoLoopingModel {
  bool? usarLooping;
  int? tipo;
  int? origem;
  bool? ordenacaoDesconto;
  bool? mostrarDescontoMedio;
  bool? utilizaCasasDecimais;
  bool? utilizaPrecoDistribuidor;
  bool? exibirDescontoBase;
  bool? exibirDescontoFaixa1;
  bool? exibirDescontoFaixa2;
  bool? exibirDescontoFaixa3;
  bool? exibirDescontoFaixa4;
  bool? exibirDescontoNegociado;
  bool? utilizaComboOferta;

  ParametrizacaoLoopingModel({
    this.usarLooping,
    this.tipo,
    this.origem,
    this.ordenacaoDesconto,
    this.mostrarDescontoMedio,
    this.utilizaCasasDecimais,
    this.utilizaPrecoDistribuidor,
    this.exibirDescontoBase,
    this.exibirDescontoFaixa1,
    this.exibirDescontoFaixa2,
    this.exibirDescontoFaixa3,
    this.exibirDescontoFaixa4,
    this.exibirDescontoNegociado,
    this.utilizaComboOferta,
  });

  ParametrizacaoLoopingModel.fromJson(Map<String, dynamic> json) {
    usarLooping = json['UsarLooping'];
    tipo = json['Tipo'];
    origem = json['Origem'];
    ordenacaoDesconto = json['OrdenacaoDesconto'];
    mostrarDescontoMedio = json['MostrarDescontoMedio'];
    utilizaCasasDecimais = json['UtilizaCasasDecimais'];
    utilizaPrecoDistribuidor = json['UtilizaPrecoDistribuidor'];
    exibirDescontoBase = json['ExibirDescontoBase'];
    exibirDescontoFaixa1 = json['ExibirDescontoFaixa1'];
    exibirDescontoFaixa2 = json['ExibirDescontoFaixa2'];
    exibirDescontoFaixa3 = json['ExibirDescontoFaixa3'];
    exibirDescontoFaixa4 = json['ExibirDescontoFaixa4'];
    exibirDescontoNegociado = json['ExibirDescontoNegociado'];
    utilizaComboOferta = json['UtilizaComboOferta'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['UsarLooping'] = usarLooping;
    data['Tipo'] = tipo;
    data['Origem'] = origem;
    data['OrdenacaoDesconto'] = ordenacaoDesconto;
    data['MostrarDescontoMedio'] = mostrarDescontoMedio;
    data['UtilizaCasasDecimais'] = utilizaCasasDecimais;
    data['UtilizaPrecoDistribuidor'] = utilizaPrecoDistribuidor;
    data['ExibirDescontoBase'] = exibirDescontoBase;
    data['ExibirDescontoFaixa1'] = exibirDescontoFaixa1;
    data['ExibirDescontoFaixa2'] = exibirDescontoFaixa2;
    data['ExibirDescontoFaixa3'] = exibirDescontoFaixa3;
    data['ExibirDescontoFaixa4'] = exibirDescontoFaixa4;
    data['ExibirDescontoNegociado'] = exibirDescontoNegociado;
    data['UtilizaComboOferta'] = utilizaComboOferta;

    return data;
  }
}
