# Pharmalink

PharmaLink Mobile by Flutter

#### Estrutura do projeto

O projeto está estruturado dentro da pasta "lib".

# Documentação da Arquitetura do Aplicativo Flutter

## 1. Visão Geral da Estrutura do Projeto

Nossa aplicação Flutter segue uma arquitetura modular, utilizando o framework GetX para gerenciamento de estado e injeção de dependências. A estrutura do projeto é organizada da seguinte forma:

```
lib/
├── app/
├── config/
├── core/
├── modules/
└── widgets/
```

## 2. Componentes Principais

### 2.1 App (`lib/app`)

Esta pasta contém as configurações globais do projeto:

- `AppController`: Controlador global que gerencia o estado compartilhado em todo o aplicativo.
- `ThemesController`: Gerencia variáveis globais, informações do workspace e usuários logados compartilhados entre os módulos.
- `routes`: Configurações das rotas do aplicativo, definindo a navegação entre as páginas.
- `service`: Contém o `StorageService` para armazenamento de dados persistentes usando GetStorage do GetX.
- `theme`: Constantes para cores padrão, imagens e tipografias.
- `utils`: Constantes de string para textos fixos e classes para gerenciar a responsividade do aplicativo.

### 2.2 Config (`lib/config`)

Contém as configurações do SQLite, incluindo:
- Inicialização do banco de dados
- Criação de tabelas
- Métodos para leitura e escrita de dados

### 2.3 Core (`lib/core`)

Componentes essenciais utilizados em todo o projeto:

- `Criptografia`: Funções e classes para criptografar e descriptografar dados.
- `Enums`: Enumerações para conjuntos constantes de valores.
- `Extensões`: Extensões de classes existentes para funcionalidades adicionais.
- `Formatadores`: Funções e classes para formatação consistente de dados.
- `HttpRequest`: Configurações e utilitários para solicitações HTTP.
- `Classes genéricas`: Classes flexíveis para trabalhar com qualquer tipo de dados.

### 2.4 Modules (`lib/modules`)

Cada módulo representa uma funcionalidade distinta do sistema (ex: Login, Pedidos, Relatórios, Home). A estrutura de um módulo típico (usando `Orders` como exemplo) é:

- `api`: Define todos os endpoints relacionados ao módulo.
- `bindings`: Classe responsável pela inicialização da instância na injeção de dependência do GetX.
- `controller`: Classe controller que usa `GetxController`, contendo variáveis, métodos e controle do estado da página.
- `extensions`: Extensões de classe específicas do módulo.
- `models`: Classes de modelo para serialização/desserialização de dados da API.
- `pages`: Páginas que compõem a interface do usuário do módulo.

### 2.5 Widgets (`lib/widgets`)

Componentes de interface do usuário genéricos utilizados em vários módulos.

## 3. Gerenciamento de Estado com GetX

- Utiliza `GetxController` para gerenciamento de estado reativo.
- Exemplo de uso no `LoginController`:
  ```dart
  void setUserLogged(bool v) {
    userLogged = v;
    update();
  }
  ```

## 4. Injeção de Dependências

- GetX é usado para injeção de dependências.
- Exemplo de injeção:
  ```dart
  Get.put(LoginController());
  ```

## 5. Navegação

- Utiliza o sistema de navegação do GetX.
- Exemplo de navegação:
  ```dart
  Get.toNamed(RoutesPath.synchronizations, arguments: {'all': false});
  ```

## 6. Persistência de Dados com SQLite

Este projeto utiliza SQLite para armazenamento offline de dados complexos com muitas ramificações. Para evitar a necessidade de criar várias tabelas e realizar queries complexas, os dados são armazenados no formato JSON, mantendo a mesma estrutura recebida pelas APIs.

### 6.1 Armazenamento de Dados

Os dados são armazenados em uma única tabela. Para adicionar um registro:

```dart
await dbContext.addData(
  key: DatabaseModels.contentModel,
  data: result.data!,
  workspaceId: appController.workspace!.workspaceId,
  clearCurrentData: true,
  hashCode: "", // opcional
  storeId: 0, // opcional
  userId: "", // opcional
)
```

Parâmetros:
- `key`: Nome da model do módulo em questão.
- `data`: Objeto comum ou lista a ser salvo.
- `workspaceId`: Código do workspace atual.
- `clearCurrentData`: Se true, limpa dados existentes antes de inserir novos.
- `hashCode`: Hash para garantir unicidade do dado.
- `storeId`: Código da loja.
- `userId`: Código do usuário logado.

### 6.2 Configuração de Modelos para SQLite

Para usar uma model com SQLite:

1. Estenda `SqfLiteBase`:
   ```dart
   class ReportContentsModel extends SqfLiteBase<ReportContentsModel>
   ```

2. Adicione `super` no construtor e `fromJson`:
   ```dart
   ReportContentsModel({this.fileId}) : super(DatabaseModels.contentModel);

   ReportContentsModel.fromJson(Map<String, dynamic> json) : super(DatabaseModels.contentModel) {
     fileId = json['FileId'];
   }
   ```

3. Implemente métodos `getFirst` e `getList`:
   ```dart
   Future<ReportContentsModel> getFirst() async {
     var list = await getAll<ReportContentsModel>(ReportContentsModel.fromJson);
     return list.first;
   }

   Future<List<ReportContentsModel>> getList() async {
     var list = await getAll<ReportContentsModel>(ReportContentsModel.fromJson);
     return list;
   }
   ```

4. Uso em um controller:
   ```dart
   final result = await ReportContentsModel().getFirst();
   final result = await ReportContentsModel().getList();
   ```

## 7. Tratamento de Erros e Logging

- Utiliza Dynatrace para monitoramento e logging.
- Exemplo de uso:
  ```dart
  var (leaveAction, subAction) = dynatraceAction.subActionReport("sendLogin");
  ```

## 8. Segurança

- Implementa autenticação, incluindo suporte para Azure B2C.
- Gerencia tokens de acesso e refresh tokens.

## 9. Testes

- Estrutura pronta para implementação de testes unitários e de widgets.

## 10. Boas Práticas

- Separação clara de responsabilidades.
- Uso de abstrações (interfaces) para flexibilidade.
- Código limpo e bem organizado.

## 11. Conclusão

Esta arquitetura oferece uma base sólida para um aplicativo Flutter escalável e manutenível. A modularidade, o uso do GetX e a integração eficiente com SQLite proporcionam flexibilidade e eficiência no desenvolvimento e manutenção do aplicativo.

## 12. Próximos Passos para a Equipe de Sustentação

1. Familiarizar-se com a estrutura modular e o uso do GetX.
2. Entender o fluxo de dados e o ciclo de vida do aplicativo.
3. Revisar a documentação do GetX e SQLite.
4. Configurar o ambiente de desenvolvimento.
5. Estabelecer processos de CI/CD.


## Comandos básicos

#### Executar em modo release

```
flutter run --release
```

#### Gerar Splash Screen
Executar no terminal:
```
dart run flutter_native_splash:create --flavor itrade --path=flutter_native_splash-itrade.yaml
```

```
dart run flutter_native_splash:create --flavor pharmalink --path=flutter_native_splash-pharmalink.yaml
```

#### Criar uma KeyStore (alterar o path)

```
keytool -genkey -v -keystore D:\ECS\MarketPlace-MobilePLK\keystore\pharmalink.jks -keyalg RSA -keysize 2048 -validity 10000 -alias pharmalink
```

#### Gerar Build (APK)

```
    flutter build apk
```

#### Gerar Build (Google)

```
    flutter build appbundle
```

#### Atualizar Flavorirz
```
dart run flutter_flavorizr
```

#### Atualizar Icones
```
dart run flutter_launcher_icons:main -f flutter_launcher_icons*
```


### Ambiente

### Windows:

Versão atual Android Studio Koala 2024.1.2

### iOS - macOS Sonoma

Versão do Flutter 3.24.5 / 3.22.2
