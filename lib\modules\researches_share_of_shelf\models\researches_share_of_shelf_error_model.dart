class ResearchesShareOfShelfErrorModel {
  String? message;
  ResearchesShareOfShelfErrorModelState? modelState;

  ResearchesShareOfShelfErrorModel({this.message, this.modelState});

  ResearchesShareOfShelfErrorModel.fromJson(Map<String, dynamic> json) {
    message = json['Message'];
    modelState = json['ModelState'] != null
        ? ResearchesShareOfShelfErrorModelState.fromJson(json['ModelState'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Message'] = message;
    if (modelState != null) {
      data['ModelState'] = modelState!.toJson();
    }
    return data;
  }
}

class ResearchesShareOfShelfErrorModelState {
  List<String>? erros;

  ResearchesShareOfShelfErrorModelState({this.erros});

  ResearchesShareOfShelfErrorModelState.fromJson(Map<String, dynamic> json) {
    erros = json['Erros'].cast<String>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Erros'] = erros;
    return data;
  }
}
