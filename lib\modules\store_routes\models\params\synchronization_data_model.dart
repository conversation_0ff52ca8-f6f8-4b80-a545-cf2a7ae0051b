import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class SynchronizationData extends SqfLiteBase<SynchronizationData> {
  String? id;
  int? storeId;
  int? workspaceId;
  String? userId;
  int? status;
  String? processStartDate;
  String? processFinishDate;
  String? creationDate;
  String? storeParameterizations;
  bool? isActive;

  SynchronizationData(
      {this.id,
      this.storeId,
      this.workspaceId,
      this.userId,
      this.status,
      this.processStartDate,
      this.processFinishDate,
      this.creationDate,
      this.storeParameterizations,
      this.isActive})
      : super(DatabaseModels.synchronizationData);

  SynchronizationData.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.synchronizationData) {
    id = json['id'];
    storeId = json['storeId'];
    workspaceId = json['workspaceId'];
    userId = json['userId'];
    status = json['status'];
    processStartDate = json['processStartDate'];
    processFinishDate = json['processFinishDate'];
    creationDate = json['creationDate'];
    storeParameterizations = json['storeParameterizations'];
    isActive = json['isActive'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['storeId'] = storeId;
    data['workspaceId'] = workspaceId;
    data['userId'] = userId;
    data['status'] = status;
    data['processStartDate'] = processStartDate;
    data['processFinishDate'] = processFinishDate;
    data['creationDate'] = creationDate;
    data['storeParameterizations'] = storeParameterizations;
    data['isActive'] = isActive;
    return data;
  }
}
