import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/synchronizations/controller/synchronizations_controller.dart';

class SynchronizationsPage extends StatelessWidget {
  SynchronizationsPage({super.key});

  final bool _allItens = Get.arguments['all'];
  final bool _isLogin = Get.arguments['isLogin'] ?? false;
  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<SynchronizationsController>(
        "Tela de Sincronização", initState: (state) {
      if (Get.arguments['autostart'] != null && Get.arguments['autostart']) {
        synchronizationsController.orderId = Get.arguments['orderId'];
        synchronizationsController.startSyncronizations();
      }
    }, builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: LabelWidget(
            title: "Sincronização",
            fontSize: DeviceSize.fontSize(18, 21),
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              if (!_isLogin && _allItens) {
                navigationPageController.selectedIndex = 1;
                Get.until(
                    (route) => Get.currentRoute == RoutesPath.navigationPage);
              } else {
                Get.offAllNamed(RoutesPath.login);
              }
            },
          ),
        ),
        body: SingleChildScrollView(
          child: ctrl.getWidgetList(_allItens, ctrl.lastSyncDate),
        ),
        floatingActionButton: FloatingActionButton(
          backgroundColor: ctrl.syncStarting()
              ? greyColor
              : themesController.getPrimaryColor(),
          onPressed: ctrl.syncStarting()
              ? null
              : () {
                  ctrl.startSyncronizations();
                },
          child: const Icon(
            FontAwesomeIcons.arrowsRotate,
            color: whiteColor,
          ),
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
