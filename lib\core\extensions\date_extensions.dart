import 'package:intl/intl.dart';

enum DateFormatType {
  ddMMyyyy('dd/MM/yyyy'),
  yyyyMMdd('yyyy-MM-dd'),
  mMddyyyy('MM/dd/yyyy'),
  ddMMyyyyHHmmss('dd/MM/yyyy HH:mm:ss'),
  ddMMyyyyHHmmssWrapped('dd/MM/yyyy\nHH:mm:ss'),
  hHmmss('HH:mm:ss'),
  ddMM('dd/MM');

  final String format;
  const DateFormatType(this.format);
}

extension DateExtensions on DateTime {
  static DateTime setTimezone(DateTime current) {
    return current.subtract(const Duration(hours: 3));
  }

  String formatDate(
      {required DateFormatType formatType, bool applyTimezone = false}) {
    DateTime date = applyTimezone ? setTimezone(this) : this;
    return DateFormat(formatType.format, 'pt_Br').format(date);
  }
}

extension DurationExtensions on Duration {
  String formatDuration() {
    String twoDigits(int n) => n.toString().padLeft(2, "0");
    final twoDigitMinutes = twoDigits(inMinutes.remainder(60));
    final twoDigitSeconds = twoDigits(inSeconds.remainder(60));
    return "${twoDigits(inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }
}
