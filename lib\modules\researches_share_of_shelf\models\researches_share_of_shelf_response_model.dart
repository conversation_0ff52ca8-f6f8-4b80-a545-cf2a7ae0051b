class ResearchesShareOfShelfResponseModel {
  bool? isConnected;

  ResearchesShareOfShelfResponseModel({
    this.isConnected,
  });

  ResearchesShareOfShelfResponseModel.fromJson(Map<String, dynamic> json) {
    isConnected = json['isConnected'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['isConnected'] = isConnected;

    return data;
  }
}
