import 'package:pharmalink/modules/orders/models/orders_mix_produto_request_model.dart';
import 'package:pharmalink/modules/orders/models/orders_tabloid_products_request_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/distribuidores_model.dart';

class OrderParametersModel {
  int? typeOrderId;
  MixProdutoRequest? paymentTypeParameters;
  TabloidProductsRequest? paymentTypeParametersSpecial;
  DistribuidoresModel? distributorCurrent;
  List<DistribuidoresModel>? distributorsSelected;
  bool? usaLooping;
  OrderParametersModel({
    this.typeOrderId,
    this.paymentTypeParameters,
    this.paymentTypeParametersSpecial,
    this.distributorCurrent,
    this.distributorsSelected,
    this.usaLooping,
  });

  OrderParametersModel.fromJson(Map<String, dynamic> json) {
    typeOrderId = json['typeOrderId'];
    paymentTypeParameters = json['paymentTypeParameters'] != null
        ? MixProdutoRequest.fromJson(json['paymentTypeParameters'])
        : null;
    paymentTypeParametersSpecial = json['paymentTypeParametersSpecial'] != null
        ? TabloidProductsRequest.fromJson(json['paymentTypeParametersSpecial'])
        : null;
    distributorCurrent = json['distributorCurrent'] != null
        ? DistribuidoresModel.fromJson(json['distributorCurrent'])
        : null;
    distributorsSelected = json['distributorsSelected'] != null
        ? (json['distributorsSelected'] as List)
            .map((e) => DistribuidoresModel.fromJson(e))
            .toList()
        : null;
    usaLooping = json['usaLooping'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['typeOrderId'] = typeOrderId;
    if (paymentTypeParameters != null) {
      data['paymentTypeParameters'] = paymentTypeParameters!.toJson();
    }
    if (paymentTypeParametersSpecial != null) {
      data['paymentTypeParametersSpecial'] =
          paymentTypeParametersSpecial!.toJson();
    }
    if (distributorCurrent != null) {
      data['distributorCurrent'] = distributorCurrent!.toJson();
    }
    if (distributorsSelected != null) {
      data['distributorsSelected'] =
          distributorsSelected!.map((e) => e.toJson()).toList();
    }
    data['usaLooping'] = usaLooping;
    return data;
  }
}
