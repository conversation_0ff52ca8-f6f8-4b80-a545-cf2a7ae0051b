import 'package:dio/dio.dart';
import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/core/models/result_error.dart';
import 'package:pharmalink/core/utils/constants.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/login/models/login_forget_password_request.dart';
import 'package:pharmalink/modules/login/models/login_model.dart';
import 'package:pharmalink/modules/login/models/login_response_model.dart';

abstract class ILoginApi {
  Future<HttpResponse<LoginResponseModel>> login({required LoginModel model});
  Future<HttpResult<LoginResponseModel>> loginInfo();
  Future<HttpResponse<bool>> forgetPassword(
      {required LoginForgetPasswordRequest model});
  Future<HttpResponse<String>> getURLPortal();
}

class <PERSON>ginApi extends ILoginApi {
  final HttpManager _httpManager;
  LoginApi(this._httpManager);

  @override
  Future<HttpResponse<LoginResponseModel>> login({
    required LoginModel model,
  }) async {
    return await _httpManager.requestFull<LoginResponseModel, ResultLoginError>(
      path: '/',
      baseUrl: appController.workspace!.token!,
      contentType: Headers.formUrlEncodedContentType,
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
       
        return LoginResponseModel.fromJson(data);
      },
      parserError: (dynamic errorData) {
        return ResultLoginError.fromJson(errorData);
      },
    );
  }

  @override
  Future<HttpResult<LoginResponseModel>> loginInfo() async {
    final result = await _httpManager.restRequest(
      url: 'usuarios/obterInformacoesComplementaresLogin',
      method: HttpMethods.get,
    );

    if (result.data.isNotEmpty && result.statusCode!.isStatusOk()) {
      return HttpResult.sucess(LoginResponseModel.fromJson(result.data));
    } else {
      return HttpResult.error(getOthersStatusCodes(result));
    }
  }

  @override
  Future<HttpResponse<bool>> forgetPassword(
      {required LoginForgetPasswordRequest model}) async {
    return await _httpManager.request<bool>(
      path: 'usuarios/insereControleEmail',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        return true;
      },
    );
  }

  @override
  Future<HttpResponse<String>> getURLPortal() async {
    return await _httpManager.request<String>(
      path: 'parametrizacoesGerais/getUrl',
      method: HttpMethods.get,
      parser: (data) {
        return data;
      },
    );
  }
}
