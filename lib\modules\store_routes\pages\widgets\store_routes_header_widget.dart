import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class StoreRoutesHeaderWidget extends StatelessWidget {
  const StoreRoutesHeaderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StoreRoutesController>(builder: (ctrl) {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    LabelWidget(
                      title: "Data",
                      textColor: themesController.getPrimaryColor(),
                      fontWeight: FontWeight.bold,
                      fontSize: DeviceSize.fontSize(14, 16),
                    ),
                    LabelWidget(
                      title: DateTime.now().formatDate(
                          formatType: DateFormatType.ddMMyyyy,
                          applyTimezone: true),
                      fontSize: DeviceSize.fontSize(14, 16),
                    ),
                  ],
                ),
              ),
              Row(
                children: [
                  if (ctrl.hasVisitToSync)
                    IconButtonWidget(
                      title: "Sinc. Visitas",
                      icon: Icon(
                        FontAwesomeIcons.solidPaperPlane,
                        size: 24,
                        color: themesController.getIconColor(),
                      ),
                      onTap: () async {
                        if (appController.hasInternet) {
                          String storesToSync = storeRoutesPlannedController
                              .getStoreToVisitToSync();
                          Dialogs.confirm(
                            "Sincronizar Visitas",
                            "As visitas salvas e completas das seguintes lojas serão sincronizadas:\n\n"
                                "$storesToSync\n\n"
                                "Deseja prosseguir com a sincronização?",
                            buttonNameCancel: "Cancelar",
                            buttonNameOk: "Enviar",
                            onPressedCancel: () {
                              GetC.close();
                            },
                            onPressedOk: () async {
                              await ctrl.sendSyncVisits();
                            },
                          );
                        } else {
                          Dialogs.info(
                            "Sem conexão com a internet",
                            "Parece que você está sem conexão com a internet no momento. Por favor, verifique se:\n\n"
                                "1. Seu Wi-Fi está ligado e conectado\n"
                                "2. Seus dados móveis (3G/4G) estão ativos\n"
                                "3. Você está em uma área com cobertura de rede\n\n"
                                "Após verificar sua conexão, tente novamente.",
                          );
                        }
                      },
                    ),
                  IconButtonWidget(
                    title: "Atualizar",
                    icon: Icon(
                      FontAwesomeIcons.arrowsRotate,
                      size: 24,
                      color: themesController.getIconColor(),
                    ),
                    onTap: () async {
                      if (appController.hasInternet) {
                        if (!ctrl.hasSyncOffline && !globalParams.hasVisit) {
                          await synchronizationsController.onReady();
                          Get.toNamed(RoutesPath.synchronizations,
                              arguments: {'all': true, 'autostart': false});
                        } else {
                          await ctrl.updateRoutesSync();
                        }
                      } else {
                        Dialogs.info(
                          "Sem conexão com a internet",
                          "Parece que você está sem conexão com a internet no momento. Por favor, verifique se:\n\n"
                              "1. Seu Wi-Fi está ligado e conectado\n"
                              "2. Seus dados móveis (3G/4G) estão ativos\n"
                              "3. Você está em uma área com cobertura de rede\n\n"
                              "Após verificar sua conexão, tente novamente.",
                        );
                      }
                    },
                  ),
                  if (ctrl.hasSyncOffline || globalParams.hasVisit)
                    IconButtonWidget(
                      title: "Ord.",
                      textColor: ctrl.selectedTab == 1
                          ? themesController.getIconColor()
                          : Colors.grey,
                      icon: Icon(
                        FontAwesomeIcons.list,
                        size: 24,
                        color: ctrl.selectedTab == 1
                            ? themesController.getIconColor()
                            : Colors.grey,
                      ),
                      onTap: ctrl.selectedTab == 1
                          ? () async {
                              await storeRoutesPlannedController
                                  .toggleReorder();
                            }
                          : null,
                    ),
                  const Gap(15),
                ],
              ),
            ],
          ),
        ],
      );
    });
  }
}
