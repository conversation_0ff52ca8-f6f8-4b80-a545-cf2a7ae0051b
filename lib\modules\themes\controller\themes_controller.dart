import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/themes/models/themes_model.dart';

class ThemesController extends GetxControllerInstrumentado<ThemesController> {
  ThemesController();

  ThemesModel? current;
  List<ThemesModel> dataList = [];

  ThemeData? theme;

  void setTheme(ThemeData newTheme) {
    theme = newTheme;
    update();
  }

  ThemeData getCustomTheme(ThemesModel item) {
    return ThemeData(
        useMaterial3: true,
        colorScheme: ColorScheme.fromSeed(
            seedColor:
                item.paletaCorPrimaria?.toColor() ?? paletaCorPrimariaDefault));
  }

  void setDataList(List<ThemesModel> data) {
    dataList = data;
    current = data.any((x) => x.status == true)
        ? data.where((x) => x.status == true).first
        : null;

    if (current != null) {
      setTheme(getCustomTheme(current!));
    }
    update();
  }

  Future<void> getData({bool? forceUpdate}) async {
    if (forceUpdate != true) {
      final themesBox = await ThemesModel()
          .getList(workspaceId: appController.workspace!.workspaceId!);

      if (themesBox.isNotEmpty) {
        setDataList(themesBox);
        return;
      }
    }
    final result = await themesApi.getThemes();
    if (result.error != null) {
      SnackbarCustom.snackbarError(result.error!.message!);
    } else {
      setDataList(result.data!);
      await dbContext
          .withControllerAction(this)
          .addData(
          key: DatabaseModels.themesModel,
          data: result.data!,
          workspaceId: appController.workspace!.workspaceId,
          clearCurrentData: true);
      SnackbarCustom.snackbarSucess(
          "Temas", "Sincronização dos temas realizado com sucesso!");
    }
  }

  Color getBackgroundColor() {
    if (current != null) {
      return current!.paletaCorFundo?.toColor() ?? paletaCorFundoDefault;
    } else {
      return greenColor;
    }
  }

  Color getIconColor() {
    if (current != null) {
      return current!.paletaCorIcone?.toColor() ?? paletaCorIconeDefault;
    } else {
      return greenColor;
    }
  }

  Color getMenuColor() {
    if (current != null) {
      return current!.paletaCorFonteMenu?.toColor() ??
          paletaCorFonteMenuDefault;
    } else {
      return greenColor;
    }
  }

  Color getPrimaryColor() {
    if (current != null) {
      return current!.paletaCorPrimaria?.toColor() ?? paletaCorPrimariaDefault;
    } else {
      return greenColor;
    }
  }

  Color getSecondaryColor() {
    if (current != null) {
      return current!.paletaCorSecundaria?.toColor() ??
          paletaCorSecundariaDefault;
    } else {
      return greenColor;
    }
  }

  Color getColorA() {
    if (current != null) {
      return current!.paletaCorA?.toColor() ?? paletaCorPrimariaDefault;
    } else {
      return greenColor;
    }
  }

  Color getColorB() {
    if (current != null) {
      return current!.paletaCorB?.toColor() ?? paletaCorPrimariaDefault;
    } else {
      return greenColor;
    }
  }

  Color getColorC() {
    if (current != null) {
      return current!.paletaCorC!.toColor();
    } else {
      return greenColor;
    }
  }

  Color getColorD() {
    if (current != null) {
      return current!.paletaCorD!.toColor();
    } else {
      return greenColor;
    }
  }

  Color getColorButton() {
    if (current != null) {
      return current!.paletaCorBotao?.toColor() ?? paletaCorBotaoDefault;
    } else {
      return greenColor;
    }
  }
}
