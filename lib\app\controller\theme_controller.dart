import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:pharmalink/app_constants.dart';

class ThemeController extends GetxController {
  var isDark = false.obs;

  Map<String, ThemeMode> themeModes = {
    'light': ThemeMode.light,
    'dark': ThemeMode.dark,
  };

  Future<void> setMode(String themeText) async {
    ThemeMode themeMode = themeModes[themeText]!;
    Get.changeThemeMode(themeMode);
    await storage.write('theme', themeText);
  }

  void loadThemMode() async {
    String themeText = await storage.read('theme') ?? 'light';
    isDark.value = themeText == 'dark' ? true : false;
    await setMode(themeText);
  }

  Future<void> changeTheme() async {
    await setMode(isDark.value ? 'light' : 'dark');
    isDark.value = !isDark.value;
  }
}
