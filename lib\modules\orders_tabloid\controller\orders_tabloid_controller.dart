import 'package:pharmalink/config/databases/call_context.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/order_types/controller/order_types_controller.dart';
import 'package:pharmalink/modules/orders/models/orders_local_data_model.dart';
import 'package:pharmalink/modules/stores_parameters/enuns/type_order_enum.dart';
import 'package:pharmalink/modules/stores_parameters/models/prazo_pagamento_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/tabloides_model.dart';

class OrdersTabloidController
    extends GetxControllerInstrumentado<OrdersTabloidController>
    with TraceableController {
  OrdersTabloidController();
  List<TabloidesModel>? tabloids;
  List<PrazoPagamentoModel> deadlinePayment = [];
  int? tabloidId;
  //TabloidGetRegistrationInformationResponse? tabloidInfo;

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  Future<void> initialize() async {
    orderTypesController = Get.find<OrderTypesController>();
    tabloids = globalParams.getParametrization()!.tabloides;
    globalParams.order.orderLocal = null;
    update();
    // Future.delayed(Duration.zero, () async {
    //   await initLocalData();
    // });
    Get.toNamed(RoutesPath.ordersTabloid);
  }

  Future<TabloidesModel?> verifyLocalData() async {
    if (ordersController.isEditOrder != true &&
        globalParams.order.orderLocal != null) {
      final orderParameters = globalParams.order.orderLocal!.parameters!;
      globalParams.order.orderParameters = orderParameters;
      if (orderParameters.typeOrderId == TyperOrderEnum.especial) {
        final parameter = orderParameters.paymentTypeParametersSpecial!;
        var tabloidItem = tabloids!
            .firstWhereOrNull((e) => e.idTabloide == parameter.tabloidId);
        if (tabloidItem != null) {
          return tabloidItem;
        }
      } else {
        return null;
      }
    }
    return null;
  }

  Future<void> verifyAndAdvance(TabloidesModel item) async {
    if (item.orderMaximumQuantity == null) {
      await advance(item);
    } else if (item.orderMaximumQuantity == 0) {
      Dialogs.info("Tabloide de Ofertas",
          "O limite de envios para essa oferta para este CNPJ já foi atingido. Para continuar suas compras, por favor, clique em “voltar” e selecione outra oferta",
          buttonName: "Voltar", buttonOnPressed: () {
        Get.back();
      });
    } else {
      Dialogs.confirm(
        "Tabloide de Ofertas",
        "Este CNPJ ainda possui o limite de *${item.orderMaximumQuantity}* envios de pedidos por este tabloide de ofertas",
        onPressedOk: () async {
          Get.back();
          await advance(item);
        },
        onPressedCancel: () {
          Get.back();
        },
        buttonNameCancel: "Voltar",
        buttonNameOk: "Prosseguir",
      );
    }
  }

  Future<void> advance(TabloidesModel item) async {
    return trace('advance', () async {
      var (leaveAction, subAction) = dynatraceAction.subActionReport("advance");

      try {
        bool orderExists = await OrdersLocalDataModel().exists(
          storeId: globalParams.currentStoreId()!,
          typeOrder: TyperOrderEnum.especial,
          hashCode: "2:${item.idTabloide!}",
        );

        globalParams.order.setTabloidSelected(item);

        if (orderExists) {
          await Dialogs.confirm(
            AppStrings.orderUnfinished,
            AppStrings.orderUnfinishedMessage(
                "Pedido Especial - ${item.descricao}"),
            buttonNameCancel: "Continuar Pedido".toUpperCase(),
            buttonNameOk: "Descartar Pedido".toUpperCase(),
            onPressedOk: () async {
              await _discardOrder(item, subAction);
            },
            onPressedCancel: () async {
              await _continueOrder(item, subAction);
            },
          );
        } else {
          await _loadData(item);
        }
      } catch (e, s) {
        SnackbarCustom.snackbarError(e.toString());
        appErrorWithDynatrace(subAction, e, s);
        rethrow;
      } finally {
        leaveAction();
      }
    });
  }

  Future<void> _discardOrder(TabloidesModel item, subAction) async {
    GetC.close();
    await OrdersLocalDataModel().deleteOrder(
      storeId: globalParams.currentStoreId()!,
      typeOrder: TyperOrderEnum.especial,
      hashCode: "2:${item.idTabloide!}",
    );
    globalParams.order.orderLocal = null;
    subAction.reportEvent("Descartar Pedido");
    await _loadData(item);
  }

  Future<void> _continueOrder(TabloidesModel item, subAction) async {
    globalParams.order.orderLocal = await OrdersLocalDataModel().getFirst(
      storeId: globalParams.currentStoreId()!,
      typeOrder: TyperOrderEnum.especial,
      hashCode: "2:${item.idTabloide!}",
    );
    subAction.reportEvent("Continuar Pedido");
    GetC.close();
    final orderBackup = await verifyLocalData();
    item = orderBackup ?? item;
    await _loadData(item);
  }

  Future<void> _loadData(TabloidesModel item) async {
    globalParams.order.setTabloid(item.idTabloide, item.descricao);
    if (globalParams.getCurrentStore()!.dataExtra?.offlineDateSync != null) {
      appLog("Carregar dados offline", data: {
        "tabloid": item.idTabloide,
        "tabloidName": item.descricao,
      });
      await setDataOffline(item);
    } else {
      appLog("Carregar dados online", data: {
        "tabloid": item.idTabloide,
        "tabloidName": item.descricao,
      });
      await setDataOnline(item);
    }
  }

  Future<void> setDataOnline(TabloidesModel item) async {
    return trace('setDataOnline', () async {
      var (leaveAction, subAction) = dynatraceAction.subActionReport("advance");

      try {
        await appController
            .withDynatraceAction(dynatraceAction)
            .isValidToken(noMessages: true);
        if (appController.hasErrorRefreshToken == true) {
          SnackbarCustom.snackbarError(AppStrings.tokenExpired);
          Get.offAndToNamed(RoutesPath.login);
          return;
        }
        deadlinePayment = item.prazoPagamento!;
        tabloidId = item.idTabloide;
        appLog("Advance to Payment Type", data: {
          "tabloid": item.idTabloide,
          "tabloidName": item.descricao,
          "deadlinePayment": item.prazoPagamento?.length,
          "products": item.produtos?.length,
          "combos": item.combosOferta?.length,
        });
        subAction.reportValue("Advance", tabloidId);
        orderPaymentTypeController.initialize();
      } catch (e, s) {
        appErrorWithDynatrace(subAction, e, s);
        rethrow;
      } finally {
        leaveAction();
      }
    });
  }

  Future<void> setDataOffline(TabloidesModel item) async {
    return trace('setDataOffline', () async {
      var (leaveAction, subAction) =
          dynatraceAction.subActionReport("setDataOffline");

      try {
        deadlinePayment = item.prazoPagamento!;
        tabloidId = item.idTabloide;
        appLog("Advance to Payment Type", data: {
          "tabloid": item.idTabloide,
          "tabloidName": item.descricao,
          "deadlinePayment": item.prazoPagamento?.length,
          "products": item.produtos?.length,
          "combos": item.combosOferta?.length,
        });
        orderPaymentTypeController.initialize();
      } catch (e, s) {
        appErrorWithDynatrace(subAction, e, s);
        rethrow;
      } finally {
        leaveAction();
      }
    });
  }

  void getTabloidInfo(
      {required int tablodId,
      required List<PrazoPagamentoModel> deadlinePayments}) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("getTabloidInfo");

    try {
      deadlinePayment = deadlinePayments;
      tabloidId = tablodId;
      subAction.reportValue("GetTabloidInfo", tablodId);
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }
}
