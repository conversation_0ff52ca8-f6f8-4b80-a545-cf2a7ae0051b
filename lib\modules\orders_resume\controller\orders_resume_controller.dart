import 'dart:developer';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:intl/intl.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders/models/orders_footer_model.dart';
import 'package:pharmalink/modules/orders/models/orders_local_data_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_list_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';
import 'package:pharmalink/modules/orders_ideal_mix/models/orders_ideal_mix_model.dart';
import 'package:pharmalink/modules/orders_resume/enuns/status_syncronization_enum.dart';
import 'package:pharmalink/modules/orders_resume/enuns/type_sync_enum.dart';
import 'package:pharmalink/modules/orders_resume/models/file_attachment.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';
import 'package:pharmalink/modules/orders_resume/models/orders_resume_model.dart';
import 'package:pharmalink/modules/orders_resume/models/system_info_model.dart';
import 'package:pharmalink/modules/stores_parameters/enuns/type_order_enum.dart';
import 'package:pharmalink/modules/stores_parameters/models/prazo_pagamento_model.dart';
import 'package:uuid/uuid.dart';

class OrdersResumeController
    extends GetxControllerInstrumentado<OrdersResumeController>
    with TraceableController {
  OrdersResumeController();

  TextEditingController numberClientController = TextEditingController();

  TextEditingController observationsController = TextEditingController();

  TextEditingController dateController = TextEditingController();

  OrdersResumeModel resume = OrdersResumeModel();

  List<OrdersProductsListModel> cartProductsList = [];
  List<CombosOfertaModel> cartCombosOfertaList = [];

  File? selectedFile;
  String? fileName;
  String? fileSize;

  DateTime? limitDate = DateTime.now();
  String? dateStr;
  DateTime? dateAdd;

  OrdersFooterModel footer = OrdersFooterModel(
    qtyReal: 0,
    totalApresentation: 0,
    totalNet: 0,
    totalUnits: 0,
    totalGross: 0,
    discount: 0,
  );

  int tabSelected = 0;

  void setTabSelected(int? value) {
    tabSelected = value ?? 0;
    update();
  }

  List<MixIdealProdutosModel> productCartMixIdeal = [];

  Future<void> initialize() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "initialize - OrdersResumeController",
    );

    orderPaymentTypeController = Get.find<OrderPaymentTypeController>();
    ordersController = Get.find<OrdersController>();
    limitDate = getLimitDateProgrammedOrder();
    resume = OrdersResumeModel(
      createOrder: DateTime.now(),
      isOrderSchedule: false,
      syncOrder: null,
      scheduleDate: [],
      products: [],
      combos: [],
    );

    if (ordersController.isEditOrder) {
      final oldResume = ordersController.syncOrderEdit?.payLoad!.backupOrder;
      if (oldResume != null) {
        observationsController.text = oldResume.observation ?? "";
        resume.observation = observationsController.text;

        numberClientController.text = oldResume.clientNumber ?? "";
        resume.clientNumber = numberClientController.text;

        resume.isOrderSchedule = oldResume.isOrderSchedule ?? false;
        resume.scheduleDate = oldResume.scheduleDate ?? [];
        if (ordersController.syncOrderEdit?.fileAttachment != null) {
          selectedFile = File(
            ordersController.syncOrderEdit?.fileAttachment?.selectedFilePath ??
                "",
          );
          fileName = ordersController.syncOrderEdit?.fileAttachment?.fileName;
          fileSize = ordersController.syncOrderEdit?.fileAttachment?.fileSize;
        }
      }
    }

    try {
      final distributors = orderPaymentTypeController.getDistributorSelecteds();
      if (distributors.isNotEmpty) {
        for (var e in distributors) {
          if (e.distributorsId != null && e.name != null) {
            //remover produtos com quantidade zerada
            cartProductsList =
                cartProductsList.where((element) => element.qtdy! > 0).toList();

            var distinctList =
                cartProductsList
                    .map(
                      (product) => DistinctDistributorPayment(
                        distributorId: product.distributorId,
                        paymentTermId: product.paymentTermId,
                      ),
                    )
                    .toSet() // Remove duplicatas
                    .toList(); // Converte de volta para List, se necessário

            for (var ddp in distinctList) {
              //filtrar pelo distribuidor e prazo de pagamento
              var item = OrdersResumeProductsModel(
                distributorId: e.distributorsId,
                distributorName: e.name!,
                orderTypePayment: getPaymentType(ddp.paymentTermId),
                items: [],
              );

              for (var p
                  in cartProductsList
                      .where(
                        (element) =>
                            element.distributorId == e.distributorsId &&
                            element.paymentTermId == ddp.paymentTermId,
                      )
                      .toList()) {
                if (globalParams.getTypeOrderId() == TyperOrderEnum.especial) {
                  item.items?.add(
                    OrdersResumeProductsItemsModel(
                      productId: p.productId,
                      productIdDun: p.productDunId,
                      code: p.productCode,
                      codelLabel: p.productCodeLabel,
                      discount: p.discountPercent,
                      discountOrigin: p.discountPercent,
                      discountApply: p.discountApply ?? p.discountChange,
                      isEditDiscount: p.isEditDiscount ?? false,
                      name: p.name,
                      photo: p.photo,
                      price: p.priceOrder,
                      qtdy: p.qtdy,
                      qtdyReal: p.qtdyReal,
                      qtdyDun: p.quantidadeDUN,
                      total: p.totalOrder,
                      priceDistributor: p.priceDistributor,
                    ),
                  );
                } else {
                  if (globalParams.getProducts()!.produtosDistribuidores!.any(
                        (element) =>
                            element.idProduto == p.productId &&
                            element.idsDistribuidores!.any(
                              (d) => d == e.distributorsId,
                            ),
                      ) ||
                      globalParams.getProducts()!.produtosDUNDistribuidor!.any(
                        (element) =>
                            element.idProdutoDUN == p.productDunId &&
                            element.idsDistribuidores!.any(
                              (d) => d == e.distributorsId,
                            ),
                      )) {
                    item.items?.add(
                      OrdersResumeProductsItemsModel(
                        productId: p.productId,
                        productIdDun: p.productDunId,
                        code: p.productCode,
                        codelLabel: p.productCodeLabel,
                        isEditDiscount: p.isEditDiscount ?? false,
                        discount: p.discountTotal,
                        discountOrigin: p.discountPercent,
                        discountApply: p.discountAdditional,
                        name: p.name,
                        photo: p.photo,
                        price: p.priceOrder,
                        qtdy: p.qtdy,
                        qtdyReal: p.qtdyReal,
                        qtdyDun: p.quantidadeDUN,
                        total: p.totalOrder,
                        priceDistributor: p.priceDistributor,
                      ),
                    );
                  }
                }
              }
              if (item.items!.isNotEmpty) {
                if (!resume.products!.any(
                  (x) =>
                      x.distributorId == item.distributorId &&
                      x.orderTypePayment?.id == item.orderTypePayment?.id,
                )) {
                  resume.products?.add(item);
                }
              }
            }
            var itemCombo = OrdersResumeProductsModel(
              distributorId: e.distributorsId,
              distributorName: e.name!,
              ordemDePreferencia: e.ordemDePreferencia,
              ordemMelhorAtendimento: e.ordemMelhorAtendimento,
              ordemSelected: e.ordemSelected,
              distribuidor: e.distribuidor,
              isSelected: e.isSelected,
              items: [],
            );

            for (var c
                in cartCombosOfertaList
                    .where(
                      (element) => element.distributorId == e.distributorsId,
                    )
                    .toList()) {
              itemCombo.orderTypePayment = getPaymentType(c.paymentTermId);
              var combo = OrdersResumeProductsItemsModel(
                offerComboId: c.idComboOferta,
                name: c.descricao,
                photo: "",
                price: c.priceOrder,
                qtdy: c.qtdy,
                qtdyReal: c.qtdyReal,
                total: c.totalOrder,
                products: [],
              );
              for (var pr in c.produtos!) {
                combo.products?.add(
                  OrdersResumeProductsItemsModel(
                    productId: pr.idProduto,
                    productIdDun: pr.idProdutoDUN,
                    code: pr.idProdutoDUN! > 0 ? pr.dUN : pr.ean,
                    codelLabel: pr.idProdutoDUN! > 0 ? "DUN" : "EAN",
                    isEditDiscount: false,
                    discount: pr.desconto,
                    discountOrigin: pr.desconto,
                    name: pr.descricao ?? "-",
                    photo: "",
                    price: pr.preco,
                    qtdy: pr.quantidade,
                    qtdyDun: pr.quantidadeDUN,
                    priceDistributor: pr.precoDistribuidor,
                    total: pr.precoLiquido,
                    dUN: pr.dUN,
                  ),
                );
              }
              itemCombo.items?.add(combo);
            }
            if (itemCombo.items!.isNotEmpty) {
              if (itemCombo.items!.isNotEmpty) {
                if (!resume.combos!.any(
                  (x) =>
                      x.distributorId == itemCombo.distributorId &&
                      x.orderTypePayment?.id == itemCombo.orderTypePayment?.id,
                )) {
                  resume.combos?.add(itemCombo);
                }
              }
            }
          }
        }
      }
      if (!ordersController.isEditOrder) {
        loadOrderLocal();
      }
      updateFooter();
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }
  }

  OrdersResumeOrderTypePaymentModel getPaymentType(int? id) {
    List<PrazoPagamentoModel> deadlinePayments =
        globalParams.getDeadlinePaymentList()!;
    if (ordersController.isEditOrder) {
      if (globalParams.getTypeOrderId() == TyperOrderEnum.especial) {
        deadlinePayments =
            ordersController
                .syncOrderEdit!
                .parameters!
                .paymentTypeParametersSpecial!
                .deadlinePayment!;
      }
    }

    final paymentTerm = deadlinePayments.firstWhereOrNull(
      (element) => element.idPrazoPagamento == id,
    );
    if (paymentTerm == null) {
      if (id == 2 || id == null) {
        return OrdersResumeOrderTypePaymentModel(
          id: 2,
          name: "À Prazo",
          code: "APRA",
        );
      } else {
        return OrdersResumeOrderTypePaymentModel(
          id: globalParams.getDeadlinePayment()?.idPrazoPagamento ?? 0,
          name: globalParams.getDeadlinePayment()?.descricao ?? "",
          code: globalParams.getDeadlinePayment()?.codigo ?? "",
        );
      }
    } else {
      return OrdersResumeOrderTypePaymentModel(
        id: paymentTerm.idPrazoPagamento ?? 0,
        name: paymentTerm.descricao ?? "",
        code: paymentTerm.codigo ?? "",
      );
    }
  }

  Future<void> setNumberClient(String? v) async {
    resume.clientNumber = v;
    await saveOrderLocal();
    update();
  }

  Future<void> setObservations(String? v) async {
    resume.observation = v;
    if (v == null) {
      observationsController.text = "";
    }
    await saveOrderLocal();
    update();
  }

  List<OrdersResumeProductsItemsModel> getAllItems(OrdersResumeModel resume) {
    final itemsFromProducts =
        resume.products?.expand((product) => product.items ?? []).toList() ??
        [];
    final itemsFromCombos =
        resume.combos?.expand((combo) => combo.items ?? []).toList() ?? [];
    return [...itemsFromProducts, ...itemsFromCombos];
  }

  void updateFooter() {
    final allItems = getAllItems(resume);

    footer.totalGross = allItems
        .where((element) => element.qtdy! > 0)
        .map((e) => e.price ?? 0)
        .fold(0, (a, b) => a! + b);

    footer.totalNet = allItems
        .where((element) => element.qtdy! > 0)
        .map((e) => e.total ?? 0)
        .fold(0, (a, b) => a! + b);

    footer.totalUnits = allItems
        .where((element) => element.qtdy! > 0)
        .map((e) => e.qtdy ?? 0)
        .fold(0, (a, b) => a! + b);

    footer.qtyReal = allItems
        .where((element) => element.qtdyReal! > 0)
        .map((e) => e.qtdyReal ?? 0)
        .fold(0, (a, b) => a! + b);

    footer.totalApresentation =
        allItems.where((element) => element.qtdy! > 0).length;

    footer.discount =
        footer.totalGross != 0
            ? (((footer.totalGross! - footer.totalNet!) / footer.totalGross!) *
                100)
            : 0;
    update();
  }

  void setDate(String? v) {
    dateStr = v;
    if (dateStr != null && RegExp(r'^\d{2}/\d{2}/\d{4}$').hasMatch(dateStr!)) {
      dateAdd = DateFormat('dd/MM/yyyy').parse(dateStr!);
    } else {
      dateAdd = null;
    }
    update();
  }

  void setDateCalendar(DateTime? date) {
    if (date != null) {
      dateStr = date.formatDate(formatType: DateFormatType.ddMMyyyy);
      dateController.text = dateStr!;
    } else {
      dateStr = "";
      dateController.text = dateStr!;
    }

    dateAdd = date;
    update();
  }

  void removeDate(DateTime date) {
    if (resume.scheduleDate!.contains(date)) {
      resume.scheduleDate!.remove(date);
    }
    update();
  }

  Future<void> saveDate() async {
    DateTime today = DateTime.now();
    if (dateAdd != null) {
      if (dateAdd!.isAfter(limitDate!)) {
        Dialogs.info(
          AppStrings.attention,
          AppStrings.orderResumeDateLimit,
          buttonName: "Entendi".toUpperCase(),
        );
        return;
      }

      if (dateAdd!.isBefore(today) || dateAdd!.isAtSameMomentAs(today)) {
        Dialogs.info(
          AppStrings.attention,
          AppStrings.orderResumeDateLimit,
          buttonName: "Entendi".toUpperCase(),
        );
        return;
      }
      final params =
          generalParameterizationController
              .generalSettingsOrderDiscountRegistration;
      if (params != null) {
        if (params.quantidadeDatasProgramadas! <= resume.scheduleDate!.length) {
          await Dialogs.info(
            AppStrings.attention,
            AppStrings.orderResumeDateLimitParam(
              params.quantidadeDatasProgramadas!,
            ),
            buttonName: "Entendi".toUpperCase(),
          );
          return;
        }
      }

      if (!resume.scheduleDate!.contains(dateAdd!)) {
        resume.scheduleDate!.add(dateAdd!);
        setDateCalendar(null);
      } else {
        setDateCalendar(null);
        await Dialogs.info(
          AppStrings.attention,
          AppStrings.orderResumeDateExists,
          buttonName: "Entendi".toUpperCase(),
        );
      }
    } else {
      await Dialogs.info(
        AppStrings.attention,
        AppStrings.orderResumeDateInvalid,
        buttonName: "Entendi".toUpperCase(),
      );
    }
    await saveOrderLocal();
  }

  void setOrderSchedule(bool? v) {
    if (disableScheduleDate()) {
      SnackbarCustom.snackbarError(
        AppStrings.orderScheduleWithDistributorConditionsNotAllowed,
      );
      return;
    }
    resume.isOrderSchedule = v ?? false;
    update();
  }

  DateTime? getLimitDateProgrammedOrder() {
    if (cartProductsList.isEmpty && cartCombosOfertaList.isEmpty) {
      return null; // Return null if the list is empty
    }
    if (cartProductsList.any((product) => product.limitDate != null)) {
      return cartProductsList
          .where((product) => product.limitDate != null)
          .map((product) => product.limitDate!)
          .reduce((a, b) => a.isBefore(b) ? a : b);
    }
    if (cartCombosOfertaList.any(
      (product) => product.menorDataVigencia != null,
    )) {
      return cartCombosOfertaList
          .where((product) => product.menorDataVigencia != null)
          .map((product) => product.menorDataVigencia!)
          .reduce((a, b) => a.isBefore(b) ? a : b);
    }
    return null;
  }

  Future<void> finishOrder() async {
    String? orderId;
    final loading = PlkLoading();
    return trace('finishOrder', () async {
      var (leaveAction, subAction) = dynatraceAction.subActionReport(
        "finishOrder",
      );
      try {
        if (resume.products!.isEmpty && resume.combos!.isEmpty) {
          await Dialogs.confirm(
            AppStrings.attention,
            AppStrings.orderNoProductsFinish,
            buttonNameCancel: "Descartar".toUpperCase(),
            buttonNameOk: "Selecionar".toUpperCase(),
            onPressedCancel: () {
              appLogWithDynatrace(subAction, "Descartar pedido");
              GetC.close();
              Get.until(
                (route) => Get.currentRoute == RoutesPath.navigationPage,
              );
            },
            onPressedOk: () {
              appLogWithDynatrace(subAction, "Descartar pedido");
              GetC.close();
              Get.until(
                (route) => Get.currentRoute == RoutesPath.navigationPage,
              );
            },
          );
          return;
        }

        if (globalParams.getTypeOrderId() == TyperOrderEnum.especial) {
          final minValueTabloid =
              ordersController.productsTabloid!.valorMinimoPedido ?? 0;

          if (minValueTabloid > 0) {
            if (minValueTabloid > footer.totalNet!) {
              await Dialogs.info(
                AppStrings.attention,
                "O total líquido não pode ser menor do que o valor mínimo do pedido.\nTotal Mínimo: ${minValueTabloid.formatReal()}",
                buttonName: "Entendi".toUpperCase(),
              );
              return;
            }
          }
        } else {
          final param =
              ordersController.isEditOrder
                  ? ordersController
                      .syncOrderEdit!
                      .parameters!
                      .paymentTypeParameters!
                  : globalParams.order.orderParameters.paymentTypeParameters!;
          if (param.valorMinimoDePedido! > 0) {
            if (param.valorMinimoDePedido! > footer.totalNet!) {
              await Dialogs.info(
                AppStrings.attention,
                "O valor mínimo para esse distribuidor não foi atendido:\nTotal Mínimo: ${param.valorMinimoDePedido!.formatReal()} => Valor Pedido: ${footer.totalNet!.formatReal()}",
                buttonName: "Entendi".toUpperCase(),
              );
              return;
            }
          }
        }

        loading.show(title: AppStrings.load);

        if (ordersController.isEditOrder) {
          appLogWithDynatrace(subAction, "Salvar pedido editado");
          orderId = await saveOrderEditToSync();
        } else {
          appLogWithDynatrace(subAction, "Salvar pedido");
          orderId = await saveOrderToSync();
        }

        loading.hide();
        await Dialogs.confirm(
          AppStrings.orderResumeFinishTitle,
          AppStrings.orderResumeFinishMessage,
          onPressedOk: () async {
            appLogWithDynatrace(subAction, "Iniciar sincronização do pedido");
            GetC.close();
            Get.until((route) => Get.currentRoute == RoutesPath.navigationPage);
            await synchronizationsController.onReady();
            synchronizationsController.setHasOrders(true);
            Get.toNamed(
              RoutesPath.synchronizations,
              arguments: {'all': true, 'autostart': true, 'orderId': orderId},
            );
          },
          onPressedCancel: () async {
            appLogWithDynatrace(subAction, "Não sincronizar pedido");
            GetC.close();
            GetC.close();
            if (ordersController.isEditOrder) {
              appLogWithDynatrace(
                subAction,
                "Retornar para tela do Status do Pedido",
              );
              Get.until((route) => Get.currentRoute == RoutesPath.reportOrders);
            } else {
              appLogWithDynatrace(subAction, "Retornar para tela inicial");
              Get.until(
                (route) => Get.currentRoute == RoutesPath.navigationPage,
              );
            }
          },
          buttonNameCancel: "Não".toUpperCase(),
          buttonNameOk: "Sim".toUpperCase(),
        );
      } catch (e, s) {
        loading.hide();
        SnackbarCustom.snackbarError(e.toString());
        appErrorWithDynatrace(subAction, e, s);
        rethrow;
      } finally {
        leaveAction();
      }
    });
  }

  List<OrdersSyncItensDoPedidoModel> buildProducts(
    List<OrdersResumeProductsItemsModel>? items,
  ) {
    if (items == null) {
      return [];
    }

    return items.map((p) {
      return OrdersSyncItensDoPedidoModel(
        descontoAdicional: p.discountApply ?? 0,
        descontoTotal: p.discount ?? 0,
        idProduto: p.productId,
        quantidade: p.qtdy,
        preco: p.price,
        precoDistribuidor: p.priceDistributor,
        dUN: p.productIdDun != null && p.productIdDun! > 0 ? p.code : null,
        qtdeDUN: p.qtdyDun ?? 0,
        qtdeProdutoTotal: (p.qtdy ?? 0) * (p.qtdyDun ?? 1),
        idProdutoDUN: p.productIdDun,
        isDUN: (p.productIdDun ?? 0) > 0,
      );
    }).toList();
  }

  List<OrdersSyncItensDoPedidoModel> buildProdutosCombo(
    List<OrdersResumeProductsItemsModel> skus,
    OrdersResumeProductsItemsModel combo,
  ) {
    return skus.map((obj) {
      final itemDoPedido = OrdersSyncItensDoPedidoModel(
        descontoAdicional:
            globalParams.getTypeOrderId() == TyperOrderEnum.especial
                ? obj.discount ?? 0
                : obj.discountApply ?? 0,
        descontoTotal: obj.discount ?? 0,
        idProduto: obj.productId,
        preco: obj.price,
        quantidade: (obj.qtdy ?? 0) * (combo.qtdy ?? 0),
        dUN: obj.dUN,
        precoDistribuidor: obj.priceDistributor,
        qtdeDUN: (obj.qtdyDun ?? 0),
        qtdeProdutoTotal:
            (obj.qtdy ?? 0) * (combo.qtdy ?? 0) * (obj.qtdyDun ?? 1) == 0
                ? (obj.qtdy ?? 0) * (combo.qtdy ?? 0)
                : (obj.qtdy ?? 0) * (combo.qtdy ?? 0) * (obj.qtdyDun ?? 1),
        idProdutoDUN: obj.productIdDun ?? 0,
        isDUN: (obj.productIdDun ?? 0) > 0,
      );

      return itemDoPedido;
    }).toList(); // Convert the Iterable to a List
  }

  int getTotalCombo({
    required bool isReal,
    required OrdersResumeProductsItemsModel combo,
  }) {
    final items = buildProdutosCombo(combo.products!, combo);
    if (isReal) {
      return items.fold(0, (prev, item) => prev + item.quantidade!);
    } else {
      return items.length;
    }
  }

  String getEnumPrazoPagamento({required String codigo}) {
    if (codigo == "AVIS") {
      return "AVista";
    } else if (codigo == "APRA") {
      return "APrazo";
    } else {
      return "Outros";
    }
  }

  List<OrdersSyncDistribuidoresModel> getDistributorsList() {
    List<OrdersSyncDistribuidoresModel> list = [];
    for (var d in globalParams.getCurrentDistributors()!) {
      list.add(
        OrdersSyncDistribuidoresModel(
          pdvId: globalParams.getCurrentStore()!.idLoja,
          distribuidorId: d.distribuidorId,
          ordemDePreferencia: d.ordemDePreferencia,
          ordemMelhorAtendimento: d.ordemMelhorAtendimento,
          distribuidor: OrdersSyncDistribuidorModel(
            id: d.distribuidorId,
            ativo: d.distribuidor!.ativo,
            valorMinimoDePedido: d.distribuidor!.valorMinimoDePedido,
            nomeFantasia: d.distribuidor!.nomeFantasia,
            razaoSocial: d.distribuidor!.razaoSocial,
            habilitaContaCorrente: d.distribuidor!.habilitaContaCorrente,
          ),
          selected: d.isSelected,
          oldOrdemDePreferencia: d.ordemDePreferencia,
        ),
      );
    }

    return list;
  }

  Future<String?> saveOrderToSync() async {
    return trace('saveOrderToSync', () async {
      var (leaveAction, subAction) = dynatraceAction.subActionReport(
        "Save Order To Sync",
      );
      final agrupadorPedido = const Uuid().v4().toString();
      try {
        List<OrdersSyncItensDoPedidoModel> itensProducts = [];
        List<OrdersResumeProductsItemsModel> itensCombos = [];

        List<OrdersSyncPayLoadModel> payloads =
            resume.products!.map((e) {
              final chaveUnicaPedido = const Uuid().v4().toString();
              final referenciaDePedido = const Uuid().v4().toString();

              final distribuidorSelected =
                  orderPaymentTypeController.usaLooping
                      ? globalParams
                          .getCurrentDistributors()!
                          .where((a) => a.distribuidorId == e.distributorId)
                          .first
                      : globalParams.getCurrentDistributor()!;

              itensProducts.addAll(buildProducts(e.items!));

              return OrdersSyncPayLoadModel(
                isPedidoComboOferta: false,
                agrupadorPedido: agrupadorPedido,
                cNPJ: globalParams.getCurrentStore()!.cNPJ,
                distribuidor: OrdersSyncDistribuidoresModel(
                  pdvId: globalParams.getCurrentStore()!.idLoja,
                  distribuidorId: e.distributorId,
                  ordemDePreferencia: distribuidorSelected.ordemDePreferencia,
                  ordemMelhorAtendimento:
                      distribuidorSelected.ordemMelhorAtendimento,
                  distribuidor: OrdersSyncDistribuidorModel(
                    id: distribuidorSelected.distribuidor!.id,
                    ativo: distribuidorSelected.distribuidor!.ativo,
                    valorMinimoDePedido:
                        distribuidorSelected.distribuidor!.valorMinimoDePedido,
                    nomeFantasia:
                        distribuidorSelected.distribuidor!.nomeFantasia,
                    razaoSocial: distribuidorSelected.distribuidor!.razaoSocial,
                    habilitaContaCorrente:
                        distribuidorSelected
                            .distribuidor!
                            .habilitaContaCorrente,
                  ),
                  selected: distribuidorSelected.isSelected,
                  oldOrdemDePreferencia:
                      distribuidorSelected.ordemDePreferencia,
                ),
                distribuidores: getDistributorsList(),
                distribuidorId: e.distributorId,
                itensDoPedido: buildProducts(e.items!),
                condicaoComercialBaseId:
                    globalParams.getCommercialCondition() != null
                        ? globalParams.getCommercialCondition()!.id
                        : globalParams
                            .getParametrization()!
                            .condicaoComercialBase
                            ?.id,
                datasProgramadas:
                    resume.scheduleDate != null
                        ? resume.scheduleDate!
                            .map(
                              (dt) => dt.formatDate(
                                formatType: DateFormatType.yyyyMMdd,
                              ),
                            )
                            .toList()
                        : [],
                idTabloide: ordersTabloidController.tabloidId ?? 0,
                forcarGerenciamento: false,
                idPrazoPagamento: e.orderTypePayment!.id,
                codigo: e.orderTypePayment!.code,
                prazo: getEnumPrazoPagamento(codigo: e.orderTypePayment!.code!),
                descricao: e.orderTypePayment!.name,
                origem: "Aplicativo",
                pdvId: globalParams.getCurrentStore()!.idLoja,
                razaoSocial: globalParams.getCurrentStore()!.razaoSocial,
                somenteValidar: "false",
                tipoLooping:
                    orderPaymentTypeController.usaLooping
                        ? "LoopingAutomatico"
                        : "",
                tipoPedido: globalParams.getTypeOrderId()?.toString() ?? "1",
                userId: appController.userLogged!.userId,
                chaveUnicaPedido: chaveUnicaPedido,
                referenciaPedido: resume.clientNumber,
                referenciaDePedido: referenciaDePedido,
                produtosCombo: [],
                totalApresentacoesSKU: footer.totalApresentation,
                totalUnidadesSKU: footer.totalUnits,
                totalBrutoSKU: footer.totalGross,
                totalLiquidoSKU: footer.totalNet,
                observacao: resume.observation,
              );
            }).toList();

        if (resume.combos != null && resume.combos!.isNotEmpty) {
          for (var e in resume.combos!) {
            final distribuidorSelected = resume.combos!.firstWhere(
              (element) => element.distributorId == e.distributorId,
            );

            itensCombos.addAll(e.items!);

            for (var c in e.items!) {
              final chaveUnicaPedido = const Uuid().v4().toString();
              final referenciaDePedido = const Uuid().v4().toString();
              payloads.add(
                OrdersSyncPayLoadModel(
                  isPedidoComboOferta: true,
                  idComboOferta: c.offerComboId ?? 0,
                  descricaoComboOferta: c.name,
                  agrupadorPedido: agrupadorPedido,
                  cNPJ: globalParams.getCurrentStore()!.cNPJ,
                  distribuidor: OrdersSyncDistribuidoresModel(
                    pdvId: globalParams.getCurrentStore()!.idLoja,
                    distribuidorId: e.distributorId,
                    ordemDePreferencia: distribuidorSelected.ordemDePreferencia,
                    ordemMelhorAtendimento:
                        distribuidorSelected.ordemMelhorAtendimento,
                    distribuidor: OrdersSyncDistribuidorModel(
                      id: distribuidorSelected.distribuidor!.id,
                      ativo: distribuidorSelected.distribuidor!.ativo,
                      valorMinimoDePedido:
                          distribuidorSelected
                              .distribuidor!
                              .valorMinimoDePedido,
                      nomeFantasia:
                          distribuidorSelected.distribuidor!.nomeFantasia,
                      razaoSocial:
                          distribuidorSelected.distribuidor!.razaoSocial,
                      habilitaContaCorrente:
                          distribuidorSelected
                              .distribuidor!
                              .habilitaContaCorrente,
                    ),
                    selected: distribuidorSelected.isSelected,
                    oldOrdemDePreferencia:
                        distribuidorSelected.ordemDePreferencia,
                  ),
                  distribuidores: getDistributorsList(),
                  distribuidorId: e.distributorId,
                  itensDoPedido: buildProdutosCombo(c.products!, c),
                  condicaoComercialBaseId:
                      globalParams.getCommercialCondition() != null
                          ? globalParams.getCommercialCondition()!.id
                          : globalParams
                              .getParametrization()!
                              .condicaoComercialBase
                              ?.id,
                  datasProgramadas:
                      resume.scheduleDate != null
                          ? resume.scheduleDate!
                              .map(
                                (dt) => dt.formatDate(
                                  formatType: DateFormatType.yyyyMMdd,
                                ),
                              )
                              .toList()
                          : [],
                  idTabloide: ordersTabloidController.tabloidId ?? 0,
                  forcarGerenciamento: false,
                  // idPrazoPagamento:
                  //     globalParams.getDeadlinePayment!.idPrazoPagamento,
                  idPrazoPagamento: e.orderTypePayment!.id,
                  codigo: e.orderTypePayment!.code,
                  descricao: e.orderTypePayment!.name,
                  prazo: getEnumPrazoPagamento(
                    codigo: e.orderTypePayment!.code!,
                  ),
                  origem: "Aplicativo",
                  pdvId: globalParams.getCurrentStore()!.idLoja,
                  razaoSocial: globalParams.getCurrentStore()!.razaoSocial,

                  somenteValidar: "false",
                  tipoLooping:
                      orderPaymentTypeController.usaLooping
                          ? "LoopingAutomatico"
                          : "",
                  tipoPedido: globalParams.getTypeOrderId()?.toString() ?? "1",
                  userId: appController.userLogged!.userId,
                  chaveUnicaPedido: chaveUnicaPedido,
                  referenciaPedido: resume.clientNumber,
                  referenciaDePedido: referenciaDePedido,
                  produtosCombo: [],
                  totalApresentacoesSKU: getTotalCombo(combo: c, isReal: true),
                  totalUnidadesSKU: getTotalCombo(combo: c, isReal: false),
                  totalBrutoSKU: footer.totalGross,
                  totalLiquidoSKU: footer.totalNet,
                  observacao: resume.observation,
                ),
              );
            }
          }
        }

        var orderInfo = OrdersDataModel(
          userId: appController.userLogged!.userId,
          orderId: agrupadorPedido,
          createAt: DateTime.now(),
          workSpace: appController.workspace!.name,
          authorization:
              (appController.userLogged!.isB2c == null ||
                      appController.userLogged!.isB2c == false)
                  ? OrdersSyncAuthorizationModel(
                    user: appController.userLogged!.userName,
                    password: appController.userLogged!.password,
                  )
                  : null,
          authorizationWithToken:
              appController.userLogged!.isB2c == true
                  ? OrdersSyncAuthorizationWithTokenModel(
                    token: appController.userLogged!.accessToken!,
                    clientKey: appController.userLogged!.userId,
                  )
                  : null,
          industryDomain:
              appController.userLogged!.isB2c == true
                  ? appController.workspace!.link!.replaceAll("/api/", "")
                  : null,
          authorizationUri: appController.workspace!.token!,
          // appController.workspace!.token?.replaceAll("/token", ""),
          timeStamp: DateTime.now().toUtc().toIso8601String(),
          orderStatus: 2,
          verb: "POST",
          payLoadUrl: "${appController.workspace!.link}pedidos/enviarPedidoMix",
          payLoad: payloads,
          systemInfo: await SystemInfoModel.create(),
          pushNotification: OrdersSyncPushNotificationModel(
            token: null,
            success: OrdersSyncSuccessModel(
              titleYes: "Sincronização",
              messageYes: "Pedido $agrupadorPedido atualizado.",
              dataYes: OrdersSyncDataYesModel(
                orderId: agrupadorPedido,
                page: "OrderDetailsPage",
                params: OrdersSyncParamsModel(
                  openFromPush: true,
                  orderId: agrupadorPedido,
                ),
              ),
            ),
            error: OrdersSyncErrorModel(
              titleNo: "Sincronização",
              messageNo: "Falha ao atualizar pedido $agrupadorPedido.",
            ),
          ),
          forApproval: false,
        );

        var orderSync = OrdersSyncModel(
          userId: appController.userLogged!.userId,
          orderId: agrupadorPedido,
          createAt: DateTime.now(),
          orderInfo: [orderInfo],
          orderStatus: getStatusSynchronizationValue(
            StatusSynchronization.notSent,
          ),
          workSpaceId: appController.workspace!.workspaceId,
          pdvId: globalParams.getCurrentStore()!.idLoja,
          pdvName: globalParams.getCurrentStore()!.razaoSocial,
          pdvCnpj: globalParams.getCurrentStore()!.cNPJ,
          backupOrder: resume,
          isOnline: !ordersController.isOfflineData,
        );

        var syncModel = SyncronizationModel(
          userId: appController.userLogged!.userId,
          transactionKey: agrupadorPedido,
          status: getStatusSynchronizationValue(StatusSynchronization.notSent),
          workSpaceId: appController.workspace!.workspaceId,
          step: 1,
          createAt: DateTime.now(),
          payLoad: orderSync,
          type: getTypeSyncValue(TypeSyncEnum.pedido),
          parameters: globalParams.order.orderParameters,
          commercialConditionParameter: globalParams.getCommercialCondition(),
          isOnline: !ordersController.isOfflineData,
          typeOrder: globalParams.getTypeOrderId(),
          storeData: globalParams.getCurrentStore()!,
          fileAttachment:
              selectedFile != null
                  ? FileAttachment(
                    selectedFilePath: selectedFile?.path,
                    fileName: fileName,
                    fileSize: fileSize,
                    status: FileAttachmentStatus.waiting,
                  )
                  : null,
        );

        await dbContext
            .withControllerAction(this)
            .addData(
              clearCurrentData: true,
              data: syncModel,
              hashCode: syncModel.transactionKey,
              workspaceId: appController.workspace?.workspaceId,
              storeId: globalParams.getCurrentStore()?.idLoja,
              userId: appController.userLogged!.userId,
              key: DatabaseModels.syncronization,
            );

        //Remover o pedido local da base
        await OrdersLocalDataModel().deleteOrder(
          storeId: globalParams.getCurrentStore()?.idLoja ?? 0,
          typeOrder: globalParams.getTypeOrderId()!,
          hashCode:
              globalParams.getTypeOrderId()! == TyperOrderEnum.especial
                  ? "2:${ordersTabloidController.tabloidId ?? 0}"
                  : null,
        );
        globalParams.order.orderLocal = null;
        globalParams.order.orderLocalFinish = true;
      } catch (e, s) {
        appErrorWithDynatrace(subAction, e, s);
        rethrow;
      } finally {
        leaveAction();
      }
      return agrupadorPedido;
    });
  }

  Future<String?> saveOrderEditToSync() async {
    return trace('saveOrderEditToSync', () async {
      var (leaveAction, subAction) = dynatraceAction.subActionReport(
        "Save Order Edit To Sync",
      );
      final agrupadorPedido = const Uuid().v4().toString();
      try {
        final oldAgrupadorPedido =
            ordersController.syncOrderEdit!.transactionKey!;

        List<OrdersSyncItensDoPedidoModel> itensProducts = [];
        List<OrdersResumeProductsItemsModel> itensCombos = [];

        int? condicaoComercialId =
            ordersController.syncOrderEdit?.commercialConditionParameter?.id ??
            ordersController
                .syncOrderEdit
                ?.parameters
                ?.paymentTypeParameters
                ?.condicaoComercialBaseId;

        List<OrdersSyncPayLoadModel> payloads =
            resume.products!.map((e) {
              final chaveUnicaPedido = const Uuid().v4().toString();
              final referenciaDePedido = const Uuid().v4().toString();

              final distribuidorEdit =
                  ordersController.editDistributors
                      .where((a) => a.distributorsId == e.distributorId)
                      .first;

              final distribuidorSelected =
                  distribuidorEdit.payLoad!.distribuidor!;
              itensProducts.addAll(buildProducts(e.items!));

              return OrdersSyncPayLoadModel(
                isPedidoComboOferta: false,
                agrupadorPedido: agrupadorPedido,
                cNPJ: distribuidorEdit.payLoad!.cNPJ,
                distribuidor: OrdersSyncDistribuidoresModel(
                  pdvId: distribuidorEdit.payLoad!.pdvId,
                  distribuidorId: e.distributorId,
                  ordemDePreferencia: distribuidorSelected.ordemDePreferencia,
                  ordemMelhorAtendimento:
                      distribuidorSelected.ordemMelhorAtendimento,
                  distribuidor: OrdersSyncDistribuidorModel(
                    id: distribuidorSelected.distribuidor!.id,
                    ativo: distribuidorSelected.distribuidor!.ativo,
                    valorMinimoDePedido:
                        distribuidorSelected.distribuidor!.valorMinimoDePedido,
                    nomeFantasia:
                        distribuidorSelected.distribuidor!.nomeFantasia,
                    razaoSocial: distribuidorSelected.distribuidor!.razaoSocial,
                    habilitaContaCorrente:
                        distribuidorSelected
                            .distribuidor!
                            .habilitaContaCorrente,
                  ),
                  selected: true,
                  oldOrdemDePreferencia:
                      distribuidorSelected.ordemDePreferencia,
                ),
                distribuidores: distribuidorEdit.payLoad!.distribuidores,
                distribuidorId: e.distributorId,
                itensDoPedido: buildProducts(e.items!),
                condicaoComercialBaseId: condicaoComercialId,
                datasProgramadas:
                    resume.scheduleDate != null
                        ? resume.scheduleDate!
                            .map(
                              (dt) => dt.formatDate(
                                formatType: DateFormatType.yyyyMMdd,
                              ),
                            )
                            .toList()
                        : [],
                idTabloide: ordersTabloidController.tabloidId ?? 0,
                forcarGerenciamento: false,
                idPrazoPagamento: e.orderTypePayment!.id,
                codigo: e.orderTypePayment!.code,
                prazo: getEnumPrazoPagamento(codigo: e.orderTypePayment!.code!),
                descricao: e.orderTypePayment!.name,
                origem: "Aplicativo",
                pdvId: distribuidorEdit.payLoad!.pdvId,
                razaoSocial: distribuidorEdit.payLoad!.razaoSocial,
                somenteValidar: "false",
                tipoLooping:
                    orderPaymentTypeController.usaLooping
                        ? "LoopingAutomatico"
                        : "",
                tipoPedido: globalParams.getTypeOrderId()?.toString() ?? "1",
                userId: appController.userLogged!.userId,
                chaveUnicaPedido: chaveUnicaPedido,
                referenciaPedido: resume.clientNumber,
                referenciaDePedido: referenciaDePedido,
                produtosCombo: [],
                totalApresentacoesSKU: footer.totalApresentation,
                totalUnidadesSKU: footer.totalUnits,
                totalBrutoSKU: footer.totalGross,
                totalLiquidoSKU: footer.totalNet,
                observacao: resume.observation,
              );
            }).toList();

        if (resume.combos != null && resume.combos!.isNotEmpty) {
          for (var e in resume.combos!) {
            final distribuidorSelected = resume.combos!.firstWhere(
              (element) => element.distributorId == e.distributorId,
            );

            final distribuidorEdit =
                ordersController.editDistributors
                    .where((a) => a.distributorsId == e.distributorId)
                    .first;

            itensCombos.addAll(e.items!);

            for (var c in e.items!) {
              final chaveUnicaPedido = const Uuid().v4().toString();
              final referenciaDePedido = const Uuid().v4().toString();
              payloads.add(
                OrdersSyncPayLoadModel(
                  isPedidoComboOferta: true,
                  idComboOferta: c.offerComboId ?? 0,
                  descricaoComboOferta: c.name,
                  agrupadorPedido: agrupadorPedido,
                  cNPJ: distribuidorEdit.payLoad!.cNPJ,
                  distribuidor: OrdersSyncDistribuidoresModel(
                    pdvId: distribuidorEdit.payLoad!.pdvId,
                    distribuidorId: e.distributorId,
                    ordemDePreferencia:
                        distribuidorSelected.ordemDePreferencia ?? 1,
                    ordemMelhorAtendimento:
                        distribuidorSelected.ordemMelhorAtendimento,
                    distribuidor: OrdersSyncDistribuidorModel(
                      id: distribuidorSelected.distribuidor!.id,
                      ativo: distribuidorSelected.distribuidor!.ativo,
                      valorMinimoDePedido:
                          distribuidorSelected
                              .distribuidor!
                              .valorMinimoDePedido,
                      nomeFantasia:
                          distribuidorSelected.distribuidor!.nomeFantasia,
                      razaoSocial:
                          distribuidorSelected.distribuidor!.razaoSocial,
                      habilitaContaCorrente:
                          distribuidorSelected
                              .distribuidor!
                              .habilitaContaCorrente,
                    ),
                    selected: distribuidorSelected.isSelected,
                    oldOrdemDePreferencia:
                        distribuidorSelected.ordemDePreferencia,
                  ),
                  distribuidores: distribuidorEdit.payLoad!.distribuidores,
                  distribuidorId: e.distributorId,
                  itensDoPedido: buildProdutosCombo(c.products!, c),
                  condicaoComercialBaseId: condicaoComercialId,
                  datasProgramadas:
                      resume.scheduleDate != null
                          ? resume.scheduleDate!
                              .map(
                                (dt) => dt.formatDate(
                                  formatType: DateFormatType.yyyyMMdd,
                                ),
                              )
                              .toList()
                          : [],
                  idTabloide: ordersTabloidController.tabloidId ?? 0,
                  forcarGerenciamento: false,
                  // idPrazoPagamento:
                  //     globalParams.getDeadlinePayment!.idPrazoPagamento,
                  idPrazoPagamento: e.orderTypePayment!.id,
                  codigo: e.orderTypePayment!.code,
                  descricao: e.orderTypePayment!.name,
                  prazo: getEnumPrazoPagamento(
                    codigo: e.orderTypePayment!.code!,
                  ),
                  origem: "Aplicativo",
                  pdvId: distribuidorEdit.payLoad!.pdvId,
                  razaoSocial: distribuidorEdit.payLoad!.razaoSocial,

                  somenteValidar: "false",
                  tipoLooping:
                      orderPaymentTypeController.usaLooping
                          ? "LoopingAutomatico"
                          : "",
                  tipoPedido: globalParams.getTypeOrderId()?.toString() ?? "1",
                  userId: appController.userLogged!.userId,
                  chaveUnicaPedido: chaveUnicaPedido,
                  referenciaPedido: resume.clientNumber,
                  referenciaDePedido: referenciaDePedido,
                  produtosCombo: [],
                  totalApresentacoesSKU: getTotalCombo(combo: c, isReal: true),
                  totalUnidadesSKU: getTotalCombo(combo: c, isReal: false),
                  totalBrutoSKU: footer.totalGross,
                  totalLiquidoSKU: footer.totalNet,
                  observacao: resume.observation,
                ),
              );
            }
          }
        }

        var orderInfo = OrdersDataModel(
          userId: appController.userLogged!.userId,
          orderId: agrupadorPedido,
          createAt: DateTime.now(),
          workSpace: appController.workspace!.name,
          authorization:
              (appController.userLogged!.isB2c == null ||
                      appController.userLogged!.isB2c == false)
                  ? OrdersSyncAuthorizationModel(
                    user: appController.userLogged!.userName,
                    password: appController.userLogged!.password,
                  )
                  : null,
          authorizationWithToken:
              appController.userLogged!.isB2c == true
                  ? OrdersSyncAuthorizationWithTokenModel(
                    token: appController.userLogged!.accessToken!,
                    clientKey: appController.userLogged!.userId,
                  )
                  : null,
          industryDomain:
              appController.userLogged!.isB2c == true
                  ? appController.workspace!.link!.replaceAll("/api/", "")
                  : null,
          authorizationUri: appController.workspace!.token!,
          // appController.workspace!.token?.replaceAll("/token", ""),
          timeStamp: DateTime.now().toUtc().toIso8601String(),
          orderStatus: 2,
          verb: "POST",
          payLoadUrl: "${appController.workspace!.link}pedidos/enviarPedidoMix",
          payLoad: payloads,
          systemInfo: await SystemInfoModel.create(),
          pushNotification: OrdersSyncPushNotificationModel(
            token: null,
            success: OrdersSyncSuccessModel(
              titleYes: "Sincronização",
              messageYes: "Pedido $agrupadorPedido atualizado.",
              dataYes: OrdersSyncDataYesModel(
                orderId: agrupadorPedido,
                page: "OrderDetailsPage",
                params: OrdersSyncParamsModel(
                  openFromPush: true,
                  orderId: agrupadorPedido,
                ),
              ),
            ),
            error: OrdersSyncErrorModel(
              titleNo: "Sincronização",
              messageNo: "Falha ao atualizar pedido $agrupadorPedido.",
            ),
          ),
          forApproval: false,
        );

        var orderSync = OrdersSyncModel(
          userId: appController.userLogged!.userId,
          orderId: agrupadorPedido,
          createAt: DateTime.now(),
          orderInfo: [orderInfo],
          orderStatus: getStatusSynchronizationValue(
            StatusSynchronization.notSent,
          ),
          workSpaceId: appController.workspace!.workspaceId,
          pdvId: ordersController.syncOrderEdit!.payLoad!.pdvId,
          pdvName: ordersController.syncOrderEdit!.payLoad!.pdvName,
          pdvCnpj: ordersController.syncOrderEdit!.payLoad!.pdvCnpj,
          backupOrder: resume,
          isOnline: !ordersController.isOfflineData,
        );

        var syncModel = SyncronizationModel(
          userId: appController.userLogged!.userId,
          createAt: DateTime.now(),
          transactionKey: agrupadorPedido,
          status: getStatusSynchronizationValue(StatusSynchronization.notSent),
          workSpaceId: appController.workspace!.workspaceId,
          step: 1,
          payLoad: orderSync,
          type: getTypeSyncValue(TypeSyncEnum.pedido),
          parameters: ordersController.syncOrderEdit!.parameters,
          commercialConditionParameter:
              ordersController.syncOrderEdit!.commercialConditionParameter,
          isOnline: !ordersController.isOfflineData,
          typeOrder: ordersController.syncOrderEdit!.typeOrder,
          storeData: ordersController.syncOrderEdit!.storeData,
          fileAttachment:
              selectedFile != null
                  ? FileAttachment(
                    selectedFilePath: selectedFile?.path,
                    fileName: fileName,
                    fileSize: fileSize,
                    status: FileAttachmentStatus.waiting,
                  )
                  : null,
        );

        //apagar o registro original
        await dbContext.deleteByKey(
          hashCode: oldAgrupadorPedido,
          workspaceId: appController.workspace?.workspaceId,
          storeId: syncModel.payLoad!.pdvId,
          userId: appController.userLogged!.userId,
          key: DatabaseModels.syncronization,
        );
        await dbContext
            .withControllerAction(this)
            .addData(
              clearCurrentData: true,
              data: syncModel,
              hashCode: syncModel.transactionKey,
              workspaceId: appController.workspace?.workspaceId,
              storeId: syncModel.payLoad!.pdvId,
              userId: appController.userLogged!.userId,
              key: DatabaseModels.syncronization,
            );
      } catch (e, s) {
        appErrorWithDynatrace(subAction, e, s);
        rethrow;
      } finally {
        leaveAction();
      }
      return agrupadorPedido;
    });
  }

  String getTypeOrder() {
    switch (globalParams.getTypeOrderId()) {
      case TyperOrderEnum.especial:
        return "ESP";
      case TyperOrderEnum.rep:
        return "REP";
      case TyperOrderEnum.padrao:
        return "PAD";
      default:
        return "PAD";
    }
  }

  Future<void> removeProduct(OrdersResumeProductsItemsModel product) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "Remove Product",
    );
    try {
      await Dialogs.confirm(
        AppStrings.attention,
        AppStrings.orderRemoveItem,
        buttonNameOk: "SIM",
        buttonNameCancel: "CANCELAR",
        onPressedOk: () async {
          subAction.reportEvent("SIM");
          GetC.close();
          ordersController = Get.find<OrdersController>();
          if (ordersController.productListFull.any(
            (element) =>
                element.productId == product.productId &&
                element.productCode == product.code,
          )) {
            ordersController.productListFull
                .where(
                  (element) =>
                      element.productId == product.productId &&
                      element.productCode == product.code,
                )
                .map((e) async {
                  await ordersController.setQtdyZero(e);
                })
                .toList();
          }
          ordersController.productList = List.from(
            ordersController.productListFull,
          );

          resume.products?.map((p) {
            p.items?.removeWhere((element) => element == product);
          }).toList();
          resume.products?.removeWhere((p) => p.items!.isEmpty);
          updateFooter();
        },
      );
    } finally {
      leaveAction();
    }
  }

  Future<void> removeProductCombo(
    OrdersResumeProductsItemsModel product,
  ) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "Remove Product Combo",
    );
    try {
      await Dialogs.confirm(
        AppStrings.attention,
        AppStrings.orderRemoveItemCombo,
        buttonNameOk: "SIM",
        buttonNameCancel: "CANCELAR",
        onPressedOk: () async {
          subAction.reportEvent("SIM");
          GetC.close();
          ordersComboController = Get.find<OrdersComboController>();
          if (ordersComboController.combosOferta.any(
            (element) => element.idComboOferta == product.offerComboId,
          )) {
            var item = ordersComboController.combosOferta.firstWhereOrNull(
              (element) => element.idComboOferta == product.offerComboId,
            );
            if (item != null) {
              await ordersComboController.setQtdyZero(item);
              item.qtdy = 0;
              item.qtdyController!.text = "0";
            }
          }

          resume.combos?.map((p) {
            p.items?.removeWhere((element) => element == product);
          }).toList();
          resume.combos?.removeWhere((p) => p.items!.isEmpty);

          updateFooter();
        },
      );
    } finally {
      leaveAction();
    }
  }

  bool disableScheduleDate() {
    final paramGeneral =
        generalParameterizationController
            .generalSettingsOrderDiscountRegistration!;
    final condicaoComercialCustom =
        [1, 3].contains(paramGeneral.condicaoComercialDistribuidor) &&
        globalParams.order.orderParameters.typeOrderId !=
            TyperOrderEnum.especial;

    return condicaoComercialCustom &&
        globalParams.order.commercialConditionSelected != null;
  }

  bool enableScheduleDate(int typeOrder) {
    if (generalParameterizationController
            .generalSettingsOrderDiscountRegistration ==
        null) {
      return false;
    }

    final params =
        generalParameterizationController
            .generalSettingsOrderDiscountRegistration!;

    if (params.tiposPedido == null || params.tiposPedido!.isEmpty) {
      return false;
    }

    final typeOrderItem = params.tiposPedido!.firstWhereOrNull(
      (element) => element.id == typeOrder,
    );

    if (typeOrderItem == null || !typeOrderItem.selecionado!) return false;

    final profileExists = params.perfisProgramacaoPedido!.firstWhereOrNull(
      (element) => element.descricao == appController.userLogged!.perfil,
    );
    if (profileExists == null) return false;

    return profileExists.selecionado!;
  }

  int getComboProductQty(
    OrdersResumeProductsItemsModel product,
    int comboQtdy,
  ) {
    return ((product.qtdy ?? 0) * comboQtdy);
  }

  double getComboProductPrice(
    OrdersResumeProductsItemsModel product,
    int comboQtdy,
    bool isTotal,
  ) {
    double result = 0;
    if (isTotal) {
      result = ((product.total ?? 0) * ((product.qtdy ?? 0) * comboQtdy));
    } else {
      result = ((product.price ?? 0) * ((product.qtdy ?? 0) * comboQtdy));
    }

    result = double.parse((result + 0.005).toStringAsFixed(2));

    return result;
  }

  void loadOrderLocal() {
    numberClientController.text = "";
    observationsController.text = "";
    if (!ordersController.isEditOrder) {
      if (globalParams.order.orderLocal != null) {
        String clientNumber = globalParams.order.orderLocal!.clientNumber ?? "";
        resume.clientNumber = clientNumber;
        numberClientController.text = clientNumber;

        String observation = globalParams.order.orderLocal!.observation ?? "";
        resume.observation = observation;
        observationsController.text = observation;

        resume.isOrderSchedule =
            globalParams.order.orderLocal!.isOrderSchedule ?? false;
        resume.scheduleDate = globalParams.order.orderLocal!.scheduleDate ?? [];
      }
    }
  }

  Future<void> saveOrderLocal() async {
    if (!ordersController.isEditOrder && !globalParams.order.orderLocalFinish) {
      if (globalParams.order.orderLocal != null) {
        // Atualiza os campos do pedido local
        globalParams.order.orderLocal!
          ..clientNumber = resume.clientNumber
          ..observation = resume.observation
          ..scheduleDate = resume.scheduleDate
          ..isOrderSchedule = resume.isOrderSchedule
          ..combosOferta = globalParams.order.orderLocal?.combosOferta
          ..productListFull = globalParams.order.orderLocal?.productListFull;

        // Salva ou atualiza o pedido local
        await globalParams.order.orderLocal!.saveOrder(
          storeId: globalParams.order.orderLocal!.storeId!,
          typeOrder: globalParams.order.orderLocal!.parameters!.typeOrderId!,
          hashCode:
              globalParams.order.orderParameters.typeOrderId! ==
                      TyperOrderEnum.especial
                  ? "2:${ordersTabloidController.tabloidId}"
                  : null,
        );
      }
    }
  }

  Future<void> pickFile(FileType fileType) async {
    if (selectedFile != null) return;
    List<String> allowedExtensions = [
      'pdf',
      'doc',
      'docx',
      'xls',
      'xlsx',
      'jpg',
      'jpeg',
      'png',
      'txt',
      'csv',
      'webp',
    ];
    try {
      FilePickerResult? result;

      // Use FileType.custom for all cases to ensure consistent behavior
      result = await FilePicker.platform.pickFiles(
        type: fileType,
        allowedExtensions:
            fileType == FileType.media ? null : allowedExtensions,
        dialogTitle: "Selecione um arquivo",
        lockParentWindow: true,
      );

      if (result != null) {
        File file = File(result.files.single.path!);
        int sizeInBytes = await file.length();
        double sizeInMb = sizeInBytes / (1024 * 1024);

        if (sizeInMb > getAttachmentMbAllowed()) {
          SnackbarCustom.snackbarError(
            'Não foi possível anexar o documento, o tamanho máximo permitido é de ${getAttachmentMbAllowed()} MB',
          );

          return;
        }
        String fileExtension =
            result.files.single.extension?.toLowerCase() ?? '';

        if (!allowedExtensions.contains(fileExtension)) {
          SnackbarCustom.snackbarError(
            'Não foi possível anexar o arquivo. As extensões permitidas são: ${allowedExtensions.join(", ")}',
          );

          return;
        }

        selectedFile = file;
        fileName = result.files.single.name;
        fileSize = '${sizeInMb.toStringAsFixed(2)} MB';
        update();

        SnackbarCustom.snackbarSucess('Arquivo', 'Arquivo anexado com sucesso');
      }
    } catch (e) {
      SnackbarCustom.snackbarError('Erro ao selecionar arquivo');
    }
  }

  Future<void> removeFile() async {
    await GetC.closeSnack();
    Dialogs.confirm(
      AppStrings.confirmation,
      'Deseja remover o arquivo anexado?',
      buttonNameOk: "Confirmar".toUpperCase(),
      buttonNameCancel: "Cancelar".toUpperCase(),
      onPressedCancel: () {
        GetC.close();
      },
      onPressedOk: () {
        if (selectedFile != null) {
          selectedFile = null;
          fileName = null;
          fileSize = null;
          update();
          GetC.close();
          SnackbarCustom.snackbarSucess(
            'Arquivo',
            'Arquivo removido com sucesso',
          );
        } else {
          GetC.close();
        }
      },
    );
  }

  double getAttachmentMbAllowed() {
    return generalParameterizationController
            .generalSettingsOrderDiscountRegistration
            ?.incluirAnexoPedidoTamanhoMb ??
        5;
  }

  bool isOrderAttachmentAllowed() {
    return generalParameterizationController
            .generalSettingsOrderDiscountRegistration
            ?.incluirAnexoPedido ??
        false;
  }

  Future<void> openCameraPicker() async {
    if (!await appController.checkCameraPermission()) {
      Get.toNamed(RoutesPath.permissionRequest)?.then((data) async {
        if (data) {
          await _handleCameraPicker();
        }
      });
    } else {
      await _handleCameraPicker();
    }
  }

  Future<void> _handleCameraPicker() async {
    Get.toNamed(RoutesPath.cameraPicker)?.then((data) async {
      if (data != null) {
        //await setPicture(item, data);
        final xFile = data.file;
        selectedFile = File(xFile.path);
        fileName = xFile.name;
        final sizeInMb = selectedFile!.lengthSync() / (1024 * 1024);

        if (sizeInMb > getAttachmentMbAllowed()) {
          SnackbarCustom.snackbarError(
            'Não foi possível anexar o documento, o tamanho máximo permitido é de ${getAttachmentMbAllowed()} MB',
          );
          selectedFile = null;
          fileName = null;
          fileSize = null;
          update();
          return;
        }

        fileSize = '${sizeInMb.toStringAsFixed(4)} MB';
        update();
        SnackbarCustom.snackbarSucess('Arquivo', 'Arquivo anexado com sucesso');
      }
    });
  }
}
