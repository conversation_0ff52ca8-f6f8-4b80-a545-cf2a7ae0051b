import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:archive/archive.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:sqlite_explorer/config/databases/ilocal_db.dart';

class SqfLiteHub {
  static Future<void> init() async {
    if (Platform.isWindows) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }
  }
}

Database? _database;

class LocalDb extends ILocalDb {
  String _dbPath;

  LocalDb(this._dbPath);

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initializeDb(_dbPath);
    return _database!;
  }

  @override
  Future<String> get databasePath async {
    final dbpath = await getDatabasesPath();
    return join(dbpath, _dbPath);
  }

  Future<Database> _initializeDb(String filePath) async {
    final dbpath = await getDatabasesPath();
    final path = join(dbpath, filePath);
    return await openDatabase(
      path,
      version: 2,
      onCreate: _createDB,
      onUpgrade: _onUpgrade,
    );
  }

  FutureOr<void> _createDB(Database db, int version) async {
    db.execute('''
              CREATE TABLE LocalData (id INTEGER PRIMARY KEY,
              KEY VARCHAR(150) NOT NULL,
              WORKSPACE_ID INT NULL,
              STORE_ID INT NULL,
              USERID VARCHAR(50) NULL,
              HASHCODE VARCHAR(255) NULL,
              VALUE JSON NOT NULL,
              IS_ONLINE BIT NULL,
              CREATEAT DATETIME NULL)
              ''');
    log("Database created successfully");
  }

  FutureOr<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Adicione aqui as alterações para a versão 2
      var columns = await db.rawQuery('PRAGMA table_info(LocalData)');
      bool isOnlineColumnExists =
          columns.any((column) => column['name'] == 'IS_ONLINE');

      if (!isOnlineColumnExists) {
        await db.execute('ALTER TABLE LocalData ADD COLUMN IS_ONLINE BIT NULL');
      }
    }
    if (oldVersion < 3) {
      // Adicione aqui as alterações para a versão 3
      var columns = await db.rawQuery('PRAGMA table_info(LocalData)');
      bool createAtColumnExists =
          columns.any((column) => column['name'] == 'CREATEAT');

      if (!createAtColumnExists) {
        await db
            .execute('ALTER TABLE LocalData ADD COLUMN CREATEAT DATETIME NULL');
      }
    }
  }

  @override
  Future<void> clearDataBase() async {
    final db = await database;
    db.rawQuery('PRAGMA busy_timeout=120000');
    await db.rawDelete('DELETE FROM LocalData ');
  }

  @override
  Future<void> deleteByKey({
    String? key,
    int? workspaceId,
    String? userId,
    int? storeId,
    String? hashCode,
    bool? isOnline,
    bool? isLog,
  }) async {
    final db = await database;
    db.rawQuery('PRAGMA busy_timeout=120000');

    final whereClauses = <String>[];
    final whereArgs = <dynamic>[];

    if (key != null) {
      whereClauses.add('KEY = ?');
      whereArgs.add(key);
    }

    if (workspaceId != null) {
      whereClauses.add('WORKSPACE_ID = ?');
      whereArgs.add(workspaceId);
    }

    if (storeId != null) {
      whereClauses.add('STORE_ID = ?');
      whereArgs.add(storeId);
    }
    if (userId != null) {
      whereClauses.add('USERID = ?');
      whereArgs.add(userId);
    }
    if (hashCode != null) {
      whereClauses.add('HASHCODE = ?');
      whereArgs.add(hashCode);
    }

    if (isOnline != null) {
      whereClauses.add('IS_ONLINE = ?');
      whereArgs.add(isOnline);
    }

    if (whereClauses.isEmpty) {
      throw ArgumentError('Pelo menos um parâmetro deve ser fornecido.');
    }

    final whereStatement = whereClauses.join(' AND ');
    final sql = 'DELETE FROM LocalData WHERE $whereStatement';

    await db.rawDelete(sql, whereArgs);
  }

  @override
  Future<void> addData({
    String? key,
    int? workspaceId,
    String? userId,
    int? storeId,
    String? hashCode,
    dynamic data,
    bool? clearCurrentData,
    bool? isOnline,
    bool? isLog,
  }) async {
    try {
      final db = await database;

      db.rawQuery('PRAGMA busy_timeout=120000');

      if (clearCurrentData == true) {
        await deleteByKey(
          key: key,
          workspaceId: workspaceId,
          userId: userId,
          storeId: storeId,
          hashCode: hashCode,
          isOnline: isOnline,
          isLog: true,
        );
      }

      if (data is List) {
        await _batchInsert(db, key, workspaceId, storeId, userId, hashCode,
            data, isOnline, isLog);
      } else {
        await _singleInsert(db, key, workspaceId, storeId, userId, hashCode,
            data, isOnline, isLog);
      }
    } catch (e) {
      log(e.toString());
    }
  }

  Future<void> _batchInsert(
    Database db,
    String? key,
    int? workspaceId,
    int? storeId,
    String? userId,
    String? hashCode,
    List data,
    bool? isOnline,
    bool? isLog,
  ) async {
    final batch = db.batch();

    for (var element in data) {
      if (element is List) {
        for (var item in element) {
          _addInsertToBatch(batch, key, workspaceId, storeId, userId, hashCode,
              item, isOnline, isLog);
        }
      } else {
        _addInsertToBatch(batch, key, workspaceId, storeId, userId, hashCode,
            element, isOnline, isLog);
      }
    }

    await batch.commit(noResult: true);
  }

  void _addInsertToBatch(
    Batch batch,
    String? key,
    int? workspaceId,
    int? storeId,
    String? userId,
    String? hashCode,
    dynamic data,
    bool? isOnline,
    bool? isLog,
  ) {
    final jsonData = jsonEncode(data);
    final compressedData = GZipEncoder().encode(utf8.encode(jsonData));
    final compressedDataString = base64Encode(compressedData!);
    batch.insert(
      'LocalData',
      {
        'KEY': key,
        'WORKSPACE_ID': workspaceId,
        'STORE_ID': storeId,
        'USERID': userId,
        'HASHCODE': hashCode,
        'VALUE': compressedDataString,
        'IS_ONLINE': isOnline,
        'CREATEAT': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<void> _singleInsert(
    Database db,
    String? key,
    int? workspaceId,
    int? storeId,
    String? userId,
    String? hashCode,
    dynamic data,
    bool? isOnline,
    bool? isLog,
  ) async {
    final jsonData = jsonEncode(data);
    final compressedData = GZipEncoder().encode(utf8.encode(jsonData));
    final compressedDataString = base64Encode(compressedData!);
    await db.insert(
      'LocalData',
      {
        'KEY': key,
        'WORKSPACE_ID': workspaceId,
        'STORE_ID': storeId,
        'USERID': userId,
        'HASHCODE': hashCode,
        'VALUE':
            compressedDataString, // Armazena os dados comprimidos como base64
        'IS_ONLINE': isOnline,
        'CREATEAT': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  @override
  Future<List<dynamic>> readAllByKey({
    String? key,
    int? workspaceId,
    String? userId,
    int? storeId,
    String? hashCode,
    bool? isOnline,
    int? limit,
    int? offset,
  }) async {
    try {
      final db = await database;

      db.rawQuery('PRAGMA busy_timeout=120000');

      final whereClauses = <String>[];
      final whereArgs = <dynamic>[];

      if (key != null) {
        whereClauses.add('KEY = ?');
        whereArgs.add(key);
      }

      if (workspaceId != null) {
        whereClauses.add('WORKSPACE_ID = ?');
        whereArgs.add(workspaceId);
      }

      if (storeId != null) {
        whereClauses.add('STORE_ID = ?');
        whereArgs.add(storeId);
      }
      if (userId != null) {
        whereClauses.add('USERID = ?');
        whereArgs.add(userId);
      }
      if (hashCode != null) {
        whereClauses.add('HASHCODE = ?');
        whereArgs.add(hashCode);
      }
      if (isOnline != null) {
        whereClauses.add('IS_ONLINE = ?');
        whereArgs.add(isOnline);
      }

      if (whereClauses.isEmpty) {
        throw ArgumentError('Pelo menos um parâmetro deve ser fornecido.');
      }

      final whereStatement = whereClauses.join(' AND ');
      var sql = 'SELECT * FROM LocalData WHERE $whereStatement';

      if (limit != null) {
        sql += ' LIMIT $limit';
      }
      if (offset != null) {
        sql += ' OFFSET $offset';
      }

      final data = await db.rawQuery(sql, whereArgs);

      // Descomprimir os dados se necessário
      final decompressedData = data.map((row) {
        final value = row['VALUE'] as String?;
        if (value == null) {
          return row;
        }
        try {
          final compressedValue = base64Decode(value);
          final decompressedValue =
              utf8.decode(GZipDecoder().decodeBytes(compressedValue));
          return {
            ...row,
            'VALUE': decompressedValue,
          };
        } catch (e) {
          // Se a descompressão falhar, assume que os dados não estão comprimidos
          return {
            ...row,
            'VALUE': value,
          };
        }
      }).toList();

      return decompressedData;
    } catch (e) {
      log("Erro ao ler dados por chave: $e");
      rethrow;
    }
  }

  @override
  Future<List<dynamic>> readAllData({int? limit, int? offset}) async {
    try {
      final db = await database;

      db.rawQuery('PRAGMA busy_timeout=120000');

      var sql = 'SELECT * FROM LocalData';

      if (limit != null) {
        sql += ' LIMIT $limit';
      }
      if (offset != null) {
        sql += ' OFFSET $offset';
      }

      final allData = await db.rawQuery(sql);

      // Descomprimir os dados se necessário
      final decompressedData = allData.map((row) {
        final value = row['VALUE'] as String?;
        if (value == null) {
          return row;
        }
        try {
          final compressedValue = base64Decode(value);
          final decompressedValue =
              utf8.decode(GZipDecoder().decodeBytes(compressedValue));
          return {
            ...row,
            'VALUE': decompressedValue,
          };
        } catch (e) {
          // Se a descompressão falhar, assume que os dados não estão comprimidos
          return {
            ...row,
            'VALUE': value,
          };
        }
      }).toList();

      return decompressedData;
    } catch (e) {
      log("Erro ao ler todos os dados: $e");
      rethrow;
    }
  }

  @override
  Future<dynamic> getById<T>({int? id}) async {
    final db = await database;
    db.rawQuery('PRAGMA busy_timeout=120000');
    final allData =
        await db.rawQuery('SELECT * FROM LocalData WHERE id = ?', [id]);

    if (allData.isNotEmpty) {
      final row = allData.first;
      final value = row['VALUE'] as String?;
      if (value == null) {
        return row;
      }
      try {
        final compressedValue = base64Decode(value);
        final decompressedValue =
            utf8.decode(GZipDecoder().decodeBytes(compressedValue));
        return {
          ...row,
          'VALUE': decompressedValue,
        };
      } catch (e) {
        // Se a descompressão falhar, assume que os dados não estão comprimidos
        return {
          ...row,
          'VALUE': value,
        };
      }
    }

    return null;
  }

  @override
  void setDatabasePath(String path) {
    _dbPath = path;
    _database = null;
  }

  @override
  Future<bool> isDatabaseCorrupted() async {
    try {
      final db = await database;
      final result = await db.rawQuery('PRAGMA integrity_check;');

      return result.isNotEmpty && result.first.values.first != 'ok';
    } catch (e) {
      log('Error checking database integrity: $e');
      return true;
    }
  }

  @override
  Future<int> getTotalCount() async {
    final db = await database;
    final result = await db.rawQuery('SELECT COUNT(*) as count FROM LocalData');
    return Sqflite.firstIntValue(result) ?? 0;
  }

  @override
  Future<List<String>> getDistinctKeys() async {
    final db = await database;
    final result = await db.rawQuery('SELECT DISTINCT KEY FROM LocalData');
    return result.map((row) => row['KEY'] as String).toList();
  }
}
