import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders_resume/pages/widgets/orders_resume_schedule_dates.dart';
import 'package:pharmalink/widgets/textbox/custom_textfield_date_widget.dart';

class OrdersResumeSchedulePage extends StatelessWidget {
  const OrdersResumeSchedulePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrdersResumeController>(builder: (ctrl) {
      return Column(
        children: [
          Row(
            children: [
              LabelWidget(
                title: "Data limite para programação:",
                fontSize: DeviceSize.fontSize(16, 18),
              ),
              5.toWidthSpace(),
              LabelWidget(
                title: ctrl.limitDate
                        ?.formatDate(formatType: DateFormatType.ddMMyyyy) ??
                    "-",
                fontSize: DeviceSize.fontSize(16, 18),
                fontWeight: FontWeight.bold,
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: CustomTextFieldDate(
              hint: "Adicionar data",
              controller: ctrl.dateController,
              onChanged: ctrl.setDate,
              onChangedCalendar: () async {
                DateTime? picked = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2101),
                );
                if (picked != null) {
                  ctrl.setDateCalendar(picked);
                  await ctrl.saveDate();
                }
              },
              trailingTap: () async {
                await ctrl.saveDate();
              },
            ),
          ),
          ...ctrl.resume.scheduleDate!
              .map((e) => OrdersResumeScheduleDateWidget(
                    date: e,
                  ))
        ],
      );
    });
  }
}
