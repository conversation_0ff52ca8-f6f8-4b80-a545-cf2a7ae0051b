import 'package:pharmalink/modules/routes/models/address_model.dart';
import 'package:pharmalink/modules/routes/models/pdv_model.dart';
import 'package:pharmalink/modules/routes/models/visit_model.dart';

class ListaRota {
  String? dataAlteracao;
  String? dataInclusao;
  bool? editarRota;
  Endereco? endereco;
  bool? excluida;
  int? idPdv;
  int? idLoja;
  int? idSetor;
  int? idRota;
  int? idRoteiro;
  bool? isDeleted;
  String? kMRodado;
  String? kMSugerido;
  String? nomeFantasia;
  int? ordem;
  String? cnpj;
  PDV? pDV;
  Visita? visita;
  VisitaStatus? visitaStatus;

  ListaRota(
      {this.dataAlteracao,
      this.dataInclusao,
      this.editarRota,
      this.endereco,
      this.excluida,
      this.idPdv,
      this.idLoja,
      this.idSetor,
      this.idRota,
      this.idRoteiro,
      this.isDeleted,
      this.kMRodado,
      this.kMSugerido,
      this.nomeFantasia,
      this.ordem,
      this.cnpj,
      this.pDV,
      this.visita,
      this.visitaStatus});

  ListaRota.fromJson(Map<String, dynamic> json) {
    dataAlteracao = json['DataAlteracao'];
    dataInclusao = json['DataInclusao'];
    editarRota = json['EditarRota'];
    endereco =
        json['Endereco'] != null ? Endereco.fromJson(json['Endereco']) : null;
    excluida = json['Excluida'];
    idPdv = json['IdPdv'];
    idLoja = json['IdLoja'];
    idSetor = json['IdSetor'];
    idRota = json['IdRota'];
    idRoteiro = json['IdRoteiro'];
    isDeleted = json['IsDeleted'];
    kMRodado = json['KMRodado'];
    kMSugerido = json['KMSugerido'];
    nomeFantasia = json['NomeFantasia'];
    ordem = json['Ordem'];
    cnpj = json['Cnpj'];
    pDV = json['PDV'] != null ? PDV.fromJson(json['PDV']) : null;
    visita = json['Visita'] != null ? Visita.fromJson(json['Visita']) : null;
    visitaStatus = json['VisitaStatus'] != null
        ? VisitaStatus.fromJson(json['VisitaStatus'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['DataAlteracao'] = dataAlteracao;
    data['DataInclusao'] = dataInclusao;
    data['EditarRota'] = editarRota;
    if (endereco != null) {
      data['Endereco'] = endereco!.toJson();
    }
    data['Excluida'] = excluida;
    data['IdPdv'] = idPdv;
    data['IdLoja'] = idLoja;
    data['IdSetor'] = idSetor;
    data['IdRota'] = idRota;
    data['IdRoteiro'] = idRoteiro;
    data['IsDeleted'] = isDeleted;
    data['KMRodado'] = kMRodado;
    data['KMSugerido'] = kMSugerido;
    data['NomeFantasia'] = nomeFantasia;
    data['Ordem'] = ordem;
    data['Cnpj'] = cnpj;
    if (pDV != null) {
      data['PDV'] = pDV!.toJson();
    }
    if (visita != null) {
      data['Visita'] = visita!.toJson();
    }
    if (visitaStatus != null) {
      data['VisitaStatus'] = visitaStatus!.toJson();
    }
    return data;
  }
}
