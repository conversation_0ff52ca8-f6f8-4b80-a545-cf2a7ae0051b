import 'package:image_picker/image_picker.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/camera_picker/models/camera_picker_model.dart';
import 'package:pharmalink/modules/researches_trade_marketing/models/researches_trade_marketing_model.dart';
import 'package:pharmalink/modules/researches_trade_marketing/models/researches_trade_marketing_request_model.dart';
import 'package:pharmalink/modules/researches_trade_marketing/models/researches_trade_marketing_send_model.dart';
import 'package:pharmalink/modules/visits/controller/visits_controller.dart';
import 'package:pharmalink/widgets/steps/models/steps_model.dart';
import 'dart:io';

class ResearchesTradeMarketingController
    extends GetxControllerInstrumentado<ResearchesTradeMarketingController> {
  ResearchesTradeMarketingController();

  int tabIndex = 0;
  int? currentStoreId;

  List<StepsModel> stepsPage = [];

  String titlePage = "Trade Marketing - Reconhecimento";

  List<ResearchesTradeMarketingDataModel> awsersList = [];
  ResearchesTradeMarketingDataModel? awsersTypes;

  List<ResearchesTradeMarketingAnswersSubmitted>? answersSubmittedSelected;
  List<ResearchesTradeMarketingAnswersSubmitted>? answersSubmittedFiltered;
  List<ResearchesTradeMarketingMaintenanceDateList> maintenanceListDate = [];

  List<ResearchesTradeMarketingImages> maintenanceImagesBefore = [];
  List<ResearchesTradeMarketingImages> maintenanceImagesAfter = [];
  DateTime? dateMaintenanceSelected;

  bool isCameraOpen = false;

  void setCameraOpen(bool value) {
    isCameraOpen = value;
    update();
  }

  void setDataList(List<ResearchesTradeMarketingDataModel> data) {
    awsersList = data;
    awsersTypes = data.first;
    stepsPage = [];
    data.first.steps!
        .map(
          (e) => stepsPage.add(
            StepsModel(
              index: e.stepIndex!,
              title: e.stepName!,
              isDone: e.readonly!,
            ),
          ),
        )
        .toList();
    tabIndex =
        stepsPage.any((element) => !element.isDone!)
            ? stepsPage.where((element) => !element.isDone!).first.index!
            : 0;
    update();
  }

  Future<void> getData({required int storeId}) async {
    currentStoreId = storeId;
    final researchesTradeMarketingBox =
        await ResearchesTradeMarketingDataModel().getListByStore(
          storeId: storeId,
        );
    if (researchesTradeMarketingBox.isNotEmpty) {
      setDataList(researchesTradeMarketingBox);

      Get.toNamed(RoutesPath.researchesTradeMarketing);
      return;
    }

    var request = ResearchesTradeMarketingRequestModel(
      idsLojas: [currentStoreId!],
    );

    final result = await researchesTradeMarketingApi
        .getResearchesTradeMarketing(model: request);

    if (result.error != null) {
      SnackbarCustom.snackbarError(result.error!.message!);
    } else {
      setDataList(result.data!);
      await dbContext
          .withControllerAction(this)
          .addData(
            key: DatabaseModels.researchesTradeMarketingModel,
            data: result.data!,
            workspaceId: appController.workspace!.workspaceId,
            storeId: currentStoreId!,
            clearCurrentData: true,
          );

      Get.toNamed(RoutesPath.researchesTradeMarketing);
    }
  }

  Future<void> getDataSync({required int storeId}) async {
    var request = ResearchesTradeMarketingRequestModel(idsLojas: [storeId]);

    final result = await researchesTradeMarketingApi
        .getResearchesTradeMarketing(model: request);

    if (result.error != null) {
      SnackbarCustom.snackbarError(result.error!.message!);
    } else {
      await dbContext
          .withControllerAction(this)
          .addData(
            key: DatabaseModels.researchesTradeMarketingModel,
            data: result.data!,
            workspaceId: appController.workspace!.workspaceId,
            storeId: storeId,
            clearCurrentData: true,
          );
    }
  }

  void setQuestionListRadioAwser(
    int? value,
    ResearchesTradeMarketingQuestions item,
  ) {
    item.answers = [value];
  }

  void setQuestionTextAwser(
    String? value,
    ResearchesTradeMarketingQuestions item,
  ) {
    item.answers = [value];
  }

  void setExplainTextAwser(
    String? value,
    ResearchesTradeMarketingQuestions item,
  ) {
    item.explainText = value;
  }

  void setQuestionNumberAwser(
    double? value,
    ResearchesTradeMarketingQuestions item,
  ) {
    item.answers = [value];
  }

  void setQuestionInputGroupAwser(
    String key,
    String? value,
    ResearchesTradeMarketingQuestions item,
  ) {
    item.answers = [value];
  }

  String? getValueInputGroupAnswer(
    ResearchesTradeMarketingQuestions item,
    String key,
  ) {
    if (item.answers is List &&
        (item.answers as List).every(
          (item) => item is ResearchesTradeMarketingAnswers,
        )) {
      // Seu código aqui

      List<ResearchesTradeMarketingAnswers> answers =
          (item.answers as List)
              .map((item) => item as ResearchesTradeMarketingAnswers)
              .toList();
      if (answers.isNotEmpty) {
        var answer = answers.first;
        switch (key) {
          case "1":
            return answer.qtd1;
          case "2":
            return answer.qtd2;
          case "3":
            return answer.qtd3;
          case "4":
            return answer.qtd4;
          default:
            return null;
        }
      }
    }
    return null;
  }

  void setTabIndexBack() {
    tabIndex--;
    if (tabIndex < 0) tabIndex = 0;
    update();
  }

  bool isFormValid() {
    bool result = true;
    for (var r in awsersTypes!.steps![tabIndex].questions!) {
      if (r.metadata?.required == true &&
          (r.answers == null || r.answers!.isEmpty)) {
        r.metadata?.isRequired = true;
        result = false;
      } else if (r.metadata?.required == true &&
          (r.answers != null || r.answers!.isNotEmpty)) {
        r.metadata?.isRequired = false;
      }
    }
    if (!result) {
      SnackbarCustom.snackbarError("Responda todas as perguntas");
    }
    update();
    return result;
  }

  bool isCanSync() {
    final currentStep = awsersTypes!.steps![tabIndex];
    setAnswersSubmittedSelected(currentStep.answersSubmitted!, isSync: true);
    final x = DateTime.now();
    final dateToday = DateTime(x.year, x.month, x.day);
    return !maintenanceListDate.any((e) => e.date == dateToday);
  }

  void setTabIndexForward() {
    if (!isFormValid()) return;
    tabIndex++;
    if (tabIndex > stepsPage.length - 1) tabIndex = stepsPage.length - 1;

    update();
  }

  Future<void> openCameraPicker(int categoryId) async {
    if (!await appController.checkCameraPermission()) {
      Get.toNamed(RoutesPath.permissionRequest)?.then((data) async {
        if (data) {
          await _handleCameraPicker(categoryId);
        }
      });
    } else {
      await _handleCameraPicker(categoryId);
    }
  }

  Future<void> _handleCameraPicker(int categoryId) async {
    Get.toNamed(RoutesPath.cameraPicker)?.then((data) async {
      if (data != null) {
        await setPicture(
          CameraPickerModel(
            file: File(data.path),
            path: data.path,
            name: data.name,
            size: File(data.path).lengthSync(),
            orientation: Orientation.portrait,
          ),
          categoryId,
        );
      }
    });
  }

  void openCameraOption(int categoryId) {
    showModalBottomSheet(
      context: Get.context!,
      builder: (BuildContext bc) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(
                  FontAwesomeIcons.camera,
                  color: Colors.black,
                ),
                title: const Text('Ir Para Câmera'),
                onTap:
                    isCameraOpen
                        ? null
                        : () async {
                          try {
                            if (!isCameraOpen) {
                              setCameraOpen(true);
                              if (Get.isSnackbarOpen) {
                                await Get.closeCurrentSnackbar();
                              }
                              Future.delayed(
                                const Duration(milliseconds: 150),
                                () async {
                                  Get.back();
                                  await openCameraPicker(categoryId);
                                  setCameraOpen(false);
                                },
                              );
                            }
                          } catch (e) {
                            setCameraOpen(false);
                          }
                        },
              ),
              ListTile(
                leading: const Icon(
                  FontAwesomeIcons.images,
                  color: Colors.black,
                ),
                title: const Text('Procurar na Galeria'),
                onTap: () async {
                  Get.back();
                  final pickedImage = await ImagePicker().pickImage(
                    maxHeight: 512,
                    maxWidth: 512,
                    source: ImageSource.gallery,
                    imageQuality: 100,
                  );
                  if (pickedImage != null) {
                    await setPicture(
                      CameraPickerModel(
                        file: File(pickedImage.path),
                        path: pickedImage.path,
                        name: pickedImage.name,
                        size: File(pickedImage.path).lengthSync(),
                        orientation: Orientation.portrait,
                      ),
                      categoryId,
                    );
                  }
                },
              ),
              ListTile(
                leading: const Icon(
                  FontAwesomeIcons.xmark,
                  color: Colors.black,
                ),
                title: const Text('Cancelar'),
                onTap: () async {
                  Get.back();
                },
              ),
            ],
          ),
        );
      },
    );
  }

  bool hasAnswer(ResearchesTradeMarketingQuestions item) {
    if (item.answers == null || item.answers!.isEmpty) {
      return false;
    }
    return true;
  }

  int? getAnswerInt(ResearchesTradeMarketingQuestions item) {
    return item.answers?.first;
  }

  String? getAnswerText(ResearchesTradeMarketingQuestions item) {
    return item.answers?.first;
  }

  String? getAnswerDouble(ResearchesTradeMarketingQuestions item) {
    return item.answers?.first.toString().toStringDoubleFormat();
  }

  List<ResearchesTradeMarketingImages> getImages(int categoryId) {
    List<ResearchesTradeMarketingImages> images = [];
    if (awsersTypes!.images!.any(
      (element) =>
          element.stepIndex == (tabIndex + 1) &&
          element.categoryId == categoryId,
    )) {
      awsersTypes!.images!
          .where(
            (element) =>
                element.stepIndex == (tabIndex + 1) &&
                element.categoryId == categoryId,
          )
          .map((e) {
            images.add(e);
          })
          .toList();
    }
    return images;
  }

  Future<void> setPicture(CameraPickerModel photo, int categoryId) async {
    final picture = ResearchesTradeMarketingImages(
      imageName: photo.name,
      imageVirtualPath: photo.path,
      orientation: photo.orientation,
      categoryId: categoryId,
    );

    if (categoryId == 1) {
      maintenanceImagesBefore.add(picture);
    } else {
      maintenanceImagesAfter.add(picture);
    }

    update();
  }

  Future<void> removePicture(ResearchesTradeMarketingImages picture) async {
    await Dialogs.confirm(
      AppStrings.attention,
      AppStrings.visitRemoveImage,
      buttonNameCancel: "Não",
      buttonNameOk: "Sim",
      onPressedOk: () async {
        Get.back();
        awsersTypes!.images!.remove(picture);

        update();
      },
    );
  }

  Future<void> save() async {
    if (!isFormValid()) return;
    if (isCanSync()) {
      awsersList.map((e) {
        e.workspaceId = appController.workspace?.workspaceId;
        e.storeId = currentStoreId;
        e.isSync = SyncEnum.awaited;
      }).toList();

      await dbContext
          .withControllerAction(this)
          .addData(
            key: DatabaseModels.researchesTradeMarketingModel,
            data: awsersList,
            workspaceId: appController.workspace!.workspaceId,
            storeId: currentStoreId,
            clearCurrentData: true,
          );

      // tabIndex = 0;

      update();

      Dialogs.confirm(
        "Pesquisa salva",
        "Pesquisa salva com sucesso. Deseja sincronizar agora?",
        buttonNameCancel: "NÃO",
        buttonNameOk: "SIM",
        onPressedOk: () async {
          Get.back();
          await synchronizationsController.onReady();
          synchronizationsController.setHasResearches(true);
          Get.toNamed(
            RoutesPath.synchronizations,
            arguments: {'all': true, 'autostart': true},
          );
        },
      );
    } else {
      Dialogs.info(
        AppStrings.attention,
        "Já foi realizada uma manutenção hoje.",
        buttonName: "OK",
      );
    }
    final visitController = Get.find<VisitsController>();
    visitController.refreshVisitsSearch();
  }

  Future<void> syncResearches() async {
    final listResearches = await ResearchesTradeMarketingDataModel()
        .getListToSync(workspaceId: appController.workspace!.workspaceId!);

    if (listResearches.isEmpty) return;

    final listToSync =
        listResearches
            .where((element) => element.isSync == SyncEnum.awaited)
            .toList();
    if (listToSync.isEmpty) return;
    final model = ResearchesTradeMarketingSendModel();
    model.pesquisas =
        listToSync
            .map(
              (e) => ResearchesTradeMarketingSendPesquisas(
                cnpj: e.cnpj,
                idLoja: e.storeId,
                idSurvey: e.idSurvey,
                surveyVersion: e.surveyVersion,
                steps:
                    e.steps
                        ?.map(
                          (s) => ResearchesTradeMarketingSendSteps(
                            idRota: s.idRota,
                            stepIdentifier: s.stepIdentifier,
                            stepName: s.stepName,
                            stepIndex: s.stepIndex,
                            readonly: s.readonly,
                            imageNumMax: s.imageNumMax,
                            imageNumMin: s.imageNumMin,
                            questions:
                                s.questions
                                    ?.map(
                                      (
                                        q,
                                      ) => ResearchesTradeMarketingSendQuestions(
                                        questionId: q.questionId,
                                        step: q.step,
                                        question: q.question,
                                        dataType: q.dataType,
                                        answers: q.answers,
                                        explainText: q.explainText,
                                        explain: q.explain,
                                        explainLabel: q.explainLabel,
                                        metadata:
                                            ResearchesTradeMarketingSendMetadata(
                                              multiple: q.metadata?.multiple,
                                              required: q.metadata?.required,
                                              placeholder:
                                                  q.metadata?.placeholder,
                                              trueLabel: q.metadata?.trueLabel,
                                              falseLabel:
                                                  q.metadata?.falseLabel,
                                              maxLength: q.metadata?.maxLength,
                                              minLength: q.metadata?.minLength,
                                              options:
                                                  q.metadata?.options!
                                                      .map(
                                                        (
                                                          o,
                                                        ) => ResearchesTradeMarketingSendOptions(
                                                          text: o.text,
                                                          value:
                                                              o.value
                                                                  .toString(),
                                                        ),
                                                      )
                                                      .toList(),
                                            ),
                                      ),
                                    )
                                    .toList(),
                            answersSubmitted:
                                s.answersSubmitted
                                    ?.map(
                                      (as) =>
                                          ResearchesTradeMarketingSendAnswersSubmitted(
                                            id: as.id,
                                            stepId: as.stepId,
                                            question: as.question,
                                            dataType: as.dataType,
                                            answers: as.answers,
                                            date: as.date?.toIso8601String(),
                                            explain: as.explain,
                                            explainText: as.explainText,
                                            explainLabel: as.explainLabel,
                                          ),
                                    )
                                    .toList(),
                          ),
                        )
                        .toList(),
              ),
            )
            .toList();

    final result = await researchesTradeMarketingApi.sendTradeMarketing(
      model: model,
    );
    if (result.error != null) {
      SnackbarCustom.snackbarError(
        "Erro ao sincronizar pesquisa de trade marketing",
      );
      return;
    }
  }

  void setAnswersSubmittedSelected(
    List<ResearchesTradeMarketingAnswersSubmitted> answers, {
    bool isSync = false,
  }) {
    answersSubmittedSelected = answers;
    maintenanceListDate = [];
    if (answersSubmittedSelected != null) {
      final dates =
          answersSubmittedSelected!.map((e) => e.dateSystem!).toSet().toList();
      dates.sort((a, b) => a.compareTo(b));
      maintenanceListDate =
          dates
              .map((e) => ResearchesTradeMarketingMaintenanceDateList(date: e))
              .toSet()
              .toList();

      int count = 1;
      maintenanceListDate.map((e) {
        e.index = count;
        count++;
      }).toList();
    }
    if (!isSync) {
      Get.toNamed(RoutesPath.researchesTradeMaintenanceList);
    }
  }

  void openMaintenanceDetail(DateTime dateSelected) {
    dateMaintenanceSelected = dateSelected;
    answersSubmittedFiltered =
        answersSubmittedSelected!
            .where((element) => element.dateSystem == dateSelected)
            .toList();
    maintenanceImagesAfter = [];
    maintenanceImagesBefore = [];
    if (awsersTypes!.images!.any(
      (element) => element.stepIndex == 2 && element.categoryId == 2,
    )) {
      maintenanceImagesAfter =
          awsersTypes!.images!
              .where(
                (element) => element.stepIndex == 2 && element.categoryId == 2,
              )
              .map((e) => e)
              .toList();
    }
    if (awsersTypes!.images!.any(
      (element) => element.stepIndex == 2 && element.categoryId == 1,
    )) {
      maintenanceImagesBefore =
          awsersTypes!.images!
              .where(
                (element) => element.stepIndex == 2 && element.categoryId == 1,
              )
              .map((e) => e)
              .toList();
    }

    Get.toNamed(RoutesPath.researchesTradeMaintenanceDetail);
  }
}
