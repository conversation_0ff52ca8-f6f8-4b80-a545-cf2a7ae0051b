import 'package:pharmalink/core/models/parameter_model.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/modules/order_types/models/order_parameters_model.dart';
import 'package:pharmalink/modules/orders/models/orders_mix_produto_request_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';
import 'package:pharmalink/modules/orders/models/orders_tabloid_products_request_model.dart';
import 'package:pharmalink/modules/stores/models/commercial_condition_model.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/distribuidores_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/parametrizacao_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/prazo_pagamento_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/tabloides_model.dart';

class ParameterController
    extends GetxControllerInstrumentado<ParameterController> {
  ParameterModel order = ParameterModel();

  int? currentStoreId() => order.currentStore?.idLoja ?? 0;

  StoresModel? getCurrentStore() => order.currentStore;

  ParametrizacaoModel? getParametrization() => order.parametrization;
  OrderParametersModel orderParam() => order.orderParameters;
  CommercialConditionModel? getCommercialCondition() =>
      order.commercialConditionSelected;

  int? getTypeOrderId() => order.typeOrderId;
  PrazoPagamentoModel? getDeadlinePayment() => order.deadlinePayment;
  List<PrazoPagamentoModel>? getDeadlinePaymentList() =>
      order.deadlinePaymentList;

  List<DistribuidoresModel>? getCurrentDistributors() =>
      order.currentDistributors ?? [];

  MixProdutoRequest? getParameters() => order.parameters;

  TabloidProductsRequest? getParametersEsp() => order.parametersEsp;
  DistribuidoresModel? getCurrentDistributor() => order.currentDistributor;

  ProductsMixModel? getProducts() => order.products;

  TabloidesModel? getTabloidSelected() => order.tabloidSelected;

  bool hasStoreInitialize = false;
  bool hasVisit = true;
  bool hasOfflineSync = true;

  String getTypeOrderName() {
    if (order.typeOrderId == null) return "";
    switch (order.typeOrderId) {
      case 2:
        return "Pedido Especial";
      case 4:
        return "Pedido Representante";
      case 1:
        return "Pedido Padrão";
      default:
        return "";
    }
  }

  void clearData() {
    order.orderLocal = null;
    order.orderLocalFinish = false;
    order.tabloidName = null;
    order.tabloidId = null;
    order.currentDistributors = null;
    order.currentDistributor = null;
  }
}
