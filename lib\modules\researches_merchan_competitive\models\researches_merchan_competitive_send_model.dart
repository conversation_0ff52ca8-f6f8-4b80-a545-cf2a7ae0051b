class ResearchesMerchanCompetitiveSendModel {
  int? idRota;
  List<ResearchesMerchanCompetitiveSendPesquisaMerchandisingItens>?
      pesquisaMerchandisingItens;
  List<ResearchesMerchanCompetitiveSendPesquisaMerchandisingItens>?
      pesquisaMerchandisingItensRemovidos;

  ResearchesMerchanCompetitiveSendModel(
      {this.idRota,
      this.pesquisaMerchandisingItens,
      this.pesquisaMerchandisingItensRemovidos});

  ResearchesMerchanCompetitiveSendModel.fromJson(Map<String, dynamic> json) {
    idRota = json['IdRota'];
    if (json['PesquisaMerchandisingItens'] != null) {
      pesquisaMerchandisingItens =
          <ResearchesMerchanCompetitiveSendPesquisaMerchandisingItens>[];
      json['PesquisaMerchandisingItens'].forEach((v) {
        pesquisaMerchandisingItens!.add(
            ResearchesMerchanCompetitiveSendPesquisaMerchandisingItens.fromJson(
                v));
      });
    }
    if (json['PesquisaMerchandisingItensRemovidos'] != null) {
      pesquisaMerchandisingItensRemovidos =
          <ResearchesMerchanCompetitiveSendPesquisaMerchandisingItens>[];
      json['PesquisaMerchandisingItensRemovidos'].forEach((v) {
        pesquisaMerchandisingItensRemovidos!.add(
            ResearchesMerchanCompetitiveSendPesquisaMerchandisingItens.fromJson(
                v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdRota'] = idRota;
    if (pesquisaMerchandisingItens != null) {
      data['PesquisaMerchandisingItens'] =
          pesquisaMerchandisingItens!.map((v) => v.toJson()).toList();
    }
    if (pesquisaMerchandisingItensRemovidos != null) {
      data['PesquisaMerchandisingItensRemovidos'] =
          pesquisaMerchandisingItensRemovidos!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResearchesMerchanCompetitiveSendPesquisaMerchandisingItens {
  int? idPesquisaMerchandising;
  int? idMerchandising;
  String? merchandisingDescricao;
  int? idFamilia;
  String? familiaDescricao;
  int? idTipoAtivacao;
  String? tipoAtivacaoDescricao;
  bool? enviado;
  int? quantidade;
  String? dataInclusao;

  ResearchesMerchanCompetitiveSendPesquisaMerchandisingItens(
      {this.idPesquisaMerchandising,
      this.idMerchandising,
      this.merchandisingDescricao,
      this.idFamilia,
      this.familiaDescricao,
      this.idTipoAtivacao,
      this.tipoAtivacaoDescricao,
      this.enviado,
      this.quantidade,
      this.dataInclusao});

  ResearchesMerchanCompetitiveSendPesquisaMerchandisingItens.fromJson(
      Map<String, dynamic> json) {
    idPesquisaMerchandising = json['IdPesquisaMerchandising'];
    idMerchandising = json['IdMerchandising'];
    merchandisingDescricao = json['MerchandisingDescricao'];
    idFamilia = json['IdFamilia'];
    familiaDescricao = json['FamiliaDescricao'];
    idTipoAtivacao = json['IdTipoAtivacao'];
    tipoAtivacaoDescricao = json['TipoAtivacaoDescricao'];
    enviado = json['Enviado'];
    quantidade = json['Quantidade'];
    dataInclusao = json['DataInclusao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPesquisaMerchandising'] = idPesquisaMerchandising;
    data['IdMerchandising'] = idMerchandising;
    data['MerchandisingDescricao'] = merchandisingDescricao;
    data['IdFamilia'] = idFamilia;
    data['FamiliaDescricao'] = familiaDescricao;
    data['IdTipoAtivacao'] = idTipoAtivacao;
    data['TipoAtivacaoDescricao'] = tipoAtivacaoDescricao;
    data['Enviado'] = enviado;
    data['Quantidade'] = quantidade;
    data['DataInclusao'] = dataInclusao;
    return data;
  }
}
