import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/researches_trade_marketing/models/researches_trade_marketing_model.dart';
import 'package:pharmalink/modules/researches_trade_marketing/models/researches_trade_marketing_request_model.dart';
import 'package:pharmalink/modules/researches_trade_marketing/models/researches_trade_marketing_send_model.dart';

abstract class IResearchesTradeMarketingApi {
  Future<HttpResponse<List<ResearchesTradeMarketingDataModel>>>
      getResearchesTradeMarketing(
          {required ResearchesTradeMarketingRequestModel model});

  Future<HttpResponse<bool>> sendTradeMarketing(
      {required ResearchesTradeMarketingSendModel model});
}

class ResearchesTradeMarketingApi extends IResearchesTradeMarketingApi {
  final HttpManager _httpManager;
  ResearchesTradeMarketingApi(this._httpManager);

  @override
  Future<HttpResponse<List<ResearchesTradeMarketingDataModel>>>
      getResearchesTradeMarketing(
          {required ResearchesTradeMarketingRequestModel model}) async {
    return await _httpManager.request<List<ResearchesTradeMarketingDataModel>>(
      path: 'pesquisaTrade/obterFormularioPesquisa',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        if (data is List) {
          return data
              .map((item) => ResearchesTradeMarketingDataModel.fromJson(item))
              .toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }

  @override
  Future<HttpResponse<bool>> sendTradeMarketing(
      {required ResearchesTradeMarketingSendModel model}) async {
    return await _httpManager.request<bool>(
      path: 'pesquisaTrade/salvarPesquisa',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        return true;
      },
    );
  }
}
