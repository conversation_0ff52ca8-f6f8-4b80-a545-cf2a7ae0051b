import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class SyncResultItemsModel extends SqfLiteBase<SyncResultItemsModel> {
  String? id;
  int? storeId;
  int? merchantConditionId;
  int? paymentDeadlineId;
  int? orderTypeId;
  String? synchronizationParams;
  String? synchronizationResult;
  String? synchronizationTabloidResult;
  String? synchronizationIdealMixResult;
  String? synchronizationSpecialProductsResult;
  String? processStartDate;
  String? processEndDate;

  SyncResultItemsModel({
    this.id,
    this.storeId,
    this.merchantConditionId,
    this.paymentDeadlineId,
    this.orderTypeId,
    this.synchronizationParams,
    this.synchronizationResult,
    this.synchronizationTabloidResult,
    this.synchronizationIdealMixResult,
    this.synchronizationSpecialProductsResult,
    this.processStartDate,
    this.processEndDate,
  }) : super(DatabaseModels.syncResultItemsModel);

  SyncResultItemsModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.syncResultItemsModel) {
    id = json['id'];
    storeId = json['storeId'];
    merchantConditionId = json['merchantConditionId'];
    paymentDeadlineId = json['paymentDeadlineId'];
    orderTypeId = json['orderTypeId'];
    synchronizationParams = json['synchronizationParams'];
    synchronizationResult = json['synchronizationResult'];
    synchronizationTabloidResult = json['synchronizationTabloidResult'];
    synchronizationIdealMixResult = json['synchronizationIdealMixResult'];
    synchronizationSpecialProductsResult =
        json['synchronizationSpecialProductsResult'];
    processStartDate = json['processStartDate'];
    processEndDate = json['processEndDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (id != null) data['id'] = id;
    if (storeId != null) data['storeId'] = storeId;
    if (merchantConditionId != null) {
      data['merchantConditionId'] = merchantConditionId;
    }
    if (paymentDeadlineId != null) {
      data['paymentDeadlineId'] = paymentDeadlineId;
    }
    if (orderTypeId != null) data['orderTypeId'] = orderTypeId;
    if (synchronizationParams != null) {
      data['synchronizationParams'] = synchronizationParams;
    }
    if (synchronizationResult != null) {
      data['synchronizationResult'] = synchronizationResult;
    }
    if (synchronizationTabloidResult != null) {
      data['synchronizationTabloidResult'] = synchronizationTabloidResult;
    }
    if (synchronizationIdealMixResult != null) {
      data['synchronizationIdealMixResult'] = synchronizationIdealMixResult;
    }
    if (synchronizationSpecialProductsResult != null) {
      data['synchronizationSpecialProductsResult'] =
          synchronizationSpecialProductsResult;
    }
    if (processStartDate != null) data['processStartDate'] = processStartDate;
    if (processEndDate != null) data['processEndDate'] = processEndDate;

    return data;
  }

  Future<SyncResultItemsModel?> getFirst(
      {required int storeId,
      int? merchantConditionId,
      int? paymentDeadlineId,
      required int orderTypeId}) async {
    final hasCode =
        '$storeId:$merchantConditionId:$paymentDeadlineId:$orderTypeId';
    var list = await getAll<SyncResultItemsModel>(
      workspaceId: appController.workspace!.workspaceId,
      userId: appController.userLogged!.userId,
      storeId: storeId,
      hashCode: hasCode,
      SyncResultItemsModel.fromJson,
    );
    return list.isEmpty ? null : list.first;
  }

  Future<List<SyncResultItemsModel>> getList() async {
    var list = await getAll<SyncResultItemsModel>(
      workspaceId: appController.workspace!.workspaceId,
      userId: appController.userLogged!.userId,
      SyncResultItemsModel.fromJson,
    );
    return list;
  }
}
