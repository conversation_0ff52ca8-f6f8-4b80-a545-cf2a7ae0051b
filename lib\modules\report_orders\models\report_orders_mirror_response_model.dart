class MirrorOrderResponse {
  int? idPedido;
  String? numeroPedidoCliente;
  int? tipoPedido;
  int? idDistribuidor;
  int? idCondicaoComercial;
  String? prazo;
  DateTime? data;
  String? status;
  String? cnpj;
  String? idUsuario;
  String? nomeUsuario;
  String? perfilUsuario;
  int? idLoja;
  String? razaoSocial;
  String? origem;
  int? idPrazoPagamento;
  String? descricaoPrazoPagamento;
  int? idComboOferta;
  String? descricaoComboOferta;
  int? idTabloide;
  String? descricaoTabloide;
  double? quantidadeApresentacao;
  double? totalUnidades;
  double? totalBruto;
  double? totalLiquido;
  double? descontoMedio;
  String? observacao;
  List<MirrorOrderDistribuidores>? distribuidores;
  String? itens;
  List<MirrorOrderPedidos>? pedidos;
  String? datasProgramadas;
  String? faturamento;
  int? pedidoOriginal;
  String? classificacao;

  MirrorOrderResponse(
      {this.idPedido,
      this.numeroPedidoCliente,
      this.tipoPedido,
      this.idDistribuidor,
      this.idCondicaoComercial,
      this.prazo,
      this.data,
      this.status,
      this.cnpj,
      this.idUsuario,
      this.nomeUsuario,
      this.perfilUsuario,
      this.idLoja,
      this.razaoSocial,
      this.origem,
      this.idPrazoPagamento,
      this.descricaoPrazoPagamento,
      this.idComboOferta,
      this.descricaoComboOferta,
      this.idTabloide,
      this.descricaoTabloide,
      this.quantidadeApresentacao,
      this.totalUnidades,
      this.totalBruto,
      this.totalLiquido,
      this.descontoMedio,
      this.observacao,
      this.distribuidores,
      this.itens,
      this.pedidos,
      this.datasProgramadas,
      this.faturamento,
      this.pedidoOriginal,
      this.classificacao});

  MirrorOrderResponse.fromJson(Map<String, dynamic> json) {
    idPedido = json['IdPedido'];
    numeroPedidoCliente = json['NumeroPedidoCliente'];
    tipoPedido = json['TipoPedido'];
    idDistribuidor = json['IdDistribuidor'];
    idCondicaoComercial = json['IdCondicaoComercial'];
    prazo = json['Prazo'];
    data = json['Data'] != null ? DateTime.parse(json['Data']) : null;
    status = json['Status'];
    cnpj = json['Cnpj'];
    idUsuario = json['IdUsuario'];
    nomeUsuario = json['NomeUsuario'];
    perfilUsuario = json['PerfilUsuario'];
    idLoja = json['IdLoja'];
    razaoSocial = json['RazaoSocial'];
    origem = json['Origem'];
    idPrazoPagamento = json['IdPrazoPagamento'];
    descricaoPrazoPagamento = json['DescricaoPrazoPagamento'];
    idComboOferta = json['IdComboOferta'];
    descricaoComboOferta = json['DescricaoComboOferta'];
    idTabloide = json['IdTabloide'];
    descricaoTabloide = json['DescricaoTabloide'];
    quantidadeApresentacao = json['QuantidadeApresentacao'];
    totalUnidades = json['TotalUnidades'];
    totalBruto = json['TotalBruto'];
    totalLiquido = json['TotalLiquido'];
    descontoMedio = json['DescontoMedio'];
    observacao = json['Observacao'];
    if (json['Distribuidores'] != null) {
      distribuidores = <MirrorOrderDistribuidores>[];
      json['Distribuidores'].forEach((v) {
        distribuidores!.add(MirrorOrderDistribuidores.fromJson(v));
      });
    }
    itens = json['Itens'];
    if (json['Pedidos'] != null) {
      pedidos = <MirrorOrderPedidos>[];
      json['Pedidos'].forEach((v) {
        pedidos!.add(MirrorOrderPedidos.fromJson(v));
      });
    }
    datasProgramadas = json['DatasProgramadas'];
    faturamento = json['Faturamento'];
    pedidoOriginal = json['PedidoOriginal'];
    classificacao = json['Classificacao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPedido'] = idPedido;
    data['NumeroPedidoCliente'] = numeroPedidoCliente;
    data['TipoPedido'] = tipoPedido;
    data['IdDistribuidor'] = idDistribuidor;
    data['IdCondicaoComercial'] = idCondicaoComercial;
    data['Prazo'] = prazo;
    data['Data'] = this.data;
    data['Status'] = status;
    data['Cnpj'] = cnpj;
    data['IdUsuario'] = idUsuario;
    data['NomeUsuario'] = nomeUsuario;
    data['PerfilUsuario'] = perfilUsuario;
    data['IdLoja'] = idLoja;
    data['RazaoSocial'] = razaoSocial;
    data['Origem'] = origem;
    data['IdPrazoPagamento'] = idPrazoPagamento;
    data['DescricaoPrazoPagamento'] = descricaoPrazoPagamento;
    data['IdComboOferta'] = idComboOferta;
    data['DescricaoComboOferta'] = descricaoComboOferta;
    data['IdTabloide'] = idTabloide;
    data['DescricaoTabloide'] = descricaoTabloide;
    data['QuantidadeApresentacao'] = quantidadeApresentacao;
    data['TotalUnidades'] = totalUnidades;
    data['TotalBruto'] = totalBruto;
    data['TotalLiquido'] = totalLiquido;
    data['DescontoMedio'] = descontoMedio;
    data['Observacao'] = observacao;
    if (distribuidores != null) {
      data['Distribuidores'] = distribuidores!.map((v) => v.toJson()).toList();
    }
    data['Itens'] = itens;
    if (pedidos != null) {
      data['Pedidos'] = pedidos!.map((v) => v.toJson()).toList();
    }
    data['DatasProgramadas'] = datasProgramadas;
    data['Faturamento'] = faturamento;
    data['PedidoOriginal'] = pedidoOriginal;
    data['Classificacao'] = classificacao;
    return data;
  }
}

class MirrorOrderDistribuidores {
  int? idDistribuidor;
  String? nomeFantasia;
  String? razaoSocial;
  String? cNPJ;
  String? iE;
  String? cEP;
  String? endereco;
  String? numero;
  String? complemento;
  String? cidade;
  String? uf;
  String? bairro;
  String? telefone;
  String? fax;
  String? webSite;
  String? email;
  String? nomeContato;
  int? idCargoContato;
  String? telefoneContato;
  String? celularContato;
  String? emailContato;
  String? ramalContato;
  String? idCentroDistribuicao;
  bool? excecao;
  bool? isPedidoCampanha;
  bool? isPedidoBonificado;
  double? pedidoMinimo;
  String? aceitaBonificacao;
  int? qtdDiasTolerancia;
  String? habilitarContaCorrente;
  bool? status;
  String? dataInclusao;
  String? dataAlteracao;
  String? distribuidorLogistica;

  MirrorOrderDistribuidores(
      {this.idDistribuidor,
      this.nomeFantasia,
      this.razaoSocial,
      this.cNPJ,
      this.iE,
      this.cEP,
      this.endereco,
      this.numero,
      this.complemento,
      this.cidade,
      this.uf,
      this.bairro,
      this.telefone,
      this.fax,
      this.webSite,
      this.email,
      this.nomeContato,
      this.idCargoContato,
      this.telefoneContato,
      this.celularContato,
      this.emailContato,
      this.ramalContato,
      this.idCentroDistribuicao,
      this.excecao,
      this.isPedidoCampanha,
      this.isPedidoBonificado,
      this.pedidoMinimo,
      this.aceitaBonificacao,
      this.qtdDiasTolerancia,
      this.habilitarContaCorrente,
      this.status,
      this.dataInclusao,
      this.dataAlteracao,
      this.distribuidorLogistica});

  MirrorOrderDistribuidores.fromJson(Map<String, dynamic> json) {
    idDistribuidor = json['IdDistribuidor'];
    nomeFantasia = json['NomeFantasia'];
    razaoSocial = json['RazaoSocial'];
    cNPJ = json['CNPJ'];
    iE = json['IE'];
    cEP = json['CEP'];
    endereco = json['Endereco'];
    numero = json['Numero'];
    complemento = json['Complemento'];
    cidade = json['Cidade'];
    uf = json['Uf'];
    bairro = json['Bairro'];
    telefone = json['Telefone'];
    fax = json['Fax'];
    webSite = json['WebSite'];
    email = json['Email'];
    nomeContato = json['NomeContato'];
    idCargoContato = json['IdCargoContato'];
    telefoneContato = json['TelefoneContato'];
    celularContato = json['CelularContato'];
    emailContato = json['EmailContato'];
    ramalContato = json['RamalContato'];
    idCentroDistribuicao = json['IdCentroDistribuicao'];
    excecao = json['Excecao'];
    isPedidoCampanha = json['IsPedidoCampanha'];
    isPedidoBonificado = json['IsPedidoBonificado'];
    pedidoMinimo = json['PedidoMinimo'];
    aceitaBonificacao = json['AceitaBonificacao'];
    qtdDiasTolerancia = json['QtdDiasTolerancia'];
    habilitarContaCorrente = json['HabilitarContaCorrente'];
    status = json['Status'];
    dataInclusao = json['DataInclusao'];
    dataAlteracao = json['DataAlteracao'];
    distribuidorLogistica = json['DistribuidorLogistica'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdDistribuidor'] = idDistribuidor;
    data['NomeFantasia'] = nomeFantasia;
    data['RazaoSocial'] = razaoSocial;
    data['CNPJ'] = cNPJ;
    data['IE'] = iE;
    data['CEP'] = cEP;
    data['Endereco'] = endereco;
    data['Numero'] = numero;
    data['Complemento'] = complemento;
    data['Cidade'] = cidade;
    data['Uf'] = uf;
    data['Bairro'] = bairro;
    data['Telefone'] = telefone;
    data['Fax'] = fax;
    data['WebSite'] = webSite;
    data['Email'] = email;
    data['NomeContato'] = nomeContato;
    data['IdCargoContato'] = idCargoContato;
    data['TelefoneContato'] = telefoneContato;
    data['CelularContato'] = celularContato;
    data['EmailContato'] = emailContato;
    data['RamalContato'] = ramalContato;
    data['IdCentroDistribuicao'] = idCentroDistribuicao;
    data['Excecao'] = excecao;
    data['IsPedidoCampanha'] = isPedidoCampanha;
    data['IsPedidoBonificado'] = isPedidoBonificado;
    data['PedidoMinimo'] = pedidoMinimo;
    data['AceitaBonificacao'] = aceitaBonificacao;
    data['QtdDiasTolerancia'] = qtdDiasTolerancia;
    data['HabilitarContaCorrente'] = habilitarContaCorrente;
    data['Status'] = status;
    data['DataInclusao'] = dataInclusao;
    data['DataAlteracao'] = dataAlteracao;
    data['DistribuidorLogistica'] = distribuidorLogistica;
    return data;
  }
}

class MirrorOrderPedidos {
  int? idPedido;
  String? numeroPedidoCliente;
  int? tipoPedido;
  int? idDistribuidor;
  int? idCondicaoComercial;
  String? prazo;
  String? data;
  String? status;
  String? cnpj;
  String? idUsuario;
  String? nomeUsuario;
  String? perfilUsuario;
  int? idLoja;
  String? razaoSocial;
  String? origem;
  int? idPrazoPagamento;
  String? descricaoPrazoPagamento;
  int? idComboOferta;
  String? descricaoComboOferta;
  int? idTabloide;
  String? descricaoTabloide;
  double? quantidadeApresentacao;
  double? totalUnidades;
  double? totalBruto;
  double? totalLiquido;
  double? descontoMedio;
  String? observacao;
  String? distribuidores;
  List<MirrorOrderItens>? itens;
  String? pedidos;
  String? datasProgramadas;
  String? faturamento;
  int? pedidoOriginal;
  String? classificacao;

  MirrorOrderPedidos(
      {this.idPedido,
      this.numeroPedidoCliente,
      this.tipoPedido,
      this.idDistribuidor,
      this.idCondicaoComercial,
      this.prazo,
      this.data,
      this.status,
      this.cnpj,
      this.idUsuario,
      this.nomeUsuario,
      this.perfilUsuario,
      this.idLoja,
      this.razaoSocial,
      this.origem,
      this.idPrazoPagamento,
      this.descricaoPrazoPagamento,
      this.idComboOferta,
      this.descricaoComboOferta,
      this.idTabloide,
      this.descricaoTabloide,
      this.quantidadeApresentacao,
      this.totalUnidades,
      this.totalBruto,
      this.totalLiquido,
      this.descontoMedio,
      this.observacao,
      this.distribuidores,
      this.itens,
      this.pedidos,
      this.datasProgramadas,
      this.faturamento,
      this.pedidoOriginal,
      this.classificacao});

  MirrorOrderPedidos.fromJson(Map<String, dynamic> json) {
    idPedido = json['IdPedido'];
    numeroPedidoCliente = json['NumeroPedidoCliente'];
    tipoPedido = json['TipoPedido'];
    idDistribuidor = json['IdDistribuidor'];
    idCondicaoComercial = json['IdCondicaoComercial'];
    prazo = json['Prazo'];
    data = json['Data'];
    status = json['Status'];
    cnpj = json['Cnpj'];
    idUsuario = json['IdUsuario'];
    nomeUsuario = json['NomeUsuario'];
    perfilUsuario = json['PerfilUsuario'];
    idLoja = json['IdLoja'];
    razaoSocial = json['RazaoSocial'];
    origem = json['Origem'];
    idPrazoPagamento = json['IdPrazoPagamento'];
    descricaoPrazoPagamento = json['DescricaoPrazoPagamento'];
    idComboOferta = json['IdComboOferta'];
    descricaoComboOferta = json['DescricaoComboOferta'];
    idTabloide = json['IdTabloide'];
    descricaoTabloide = json['DescricaoTabloide'];
    quantidadeApresentacao = json['QuantidadeApresentacao'];
    totalUnidades = json['TotalUnidades'];
    totalBruto = json['TotalBruto'];
    totalLiquido = json['TotalLiquido'];
    descontoMedio = json['DescontoMedio'];
    observacao = json['Observacao'];
    distribuidores = json['Distribuidores'];
    if (json['Itens'] != null) {
      itens = <MirrorOrderItens>[];
      json['Itens'].forEach((v) {
        itens!.add(MirrorOrderItens.fromJson(v));
      });
    }
    pedidos = json['Pedidos'];
    datasProgramadas = json['DatasProgramadas'];
    faturamento = json['Faturamento'];
    pedidoOriginal = json['PedidoOriginal'];
    classificacao = json['Classificacao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPedido'] = idPedido;
    data['NumeroPedidoCliente'] = numeroPedidoCliente;
    data['TipoPedido'] = tipoPedido;
    data['IdDistribuidor'] = idDistribuidor;
    data['IdCondicaoComercial'] = idCondicaoComercial;
    data['Prazo'] = prazo;
    data['Data'] = this.data;
    data['Status'] = status;
    data['Cnpj'] = cnpj;
    data['IdUsuario'] = idUsuario;
    data['NomeUsuario'] = nomeUsuario;
    data['PerfilUsuario'] = perfilUsuario;
    data['IdLoja'] = idLoja;
    data['RazaoSocial'] = razaoSocial;
    data['Origem'] = origem;
    data['IdPrazoPagamento'] = idPrazoPagamento;
    data['DescricaoPrazoPagamento'] = descricaoPrazoPagamento;
    data['IdComboOferta'] = idComboOferta;
    data['DescricaoComboOferta'] = descricaoComboOferta;
    data['IdTabloide'] = idTabloide;
    data['DescricaoTabloide'] = descricaoTabloide;
    data['QuantidadeApresentacao'] = quantidadeApresentacao;
    data['TotalUnidades'] = totalUnidades;
    data['TotalBruto'] = totalBruto;
    data['TotalLiquido'] = totalLiquido;
    data['DescontoMedio'] = descontoMedio;
    data['Observacao'] = observacao;
    data['Distribuidores'] = distribuidores;
    if (itens != null) {
      data['Itens'] = itens!.map((v) => v.toJson()).toList();
    }
    data['Pedidos'] = pedidos;
    data['DatasProgramadas'] = datasProgramadas;
    data['Faturamento'] = faturamento;
    data['PedidoOriginal'] = pedidoOriginal;
    data['Classificacao'] = classificacao;
    return data;
  }
}

class MirrorOrderItens {
  int? idItemPedido;
  int? idPedido;
  int? idProduto;
  int? qtdeProduto;
  int? qtdeAtendida;
  String? motivo;
  String? retorno;
  String? eAN;
  double? valorUnitario;
  double? desconto;
  double? descontoAdicionalGestor;
  int? idProdutoGerador;
  String? dataAlteracao;
  String? apresentacao;
  String? descricao;
  String? familia;
  bool? bonificado;
  int? idProdutoDUN;
  int? qtdeDUN;
  String? produtoTotal;
  String? dUN;
  bool? isDUN;
  String? apresentacaoDUN;
  int? idContrato;
  String? motivoNaoAtendimento;
  String? retornoItem;
  int? qtdeFaturada;
  int? descontoFaturado;

  MirrorOrderItens(
      {this.idItemPedido,
      this.idPedido,
      this.idProduto,
      this.qtdeProduto,
      this.qtdeAtendida,
      this.motivo,
      this.retorno,
      this.eAN,
      this.valorUnitario,
      this.desconto,
      this.descontoAdicionalGestor,
      this.idProdutoGerador,
      this.dataAlteracao,
      this.apresentacao,
      this.descricao,
      this.familia,
      this.bonificado,
      this.idProdutoDUN,
      this.qtdeDUN,
      this.produtoTotal,
      this.dUN,
      this.isDUN,
      this.apresentacaoDUN,
      this.idContrato,
      this.motivoNaoAtendimento,
      this.retornoItem,
      this.qtdeFaturada,
      this.descontoFaturado});

  MirrorOrderItens.fromJson(Map<String, dynamic> json) {
    idItemPedido = json['idItemPedido'];
    idPedido = json['idPedido'];
    idProduto = json['idProduto'];
    qtdeProduto = json['QtdeProduto'];
    qtdeAtendida = json['QtdeAtendida'];
    motivo = json['Motivo'];
    retorno = json['Retorno'];
    eAN = json['EAN'];
    valorUnitario = json['ValorUnitario'];
    desconto = json['Desconto'];
    descontoAdicionalGestor = json['DescontoAdicionalGestor'];
    idProdutoGerador = json['idProdutoGerador'];
    dataAlteracao = json['dataAlteracao'];
    apresentacao = json['Apresentacao'];
    descricao = json['Descricao'];
    familia = json['Familia'];
    bonificado = json['Bonificado'];
    idProdutoDUN = json['idProdutoDUN'];
    qtdeDUN = json['QtdeDUN'];
    produtoTotal = json['ProdutoTotal'];
    dUN = json['DUN'];
    isDUN = json['isDUN'];
    apresentacaoDUN = json['ApresentacaoDUN'];
    idContrato = json['idContrato'];
    motivoNaoAtendimento = json['MotivoNaoAtendimento'];
    retornoItem = json['RetornoItem'];
    qtdeFaturada = json['QtdeFaturada'];
    descontoFaturado = json['DescontoFaturado'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idItemPedido'] = idItemPedido;
    data['idPedido'] = idPedido;
    data['idProduto'] = idProduto;
    data['QtdeProduto'] = qtdeProduto;
    data['QtdeAtendida'] = qtdeAtendida;
    data['Motivo'] = motivo;
    data['Retorno'] = retorno;
    data['EAN'] = eAN;
    data['ValorUnitario'] = valorUnitario;
    data['Desconto'] = desconto;
    data['DescontoAdicionalGestor'] = descontoAdicionalGestor;
    data['idProdutoGerador'] = idProdutoGerador;
    data['dataAlteracao'] = dataAlteracao;
    data['Apresentacao'] = apresentacao;
    data['Descricao'] = descricao;
    data['Familia'] = familia;
    data['Bonificado'] = bonificado;
    data['idProdutoDUN'] = idProdutoDUN;
    data['QtdeDUN'] = qtdeDUN;
    data['ProdutoTotal'] = produtoTotal;
    data['DUN'] = dUN;
    data['isDUN'] = isDUN;
    data['ApresentacaoDUN'] = apresentacaoDUN;
    data['idContrato'] = idContrato;
    data['MotivoNaoAtendimento'] = motivoNaoAtendimento;
    data['RetornoItem'] = retornoItem;
    data['QtdeFaturada'] = qtdeFaturada;
    data['DescontoFaturado'] = descontoFaturado;
    return data;
  }
}
