enum Flavor {
  itrade,
  pharmalink,
}

class F {
  static Flavor? appFlavor;

  static String get name => appFlavor?.name ?? '';

  static String get title {
    switch (appFlavor) {
      case Flavor.itrade:
        return 'iTrade';
      case Flavor.pharmalink:
      default:
        return 'Pharmalink';
    }
  }

  static String get logo {
    switch (appFlavor) {
      case Flavor.itrade:
        return 'assets/app/itrade/logo.png';
      case Flavor.pharmalink:
      default:
        return 'assets/app/pharmalink/logo.png';
    }
  }

  static String get iconlogo {
    switch (appFlavor) {
      case Flavor.itrade:
        return 'assets/app/itrade/icon.png';
      case Flavor.pharmalink:
      default:
        return 'assets/app/pharmalink/icon.png';
    }
  }

  static String get splashScreen {
    switch (appFlavor) {
      case Flavor.itrade:
        return 'assets/app/itrade/splash.png';
      case Flavor.pharmalink:
      default:
        return 'assets/app/pharmalink/splash.png';
    }
  }
}
