﻿import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:archive/archive.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path/path.dart' as p;
import 'package:path_provider/path_provider.dart';
import 'package:sqlite_explorer/exports/basic_exports.dart';
import 'package:sqlite_explorer/exports/get_exports.dart';
import 'package:sqlite_explorer/modules/home/<USER>/data_model.dart';
import 'package:sqlite_explorer/modules/home/<USER>/table_key.dart';

class HomeController extends GetxController {
  HomeController();
  List<DataModel> dataModels = [];
  List<DataModel> dataModelsFull = [];
  String? jsonData;
  DataModel? selectedDataModel;
  List<TableKey> tableKeys = [];
  TableKey? selectedTableKey;
  String? searchKey;
  int totalCount = 0;
  int totalCountFiltered = 0;

  int currentPage = 1;
  int pageSize = 10;

  TextEditingController searchIdController = TextEditingController();
  TextEditingController searchTypeController = TextEditingController();
  TextEditingController workspaceIdController = TextEditingController();
  TextEditingController storeIdController = TextEditingController();

  TextEditingController startDateController = TextEditingController();
  TextEditingController endDateController = TextEditingController();

  DateTime? startDate;
  DateTime? endDate;

  @override
  Future<void> onReady() async {
    super.onReady();
  }

  void clearData() {
    clearFilter();
    dataModelsFull = [];
    dataModels = [];
    tableKeys = [];
    update();
  }

  Future<String?> selectZipFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['zip'],
      );

      if (result != null) {
        String filePath = result.files.single.path!;
        if (filePath.toLowerCase().endsWith('.zip')) {
          await downloadDatabase(zipFilePath: filePath);
          return filePath;
        } else {
          Dialogs.info("Erro", "Por favor, selecione um arquivo .zip válido.");
          return null;
        }
      } else {
        // User canceled the picker
        return null;
      }
    } catch (e) {
      log('Erro ao selecionar o arquivo: $e');
      Dialogs.info("Erro", "Ocorreu um erro ao selecionar o arquivo.");
      return null;
    }
  }

  Future<void> downloadDatabase({required String zipFilePath}) async {
    clearData();

    try {
      // Get the application documents directory
      final appDocDir = await getApplicationDocumentsDirectory();
      final appDocPath = appDocDir.path;

      // Create a directory to store the database
      final dbDir = Directory(p.join(appDocPath, 'pharmalink\\databases'));
      if (!await dbDir.exists()) {
        await dbDir.create(recursive: true);
      }

      // Read the zip file
      final bytes = File(zipFilePath).readAsBytesSync();
      final archive = ZipDecoder().decodeBytes(bytes);

      // Extract the contents (assuming there's only one .db file)
      for (final file in archive) {
        final filename = file.name;
        if (filename.endsWith('.db')) {
          final data = file.content as List<int>;
          File(p.join(dbDir.path, filename))
            ..createSync(recursive: true)
            ..writeAsBytesSync(data);

          dbContext.setDatabasePath(p.join(dbDir.path, filename));
          final isCorrupted = await dbContext.isDatabaseCorrupted();
          if (isCorrupted) {
            Dialogs.info("Atenção", "O banco de dados está corrompido");
          } else {
            await getTablesList();
            await getTables();
          }

          log('Database extracted to: ${p.join(dbDir.path, filename)}');
          // You can store this path for later use
          // String dbPath = p.join(dbDir.path, filename);
        }
      }
    } catch (e) {
      log('Error during database extraction: $e');
      // Handle the error appropriately
    }
    update();
  }

  Future<void> getTablesList() async {
    totalCount = await dbContext.getTotalCount();
    final distinctKeys = await dbContext.getDistinctKeys();

    List<TableKey> listKeys = distinctKeys
        .map((item) => TableKey(
            key: item, name: keyToFriendlyName[item] ?? item, order: 0))
        .toList();

    listKeys.sort((a, b) => a.name.compareTo(b.name));

    for (int i = 0; i < listKeys.length; i++) {
      listKeys[i] =
          TableKey(key: listKeys[i].key, name: listKeys[i].name, order: i + 1);
    }

    tableKeys = [TableKey(key: '', name: 'Todas as tabelas', order: 0)];
    tableKeys.addAll(listKeys);
    tableKeys.sort((a, b) => a.order.compareTo(b.order));
    update();
  }

  Future<void> getTables({int page = 1, int size = 10}) async {
    final tables = await dbContext.readAllData();
    if (tables.isNotEmpty) {
      dataModelsFull = tables.map((item) => DataModel.fromJson(item)).toList();
      dataModelsFull.sort((a, b) => b.id!.compareTo(a.id!));

      // Pagination logic
      int startIndex = (page - 1) * size;
      int endIndex = startIndex + size;
      if (startIndex < dataModelsFull.length) {
        dataModels = dataModelsFull.sublist(
          startIndex,
          endIndex > dataModelsFull.length ? dataModelsFull.length : endIndex,
        );
      } else {
        dataModels = [];
      }
    } else {
      dataModels = [];
      log('Unexpected data format in tables');
    }
    update();
  }

  Future<void> nextPage() async {
    currentPage++;
    await filterDataModelByParams(page: currentPage, size: pageSize);
  }

  Future<void> previousPage() async {
    if (currentPage > 1) {
      currentPage--;
      await filterDataModelByParams(page: currentPage, size: pageSize);
    }
  }

  void setJsonData(DataModel? item) {
    if (item == null) {
      jsonData = "";
    } else {
      jsonData = jsonEncode(item.toJson());
    }
    selectedDataModel = item;
    update();
  }

  void setSearchKey(String? key) {
    searchKey = key == '' ? null : key;
    update();
  }

  Future<void> filterDataModelByParams({int page = 1, int size = 10}) async {
    List<DataModel> filteredDataModels = dataModelsFull.where((item) {
      bool matchesKey = searchKey == null ||
          item.key.toLowerCase() == searchKey!.toLowerCase();
      bool matchesSearchId = searchIdController.text.isEmpty ||
          item.id?.toString() == searchIdController.text;
      bool matchesWorkspaceId = workspaceIdController.text.isEmpty ||
          item.workspaceId?.toString() == workspaceIdController.text;
      bool matchesStoreId = storeIdController.text.isEmpty ||
          item.storeId?.toString() == storeIdController.text;
      bool matchesDateRange =
          (startDate == null || item.createdAt!.isAfter(startDate!)) &&
              (endDate == null || item.createdAt!.isBefore(endDate!));
      bool matchesType = searchTypeController.text.isEmpty ||
          item.value
                  ?.toLowerCase()
                  .contains(searchTypeController.text.toLowerCase()) ==
              true;

      return matchesKey &&
          matchesSearchId &&
          matchesWorkspaceId &&
          matchesStoreId &&
          matchesType &&
          matchesDateRange;
    }).toList();

    filteredDataModels.sort((a, b) => b.id!.compareTo(a.id!));
    totalCountFiltered = filteredDataModels.length;

    // Pagination logic
    int startIndex = (page - 1) * size;
    int endIndex = startIndex + size;
    if (startIndex < filteredDataModels.length) {
      dataModels = filteredDataModels.sublist(
        startIndex,
        endIndex > filteredDataModels.length
            ? filteredDataModels.length
            : endIndex,
      );
    } else {
      dataModels = [];
    }
    update();
  }

  void clearFilter() {
    searchKey = null;
    selectedDataModel = null;
    selectedTableKey = null;
    startDate = null;
    endDate = null;
    startDateController.clear();
    endDateController.clear();
    searchIdController.clear();
    workspaceIdController.clear();
    storeIdController.clear();
    dataModels = List.from(dataModelsFull);
    dataModels.sort((a, b) => b.id!.compareTo(a.id!));
    update();
  }

  String getValueFromLogTraceMonitor(DataModel item, String fieldName) {
    if (item.key == 'logTraceMonitorModel') {
      try {
        Map<String, dynamic> valueMap = json.decode(item.value ?? '');
        if (valueMap.containsKey(fieldName)) {
          String value = valueMap[fieldName].toString();
          return value == 'null' ? '' : value;
        } else {
          return 'Field not found';
        }
      } catch (e) {
        log('Error decoding JSON: $e');
        return 'Error decoding JSON';
      }
    } else {
      return 'Not found';
    }
  }

  bool hasValueInLogTraceMonitor(DataModel item, String fieldName) {
    if (item.key == 'logTraceMonitorModel') {
      try {
        Map<String, dynamic> valueMap = json.decode(item.value ?? '');
        if (valueMap.containsKey(fieldName)) {
          return valueMap[fieldName] != null &&
              valueMap[fieldName].toString() != 'null';
        }
        return false;
      } catch (e) {
        log('Error decoding JSON: $e');
        return false;
      }
    }
    return false;
  }

  bool isTypeInLogTraceMonitor(DataModel item, String typeValue) {
    if (item.key == 'logTraceMonitorModel') {
      try {
        Map<String, dynamic> valueMap = json.decode(item.value ?? '');
        if (valueMap.containsKey('type')) {
          return valueMap['type'].toString().toLowerCase() ==
              typeValue.toLowerCase();
        }
        return false;
      } catch (e) {
        log('Error decoding JSON: $e');
        return false;
      }
    }
    return false;
  }

  Future<void> setPageSize(int newSize) async {
    pageSize = newSize;
    currentPage = 1; // Reset to first page when changing page size
    await filterDataModelByParams(
        page: currentPage, size: pageSize); // Fetch data with new page size
  }
}
