import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/stores_parameters/models/tabloides_model.dart';

class OrdersTabloidCardWidget extends StatelessWidget {
  const OrdersTabloidCardWidget({
    super.key,
    required this.item,
  });
  final TabloidesModel item;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: MediaQuery.of(context).size.width,
      child: Card(
        elevation: 5,
        child: Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelWidget(
                title: item.descricao ?? "-",
                fontSize: DeviceSize.fontSize(18, 21),
                fontWeight: FontWeight.bold,
              ),
              5.toHeightSpace(),
              LabelWidget(
                title: item.dataFim
                        ?.formatDate(formatType: DateFormatType.ddMMyyyy) ??
                    "-",
                fontSize: 13.sp,
                textColor: Colors.grey.shade600,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
