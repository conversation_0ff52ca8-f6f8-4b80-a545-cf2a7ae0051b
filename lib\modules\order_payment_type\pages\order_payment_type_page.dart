import 'dart:ui';

import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/order_payment_type/pages/widgets/order_payment_type_item_selected.dart';
import 'package:pharmalink/modules/stores/models/commercial_condition_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/prazo_pagamento_model.dart';

class OrderPaymentTypePage extends StatelessWidget {
  const OrderPaymentTypePage({super.key});
  Widget proxyDecorator(Widget child, int index, Animation<double> animation) {
    return AnimatedBuilder(
      animation: animation,
      builder: (BuildContext context, Widget? child) {
        final double animValue = Curves.easeInOut.transform(animation.value);
        final double elevation = lerpDouble(0, 6, animValue)!;
        return Material(
          elevation: elevation,
          color: themesController.getPrimaryColor(),
          shadowColor: themesController.getPrimaryColor(),
          child: child,
        );
      },
      child: child,
    );
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<OrderPaymentTypeController>(
        "OrderPaymentTypePage", builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelWidget(
                title: globalParams.getCurrentStore()?.razaoSocial ?? "-",
                fontSize: DeviceSize.fontSize(14, 18),
                fontWeight: FontWeight.w600,
                textColor: whiteColor,
              ),
              LabelWidget(
                title: globalParams.getCurrentStore()?.cNPJ ?? "-",
                fontSize: DeviceSize.fontSize(11, 13),
                textColor: whiteColor,
              ),
            ],
          ),
          actions: [
            CustomInkWell(
              onTap: () async => ctrl.dynatraceAction.reportClickEvent(
                  "Avançar", () async => await ctrl.advance()),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
                child: LabelWidget(
                  title: "Avançar".toUpperCase(),
                  fontSize: DeviceSize.fontSize(16, 19),
                  textColor: whiteColor,
                ),
              ),
            ),
          ],
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () async {
              GetC.close();
            },
          ),
        ),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: ListView(
            children: [
              OrdersCardInfoWidget(
                orderType: globalParams.getTypeOrderName(),
                tabloidName: globalParams.order.tabloidName,
              ),
              10.toHeightSpace(),
              LabelWidget(
                title: "Forma de Pagamento",
                fontSize: DeviceSize.fontSize(14, 18),
              ),
              10.toHeightSpace(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey.shade300),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<PrazoPagamentoModel>(
                    isExpanded: true,
                    value: globalParams.getDeadlinePayment(),
                    hint: const Text('Selecione uma forma de pagamento'),
                    items: ctrl.deadlinePayment.map((item) {
                      return DropdownMenuItem<PrazoPagamentoModel>(
                        value: item,
                        child: Text(
                          item.descricao ?? '',
                          style: TextStyle(
                            fontSize: DeviceSize.fontSize(16, 19),
                            color: Colors.black,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                      );
                    }).toList(),
                    onChanged: (PrazoPagamentoModel? item) {
                      if (item != null) {
                        ctrl.setSeletedType(item);
                      }
                    },
                  ),
                ),
              ),
              if (ctrl.condicaoComercialCustom) const Gap(10),
              if (ctrl.condicaoComercialCustom)
                LabelWidget(
                  title: "Condição Comercial",
                  fontSize: DeviceSize.fontSize(14, 18),
                ),
              if (ctrl.condicaoComercialCustom) 10.toHeightSpace(),
              if (ctrl.condicaoComercialCustom)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  decoration: BoxDecoration(
                    border: Border.all(color: Colors.grey.shade300),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: DropdownButtonHideUnderline(
                    child: DropdownButton<CommercialConditionModel>(
                      isExpanded: true,
                      value: ctrl.commercialConditionSelected,
                      hint: const Text('Selecione uma condição comercial'),
                      items: [
                        DropdownMenuItem<CommercialConditionModel>(
                          value: null,
                          child: Text(
                            'Selecione',
                            style: TextStyle(
                              fontSize: DeviceSize.fontSize(16, 19),
                              color: Colors.black,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                        ),
                        ...ctrl.commercialConditions.map((item) {
                          return DropdownMenuItem<CommercialConditionModel>(
                            value: item,
                            child: Text(
                              item.description,
                              style: TextStyle(
                                fontSize: DeviceSize.fontSize(16, 19),
                                color: Colors.black,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          );
                        }),
                      ],
                      onChanged: (CommercialConditionModel? item) {
                        ctrl.setCommercialCondition(item);
                      },
                    ),
                  ),
                ),
              Visibility(
                visible: ctrl.isSelected &&
                    ctrl.distributorsSelected.isNotEmpty &&
                    ctrl.usaLooping,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    10.toHeightSpace(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        LabelWidget(
                          title: "Selecionados",
                          fontSize: DeviceSize.fontSize(14, 18),
                        ),
                        CustomInkWell(
                          onTap: () async => ctrl.setOrderEnabled(),
                          child: LabelWidget(
                            title: "Ordenar",
                            fontSize: DeviceSize.fontSize(14, 18),
                          ),
                        ),
                      ],
                    ),
                    5.toHeightSpace(),
                    Divider(color: Colors.grey.shade300, height: 1),
                    10.toHeightSpace(),
                  ],
                ),
              ),
              Visibility(
                visible: ctrl.isSelected && ctrl.usaLooping,
                child: ctrl.orderEnabled
                    ? SizedBox(
                        height: 49.0 * ctrl.distributorsSelected.length,
                        child: ReorderableListView.builder(
                          proxyDecorator: proxyDecorator,
                          itemCount: ctrl.distributorsSelected.length,
                          itemBuilder: (context, index) {
                            final item = ctrl.distributorsSelected[index];
                            return OrderPaymentTypeItemSelectedWidget(
                              key: ValueKey(item.distribuidorId),
                              item: item,
                              isSelected: true,
                              canReorder: true,
                            );
                          },
                          onReorder: (oldIndex, newIndex) {
                            ctrl.reorderData(oldIndex, newIndex);
                          },
                        ),
                      )
                    : Column(
                        children: ctrl.distributorsSelected.map((item) {
                          return OrderPaymentTypeItemSelectedWidget(
                            canReorder: false,
                            item: item,
                            isSelected: true,
                          );
                        }).toList(),
                      ),
              ),
              Visibility(
                visible: ctrl.isSelected,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    10.toHeightSpace(),
                    LabelWidget(
                      title: "Distribuidores",
                      fontSize: DeviceSize.fontSize(14, 18),
                    ),
                    5.toHeightSpace(),
                    Divider(color: Colors.grey.shade300, height: 1),
                    const Gap(10),
                  ],
                ),
              ),
              Visibility(
                visible: ctrl.isSelected && ctrl.usaLooping,
                child: Column(
                  children: ctrl.distributors.map((item) {
                    return OrderPaymentTypeItemSelectedWidget(
                      canReorder: false,
                      isSelected: false,
                      item: item,
                    );
                  }).toList(),
                ),
              ),
              Visibility(
                visible: ctrl.isSelected && !ctrl.usaLooping,
                child: Column(
                  children: ctrl.distributors.map((item) {
                    return Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: CustomInkWell(
                                onTap: () {
                                  ctrl.setDistribuitorSingle(item);
                                },
                                child: LabelWidget(
                                  title: item.distribuidor!.nomeFantasia!
                                      .toUpperCase(),
                                  fontSize: DeviceSize.fontSize(15, 19),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ),
                            Transform.scale(
                              scale: DeviceSize.scale(),
                              child: Radio<bool>(
                                value: item.isSelected! &&
                                    globalParams.order.currentDistributor
                                            ?.distribuidorId ==
                                        item.distribuidorId,
                                groupValue: true,
                                onChanged: (value) {
                                  ctrl.setDistribuitorSingle(item);
                                },
                                activeColor: themesController.getPrimaryColor(),
                              ),
                            ),
                          ],
                        ),
                        Divider(color: Colors.grey.shade300, height: 1)
                      ],
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
