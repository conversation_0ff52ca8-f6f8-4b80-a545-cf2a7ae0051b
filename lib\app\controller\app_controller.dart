import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/login/models/login_response_model.dart';
import 'package:pharmalink/modules/workspaces/models/workspaces_model.dart';

enum Environment { production, developer, staging }

enum DeviceType { mobile, tablet }

class AppController extends GetxControllerInstrumentado<AppController> {
  AppController();

  ConnectivityResult? _lastConnectivityResult;
  late String internetMode = "";
  late bool hasInternet = true;
  late StreamSubscription<List<ConnectivityResult>> connectivitySubscription;

  bool hasErrorRefreshToken = false;
  String get apiUrlAzure => urlsAzure[currentEnvironment] ?? "";
  String get apiUrlSyncOffline => urlsSyncOffline[currentEnvironment] ?? "";
  String get apiUrlDataVault => urlsSyncOffline[currentEnvironment] ?? "";

  int get httpExpirationDays => 7;
  Environment currentEnvironment = Environment.staging;

  @override
  void onReady() {
    super.onReady();

    const envString = String.fromEnvironment("ENV", defaultValue: "developer");

    currentEnvironment = Environment.values.byName(envString);
    log('AppController: currentEnvironment: $currentEnvironment');
    connectivitySubscription = Connectivity().onConnectivityChanged.listen((
      List<ConnectivityResult> results,
    ) {
      if (results.isNotEmpty) {
        updateInternetMode(results.first);
      }
    });
    Connectivity().checkConnectivity().then((List<ConnectivityResult> results) {
      if (results.isNotEmpty) {
        updateInternetMode(results.first);
      }
    });
  }

  @override
  void onClose() {
    connectivitySubscription.cancel();
    super.onClose();
  }

  void updateInternetMode(ConnectivityResult result) {
    hasInternet = result != ConnectivityResult.none;
    _lastConnectivityResult = result;
    switch (result) {
      case ConnectivityResult.none:
        internetMode = "Offline";
        break;
      case ConnectivityResult.wifi:
        internetMode = "Wi-Fi";
        break;
      case ConnectivityResult.mobile:
        internetMode = "3G/4G";
        break;
      default:
        internetMode = "Offline";
        break;
    }
    update();
  }

  String formsUrl =
      "https://forms.office.com/Pages/ResponsePage.aspx?id=0jTMAVs2REieuPyf6ejpiO_7WJdWqahMrR-J-_ZxF2JUMjA5R1g3MUZWMTY2NjZMOFdMOEY1QkNDUSQlQCN0PWcu";

  Map<Environment, String> urlsAzure = {
    Environment.developer:
        'https://idp-api-gtw.azure-api.net/marketplace-pedido-api-dev/',
    Environment.staging: 'https://idp-api-gtw.azure-api.net/marketplace-pedido-api-pre/',
    Environment.production: 'https://api-mng.interplayers.com.br/marketplace-pedido-api/',
  };

  Map<Environment, String> urlsSyncOffline = {
    Environment.developer:
        'https://aks-dev.interplayers.com.br/marketplace-appoffline-api/store/',
    Environment.staging:
        'https://idp-api-gtw.azure-api.net/marketplace-appoffline-api-pre/store/',
    Environment.production:
        'https://api-mng.interplayers.com.br/marketplace-appoffline-api/store/',
  };

  Map<Environment, String> urlsDataVault = {
    Environment.developer:
        'https://aks-dev.interplayers.com.br/pharmalink-datavault-api/',
    Environment.staging: 'https://aks-pre.interplayers.com.br/pharmalink-datavault-api/',
    Environment.production:
        'https://api-mng.interplayers.com.br/pharmalink-datavault-api/',
  };

  bool hasInitialize = true;

  LoginResponseModel? userLogged;
  bool get hasUserLogged => userLogged != null;

  LoginResponseModel? userLogout;

  WorkspacesModel? workspace;
  bool hasWorkspaces() => workspace != null;

  DeviceType deviceType = DeviceType.mobile;

  void setDevice(bool isTablet) {
    if (isTablet) {
      deviceType = DeviceType.tablet;
    } else {
      deviceType = DeviceType.mobile;
    }
  }

  bool get isMobile => deviceType == DeviceType.mobile;

  bool get isTablet => deviceType == DeviceType.tablet;


  bool get isProduction => currentEnvironment == Environment.production;

  Future<String> loadUrl() async {
    return apiUrlAzure;
  }

  void simulateTokenExpired() {
    userLogged?.expires = DateTime.now();
  }

  Future<void> isValidToken({bool? noMessages}) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "isValidToken",
    );
    try {
      bool isConnected = await checkConnectivity();
      if (isConnected) {
        if (userLogged != null &&
            userLogged!.expires != null &&
            userLogged!.isB2c != true) {
          final duration = DateTime.now().difference(userLogged!.expires!);

          debugPrint(DateTime.now().toString());
          debugPrint(userLogged!.expires?.toString());

          if (appController.hasErrorRefreshToken == false) {
            if (duration.inMinutes >= -25) {
              await loginController
                  .withDynatraceAction(dynatraceAction)
                  .refreshToken(userLogged!, noMessages: noMessages);
              subAction.reportEvent("Token Renovado");
            }
          }

          dynatrace.collectAndReportSessionData();
        }
      }
    } finally {
      leaveAction();
    }
  }

  Future<bool> checkConnectivity() async {
    final stopwatch = Stopwatch()..start();
    log(
      'Connectivity: Iniciando checkConnectivity: ${stopwatch.elapsedMilliseconds}ms',
    );

    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "checkConnectivity",
    );
    try {
      if (_lastConnectivityResult == ConnectivityResult.none) {
        subAction.reportValue(
          "ConnectivityResult",
          ConnectivityResult.none.name,
        );
        return false;
      }
      return await checkInternetStability();
    } finally {
      leaveAction();
      stopwatch.stop();
      log(
        'Connectivity: Finalizando checkConnectivity: ${stopwatch.elapsedMilliseconds}ms',
      );
    }
  }

  Future<bool> checkCameraPermission() async {
    var status = await Permission.camera.status;
    return status.isGranted;
  }

  Future<bool> checkInternetStability() async {
    final stopwatch = Stopwatch()..start();
    log(
      'Connectivity: Iniciando checkInternetStability: ${stopwatch.elapsedMilliseconds}ms',
    );

    try {
      if (workspace == null) return true;
      final hostname = extractHostname(workspace!.link ?? "");
      final result = await InternetAddress.lookup(hostname);
      if (result.isNotEmpty && result[0].rawAddress.isNotEmpty) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    } finally {
      stopwatch.stop();
      log(
        'Connectivity: Finalizando checkInternetStability: ${stopwatch.elapsedMilliseconds}ms',
      );
    }
  }

  String extractHostname(String url) {
    final uri = Uri.parse(url);
    return uri.host;
  }

  Future<void> testInternetConnection({bool? closeMessage}) async {
    Future.delayed(const Duration(seconds: 1), () async {
      if (closeMessage == true) {
        GetC.close();
      }
      await Dialogs.confirm(
        "Sem Conexão à Internet",
        "Identificamos um problema na sua conexão de internet. Deseja testar sua conexão?\n\nHost: ${extractHostname(workspace!.link ?? "")}",
        buttonNameOk: "Testar Conexão".toUpperCase(),
        buttonNameCancel: "Cancelar".toUpperCase(),
        onPressedCancel: () async {
          GetC.close();
        },
        onPressedOk: () async {
          GetC.close();
          final state = await appController.checkInternetStability();
          if (!state) {
            await testInternetConnection();
          } else {
            SnackbarCustom.snackbarSucess(
              "Conexão Estabelecida",
              "Sua conexão com a internet foi restabelecida.",
            );
          }
        },
      );
    });
  }
}
