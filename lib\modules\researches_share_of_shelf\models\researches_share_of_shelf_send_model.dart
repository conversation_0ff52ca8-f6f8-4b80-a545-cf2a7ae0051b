class ResearchesShareOfShelfSendModel {
  int? idPesquisaShareOfShelf;
  int? idRota;
  String? dataPlanejamentoVisita;
  List<ResearchesShareOfShelfSendProdutosShare>? produtosShare;

  ResearchesShareOfShelfSendModel(
      {this.idPesquisaShareOfShelf,
      this.idRota,
      this.dataPlanejamentoVisita,
      this.produtosShare});

  ResearchesShareOfShelfSendModel.fromJson(Map<String, dynamic> json) {
    idPesquisaShareOfShelf = json['idPesquisaShareOfShelf'];
    idRota = json['idRota'];
    dataPlanejamentoVisita = json['dataPlanejamentoVisita'];
    if (json['produtosShare'] != null) {
      produtosShare = <ResearchesShareOfShelfSendProdutosShare>[];
      json['produtosShare'].forEach((v) {
        produtosShare!.add(ResearchesShareOfShelfSendProdutosShare.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idPesquisaShareOfShelf'] = idPesquisaShareOfShelf;
    data['idRota'] = idRota;
    data['dataPlanejamentoVisita'] = dataPlanejamentoVisita;
    if (produtosShare != null) {
      data['produtosShare'] = produtosShare!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResearchesShareOfShelfSendProdutosShare {
  int? idPesquisaShareOfShelfProduto;
  int? idFamilia;
  double? shareIdeal;
  double? shareApurado;
  int? facingGondola;
  int? facingProduto;
  String? imagem;
  int? tipoResposta;

  ResearchesShareOfShelfSendProdutosShare(
      {this.idPesquisaShareOfShelfProduto,
      this.idFamilia,
      this.shareIdeal,
      this.shareApurado,
      this.facingGondola,
      this.facingProduto,
      this.imagem,
      this.tipoResposta});

  ResearchesShareOfShelfSendProdutosShare.fromJson(Map<String, dynamic> json) {
    idPesquisaShareOfShelfProduto = json['idPesquisaShareOfShelfProduto'];
    idFamilia = json['idFamilia'];
    shareIdeal = json['shareIdeal'];
    shareApurado = json['shareApurado'];
    facingGondola = json['facingGondola'];
    facingProduto = json['facingProduto'];
    imagem = json['imagem'];
    tipoResposta = json['tipoResposta'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idPesquisaShareOfShelfProduto'] = idPesquisaShareOfShelfProduto;
    data['idFamilia'] = idFamilia;
    data['shareIdeal'] = shareIdeal;
    data['shareApurado'] = shareApurado;
    data['facingGondola'] = facingGondola;
    data['facingProduto'] = facingProduto;
    data['imagem'] = imagem;
    data['tipoResposta'] = tipoResposta;
    return data;
  }
}
