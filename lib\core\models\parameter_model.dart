import 'package:pharmalink/modules/order_types/models/order_parameters_model.dart';
import 'package:pharmalink/modules/orders/models/orders_local_data_model.dart';
import 'package:pharmalink/modules/orders/models/orders_mix_produto_request_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';
import 'package:pharmalink/modules/orders/models/orders_tabloid_products_request_model.dart';
import 'package:pharmalink/modules/stores/models/commercial_condition_model.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/distribuidores_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/parametrizacao_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/prazo_pagamento_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/tabloides_model.dart';

class ParameterModel {
  ParametrizacaoModel? parametrization;
  int? typeOrderId;
  String? typeOrderStr;
  OrderParametersModel orderParameters = OrderParametersModel();
  StoresModel? currentStore;
  OrdersLocalDataModel? orderLocal;
  bool orderLocalFinish = false;
  PrazoPagamentoModel? deadlinePayment;
  List<PrazoPagamentoModel>? deadlinePaymentList;
  List<DistribuidoresModel>? currentDistributors;
  MixProdutoRequest? parameters;
  TabloidProductsRequest? parametersEsp;
  DistribuidoresModel? currentDistributor;
  ProductsMixModel? products;

  CommercialConditionModel? commercialConditionSelected;

  int? tabloidId;
  String? tabloidName;

  TabloidesModel? tabloidSelected;

  ParameterModel({
    this.parametrization,
    this.typeOrderId,
    this.typeOrderStr,
    OrderParametersModel? orderParameters,
    this.currentStore,
    this.orderLocal,
    this.orderLocalFinish = false,
    this.deadlinePayment,
    this.deadlinePaymentList,
    this.currentDistributors,
    this.parameters,
    this.parametersEsp,
    this.currentDistributor,
    this.products,
    this.tabloidId,
    this.tabloidName,
    this.tabloidSelected,
  }) : orderParameters = orderParameters ?? OrderParametersModel();

  // Método para iniciar o fluxo
  void startFlow(StoresModel? store) {
    setCurrentStore(store);
    parametrization = null;
    typeOrderId = null;
    typeOrderStr = null;
    orderParameters = OrderParametersModel();
    orderLocal = null;
    orderLocalFinish = false;
    deadlinePayment = null;
    deadlinePaymentList = [];
    currentDistributors = [];
    parameters = null;
    parametersEsp = null;
    currentDistributor = null;
    products = null;
    tabloidId = null;
    tabloidName = null;
    tabloidSelected = null;
  }

  // Método para setar parametrization
  void setParametrization(ParametrizacaoModel? param) {
    parametrization = param;
  }

  // Método para setar typeOrderId
  void setTypeOrderId(int? type) {
    typeOrderId = type;
  }

  // Método para setar typeOrderStr
  void setTypeOrderStr(String? typeStr) {
    typeOrderStr = typeStr;
  }

  // Método para setar orderParameters
  void setOrderParameters(OrderParametersModel? parameters) {
    orderParameters = parameters ?? OrderParametersModel();
  }

  // Método para setar currentStore
  void setCurrentStore(StoresModel? store) {
    currentStore = store;
  }

  // Método para setar orderLocal
  void setOrderLocal(OrdersLocalDataModel? orderLocal) {
    this.orderLocal = orderLocal;
  }

  // Método para setar orderLocalFinish
  void setOrderLocalFinish(bool orderLocalFinish) {
    this.orderLocalFinish = orderLocalFinish;
  }

  // Método para setar deadlinePayment
  void setDeadlinePayment(PrazoPagamentoModel? deadlinePayment) {
    this.deadlinePayment = deadlinePayment;
  }

  // Método para setar deadlinePaymentList
  void setDeadlinePaymentList(List<PrazoPagamentoModel>? deadlinePaymentList) {
    this.deadlinePaymentList = deadlinePaymentList;
  }

  // Método para setar currentDistributors
  void setCurrentDistributors(List<DistribuidoresModel>? distributors) {
    currentDistributors = distributors;
  }

  // Método para setar parameters
  void setParameters(MixProdutoRequest? parameters) {
    this.parameters = parameters;
  }

  // Método para setar parametersEsp
  void setParametersEsp(TabloidProductsRequest? parametersEsp) {
    this.parametersEsp = parametersEsp;
  }

  // Método para setar currentDistributor
  void setCurrentDistributor(DistribuidoresModel? distributor) {
    currentDistributor = distributor;
  }

  // Método para setar products
  void setProducts(ProductsMixModel? products) {
    this.products = products;
  }

  // Método para setar tabloidId
  void setTabloid(int? tabloidId, String? tabloidName) {
    this.tabloidId = tabloidId;
    this.tabloidName = tabloidName;
  }

  void setTabloidSelected(TabloidesModel? tabloidSelected) {
    this.tabloidSelected = tabloidSelected;
  }

  void setCommercialConditionSelected(
    CommercialConditionModel? commercialCondition,
  ) {
    commercialConditionSelected = commercialCondition;
  }

  // Método para trocar todos os parâmetros
  void updateParameters({
    ParametrizacaoModel? newParametrization,
    int? newTypeOrderId,
    String? newTypeOrderStr,
    OrderParametersModel? newOrderParameters,
    StoresModel? newCurrentStore,
    OrdersLocalDataModel? newOrderLocal,
    bool? newOrderLocalFinish,
    PrazoPagamentoModel? newDeadlinePayment,
    List<PrazoPagamentoModel>? newDeadlinePaymentList,
    List<DistribuidoresModel>? newCurrentDistributors,
    MixProdutoRequest? newParameters,
    TabloidProductsRequest? newParametersEsp,
    DistribuidoresModel? newCurrentDistributor,
    ProductsMixModel? newProducts,
    int? newTabloidId,
    String? newTabloidName,
    TabloidesModel? newTabloidSelected,
  }) {
    if (newParametrization != null) parametrization = newParametrization;
    if (newTypeOrderId != null) typeOrderId = newTypeOrderId;
    if (newTypeOrderStr != null) typeOrderStr = newTypeOrderStr;
    if (newOrderParameters != null) orderParameters = newOrderParameters;
    if (newCurrentStore != null) currentStore = newCurrentStore;
    if (newOrderLocal != null) orderLocal = newOrderLocal;
    if (newOrderLocalFinish != null) orderLocalFinish = newOrderLocalFinish;
    if (newDeadlinePayment != null) deadlinePayment = newDeadlinePayment;
    if (newDeadlinePaymentList != null) {
      deadlinePaymentList = newDeadlinePaymentList;
    }
    if (newCurrentDistributors != null) {
      currentDistributors = newCurrentDistributors;
    }
    if (newParameters != null) parameters = newParameters;
    if (newParametersEsp != null) parametersEsp = newParametersEsp;
    if (newCurrentDistributor != null) {
      currentDistributor = newCurrentDistributor;
    }
    if (newProducts != null) products = newProducts;
    if (newTabloidId != null) tabloidId = newTabloidId;
    if (newTabloidName != null) tabloidName = newTabloidName;
    if (newTabloidSelected != null) tabloidSelected = newTabloidSelected;
  }

  ParameterModel copyWith({
    ParametrizacaoModel? parametrization,
    int? typeOrderId,
    String? typeOrderStr,
    OrderParametersModel? orderParameters,
    StoresModel? currentStore,
    OrdersLocalDataModel? orderLocal,
    bool? orderLocalFinish,
    PrazoPagamentoModel? deadlinePayment,
    List<PrazoPagamentoModel>? deadlinePaymentList,
    List<DistribuidoresModel>? currentDistributors,
    MixProdutoRequest? parameters,
    TabloidProductsRequest? parametersEsp,
    DistribuidoresModel? currentDistributor,
    ProductsMixModel? products,
    int? tabloidId,
    String? tabloidName,
    TabloidesModel? tabloidSelected,
  }) {
    return ParameterModel(
      parametrization: parametrization ?? this.parametrization,
      typeOrderId: typeOrderId ?? this.typeOrderId,
      typeOrderStr: typeOrderStr ?? this.typeOrderStr,
      orderParameters: orderParameters ?? this.orderParameters,
      currentStore: currentStore ?? this.currentStore,
      orderLocal: orderLocal ?? this.orderLocal,
      orderLocalFinish: orderLocalFinish ?? this.orderLocalFinish,
      deadlinePayment: deadlinePayment ?? this.deadlinePayment,
      deadlinePaymentList: deadlinePaymentList ?? this.deadlinePaymentList,
      currentDistributors: currentDistributors ?? this.currentDistributors,
      parameters: parameters ?? this.parameters,
      parametersEsp: parametersEsp ?? this.parametersEsp,
      currentDistributor: currentDistributor ?? this.currentDistributor,
      products: products ?? this.products,
      tabloidId: tabloidId ?? this.tabloidId,
      tabloidName: tabloidName ?? this.tabloidName,
      tabloidSelected: tabloidSelected ?? this.tabloidSelected,
    );
  }
}
