class OrderAttachmentFileRequestModel {
  int? orderId;
  String? userId;
  List<int>? fileBytes;
  String? fileName;
  String? fileType;
  String? filePath;
  bool? orderSentForApproval;
  String? orderGroupId;

  OrderAttachmentFileRequestModel({
    this.orderId,
    this.userId,
    this.fileBytes,
    this.fileName,
    this.fileType,
    this.filePath,
    this.orderSentForApproval,
    this.orderGroupId,
  });

  OrderAttachmentFileRequestModel.fromJson(Map<String, dynamic> json) {
    orderId = json['idPedido'];
    userId = json['userId'];
    fileBytes = json['arquivo']?.cast<int>();
    fileName = json['nomeArquivo'];
    fileType = json['tipoArquivo'];
    filePath = json['caminhoArquivo'];
    orderSentForApproval = json['pedidoEnviadoParaAprovacao'];
    orderGroupId = json['agrupadorPedido'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idPedido'] = orderId;
    data['userId'] = userId;
    data['arquivo'] = fileBytes;
    data['nomeArquivo'] = fileName;
    data['tipoArquivo'] = fileType;
    data['caminhoArquivo'] = filePath;
    data['pedidoEnviadoParaAprovacao'] = orderSentForApproval;
    data['agrupadorPedido'] = orderGroupId;
    return data;
  }
}
