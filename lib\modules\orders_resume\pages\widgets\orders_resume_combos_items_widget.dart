import 'package:cached_network_image/cached_network_image.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/orders_resume/models/orders_resume_model.dart';
import 'package:pharmalink/modules/orders_resume/pages/widgets/orders_resume_combos_subitems_widget.dart';

class OrdersResumeCombosItemsWidget extends StatelessWidget {
  final OrdersResumeProductsItemsModel item;

  const OrdersResumeCombosItemsWidget({
    super.key,
    required this.item,
  });
  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        child: Column(
          children: [
            Row(
              children: [
                SizedBox(
                  width: 60,
                  child: CachedNetworkImage(
                      imageUrl: item.photo ?? "",
                      placeholder: (context, url) => Image.asset(
                            AppImages.productGeneric,
                          ),
                      errorWidget: (context, url, error) => Image.asset(
                            AppImages.productGeneric,
                          )),
                ),
                LabelWidget(
                  title: item.name ?? "-",
                  fontSize: DeviceSize.fontSize(15, 18),
                  fontWeight: FontWeight.bold,
                )
              ],
            ),
            Padding(
              padding: const EdgeInsets.all(10.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  TilesInfoWidget(
                    title: "De",
                    value: item.price!.formatReal(),
                    isRitchText: true,
                    titleFontSize: DeviceSize.fontSize(11, 13),
                    valueFontSize: DeviceSize.fontSize(13, 16),
                    hasDivider: false,
                  ),
                  TilesInfoWidget(
                    title: "Por",
                    value: item.total!.formatReal(),
                    titleFontSize: DeviceSize.fontSize(11, 13),
                    valueFontSize: DeviceSize.fontSize(13, 16),
                    hasDivider: false,
                    valueFontWeight: FontWeight.bold,
                  ),
                  TilesInfoWidget(
                    title: "Quantidade",
                    value: item.qtdy?.toString() ?? "-",
                    titleFontSize: DeviceSize.fontSize(11, 13),
                    valueFontSize: DeviceSize.fontSize(13, 16),
                    hasDivider: false,
                    valueFontWeight: FontWeight.bold,
                  ),
                  IconButton(
                      onPressed: () {
                        ordersResumeController.removeProductCombo(item);
                      },
                      icon: Icon(
                        FontAwesomeIcons.trash,
                        color: Colors.red,
                        size: 20.w,
                      ))
                ],
              ),
            ),
            //8.toHeightSpace(),
            const Divider(thickness: 1),
            ExpansionTile(
              title: LabelWidget(
                title: "Ver produtos",
                fontSize: 15.sp,
                fontWeight: FontWeight.bold,
              ),
              children: [
                if (item.products != null)
                  ...item.products!.map((e) => OrdersResumeCombosSubItemsWidget(
                        data: e,
                        comboQtdy: item.qtdy ?? 0,
                      )),
              ],
            )
          ],
        ),
      ),
    );
  }
}
