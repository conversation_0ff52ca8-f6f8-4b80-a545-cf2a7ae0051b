import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class CustomModal extends StatelessWidget {
  final String status;
  final String message;
  final bool fillColor;

  const CustomModal({
    super.key,
    required this.status,
    required this.message,
    required this.fillColor,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(5.0),
      ),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(5.0),
          color: Colors.white,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Container(
              height: 10,
              width: double.infinity,
              decoration: BoxDecoration(
                color: themesController.getBackgroundColor(),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(5.0)),
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 3, horizontal: 8),
                    decoration: BoxDecoration(
                      color: fillColor
                          ? themesController.getColorButton()
                          : Colors.white,
                      borderRadius: BorderRadius.circular(20.0),
                      border: Border.all(
                        color: themesController.getColorButton(),
                        width: 2,
                      ),
                    ),
                    child: Text(
                      status,
                      style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: fillColor
                              ? Colors.white
                              : themesController.getColorButton()),
                    ),
                  ),
                  const Gap(20),
                  MarkdownBody(
                    data: message,
                    styleSheet: MarkdownStyleSheet(
                      p: const TextStyle(
                        fontSize: 16.0,
                        color: Colors.black,
                      ),
                      strong: const TextStyle(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    selectable: false,
                  ),
                  const SizedBox(height: 8.0),
                  const Divider(thickness: 1),
                  const SizedBox(height: 8.0),
                  SizedBox(
                    width: 240,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: themesController.getColorButton(),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20.0),
                        ),
                      ),
                      child: const Text(
                        'Fechar',
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
