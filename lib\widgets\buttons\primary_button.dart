import 'dart:developer';

import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class PrimaryButtonWidget extends StatefulWidget {
  const PrimaryButtonWidget({
    super.key,
    this.titleButtom,
    this.height,
    this.width,
    this.onTap,
    this.child,
    this.isLoading = false,
    this.borderRadius,
    this.buttonColor,
    this.titleFontSize,
    this.borderColor,
    this.titleColor,
    this.textPadding,
    this.textFontWeight,
  });

  final String? titleButtom;
  final double? height;
  final double? width;
  final Future<void> Function()? onTap;
  final Widget? child;
  final bool? isLoading;
  final double? borderRadius;
  final Color? buttonColor;
  final Color? borderColor;
  final double? titleFontSize;
  final Color? titleColor;
  final EdgeInsetsGeometry? textPadding;
  final FontWeight? textFontWeight;
  @override
  State<PrimaryButtonWidget> createState() => _PrimaryButtonWidgetState();
}

class _PrimaryButtonWidgetState extends State<PrimaryButtonWidget> {
  bool _isProcessing = false;

  Future<void> _handleTap() async {
    if (_isProcessing || widget.onTap == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      await widget.onTap!();
    } catch (e) {
      // Handle error if needed
      log('Error occurred: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width ?? MediaQuery.sizeOf(context).width,
      height: widget.height,
      child: ElevatedButton(
        style: ButtonStyle(
            backgroundColor: WidgetStateProperty.all(
                widget.buttonColor ?? themesController.getColorButton()),
            shape: WidgetStateProperty.all<RoundedRectangleBorder>(
              RoundedRectangleBorder(
                borderRadius:
                    BorderRadius.circular(widget.borderRadius ?? 30.0),
                side: BorderSide(
                    color: widget.borderColor ?? Colors.transparent, width: 2),
              ),
            ),
            elevation: WidgetStateProperty.all(3)),
        onPressed: (!widget.isLoading! && !_isProcessing) ? _handleTap : null,
        child: (!widget.isLoading! && !_isProcessing)
            ? widget.child ??
                LabelWidget(
                  title: widget.titleButtom!,
                  textColor: widget.titleColor ?? whiteColor,
                  fontSize: widget.titleFontSize ?? DeviceSize.fontSize(16, 20),
                  textAlign: TextAlign.center,
                  padding: widget.textPadding,
                  fontWeight: widget.textFontWeight,
                )
            : const LoadingButtonChildWidget(),
      ),
    );
  }
}
