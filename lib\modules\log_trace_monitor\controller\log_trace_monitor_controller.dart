import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:archive/archive_io.dart';
import 'package:pharmalink/config/databases/call_context.dart';
import 'package:pharmalink/core/utils/load_widget.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/log_trace_monitor/models/log_trace_monitor_model.dart';
import 'package:pharmalink/modules/orders_resume/models/system_info_model.dart';
import 'package:pharmalink/modules/store_routes/models/params/sync_result_items_params.dart';
import 'package:share_plus/share_plus.dart';

class LogTraceMonitorController extends GetxController
    with TraceableController {
  LogTraceMonitorController();

  LogTraceMonitorModel? selected;
  List<LogTraceMonitorModel> dataList = [];
  List<LogTraceMonitorModel> dataListFull = [];
  String? searchQuery;
  TextEditingController searchController = TextEditingController();

  @override
  Future<void> onReady() async {
    super.onReady();
    await getData();
  }

  void setDataList(List<LogTraceMonitorModel> data) {
    dataList = data;
    dataList.sort((a, b) => b.createAt!.compareTo(a.createAt!));
    update();
  }

  Future<void> getData() async {
    final logTraceMonitorBox = await LogTraceMonitorModel().getList();
    if (logTraceMonitorBox.isNotEmpty) {
      setDataList(logTraceMonitorBox);
      dataListFull = logTraceMonitorBox;
      return;
    }
  }

  Future<void> filterLogs() async {
    if (searchQuery == null || searchQuery!.isEmpty) {
      await getData();
      return;
    }
    final lowercaseQuery = searchQuery!.toLowerCase();
    final filteredLogs = dataListFull
        .where((log) =>
            (log.controllerName?.toLowerCase().contains(lowercaseQuery) ??
                false) ||
            (log.methodName?.toLowerCase().contains(lowercaseQuery) ?? false) ||
            (log.type?.toLowerCase().contains(lowercaseQuery) ?? false) ||
            (log.textData?.toLowerCase().contains(lowercaseQuery) ?? false))
        .toList();
    setDataList(filteredLogs);
  }

  Future<void> sendBackup() async {
    final loading = PlkLoading();

    loading.show(title: AppStrings.loadBackup);

    try {
      final zipFilePath = await generateZipBackup();

      if (zipFilePath != null) {
        final zipFileName = zipFilePath.split('/').last;

        final result =
            await logTraceMonitorApi.sendBackup(zipFilePath, zipFileName);
        loading.hide();

        if (result.error != null) {
          SnackbarCustom.snackbarWarning(result.error!.message.toString());
        } else {
          SnackbarCustom.snackbarSucess(
              'Backup', 'Backup enviado com sucesso!');
        }
      }
    } catch (e) {
      loading.hide();
      SnackbarCustom.snackbarError(e.toString());
      log(e.toString());
    }
  }

  Future<void> sendAndShareBackup() async {
    final loading = PlkLoading();

    loading.show(title: AppStrings.loadBackup);

    try {
      final zipFilePath = await generateZipBackup();
      loading.hide();
      if (zipFilePath != null) {
        await Share.shareXFiles([XFile(zipFilePath)],
            text: 'Backup do PharmaLink');
      }
    } catch (e) {
      loading.hide();
      SnackbarCustom.snackbarError(e.toString());
      log(e.toString());
    }
  }

  Future<String?> generateZipBackup() async {
    return trace('generateZipBackup', () async {
      try {
        final dbPath = await dbContext.databasePath;

        final jsonFiles = await JsonStorage.getJsonFiles();
        if (jsonFiles.isNotEmpty) {
          for (final json in jsonFiles) {
            final fileData = File(json);
            final jsonString = await fileData.readAsString();
            final jsonMap = jsonDecode(jsonString);
            final data = SyncResultItemsModel.fromJson(jsonMap);

            appLog("Json File", data: data);
          }
        }

        // Log the system info
        appLog("App Version", data: await SystemInfoModel.create());

        // Add a 5-second delay
        await Future.delayed(const Duration(seconds: 5));

        final dbFile = File(dbPath);

        if (await dbFile.exists()) {
          // Create a copy of the database file with a new name
          final backupFileName =
              'pharmalink_${appController.userLogged?.userName?.replaceAll(' ', '').replaceAll('.', '_').replaceAll('@', '_')}_${DateTime.now().toIso8601String().replaceAll(':', '').replaceAll('-', '').replaceAll('.', '')}.db'
                  .replaceAll(' ', '');

          final backupFilePath = '${dbFile.parent.path}/$backupFileName';
          final backupFile = await dbFile.copy(backupFilePath);

          // Create a zip file

          final zipFileName = '$backupFileName.zip';

          final zipFilePath = '${dbFile.parent.path}/$zipFileName';

          final encoder = ZipFileEncoder();
          encoder.create(zipFilePath);

          // Add the database file to the zip
          encoder.addFile(backupFile);
          encoder.close();
          // Add JSON files to the zip

          // for (final jsonFilePath in jsonFiles) {
          //   final jsonFile = File(jsonFilePath);

          //   if (await jsonFile.exists()) {
          //     encoder.addFile(jsonFile);
          //   }
          // }

          return zipFilePath;
        } else {
          SnackbarCustom.snackbarError('Arquivo de backup não encontrado!');
          return null;
        }
      } catch (e) {
        log(e.toString());

        return null;
      }
    });
  }
}
