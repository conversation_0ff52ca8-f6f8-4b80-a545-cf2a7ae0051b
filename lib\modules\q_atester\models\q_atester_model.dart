class QATesterModel {
  int? loginResponseModel;
  int? workspacesModel;
  int? currentWorkspace;
  int? themesModel;
  int? storesModel;
  int? storesTakeModel;
  int? storeSelected;
  int? storeParametersModel;
  int? settingsAppModel;
  int? routersListModel;
  int? productParameterModel;
  int? productsMixModel;
  int? productFilterDefaultModel;
  int? storeOrders;
  int? systemParameterizationPermissoesAcessoModel;
  int? shareOfShelf;
  int? logTraceMonitorModel;

  QATesterModel({
    this.loginResponseModel,
    this.workspacesModel,
    this.currentWorkspace,
    this.themesModel,
    this.storesModel,
    this.storesTakeModel,
    this.storeSelected,
    this.storeParametersModel,
    this.settingsAppModel,
    this.routersListModel,
    this.productParameterModel,
    this.productsMixModel,
    this.productFilterDefaultModel,
    this.storeOrders,
    this.systemParameterizationPermissoesAcessoModel,
    this.shareOfShelf,
    this.logTraceMonitorModel,
  });

  QATesterModel.fromJson(Map<String, dynamic> json) {
    loginResponseModel = json['loginResponseModel'];
    workspacesModel = json['workspacesModel'];
    currentWorkspace = json['currentWorkspace'];
    themesModel = json['themesModel'];
    storesModel = json['storesModel'];
    storesTakeModel = json['storesTakeModel'];
    storeSelected = json['storeSelected'];
    storeParametersModel = json['storeParametersModel'];
    settingsAppModel = json['settingsAppModel'];
    routersListModel = json['routersListModel'];
    productParameterModel = json['productParameterModel'];
    productsMixModel = json['productsMixModel'];
    productFilterDefaultModel = json['productFilterDefaultModel'];
    storeOrders = json['storeOrders'];
    systemParameterizationPermissoesAcessoModel =
        json['systemParameterizationPermissoesAcessoModel'];

    shareOfShelf = json['shareOfShelf'];
    logTraceMonitorModel = json['logTraceMonitorModel'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['loginResponseModel'] = loginResponseModel;
    data['workspacesModel'] = workspacesModel;
    data['currentWorkspace'] = currentWorkspace;
    data['themesModel'] = themesModel;
    data['storesModel'] = storesModel;
    data['storesTakeModel'] = storesTakeModel;
    data['storeSelected'] = storeSelected;
    data['storeParametersModel'] = storeParametersModel;
    data['settingsAppModel'] = settingsAppModel;
    data['routersListModel'] = routersListModel;
    data['productParameterModel'] = productParameterModel;
    data['productsMixModel'] = productsMixModel;
    data['productFilterDefaultModel'] = productFilterDefaultModel;
    data['storeOrders'] = storeOrders;
    data['systemParameterizationPermissoesAcessoModel'] =
        systemParameterizationPermissoesAcessoModel;
    data['shareOfShelf'] = shareOfShelf;
    data['logTraceMonitorModel'] = logTraceMonitorModel;
    return data;
  }
}
