import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/stores_parameters/models/distribuidores_model.dart';

class OrderPaymentTypeItemSelectedWidget extends StatelessWidget {
  const OrderPaymentTypeItemSelectedWidget({
    super.key,
    required this.item,
    required this.canReorder,
    required this.isSelected,
  });
  final DistribuidoresModel item;
  final bool canReorder;
  final bool isSelected;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrderPaymentTypeController>(builder: (ctrl) {
      return CustomInkWell(
        onTap: () {
          if (isSelected) {
            ctrl.setDistribuitorUnselected(item);
          } else {
            ctrl.setDistribuitor(item);
          }
        },
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  flex: 10,
                  child: Row(
                    children: [
                      SizedBox(
                        width: 25,
                        child: Transform.scale(
                          scale: DeviceSize.scale(),
                          child: Checkbox(
                            value: item.isSelected!,
                            onChanged: (value) {
                              if (isSelected) {
                                ctrl.setDistribuitorUnselected(item);
                              } else {
                                ctrl.setDistribuitor(item);
                              }
                            },
                            activeColor: themesController.getPrimaryColor(),
                          ),
                        ),
                      ),
                      10.toWidthSpace(),
                      Flexible(
                        flex: 8,
                        child: LabelWidget(
                          title: item.ordemSelected != null
                              ? "${item.ordemSelected} - ${item.distribuidor!.nomeFantasia!.toUpperCase()}"
                              : item.distribuidor!.nomeFantasia!.toUpperCase(),
                          fontSize: DeviceSize.fontSize(15, 19),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                Visibility(
                    visible: canReorder && ctrl.orderEnabled,
                    child: const Spacer()),
                Visibility(
                  visible: canReorder && ctrl.orderEnabled,
                  child: IconButton(
                    icon: const Icon(FontAwesomeIcons.list),
                    onPressed: () {},
                  ),
                )
              ],
            ),
            Divider(color: Colors.grey.shade300, height: 1)
          ],
        ),
      );
    });
  }
}
