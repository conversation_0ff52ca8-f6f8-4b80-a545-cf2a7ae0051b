import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:webview_flutter/webview_flutter.dart';

class LogoutB2cPage extends StatelessWidget {
  const LogoutB2cPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: WebViewWidget(
          controller: WebViewController()
            ..setJavaScriptMode(JavaScriptMode.unrestricted)
            ..setBackgroundColor(const Color(0x00000000))
            ..loadRequest(Uri.parse(
                "https://${loginController.b2cTenant}.b2clogin.com/${loginController.b2cTenant}.onmicrosoft.com/${loginController.b2cPolicyName}/oauth2/v2.0/logout?p=${loginController.b2cPolicyName}&post_logout_redirect_uri=${loginController.b2cRedirectUrl}"))
            ..setNavigationDelegate(
              NavigationDelegate(
                onProgress: (int progress) {},
                onPageStarted: (String url) {},
                onPageFinished: (String url) {
                  GetC.close();
                },
                onWebResourceError: (WebResourceError error) {},
                onNavigationRequest: (NavigationRequest request) {
                  return NavigationDecision.navigate;
                },
              ),
            )),
    );
  }
}
