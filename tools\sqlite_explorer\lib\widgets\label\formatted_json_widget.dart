import 'dart:convert';

import 'package:flutter/services.dart';
import 'package:sqlite_explorer/core/utils/device_size.dart';
import 'package:sqlite_explorer/exports/basic_exports.dart';

class FormattedJsonWidget extends StatelessWidget {
  final String jsonString;

  const FormattedJsonWidget({required this.jsonString, super.key});

  @override
  Widget build(BuildContext context) {
    String formattedJson = _formatJson(jsonString);
    return SizedBox(
      width: double.infinity,
      child: Stack(
        children: [
          SelectableText(
            formattedJson,
            style: TextStyle(fontSize: DeviceSize.fontSize(12, 14)),
          ),
          Positioned(
            top: 0,
            right: 0,
            child: IconButton(
              icon: const Icon(Icons.copy),
              onPressed: () {
                Clipboard.setData(ClipboardData(text: formattedJson));
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                      content: Text(
                          'Conteúdo copiado para a área de transferência')),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _formatJson(String jsonString) {
    try {
      final jsonObject = json.decode(jsonString);
      const encoder = JsonEncoder.withIndent('  ');

      // Check if the root JSON object contains a 'VALUE' field
      if (jsonObject is Map<String, dynamic> &&
          jsonObject.containsKey('VALUE')) {
        final valueContent = jsonObject['VALUE'];
        if (valueContent is String) {
          try {
            final nestedJsonObject = json.decode(valueContent);
            if (nestedJsonObject is Map<String, dynamic> &&
                nestedJsonObject.containsKey('jsonData')) {
              final jsonDataContent = nestedJsonObject['jsonData'];
              if (jsonDataContent is String) {
                try {
                  final nestedJsonDataObject = json.decode(jsonDataContent);

                  // Check if nestedJsonDataObject contains 'VALUE' field
                  if (nestedJsonDataObject is Map<String, dynamic> &&
                      nestedJsonDataObject.containsKey('VALUE')) {
                    final nestedValueContent = nestedJsonDataObject['VALUE'];
                    if (nestedValueContent is String) {
                      try {
                        final nestedValueJsonObject =
                            json.decode(nestedValueContent);
                        nestedJsonDataObject['VALUE'] = nestedValueJsonObject;
                      } catch (e) {
                        // If decoding fails, keep the original string
                        nestedJsonDataObject['VALUE'] = nestedValueContent;
                      }
                    }
                  }

                  nestedJsonObject['jsonData'] = nestedJsonDataObject;
                } catch (e) {
                  // If decoding fails, keep the original string
                  nestedJsonObject['jsonData'] = jsonDataContent;
                }
              }
            }

            jsonObject['VALUE'] =
                nestedJsonObject; // Store the nested JSON object
          } catch (e) {
            // If decoding fails, keep the original string
            jsonObject['VALUE'] = valueContent;
          }
        }
      }

      return encoder.convert(jsonObject);
    } catch (e) {
      return 'Invalid JSON';
    }
  }
}
