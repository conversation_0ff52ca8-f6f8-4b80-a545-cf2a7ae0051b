class AppStrings {
  static const refresh = 'Atualizando...';
  static const loadProducts = 'Carregando os produtos...';
  static const load = 'Carregando...';
  static const validMixIdeal = 'Validando Mix Ideal...';
  static const attention = 'Atenção';
  static const tokenExpired =
      "Sua sessão expirou! Para sua segurança realize o login novamente para continuar.";
  static const noInternet =
      'Conexão com a internet ruim ou indisponível no momento.';
  static const confirmation = 'Confirmação';
  static const orderPaymentTypeMsg1 =
      'É necessário selecionar a forma de pagamento e ao menos um distribuidor.';

  static const orderPaymentTypeMsgBack =
      'O pedido em andamento será perdido, deseja continuar?';

  static const orderRemoveItem = "Deseja realmente remover produto do pedido?";
  static const orderRemoveItemCombo =
      "Deseja realmente remover o combo do pedido?";

  static const orderFooterTotalApresentation = "Total\nApresentações";
  static const orderFooterTotalUnits = "Total\nUnidades";
  static const orderFooterQtdyReal = "Quantidade\nReal";
  static const orderFooterDiscount = "Desconto\nMédio";
  static const orderFooterTotalNet = "Total\nBruto (R\$)";
  static const orderFooterTotalNet2 = "Total\nLíquido (R\$)";
  static const orderConfirmTitle1 = "Olá";
  static const orderConfirmMessage1 =
      "Separamos algumas ofertas especiais para você. Comprando os produtos do mix ideal você recebe desconto adicional nos produtos selecionados!\n\nGostaria de Conferir?";

  static const mixIdealValidate = 'Validando Mix Ideal';
  static String mixIdealQtyMessage(String familia) {
    return 'A quantidade mínima da família "$familia" não foi alcançada.\nDeseja continuar?';
  }

  static String storeOrderMsg(String storeId) {
    return 'Já foi realizado no dia de hoje um pedido para $storeId. Deseja continuar ou selecionar outra?';
  }

  static const orderResumeDateInvalid =
      "Data inválida. O formato deve ser DD/MM/YYYY, tente novamente.";
  static const orderResumeDateExists =
      "A data selecionada já foi incluída. Por favor, escolha outra.";

  static const orderNoProducts =
      "Não foram adicionados produtos e combos ao pedido.";
  static const orderNoProductsFinish =
      "Seu pedido não contém produtos e nem combos, deseja descartar o pedido ou selecionar produtos/combos";

  static const orderResumeDateLimit =
      "Data existente ou fora do limite permitido. Tente novamente.";

  static String orderResumeDateLimitParam(int qtdy) {
    return 'Você alcançou o limite máximo de $qtdy datas programadas.';
  }

  static const syncStores =
      "Sincronização concluída! Todas as lojas estão agora atualizadas com sucesso.";

  static const syncSettings =
      "Sincronização concluída! As parametrizações foram atualizadas com sucesso.";

  static const syncResearches =
      "Sincronização concluída! As pesquisas foram atualizadas com sucesso.";

  static const syncVisits =
      "Sincronização concluída! As visitas foram atualizadas com sucesso.";
  static const syncVisitsInfo =
      "Sincronização concluída! As informações da visitas foram atualizadas com sucesso.";

  static const orderResumeFinishTitle = "Pedido Salvo";
  static const orderResumeFinishMessage =
      "Pedido salvo com sucesso. Deseja sincronizar agora?";
  static String orderSpecialMinValueMessage(
      String minValue, String orderValue) {
    return "O valor mínimo para este distribuidor não foi atendido. Valor Mín: $minValue => Valor Pedido: $orderValue";
  }

  static const discountMax1 =
      "O desconto disponível é maior que o máximo, assim o ele foi revertido ao máximo disponível.";
  static const discountMin1 =
      "O desconto disponível é menor que o mínimo, assim o ele foi revertido ao mínimo disponível.";
  static const discountEditMin =
      "O desconto editado é menor que o mínimo, portanto ele foi revertido ao mínimo disponível.";
  static const discountEditMax =
      "O desconto editado é maior que o máximo, portanto ele foi revertido ao máximo disponível.";
  static const discountDiscart = "Os descontos alterados serão descartados";
  static const visitSave = "Visita salva com sucesso!";
  static const visitRemoveImage =
      "Você deseja realmente remover essa imagem? Ao confirmar, a imagem será permanentemente excluída.";

  static String researchShareNotFound(String storeId) {
    return "Não encontramos pesquisa cadastrada para o PDV $storeId. Sincronize e tente novamente";
  }

  static const researchesShareDelete = 'Deseja excluir esta foto?';

  static const editOrderMessage1 =
      "Antes de corrigir é necessário atualizar as informações do pedido. Caso houver alteração de desconto os dados serão atualizados.";

  static const orderUnfinished = "Pedido Não Finalizado";

  static String orderUnfinishedMessage(String typeOrder) {
    return "Parece que você tem um *$typeOrder* que não foi concluído. Gostaria de continuar finalizando este pedido ou prefere descartá-lo e começar um novo?";
  }

  static const noOrderSelected =
      "Nenhum pedido foi selecionado para sincronização. Por favor, selecione ao menos um pedido para continuar.";
}
