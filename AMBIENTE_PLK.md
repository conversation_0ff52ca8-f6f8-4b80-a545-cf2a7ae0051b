# Guia de configuração e build de apps Flutter - iTrade e Pharmalink

Vou criar um passo a passo detalhado para configurar o ambiente de desenvolvimento Flutter e gerar builds para os apps iTrade e Pharmalink. Este guia abordará a instalação das ferramentas necessárias e os procedimentos para gerar APKs e AABs.

## Requisitos de Versão

- **Flutter**: 3.29.3
- **Android Studio**: Meerkat Feature Drop | 2024.3.2 Patch 1

## 1. Configuração do Ambiente

### Instalar o Android Studio

1. Acesse o site oficial do Android Studio: https://developer.android.com/studio
2. Faça o download do Android Studio: Meerkat Feature Drop | 2024.3.2 Patch 1
3. Execute o instalador e siga as instruções na tela
4. Durante a instalação, certifique-se de selecionar:
   - Android SDK
   - Android SDK Platform-Tools
   - Android SDK Build-Tools
   - Android Emulator

### Instalar o Flutter

1. Acesse o site oficial do Flutter: https://flutter.dev/docs/get-started/install
2. Faça o download da versão específica 3.29.3 do Flutter

   Você pode usar o comando git para obter uma versão específica:

   ```
   git clone https://github.com/flutter/flutter.git -b 3.29.3
   ```

3. Extraia o arquivo baixado para o local desejado (ex: C:\Flutter no Windows ou /Users/<USER>/flutter no macOS)
4. Adicione o caminho do Flutter ao PATH do sistema:
   - **Windows**: Adicione C:\Flutter\bin às variáveis de ambiente
   - **macOS/Linux**: Adicione export PATH="$PATH:[CAMINHO_PARA_FLUTTER]/bin" ao seu arquivo .bash_profile ou .zshrc

5. Verifique a instalação executando:
   ```
   flutter doctor
   ```

6. Resolva quaisquer problemas identificados pelo flutter doctor

### Configurar o Android SDK

1. Abra o Android Studio
2. Vá para Tools > SDK Manager
3. Na aba "SDK Platforms", selecione as versões do Android que deseja dar suporte
4. Na aba "SDK Tools", certifique-se de ter instalado:
   - Android SDK Build-Tools
   - Android SDK Command-line Tools
   - Android Emulator
   - Android SDK Platform-Tools

5. Clique em "Apply" e aguarde a instalação

## 2. Configuração do Projeto

1. Clone o repositório do projeto:
   ```
   git clone [URL_DO_REPOSITÓRIO]
   ```

2. Navegue até a pasta do projeto:
   ```
   cd [NOME_DO_PROJETO]
   ```

3. Obtenha as dependências do Flutter:
   ```
   flutter pub get
   ```

## 3. Gerar Builds

### Para o App iTrade

- **Gerar APK para Developer**
  ```bash
  ./scripts/build_itrade_developer_apk.sh
  ```
- **Gerar APK para Staging**
  ```bash
  ./scripts/build_itrade_staging_apk.sh
  ```
- **Gerar APK para Production**
  ```bash
  ./scripts/build_itrade_production_apk.sh
  ```
- **Gerar AAB para Developer**
  ```bash
  ./scripts/build_itrade_developer_aab.sh
  ```
- **Gerar AAB para Staging**
  ```bash
  ./scripts/build_itrade_staging_aab.sh
  ```
- **Gerar AAB para Production**
  ```bash
  ./scripts/build_itrade_production_aab.sh
  ```

### Para o App Pharmalink

- **Gerar APK para Developer**
  ```bash
  ./scripts/build_pharmalink_developer_apk.sh
  ```
- **Gerar APK para Staging**
  ```bash
  ./scripts/build_pharmalink_staging_apk.sh
  ```
- **Gerar APK para Production**
  ```bash
  ./scripts/build_pharmalink_production_apk.sh
  ```
- **Gerar AAB para Developer**
  ```bash
  ./scripts/build_pharmalink_developer_aab.sh
  ```
- **Gerar AAB para Staging**
  ```bash
  ./scripts/build_pharmalink_staging_aab.sh
  ```
- **Gerar AAB para Production**
  ```bash
  ./scripts/build_pharmalink_production_aab.sh
  ```

## 4. Detalhes sobre o Processo de Build

Ao executar qualquer um dos scripts acima, o processo realizará as seguintes etapas:

1. Atualização dos pacotes do Flutter (`flutter pub get`)
2. Limpeza de builds anteriores (`flutter clean`)
3. Compilação do aplicativo para a configuração específica (Developer, Staging ou Production)
4. Geração do arquivo final (APK para App Center ou AAB para Google Play Store)

## 5. Observações Importantes

- Os APKs são utilizados para testes no App Center
- Os AABs são utilizados para publicação na Google Play Store
- Cada script selecionará automaticamente o ambiente correto (Developer, Staging ou Production)
- Certifique-se de ter as permissões necessárias para executar os scripts

## 6. Solução de Problemas

Se encontrar problemas durante o processo de build:

- Verifique se o Flutter está na versão correta (3.29.3):
  ```
  flutter --version
  ```

- Verifique se o Android Studio está configurado corretamente:
  ```
  flutter doctor -v
  ```

- Certifique-se de que os scripts têm permissão de execução:
  ```
  chmod +x ./scripts/*.sh
  ```

- Verifique os logs de erro para identificar problemas específicos

## 7. Build para iOS

Para gerar builds para iOS, siga os passos abaixo:

### 7.1. Preparação do Ambiente

1. Atualize os pacotes do Flutter:
   ```bash
   flutter pub get
   ```

2. Instale as dependências do CocoaPods:
   ```bash
   cd ios && pod install && cd ..
   ```

### 7.2. Processo de Build no Xcode

1. Abra o projeto no Xcode:
   ```bash
   open ios/Runner.xcworkspace
   ```

2. Selecione o Schema desejado:
   - `pharmalink` - para o app PharmaLink
   - `itrade` - para o app iTrade

3. Selecione o dispositivo de destino (qualquer dispositivo iOS ou "Any iOS Device")

4. Vá para o menu `Product > Archive` para criar um arquivo

5. Após a conclusão do Archive, o Xcode Organizer será aberto automaticamente

6. Escolha uma das opções:
   - `Distribute App` > `App Store Connect` - para publicar na App Store
   - `Distribute App` > `Ad Hoc` - para distribuição interna/testes

### 7.3. Configuração de Ambiente

Para alterar o ambiente de destino (Production, Staging ou Developer):

1. No Xcode, selecione o Schema desejado (pharmalink ou itrade)
2. Clique em `Edit Scheme...`
3. Selecione `Run` na barra lateral
4. Vá para a aba `Arguments`
5. Em `Arguments Passed On Launch`, modifique o parâmetro `--dart-define=ENV=production` para:
   - `--dart-define=ENV=production` (para Produção)
   - `--dart-define=ENV=staging` (para Staging)
   - `--dart-define=ENV=developer` (para Developer)

### 7.4. Observações Importantes

- Certifique-se de que o certificado de assinatura e o perfil de provisionamento estejam configurados corretamente
- Para builds Ad Hoc, você precisará dos UDIDs dos dispositivos registrados no Apple Developer Portal
- Verifique se a versão e o build number estão corretos em `ios/Runner/Info.plist` antes de gerar o Archive
- O ambiente selecionado (production, staging, developer) determinará para qual backend o aplicativo irá apontar




## Gerenciamento de Branches

### Branches de Desenvolvimento Ativas

As branches abaixo referem-se às atividades realizadas pelas equipes Echo e Quebec entre janeiro e março de 2025. Todas foram criadas a partir da branch `release/pharmalink/version-2.2.1`:

- `feature/echo/147214-AnexoPedido`
- `feature/quebec/153237-tabloide-limitador-envio`

**Importante:** Estas branches precisam ser mescladas (merge) com a branch `release/pharmalink/version-2.3.0`, que será a próxima versão de produção.

### Versões de Produção

#### Versões Atuais em Produção:
- `release/itrade/version-2.2.1`
- `release/pharmalink/version-2.2.1`

#### Próxima Versão de Produção:
- `release/pharmalink/version-2.3.0`

**Nota:** Esta nova branch permitirá gerar publicações tanto para iTrade quanto para PharmaLink usando a mesma base de código, funcionalidade que não estava disponível anteriormente.

### Branches Principais

#### Equalizadas com `release/pharmalink/version-2.2.1`:
- `develop`
- `main`

#### Equalizada com `release/itrade/version-2.2.1`:
- `main-itrade`

