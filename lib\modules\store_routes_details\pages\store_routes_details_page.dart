import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class StoreRoutesDetailsPage extends StatefulWidget {
  const StoreRoutesDetailsPage({super.key});

  @override
  State<StoreRoutesDetailsPage> createState() => _StoreRoutesDetailsPageState();
}

class _StoreRoutesDetailsPageState extends State<StoreRoutesDetailsPage>
    with SingleTickerProviderStateMixin {
  late TabController tabDetailControler;
  @override
  void initState() {
    super.initState();
    tabDetailControler = TabController(length: 3, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StoreRoutesDetailsController>(builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: LabelWidget(
            title: "Visualizar PDV",
            fontSize: DeviceSize.fontSize(18, 21),
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              GetC.close();
            },
          ),
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(50.0),
            child: Container(
              color: Colors.white,
              child: TabBar(
                controller: tabDetailControler,
                labelColor: themesController.getPrimaryColor(),
                unselectedLabelColor: Colors.grey,
                indicator: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: themesController.getPrimaryColor(),
                      width: 3.0,
                    ),
                  ),
                ),
                tabs: const [
                  Tab(text: 'Informações'),
                  Tab(text: 'Endereço'),
                  Tab(text: 'Contato'),
                ],
              ),
            ),
          ),
        ),
        body: ctrl.storeDetail == null
            ? const Center(child: SyncLoading(size: 50))
            : TabBarView(
                controller: tabDetailControler,
                children: [
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        15.toHeightSpace(),
                        TilesInfoWidget(
                          title: "Razão social",
                          value: ctrl.storeDetail!.razaoSocial!,
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Nome fantasia",
                          value: ctrl.storeDetail!.nomeFantasia!,
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "CNPJ",
                          value: ctrl.storeDetail!.cNPJ!,
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Setor",
                          value: ctrl.storeDetail!.setor!,
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Bandeira Nome fantasia",
                          value: ctrl.storeDetail!.bandeiraNomeFantasia!,
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Bandeira Razão social",
                          value: ctrl.storeDetail!.bandeiraRazaoSocial!,
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        15.toHeightSpace(),
                        TilesInfoWidget(
                          title: "Logradouro",
                          value: ctrl.storeDetail!.logradouro ?? "",
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Número",
                          value: ctrl.storeDetail!.numero ?? "",
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Complemento",
                          value: ctrl.storeDetail!.complemento ?? "",
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Bairro",
                          value: ctrl.storeDetail!.bairro ?? "",
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Cidade",
                          value: ctrl.storeDetail!.cidade ?? "",
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Estado",
                          value: ctrl.storeDetail!.estado ?? "",
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "CEP",
                          value: ctrl.storeDetail!.cEP ?? "",
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                      ],
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        15.toHeightSpace(),
                        TilesInfoWidget(
                          title: "E-mail",
                          value: ctrl.storeDetail!.email ?? "Não informado",
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Telefone",
                          value: ctrl.storeDetail!.telefone ?? "Não informado",
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Telefone 2",
                          value: ctrl.storeDetail!.telefone2 ?? "Não informado",
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Celular",
                          value: ctrl.storeDetail!.celular ?? "Não informado",
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                        TilesInfoWidget(
                          title: "Fax",
                          value: ctrl.storeDetail!.fax ?? "Não informado",
                          crossAxisAlignment: CrossAxisAlignment.start,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
