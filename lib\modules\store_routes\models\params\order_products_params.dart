import 'package:pharmalink/modules/stores_parameters/models/tabloides_model.dart';

class OrderProductsParamsModel {
  String? userId;
  int? storeId;
  int? merchantConditionId;
  List<OrderProductsParamsDistributorIds>? distributorIds;
  int? orderType;
  bool? usesComboOffer;
  String? cnpj;
  int? paymentDeadlineId;
  String? currentDate;
  List<TabloidesModel>? tabloids;

  OrderProductsParamsModel(
      {this.userId,
      this.storeId,
      this.merchantConditionId,
      this.distributorIds,
      this.orderType,
      this.usesComboOffer,
      this.cnpj,
      this.paymentDeadlineId,
      this.currentDate,
      this.tabloids});

  OrderProductsParamsModel.fromJson(Map<String, dynamic> json) {
    userId = json['UserId'];
    storeId = json['StoreId'];
    merchantConditionId = json['MerchantConditionId'];
    if (json['DistributorIds'] != null) {
      distributorIds = <OrderProductsParamsDistributorIds>[];
      json['DistributorIds'].forEach((v) {
        distributorIds!.add(OrderProductsParamsDistributorIds.fromJson(v));
      });
    }
    orderType = json['OrderType'];
    usesComboOffer = json['UsesComboOffer'];
    cnpj = json['Cnpj'];
    paymentDeadlineId = json['PaymentDeadlineId'];
    currentDate = json['CurrentDate'];
    if (json['Tabloids'] != null) {
      tabloids = <TabloidesModel>[];
      json['Tabloids'].forEach((v) {
        tabloids!.add(TabloidesModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['UserId'] = userId;
    data['StoreId'] = storeId;
    data['MerchantConditionId'] = merchantConditionId;
    if (distributorIds != null) {
      data['DistributorIds'] = distributorIds!.map((v) => v.toJson()).toList();
    }
    data['OrderType'] = orderType;
    data['UsesComboOffer'] = usesComboOffer;
    data['Cnpj'] = cnpj;
    data['PaymentDeadlineId'] = paymentDeadlineId;
    data['CurrentDate'] = currentDate;
    if (tabloids != null) {
      data['Tabloids'] = tabloids!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class OrderProductsParamsDistributorIds {
  int? distributorId;
  int? order;

  OrderProductsParamsDistributorIds({this.distributorId, this.order});

  OrderProductsParamsDistributorIds.fromJson(Map<String, dynamic> json) {
    distributorId = json['DistributorId'];
    order = json['Order'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['DistributorId'] = distributorId;
    data['Order'] = order;
    return data;
  }
}
