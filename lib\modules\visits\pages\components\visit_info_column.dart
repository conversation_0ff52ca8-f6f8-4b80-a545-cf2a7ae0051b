import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/core/extensions/export.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/visits/controller/visits_controller.dart';
import 'package:pharmalink/modules/visits/models/visit_selected_model.dart';
import 'package:pharmalink/widgets/inkwell/custom_inkwell_widget.dart';
import 'package:pharmalink/widgets/label/label_widget.dart';
import 'package:pharmalink/widgets/textbox/custom_textfield_widget.dart';

class VisitInfoColumn extends StatelessWidget {
  final VisitsController ctrl;
  final VisitSelectedModel model;
  final int index;

  const VisitInfoColumn({
    super.key,
    required this.ctrl,
    required this.model,
    required this.index,
  });

  @override
  Widget build(BuildContext context) {
    final expanded = ctrl.isExpanded(index);
    final options =
        expanded ||
                model.role.descricao == null ||
                (model.role.descricao?.isEmpty ?? false)
            ? ctrl.currentRouteVisit!.acompanhamentosVisita!
            : ctrl.currentRouteVisit!.acompanhamentosVisita!
                .where((e) => e.descricao == model.role.descricao)
                .toList();

    return Column(
      children: [
        Visibility(
          visible: ctrl.shouldShowVisitaAcompanhada(),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              5.toHeightSpace(),
              const Divider(thickness: 2),
              5.toHeightSpace(),
              Row(
                children: [
                  LabelWidget(
                    title: "Cargo Acompanhante #${index + 1}",
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                  LabelWidget(
                    title: "*",
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    textColor: Colors.red,
                  ),
                  const Spacer(),

                  Visibility(
                    visible:
                        ctrl.canEdit() &&
                        ctrl.isEnabled() &&
                        ctrl.currentRouteVisit!.visitSelectedList?.first !=
                            model,
                    child: IconButton(
                      icon: Icon(
                        FontAwesomeIcons.trash,
                        color: Colors.red,
                        size: DeviceSize.fontSize(16, 20),
                      ),
                      onPressed: () {
                        ctrl.removeVisitSelected(index);
                      },
                    ),
                  ),
                ],
              ),
              ...options.map((e) {
                final isSelected = model.role.descricao == e.descricao;

                return CustomInkWell(
                  onTap:
                      ctrl.canEdit() && ctrl.isEnabled()
                          ? () {
                            if (isSelected) {
                              ctrl.toggleExpanded(index);
                            } else {
                              ctrl.setVisitaAcompanhada(
                                e.idVisitaAcompanhamento!,
                                index,
                              );
                            }
                          }
                          : null,
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: Container(
                      width: MediaQuery.sizeOf(Get.context!).width,
                      color:
                          isSelected
                              ? themesController.getPrimaryColor()
                              : Colors.white,
                      padding: const EdgeInsets.all(8.0),
                      child: LabelWidget(
                        title: e.descricao ?? "-",
                        fontSize: 15,
                        textColor: isSelected ? Colors.white : Colors.black,
                      ),
                    ),
                  ),
                );
              }),
            ],
          ),
        ),
        5.toHeightSpace(),
        Visibility(
          visible: ctrl.shouldShowAcompanhante(index),
          child: CustomTextField(
            readOnly: !(ctrl.canEdit() && ctrl.isEnabled()),
            labelText: "Nome Acompanhante",
            controller: TextEditingController(text: model.name)
              ..selection = TextSelection.collapsed(offset: model.name.length),
            onChanged: (v) => ctrl.setAcompanhante(v, index),
            maxLength: 500,
            maxLengthEnforcement: MaxLengthEnforcement.enforced,
            minLines: 1,
          ),
        ),
      ],
    );
  }
}
