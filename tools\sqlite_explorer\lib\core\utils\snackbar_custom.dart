import 'package:flutter/material.dart';
import 'package:sqlite_explorer/exports/get_exports.dart';

abstract class SnackbarCustom {
  static void snackbarError(String error) {
    Get.snackbar(
      "Atenção",
      error,
      backgroundColor: Colors.red,
      colorText: Colors.white,
      isDismissible: false,
      duration: const Duration(seconds: 30),
      mainButton: TextButton(
        child: const Icon(
          Icons.close,
          color: Colors.white,
        ),
        onPressed: () {
          Get.back();
        },
      ),
    );
  }

  static void snackbarException(Object e) {
    Get.snackbar(
      "Atenção",
      e.toString(),
      backgroundColor: Colors.red.shade900,
      colorText: Colors.white,
      isDismissible: false,
      duration: const Duration(seconds: 30),
      mainButton: TextButton(
        child: const Icon(
          Icons.close,
          color: Colors.white,
        ),
        onPressed: () {
          Get.back();
        },
      ),
    );
  }

  static void snackbarSucess(String title, String message,
      {int? secondsDuration}) {
    Get.snackbar(
      title,
      message,
      backgroundColor: Colors.green,
      colorText: Colors.white,
      isDismissible: true,
      duration: Duration(seconds: secondsDuration ?? 5),
      mainButton: TextButton(
        child: const Icon(
          Icons.close,
          color: Colors.white,
        ),
        onPressed: () {
          Get.back();
        },
      ),
    );
  }

  static void snackbarWarning(String message, {int? secondsDuration}) {
    Get.snackbar(
      "Atenção",
      message,
      backgroundColor: Colors.yellow[900],
      colorText: Colors.white,
      isDismissible: false,
      duration: Duration(seconds: secondsDuration ?? 10),
      mainButton: TextButton(
        child: const Icon(
          Icons.close,
          color: Colors.white,
        ),
        onPressed: () {
          Get.back();
        },
      ),
    );
  }

  static void snackbarWarningWithFields(String message,
      {int? secondsDuration, required List<String?> fieldsToConcat}) {
    for (var fieldToConcat in fieldsToConcat) {
      message = "$message $fieldToConcat, ";
    }
    message = "${message.substring(0, message.length - 2)}.";
    SnackbarCustom.snackbarWarning(message);

    Get.snackbar(
      "Atenção",
      message,
      backgroundColor: Colors.yellow[900],
      colorText: Colors.white,
      isDismissible: false,
      duration: Duration(seconds: secondsDuration ?? 10),
      mainButton: TextButton(
        child: const Icon(
          Icons.close,
          color: Colors.white,
        ),
        onPressed: () {
          Get.back();
        },
      ),
    );
  }
}
