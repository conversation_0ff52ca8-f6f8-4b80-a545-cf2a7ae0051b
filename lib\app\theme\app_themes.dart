import 'package:google_fonts/google_fonts.dart';
import 'package:pharmalink/app/theme/app_typography.dart';
import 'package:pharmalink/app/utils/responsive_widget.dart';
import 'package:pharmalink/app/utils/shadow_borders.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class AppThemes {
  static ThemeData baseThemeLight() {
    return ThemeData(
      brightness: Brightness.light,
      useMaterial3: baseTheme.useMaterial3,
      textTheme: TextTheme(
        displayLarge: baseTheme.textTheme.displayLarge!
            .copyWith(fontWeight: FontWeight.bold),
        displayMedium: baseTheme.textTheme.displayMedium!
            .copyWith(fontWeight: FontWeight.bold),
        displaySmall: baseTheme.textTheme.displaySmall!
            .copyWith(fontWeight: FontWeight.bold),
        headlineMedium: baseTheme.textTheme.headlineMedium!
            .copyWith(fontWeight: FontWeight.bold),
        headlineSmall: baseTheme.textTheme.headlineSmall!
            .copyWith(fontWeight: FontWeight.bold),
        titleLarge: baseTheme.textTheme.titleLarge!
            .copyWith(fontWeight: FontWeight.bold),
        titleMedium: baseTheme.textTheme.titleMedium!
            .copyWith(fontWeight: FontWeight.bold),
        titleSmall: baseTheme.textTheme.titleSmall!
            .copyWith(fontWeight: FontWeight.bold),
      ),
      textButtonTheme: baseTheme.textButtonTheme,
      elevatedButtonTheme: baseTheme.elevatedButtonTheme,
      outlinedButtonTheme: baseTheme.outlinedButtonTheme,
      filledButtonTheme: baseTheme.filledButtonTheme,
      appBarTheme: baseTheme.appBarTheme,
      cardColor: whiteColor,
      bottomSheetTheme: baseTheme.bottomSheetTheme,
      bottomAppBarTheme: baseTheme.bottomAppBarTheme,
      dividerTheme: baseTheme.dividerTheme,
      progressIndicatorTheme: baseTheme.progressIndicatorTheme,
      inputDecorationTheme: baseTheme.inputDecorationTheme
          .copyWith(fillColor: greyColor.withOpacity(0.7)),
      cardTheme: baseTheme.cardTheme.copyWith(
        color: whiteColor,
        surfaceTintColor: whiteColor,
      ),
      dialogTheme: baseTheme.dialogTheme.copyWith(surfaceTintColor: whiteColor),
      bottomNavigationBarTheme: baseTheme.bottomNavigationBarTheme,
      iconTheme: baseTheme.iconTheme,
      colorScheme: const ColorScheme.light().copyWith(tertiary: whiteColor),
    );
  }

  static ThemeData themeLight() {
    return ThemeData(
      appBarTheme: AppBarTheme(
        iconTheme: IconThemeData(
          color: themesController.getMenuColor(),
        ),
      ),
      brightness: Brightness.light,
      useMaterial3: false,
      colorScheme: ColorScheme.fromSeed(
        seedColor: Colors.white,
      ),
    );
  }
}

final ThemeData baseTheme = ThemeData(
    useMaterial3: true,
    textButtonTheme: TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: textColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
        textStyle: TextStyle(fontSize: title, fontFamily: gilroyFontFamily),
      ),
    ),
    elevatedButtonTheme: ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: greenColor,
        foregroundColor: whiteColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
      ),
    ),
    outlinedButtonTheme: OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: greenColor,
        side: BorderSide(width: 2.h, color: greenColor),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
      ),
    ),
    filledButtonTheme: FilledButtonThemeData(
      style: FilledButton.styleFrom(
        backgroundColor: greenColor,
        foregroundColor: whiteColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(radiusMedium),
        ),
      ),
    ),
    appBarTheme: AppBarTheme(
      toolbarHeight: 60.h,
      elevation: 10,
      centerTitle: true,
      backgroundColor: greenColor,
      foregroundColor: whiteColor,
      titleTextStyle: textTheme.headlineMedium,
    ),
    bottomNavigationBarTheme: const BottomNavigationBarThemeData(
      type: BottomNavigationBarType.shifting,
      elevation: 10,
      selectedItemColor: greenColor,
    ),
    bottomSheetTheme: const BottomSheetThemeData(
      elevation: 0,
      shadowColor: Colors.transparent,
      backgroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
    ),
    dividerTheme: const DividerThemeData(thickness: 1, color: greyColor),
    progressIndicatorTheme:
        const ProgressIndicatorThemeData(color: loadingColor),
    inputDecorationTheme: InputDecorationTheme(
      alignLabelWithHint: true,
      floatingLabelAlignment: FloatingLabelAlignment.start,
      contentPadding: EdgeInsets.symmetric(
          horizontal: Responsive.sizes(mobile: 16.h, tablet: 12.h, desktop: 16),
          vertical: Responsive.sizes(mobile: 14.h, tablet: 10.h, desktop: 14)),
      focusColor: greenColor,
      isDense: false,
      isCollapsed: false,
      filled: true,
      focusedBorder: border.copyWith(
          borderSide: const BorderSide(width: 4, color: greenColor)),
      border: border,
      enabledBorder: border,
      focusedErrorBorder: border.copyWith(
          borderSide: const BorderSide(width: 4, color: greenColor)),
      errorBorder: border,
    ),
    textTheme: textTheme.copyWith(),
    cardTheme: const CardTheme(clipBehavior: Clip.none, elevation: 5),
    dialogTheme: const DialogTheme(shape: RoundedRectangleBorder()),
    iconTheme: IconThemeData(size: 20.sp));

const border = UnderlineInputBorder(
  borderSide: BorderSide.none,
  borderRadius: BorderRadius.all(Radius.circular(radiusMedium)),
);

final textTheme = GoogleFonts.nunitoSansTextTheme();
