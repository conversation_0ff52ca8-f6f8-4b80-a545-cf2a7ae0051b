import 'dart:developer';

import 'package:permission_handler/permission_handler.dart';
import 'package:pharmalink/core/utils/dialogs.dart';
import 'package:pharmalink/exports/get_exports.dart';

class PermissionRequestController extends GetxController {
  bool acceptButtonLoading = false;

  void setAcceptButtonLoading(bool v) {
    acceptButtonLoading = v;
    update();
  }

  Future<void> requestPermission() async {
    setAcceptButtonLoading(true);
    try {
      var status = await Permission.camera.status;
      if (!status.isGranted) {
        status = await Permission.camera.request();
      }
      setAcceptButtonLoading(false);
      if (status.isGranted) {
        Get.back(result: true);
      } else {
        Get.back(result: false);
        if (status.isPermanentlyDenied) {
          Dialogs.info('Permissão Negada',
              'Este aplicativo precisa de permissão para acessar a câmera. Por favor, vá até as configurações do aplicativo e conceda a permissão.');
        }
      }
    } catch (e) {
      setAcceptButtonLoading(false);
      log('Erro');
    }
  }
}
