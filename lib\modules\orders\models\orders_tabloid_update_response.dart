class OrdersTabloidUpdateResponse {
  String? dataFim;
  String? dataInicio;
  int? idTabloide;
  bool? mostrarBotaoVisualizacao;
  bool? mostrarDataExpiracao;
  int? posicionamento;

  OrdersTabloidUpdateResponse(
      {this.dataFim,
      this.dataInicio,
      this.idTabloide,
      this.mostrarBotaoVisualizacao,
      this.mostrarDataExpiracao,
      this.posicionamento});

  OrdersTabloidUpdateResponse.fromJson(Map<String, dynamic> json) {
    dataFim = json['DataFim'];
    dataInicio = json['DataInicio'];
    idTabloide = json['IdTabloide'];
    mostrarBotaoVisualizacao = json['MostrarBotaoVisualizacao'];
    mostrarDataExpiracao = json['MostrarDataExpiracao'];
    posicionamento = json['Posicionamento'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['DataFim'] = dataFim;
    data['DataInicio'] = dataInicio;
    data['IdTabloide'] = idTabloide;
    data['MostrarBotaoVisualizacao'] = mostrarBotaoVisualizacao;
    data['MostrarDataExpiracao'] = mostrarDataExpiracao;
    data['Posicionamento'] = posicionamento;
    return data;
  }
}
