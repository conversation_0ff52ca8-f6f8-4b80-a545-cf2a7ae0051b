//OrdersCardInfoWidget
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class OrdersCardInfoWidget extends StatelessWidget {
  final String? orderType;
  final String? paymentType;
  final List<String>? distributors;
  final String? tabloidName;

  const OrdersCardInfoWidget({
    super.key,
    this.orderType,
    this.paymentType,
    this.distributors,
    this.tabloidName,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 5, right: 5, bottom: 10),
      child: ExpansionTile(
        title: LabelWidget(
          title: orderType != null
              ? (tabloidName != null ? '$orderType - $tabloidName' : orderType!)
              : 'Pedido',
          fontSize: DeviceSize.fontSize(18, 24),
          fontWeight: FontWeight.bold,
        ),
        children: [
          if (paymentType != null)
            _buildInfoRow('Tipo de Pagamento:', paymentType!),
          if (distributors != null && distributors!.isNotEmpty)
            _buildDistributorsSection(),
          _buildInfoRow('Usuário:', appController.userLogged?.nome ?? ""),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5, left: 20, right: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LabelWidget(
            title: label,
            fontSize: DeviceSize.fontSize(14, 16),
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: LabelWidget(
              title: value,
              fontSize: DeviceSize.fontSize(14, 16),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDistributorsSection() {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5, left: 20, right: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          LabelWidget(
            title: 'Distribuidores:',
            fontSize: DeviceSize.fontSize(14, 16),
            fontWeight: FontWeight.bold,
          ),
          const SizedBox(height: 4),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: distributors!.map((distributor) {
              return Padding(
                padding: const EdgeInsets.only(left: 16, top: 2),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ', style: TextStyle(fontSize: 14)),
                    Expanded(
                      child: LabelWidget(
                        title: distributor,
                        fontSize: DeviceSize.fontSize(14, 16),
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
