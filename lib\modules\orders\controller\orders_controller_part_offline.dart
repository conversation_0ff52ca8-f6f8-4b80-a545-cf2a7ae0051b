import 'dart:developer';

import 'package:pharmalink/core/utils/load_widget.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/order_types/models/order_parameters_model.dart';
import 'package:pharmalink/modules/orders/controller/orders_controller.dart';
import 'package:pharmalink/modules/orders/models/orders_mix_product_special_response.dart';
import 'package:pharmalink/modules/orders/models/orders_tabloid_products_response_model.dart';
import 'package:pharmalink/modules/stores_parameters/enuns/type_order_enum.dart';
import 'package:pharmalink/modules/stores_parameters/models/distribuidores_model.dart';
import 'package:tuple/tuple.dart';

class OrdersControllerPartOffline {
  static Future<void> loadInfoEditOrderOffline(
      OrdersController ctrl, String orderId) async {
    var (leaveAction, subAction) =
        ctrl.dynatraceAction.subActionReport("loadInfoEditOrderOffline");
    final loading = PlkLoading();

    try {
      if (ctrl.syncOrders.any((element) => element.transactionKey == orderId)) {
        loading.show(title: AppStrings.load);

        //recuperar os dados do pedido
        ctrl.syncOrderEdit = ctrl.syncOrders
            .where((element) => element.transactionKey == orderId)
            .first;

        if (ctrl.syncOrderEdit!.parameters?.typeOrderId ==
            TyperOrderEnum.especial) {
          globalParams.order.setParametersEsp(
              ctrl.syncOrderEdit!.parameters!.paymentTypeParametersSpecial);
          globalParams.order.setCurrentDistributor(
              ctrl.syncOrderEdit!.parameters!.distributorCurrent);
          loading.hide();
          await getProductsEspByOrderOffline(
              ctrl, ctrl.syncOrderEdit!.parameters!);
        } else {
          await storeParametersController.loadOfflineData(
            storeId:
                ctrl.syncOrderEdit!.parameters!.paymentTypeParameters!.pdvId!,
            typeOrder: ctrl.getTypeOrderString(
                ctrl.syncOrderEdit!.parameters!.typeOrderId!),
          );

          final updateParameter =
              storeParametersController.dataList.firstOrNull;
          if (updateParameter != null) {
            ctrl.syncOrderEdit!.parameters!.paymentTypeParameters!
                    .condicaoComercialBaseId =
                updateParameter.parametrizacao!.condicaoComercialBase!.id!;
            ctrl.syncOrderEdit!.parameters!.paymentTypeParameters!
                    .valorMinimoDePedido =
                updateParameter.parametrizacao!.condicaoComercialBase!
                    .valorMinimoDePedido!;

            ctrl.syncOrderEdit!.parameters!.usaLooping = updateParameter
                    .parametrizacao!.parametrizacaoLooping!.usarLooping ??
                false;
            // Primeiro, criamos um conjunto (Set) dos IDs dos distribuidores para uma busca mais eficiente
            final Map<int, int> distributorOrderMap = {
              for (var distribuidor in ctrl.syncOrderEdit!.parameters!
                  .paymentTypeParameters!.idsDistribuidores!)
                distribuidor.idDistribuidor!: distribuidor.ordem!
            };

// Filtramos e mapeamos a lista de distribuidores
            ctrl.syncOrderEdit!.parameters!.distributorsSelected =
                updateParameter.parametrizacao!.distribuidoresPrazoPagamento!
                    .where((e) =>
                        distributorOrderMap.containsKey(e.distribuidorId))
                    .map((e) => DistribuidoresModel(
                          pdvId: e.pdvId,
                          distribuidorId: e.distribuidorId,
                          distribuidor: e.distribuidor,
                          ordemDePreferencia: e.ordemDePreferencia,
                          ordemMelhorAtendimento: e.ordemMelhorAtendimento,
                          ordemSelected: distributorOrderMap[e.distribuidorId],
                        ))
                    .toList();
          }

          loading.hide();
          globalParams.order.setParameters(
              ctrl.syncOrderEdit!.parameters!.paymentTypeParameters);
          await getProductsByOrderOffline(
              ctrl, ctrl.syncOrderEdit!.parameters!);
        }
      } else {
        GetC.close();
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      loading.hide();
      Dialogs.info(
          AppStrings.attention, e.toString().replaceAll("Exception: ", ""),
          buttonOnPressed: () {
        GetC.close();
        Get.until((route) => Get.currentRoute == RoutesPath.reportOrders);
      });
      rethrow;
    } finally {
      leaveAction();
    }
  }

  static Future<void> getProductsEspByOrderOffline(
      OrdersController ctrl, OrderParametersModel parametersModel) async {
    var (leaveAction, subAction) =
        ctrl.dynatraceAction.subActionReport("getProductsEspByOrderOffline");

    try {
      Tuple2<TabloidProductsResponse?, GetMixSpecialProductsResponse?>
          resultData =
          await orderPaymentTypeController.getProductsEspOfflineEdit(
              parametersModel.paymentTypeParametersSpecial!);

      if (resultData.item1 != null) {
        ctrl.productsTabloid = resultData.item1;
        ctrl.productsTabloidSpecial = resultData.item2;
        await ctrl.loadProductsEsp();

        await ctrl.productExistsTabloid();
        ctrl.update();
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);

      log(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }
  }

  static Future<void> getProductsByOrderOffline(
      OrdersController ctrl, OrderParametersModel parametersModel) async {
    var (leaveAction, subAction) =
        ctrl.dynatraceAction.subActionReport("getProductsByOrderOffline");

    try {
      globalParams.order.setProducts(
          await orderPaymentTypeController.getProductsOfflineEdit(
              parametersModel.paymentTypeParameters!.condicaoComercialBaseId!));
      if (globalParams.getProducts() != null) {
        await ctrl.loadProducts();
        await ctrl.productExists();
      }
      ctrl.update();
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      // loading.hide();
      log(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }
  }
}
