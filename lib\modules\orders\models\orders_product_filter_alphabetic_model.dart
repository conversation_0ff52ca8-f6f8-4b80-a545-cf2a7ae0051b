import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class OrdersProductFilterAlphabeticModel
    extends SqfLiteBase<OrdersProductFilterAlphabeticModel> {
  String? letter;
  bool? hasProduct;

  OrdersProductFilterAlphabeticModel({this.letter, this.hasProduct})
      : super(DatabaseModels.productFilterDefaultModel);

  OrdersProductFilterAlphabeticModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.productFilterDefaultModel) {
    letter = json['letter'];
    hasProduct = false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['letter'] = letter;
    data['hasProduct'] = hasProduct;

    return data;
  }

  static List<OrdersProductFilterAlphabeticModel> getList() {
    var letters = ["A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"];
    List<OrdersProductFilterAlphabeticModel> alphabet = [];

    for (var item in letters) {
      alphabet.add(
          OrdersProductFilterAlphabeticModel(letter: item, hasProduct: false));
    }

    return alphabet;
  }
}
