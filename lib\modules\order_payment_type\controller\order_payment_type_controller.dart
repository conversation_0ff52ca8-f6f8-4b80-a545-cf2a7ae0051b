import 'dart:convert';
import 'dart:developer';

import 'package:dynatrace_flutter_plugin/dynatrace_flutter_plugin.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/utils/dynatrace/dynatrace_logger.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/order_payment_type/models/order_payment_type_model.dart';
import 'package:pharmalink/modules/orders/models/orders_mix_product_special_request.dart';
import 'package:pharmalink/modules/orders/models/orders_mix_product_special_response.dart';
import 'package:pharmalink/modules/orders/models/orders_mix_produto_request_model.dart';
import 'package:pharmalink/modules/orders/models/orders_products_model.dart';
import 'package:pharmalink/modules/orders/models/orders_tabloid_products_request_model.dart';
import 'package:pharmalink/modules/orders/models/orders_tabloid_products_response_model.dart';
import 'package:pharmalink/modules/store_routes/models/params/sync_result_items_params.dart';
import 'package:pharmalink/modules/stores/models/commercial_condition_model.dart';
import 'package:pharmalink/modules/stores_parameters/enuns/type_order_enum.dart';
import 'package:pharmalink/modules/stores_parameters/models/distribuidor_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/distribuidores_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/distributors_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/prazo_pagamento_model.dart';
import 'package:tuple/tuple.dart';

class OrderPaymentTypeController
    extends GetxControllerInstrumentado<OrderPaymentTypeController>
    with TraceableController {
  OrderPaymentTypeController();
  List<OrderPaymentTypeModel> types = [];

  // Refatorado
  List<PrazoPagamentoModel> deadlinePayment = [];
  // Refatorado
  List<DistribuidoresModel> distributors = [];
  // Refatorado
  List<DistribuidoresModel> distributorsSelected = [];

  List<DistribuidoresModel> distributorsCommercialCondition = [];

  String? paramHash;
  bool usaLooping = false;
  bool orderEnabled = false;
  final loading = PlkLoading();
  SyncResultItemsModel? offlineData;

  bool condicaoComercialCustom = false;
  bool needValidateCommercialCondition = false;
  List<CommercialConditionModel> commercialConditions = [];
  CommercialConditionModel? commercialConditionSelected;
  @override
  Future<void> onReady() async {
    super.onReady();
  }

  Future<void> initialize() async {
    log("OrderPaymentTypeController onReady ${DateTime.now().toIso8601String()}");
    orderTypesController = Get.find<OrderTypesController>();
    ordersIdealMixController = Get.find<OrdersIdealMixController>();
    ordersController = Get.find<OrdersController>();
    ordersResumeController = Get.find<OrdersResumeController>();
    ordersTabloidController = Get.find<OrdersTabloidController>();
    setSeletedType(null);
    usaLooping = globalParams
            .order.parametrization?.parametrizacaoLooping!.usarLooping ??
        false;

    if (globalParams.getTypeOrderId() == TyperOrderEnum.especial) {
      if (ordersTabloidController.deadlinePayment.isNotEmpty) {
        deadlinePayment = ordersTabloidController.deadlinePayment;
      }
    } else {
      deadlinePayment =
          globalParams.order.parametrization!.prazoPagamento ?? [];
    }
    globalParams.order.setDeadlinePaymentList(deadlinePayment);
    final paramGeneral = generalParameterizationController
        .generalSettingsOrderDiscountRegistration!;
    condicaoComercialCustom =
        [1, 3].contains(paramGeneral.condicaoComercialDistribuidor) &&
            globalParams.order.orderParameters.typeOrderId !=
                TyperOrderEnum.especial;
    needValidateCommercialCondition = condicaoComercialCustom &&
        paramGeneral.condicaoComercialDistribuidor == 1;
    setCommercialCondition(null);
    distributorsSelected = [];
    update();
    Get.toNamed(RoutesPath.orderPaymentType);
    Future.delayed(const Duration(milliseconds: 400), () async {
      log("OrderPaymentTypeController initLocalData ${DateTime.now().toIso8601String()}");
      await initLocalData();
      if (condicaoComercialCustom) {
        if (globalParams.getCurrentStore()!.dataExtra?.offlineDateSync ==
            null) {
          await getCommercialConditions();
        }
      }
    });
  }

  Future<void> initLocalData() async {
    if (ordersController.isEditOrder != true &&
        globalParams.order.orderLocal != null) {
      if (globalParams.order.orderParameters.typeOrderId ==
              TyperOrderEnum.padrao ||
          globalParams.order.orderParameters.typeOrderId ==
              TyperOrderEnum.rep) {
        final parameter =
            globalParams.order.orderParameters.paymentTypeParameters!;

        final deadline = deadlinePayment
            .where((e) => e.idPrazoPagamento == parameter.idPrazoPagamento)
            .firstOrNull;
        if (deadline != null) {
          setSeletedType(deadline);
          if (usaLooping) {
            parameter.idsDistribuidores!.map((e) {
              final distributor = deadline.distribuidores!
                  .where((x) => x.distribuidorId == e.idDistribuidor)
                  .firstOrNull;
              if (distributor != null) {
                setDistribuitor(distributor);
              }
            }).toList();
          } else {
            final distributor = deadline.distribuidores!
                .where((x) =>
                    x.distribuidorId ==
                    parameter.idsDistribuidores!.first.idDistribuidor)
                .firstOrNull;
            if (distributor != null) {
              setDistribuitorSingle(distributor);
            }
          }
          await advance();
        }
      } else if (globalParams.order.orderParameters.typeOrderId ==
          TyperOrderEnum.especial) {
        final parameter =
            globalParams.order.orderParameters.paymentTypeParametersSpecial!;

        if (parameter.tabloidId == globalParams.order.tabloidId) {
          final deadline = deadlinePayment
              .where((e) => e.idPrazoPagamento == parameter.paymentTermId)
              .firstOrNull;
          if (deadline != null) {
            setSeletedType(deadline);
            if (usaLooping) {
              parameter.distributorsIds!.map((e) {
                final distributor = deadline.distribuidores!
                    .where((x) => x.distribuidorId == e)
                    .firstOrNull;
                if (distributor != null) {
                  setDistribuitor(distributor);
                }
              }).toList();
            } else {
              final distributor = deadline.distribuidores!
                  .where((x) => x.distribuidorId == parameter.distributorId!)
                  .firstOrNull;
              if (distributor != null) {
                setDistribuitorSingle(distributor);
              }
            }
            update();
            await advance();
          }
        }
      }
    }
  }

  bool get isSelected => globalParams.order.deadlinePayment != null;

  void setSeletedType(PrazoPagamentoModel? item) {
    commercialConditionSelected = null;
    resetDistributorsSelecteds();
    if (item == null) {
      globalParams.order.setDeadlinePayment(null);
      globalParams.order.setDeadlinePaymentList(null);

      distributors = [];
      distributorsSelected = [];

      globalParams.order.setCurrentDistributor(null);
    }

    if (item != null && globalParams.getDeadlinePayment() != item) {
      globalParams.order.setCurrentDistributor(null);
      globalParams.order.setDeadlinePayment(item);

      distributors = [];
      distributorsSelected = [];
      item.distribuidores!.map((e) {
        e.isSelected = false;
        e.ordemSelected = null;
      }).toList();
      distributors = [...item.distribuidores!];
    }
    update();
  }

  void setOrderEnabled() {
    orderEnabled = !orderEnabled;
    update();
  }

  void setDistribuitor(DistribuidoresModel item) {
    if (!distributorsSelected.contains(item)) {
      if (commercialConditionSelected != null) {
        final firstDistributor = distributorsSelected.firstOrNull;
        if (firstDistributor != null) {
          if ((item.lockId != null && firstDistributor.lockId != item.lockId) ||
              (item.lockId == null && firstDistributor.lockId != null) ||
              (item.lockId != null && firstDistributor.lockId == null)) {
            Dialogs.info("Atenção",
                "Não é possível adicionar este item, pois ele não faz parte do mesmo desconto Maximo (trava)");
            return;
          }
        }
      }

      item.distribuidor?.habilitaContaCorrente ??= false;

      item.isSelected = true;

      distributorsSelected.add(item);
      int count = 1;
      distributorsSelected.map((e) {
        e.ordemSelected = count;
        e.ordemDePreferencia = e.ordemDePreferencia ?? count;
        e.ordemMelhorAtendimento = e.ordemMelhorAtendimento ?? 0;
        count++;
      }).toList();
      distributors.remove(item);
      distributors.sort((a, b) => a.distribuidor!.nomeFantasia!
          .compareTo(b.distribuidor!.nomeFantasia!));
      update();
    }
  }

  void setDistribuitorSingle(DistribuidoresModel item) {
    globalParams.order.setCurrentDistributor(item);
    globalParams.order.currentDistributor!.isSelected = false;
    item.isSelected = true;
    update();
  }

  void setDistribuitorUnselected(DistribuidoresModel item) {
    if (!distributors.contains(item)) {
      item.isSelected = false;
      item.ordemSelected = null;
      distributorsSelected.remove(item);
      int count = 1;
      distributorsSelected.map((e) {
        e.ordemSelected = count;
        e.ordemDePreferencia = e.ordemDePreferencia ?? count;
        e.ordemMelhorAtendimento = e.ordemMelhorAtendimento ?? 0;
        count++;
      }).toList();
      distributors.add(item);
      distributors.sort((a, b) => a.distribuidor!.nomeFantasia!
          .compareTo(b.distribuidor!.nomeFantasia!));
      update();
    }
  }

  Future<void> advance() async {
    return trace('advance', () async {
      var (leaveAction, subAction) = dynatraceAction.subActionReport("advance");
      try {
        if (needValidateCommercialCondition) {
          if (commercialConditionSelected == null) {
            SnackbarCustom.snackbarError("Selecione uma Condição Comercial");
            return;
          }
        }
        await reportEventByAdvance();
        globalParams.order.setCurrentDistributors(distributorsSelected);
        globalParams.order
            .setCommercialConditionSelected(commercialConditionSelected);

        if (globalParams.getCurrentStore()!.dataExtra?.offlineDateSync !=
            null) {
          appLogWithDynatrace(subAction, "Buscar dados offline");
          await setDataOffline();
        } else {
          appLogWithDynatrace(subAction, "Buscar dados online");
          await setDataOnline();
        }
      } catch (e, s) {
        loading.hide();
        SnackbarCustom.snackbarError(e.toString());
        appErrorWithDynatrace(subAction, e, s);
        rethrow;
      } finally {
        leaveAction();
      }
    });
  }

  Future<void> setDataOnline() async {
    await appController.isValidToken(noMessages: true);
    if (appController.hasErrorRefreshToken == true) {
      SnackbarCustom.snackbarError(AppStrings.tokenExpired);
      Get.offAndToNamed(RoutesPath.login);
      return;
    }

    if ((usaLooping && distributorsSelected.isEmpty) ||
        (!usaLooping && globalParams.getCurrentDistributor() == null)) {
      Dialogs.info(AppStrings.attention, AppStrings.orderPaymentTypeMsg1,
          buttonName: "ENTENDI");
      return;
    }

    loading.show(title: AppStrings.loadProducts);

    distributorsSelected
        .sort((a, b) => a.ordemSelected!.compareTo(b.ordemSelected!));

    ordersIdealMixController.setDistributorId(
        ids: usaLooping
            ? distributorsSelected.map((x) => x.distribuidorId!).toList()
            : null,
        singleId: globalParams.getCurrentDistributor()?.distribuidorId);

    if (globalParams.getTypeOrderId() == TyperOrderEnum.especial) {
      globalParams.order.setCurrentDistributor(distributorsSelected.first);
      await getProductsEsp();
    } else {
      await getProducts();
    }
  }

  Future<void> setDataOffline() async {
    if ((usaLooping && distributorsSelected.isEmpty) ||
        (!usaLooping && globalParams.getCurrentDistributor() == null)) {
      Dialogs.info(AppStrings.attention, AppStrings.orderPaymentTypeMsg1,
          buttonName: "ENTENDI");
      return;
    }

    loading.show(title: AppStrings.loadProducts);
    distributorsSelected
        .sort((a, b) => a.ordemSelected!.compareTo(b.ordemSelected!));

    ordersIdealMixController.setDistributorId(
        ids: usaLooping
            ? distributorsSelected.map((x) => x.distribuidorId!).toList()
            : null,
        singleId: globalParams.getCurrentDistributor()?.distribuidorId);

    if (globalParams.getTypeOrderId() == TyperOrderEnum.especial) {
      globalParams.order.setCurrentDistributor(distributorsSelected.first);
      await getProductsEspOffline();
    } else {
      await getProductsOffline();
    }
  }

  List<DistributorsModel> getDistributorSelecteds() {
    List<DistributorsModel> ids = [];

    if (ordersController.isEditOrder == true) {
      ids = ordersController.editDistributors;
    } else {
      if (usaLooping) {
        distributorsSelected
            .map((e) => ids.add(DistributorsModel(
                  distributorsId: e.distribuidorId!,
                  name: e.distribuidor!.nomeFantasia,
                  ordemDePreferencia: e.ordemDePreferencia,
                  distribuidor: e.distribuidor,
                  isSelected: e.isSelected,
                  ordemMelhorAtendimento: e.ordemMelhorAtendimento,
                  ordemSelected: e.ordemSelected,
                )))
            .toList();
      } else {
        ids.add(DistributorsModel(
          distributorsId: globalParams.getCurrentDistributor()!.distribuidorId!,
          name:
              globalParams.getCurrentDistributor()!.distribuidor!.nomeFantasia,
          ordemDePreferencia:
              globalParams.getCurrentDistributor()!.ordemDePreferencia,
          distribuidor: globalParams.getCurrentDistributor()!.distribuidor,
          isSelected: globalParams.getCurrentDistributor()!.isSelected,
          ordemMelhorAtendimento:
              globalParams.getCurrentDistributor()!.ordemMelhorAtendimento,
          ordemSelected: globalParams.getCurrentDistributor()!.ordemSelected,
        ));
      }
    }

    return ids;
  }

  List<DistribuidoresModel> getDistributorIds() {
    List<DistribuidoresModel> ids = [];

    if (usaLooping) {
      distributorsSelected.map((e) => ids.add(e)).toList();
    } else {
      ids.add(globalParams.getCurrentDistributor()!);
    }

    return ids;
  }

  Future<void> getProducts() async {
    distributorsSelected
        .sort((a, b) => a.ordemSelected!.compareTo(b.ordemSelected!));

    double valorMinimoPedidoCondicaoComercial = globalParams.order
            .parametrization?.condicaoComercialBase?.valorMinimoDePedido ??
        0;
    double valorMinimoPedido = usaLooping
        ? distributorsSelected.first.distribuidor?.valorMinimoDePedido ?? 0
        : globalParams
                .order.currentDistributor?.distribuidor?.valorMinimoDePedido ??
            0;
    if (valorMinimoPedido <= valorMinimoPedidoCondicaoComercial) {
      valorMinimoPedido = valorMinimoPedidoCondicaoComercial;
    }
    globalParams.order.setParameters(MixProdutoRequest(
      pdvId: globalParams.getCurrentStore()?.idLoja,
      idPrazoPagamento: globalParams.getDeadlinePayment()?.idPrazoPagamento,
      cNPJ: globalParams.getCurrentStore()?.cNPJ,
      condicaoComercialBaseId: commercialConditionSelected == null
          ? globalParams.order.parametrization?.condicaoComercialBase?.id
          : commercialConditionSelected!.id,
      valorMinimoDePedido: valorMinimoPedido,
      tipoDePedido: globalParams.getTypeOrderId(),
      utilizaComboOferta: true,
      idsDistribuidores: usaLooping
          ? distributorsSelected
              .map(
                (x) => IdsDistribuidores(
                  idDistribuidor: x.distribuidorId,
                  ordem: x.ordemSelected ?? 1,
                ),
              )
              .toList()
          : [
              IdsDistribuidores(
                idDistribuidor:
                    globalParams.getCurrentDistributor()!.distribuidorId,
                ordem: globalParams.getCurrentDistributor()!.ordemSelected ?? 1,
              )
            ],
      currentDate:
          DateTime.now().formatDate(formatType: DateFormatType.ddMMyyyy),
    ));

    paramHash = globalParams.getParameters()!.toJsonHash();
    globalParams.order.orderParameters.paymentTypeParameters =
        globalParams.getParameters()!;
    if (!connectivityController.isConnected) {
      final productsMixModelBox = await ProductsMixModel().getFirst(
        workspaceId: appController.workspace!.workspaceId!,
        storeId: globalParams.getCurrentStore()!.idLoja!,
        hashCode: paramHash!,
      );
      if (productsMixModelBox != null) {
        globalParams.order.setProducts(productsMixModelBox);
        update();
        loading.hide();
        Get.toNamed(RoutesPath.orders)?.then((value) => loading.hide());
        return;
      }
    }
    final result = await ordersApi.getMixProducts(
      model: globalParams.getParameters()!,
    );

    result.when(
      sucess: (data) async {
        globalParams.order.setProducts(data);
        await dbContext.withControllerAction(this).addData(
            key: DatabaseModels.productsMixModel,
            data: data,
            storeId: globalParams.getCurrentStore()?.idLoja,
            workspaceId: appController.workspace!.workspaceId,
            hashCode: paramHash,
            clearCurrentData: true);
        update();
        loading.hide();
        Get.toNamed(RoutesPath.orders)?.then((value) {
          loading.hide();
        });
      },
      error: (error) {
        loading.hide();
        SnackbarCustom.snackbarError(error.error);
      },
    );
  }

  List<int> getProductIdsEsp(bool isDun) {
    List<int> list = [];
    ordersController = Get.find<OrdersController>();
    var data = ordersController.productsTabloid!;

    if (isDun) {
      List<int> idProdutosDUNCombos = data.combosOferta!
          .expand((combo) => combo.produtos!)
          .where((produto) =>
              produto.idProdutoDUN != null && produto.idProdutoDUN! > 0)
          .map((produto) => produto.idProdutoDUN!)
          .toSet()
          .toList();

      List<int> idProdutosDUN = data.produtos!
          .where((produto) =>
              produto.idProdutoDUN != null && produto.idProdutoDUN! > 0)
          .map((produto) => produto.idProdutoDUN!)
          .toSet()
          .toList();

      list = {...idProdutosDUNCombos, ...idProdutosDUN}.toList();
    } else {
      List<int> idProdutosCombos = data.combosOferta!
          .expand((combo) => combo.produtos!)
          .map((produto) => produto.idProduto!)
          .toSet()
          .toList();

      List<int> idProdutos =
          data.produtos!.map((produto) => produto.idProduto!).toSet().toList();

      list = {...idProdutosCombos, ...idProdutos}.toList();
    }
    return list;
  }

  Future<void> getProductsEsp() async {
    distributorsSelected
        .sort((a, b) => a.ordemSelected!.compareTo(b.ordemSelected!));

    globalParams.order.setParametersEsp(TabloidProductsRequest(
      paymentTermId: globalParams.getDeadlinePayment()?.idPrazoPagamento!,
      deadlinePayment: ordersTabloidController.deadlinePayment,
      storeId: globalParams.getCurrentStore()?.idLoja,
      cNPJ: globalParams.getCurrentStore()?.cNPJ,
      userId: appController.userLogged!.userId,
      tabloidId: ordersTabloidController.tabloidId,
      distributorsIds: usaLooping
          ? distributorsSelected.map((x) => x.distribuidorId!).toList()
          : [globalParams.getCurrentDistributor()!.distribuidorId!],
      distributorId: usaLooping
          ? distributorsSelected
              .map((x) => IdsDistribuidores(
                  idDistribuidor: x.distribuidorId, ordem: x.ordemSelected))
              .toList()
              .first
              .idDistribuidor
          : globalParams.getCurrentDistributor()!.distribuidorId,
    ));

    globalParams.order.orderParameters.paymentTypeParametersSpecial =
        globalParams.getParametersEsp()!;
    globalParams.order.orderParameters.distributorCurrent =
        globalParams.getCurrentDistributor()!;

    final result = await ordersApi.getTabloidProducts(
      model: globalParams.getParametersEsp()!,
    );

    result.when(
      sucess: (data) async {
        ordersController.productsTabloid = data;

        // await dbContext.addData(
        //     key: DatabaseModels.productsMixModel,
        //     data: data,
        //     storeId: globalParams.getCurrentStore()?.idLoja,
        //     workspaceId: appController.workspace!.workspaceId,
        //     hashCode: paramHash,
        //     clearCurrentData: true);

        var modelDistribuidtors = GetMixSpecialProductsRequest(
          idPrazoPagamento: globalParams.getDeadlinePayment()?.idPrazoPagamento,
          idsDistribuidores:
              distributorsSelected.map((e) => e.distribuidorId!).toList(),
          idsProdutosDUN: getProductIdsEsp(true),
          idsProdutos: getProductIdsEsp(false),
        );

        final resultDistributors = await ordersApi.getTabloidProductsSpecial(
          model: modelDistribuidtors,
        );

        if (resultDistributors.error != null) {
          SnackbarCustom.snackbarError(resultDistributors.error!.message ??
              "Ocorreu um erro ao buscar os produtos especiais");
          loading.hide();
          return;
        } else {
          ordersController.productsTabloidSpecial = resultDistributors.data!;
        }

        update();
        loading.hide();
        Get.toNamed(RoutesPath.orders)?.then((value) => loading.hide());
      },
      error: (error) {
        loading.hide();
        SnackbarCustom.snackbarError(error.error);
      },
    );
  }

  void reorderData(int oldIndex, int newIndex) {
    if (oldIndex < newIndex) {
      newIndex -= 1;
    }
    final item = distributorsSelected.removeAt(oldIndex);
    distributorsSelected.insert(newIndex, item);

    int count = 1;
    distributorsSelected.map((e) {
      e.ordemSelected = count;
      count++;
    }).toList();

    update();
  }

  Future<void> getProductsEspOffline() async {
    return trace('getProductsEspOffline', () async {
      var (leaveAction, subAction) =
          dynatraceAction.subActionReport("getProductsEspOffline");

      try {
        distributorsSelected
            .sort((a, b) => a.ordemSelected!.compareTo(b.ordemSelected!));

        globalParams.order.setParametersEsp(TabloidProductsRequest(
          paymentTermId: globalParams.getDeadlinePayment()?.idPrazoPagamento!,
          deadlinePayment: ordersTabloidController.deadlinePayment,
          storeId: globalParams.getCurrentStore()?.idLoja,
          cNPJ: globalParams.getCurrentStore()?.cNPJ,
          userId: appController.userLogged!.userId,
          tabloidId: ordersTabloidController.tabloidId,
          distributorsIds: usaLooping
              ? distributorsSelected.map((x) => x.distribuidorId!).toList()
              : [globalParams.getCurrentDistributor()!.distribuidorId!],
          distributorId: usaLooping
              ? distributorsSelected
                  .map((x) => IdsDistribuidores(
                      idDistribuidor: x.distribuidorId, ordem: x.ordemSelected))
                  .toList()
                  .first
                  .idDistribuidor
              : globalParams.getCurrentDistributor()!.distribuidorId,
        ));

        globalParams.order.orderParameters.paymentTypeParametersSpecial =
            globalParams.getParametersEsp()!;
        globalParams.order.orderParameters.distributorCurrent =
            globalParams.getCurrentDistributor()!;

        final storeId = globalParams.getCurrentStore()?.idLoja;
        final orderTypeId = globalParams.getTypeOrderId();

        offlineData = await SyncResultItemsModel()
            .getFirst(storeId: storeId!, orderTypeId: orderTypeId!);

        if (offlineData == null) {
          // Log to dynatrace
          DynatraceLogger.logToDynatrace(
              "Order Payment Type Controller: getProductsEspOffline (not found)",
              {"storeId": storeId, "orderTypeId": orderTypeId});

          appLog("Não foi possível encontrar os produtos",
              data: {"storeId": storeId, "orderTypeId": orderTypeId});

          loading.hide();
          SnackbarCustom.snackbarError(
              "Não foi possível encontrar os produtos");
          return;
        }

        final productsTabloidData =
            await getParameterOfflineSynchronizationTabloidResult(
                offlineData!.id!, ordersTabloidController.tabloidId!);

        if (productsTabloidData == null) {
          loading.hide();
          appLog(
              "Os dados do tabloide não foram encontrados, faça a sincronização novamente.",
              data: {
                "id": offlineData!.id!,
                "tabloidId": ordersTabloidController.tabloidId!
              });
          SnackbarCustom.snackbarError(
              "Os dados do tabloide não foram encontrados, faça a sincronização novamente.");
          return;
        }

        ordersController.productsTabloid = productsTabloidData;

        final productsTabloidSpecialData =
            await getParameterOfflineSynchronizationSpecialProductsResult(
                offlineData!.id!,
                ordersTabloidController.tabloidId!,
                globalParams.getDeadlinePayment()!.idPrazoPagamento!);

        if (productsTabloidSpecialData == null) {
          loading.hide();
          appLog(
              "Os dados do tabloide não foram encontrados, faça a sincronização novamente.",
              data: {
                "id": offlineData!.id!,
                "tabloidId": ordersTabloidController.tabloidId!,
                "idPrazoPagamento":
                    globalParams.getDeadlinePayment()!.idPrazoPagamento!
              });
          SnackbarCustom.snackbarError(
              "Os dados do tabloide não foram encontrados, faça a sincronização novamente.");
          return;
        } else {
          ordersController.productsTabloidSpecial = productsTabloidSpecialData;
        }

        update();
        loading.hide();
        Get.toNamed(RoutesPath.orders)?.then((value) => loading.hide());
      } catch (e, s) {
        appErrorWithDynatrace(subAction, e, s);
        loading.hide();
        log("Error: $e");
      } finally {
        leaveAction();
      }
    });
  }

  Future<void> getProductsOffline() async {
    return trace('getProductsOffline', () async {
      var (leaveAction, subAction) =
          dynatraceAction.subActionReport("getProductsOffline");

      try {
        distributorsSelected
            .sort((a, b) => a.ordemSelected!.compareTo(b.ordemSelected!));

        double valorMinimoPedidoCondicaoComercial = globalParams.order
                .parametrization?.condicaoComercialBase?.valorMinimoDePedido ??
            0;
        double valorMinimoPedido =
            distributorsSelected.first.distribuidor?.valorMinimoDePedido ?? 0;
        if (valorMinimoPedido <= valorMinimoPedidoCondicaoComercial) {
          valorMinimoPedido = valorMinimoPedidoCondicaoComercial;
        }

        final storeId = globalParams.getCurrentStore()?.idLoja;
        final paymentDeadlineId =
            globalParams.getDeadlinePayment()?.idPrazoPagamento;
        final merchantConditionId =
            globalParams.order.parametrization?.condicaoComercialBase?.id;
        final orderTypeId = globalParams.getTypeOrderId();

        offlineData = await SyncResultItemsModel().getFirst(
            storeId: storeId!,
            paymentDeadlineId: paymentDeadlineId!,
            merchantConditionId: merchantConditionId!,
            orderTypeId: orderTypeId!);

        if (offlineData == null) {
          // Log to dynatrace
          DynatraceLogger.logToDynatrace(
              "Order Payment Type Controller: getProductsOffline (not found)", {
            "storeId": storeId,
            "paymentDeadlineId": paymentDeadlineId,
            "merchantConditionId": merchantConditionId,
            "orderTypeId": orderTypeId
          });

          appLog("Não foi possível encontrar os produtos", data: {
            "storeId": storeId,
            "paymentDeadlineId": paymentDeadlineId,
            "merchantConditionId": merchantConditionId,
            "orderTypeId": orderTypeId
          });

          loading.hide();
          SnackbarCustom.snackbarError(
              "Não foi possível encontrar os produtos");
          return;
        }

        final productData =
            await getParameterOfflineSynchronizationResult(offlineData!.id!);

        globalParams.order.setParameters(MixProdutoRequest(
          pdvId: globalParams.getCurrentStore()?.idLoja,
          idPrazoPagamento: globalParams.getDeadlinePayment()?.idPrazoPagamento,
          cNPJ: globalParams.getCurrentStore()?.cNPJ,
          condicaoComercialBaseId:
              globalParams.order.parametrization?.condicaoComercialBase?.id,
          valorMinimoDePedido: valorMinimoPedido,
          tipoDePedido: globalParams.getTypeOrderId(),
          utilizaComboOferta: true,
          idsDistribuidores: usaLooping
              ? distributorsSelected
                  .map(
                    (x) => IdsDistribuidores(
                      idDistribuidor: x.distribuidorId,
                      ordem: x.ordemSelected ?? 1,
                    ),
                  )
                  .toList()
              : [
                  IdsDistribuidores(
                    idDistribuidor:
                        globalParams.getCurrentDistributor()!.distribuidorId,
                    ordem:
                        globalParams.getCurrentDistributor()!.ordemSelected ??
                            1,
                  )
                ],
          currentDate:
              DateTime.now().formatDate(formatType: DateFormatType.ddMMyyyy),
        ));

        globalParams.order.orderParameters.paymentTypeParameters =
            globalParams.getParameters()!;
        paramHash = globalParams.getParameters()!.toJsonHash();

        globalParams.order.setProducts(productData);
        await dbContext.withControllerAction(this).addData(
              key: DatabaseModels.productsMixModel,
              data: productData,
              storeId: globalParams.getCurrentStore()?.idLoja,
              workspaceId: appController.workspace!.workspaceId,
              hashCode: paramHash,
              clearCurrentData: true,
              isLog: true,
            );
        update();
        loading.hide();
        Get.toNamed(RoutesPath.orders)?.then((value) {
          loading.hide();
        });
      } catch (e, s) {
        appErrorWithDynatrace(subAction, e, s);
        loading.hide();
        log("Error: $e");
      } finally {
        leaveAction();
      }
    });
  }

  Future<ProductsMixModel?> getProductsOfflineEdit(int merContitionId) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("getProductsOfflineEdit");

    try {
      ordersController = Get.find<OrdersController>();
      usaLooping = ordersController.syncOrderEdit!.parameters!.usaLooping!;
      distributorsSelected =
          ordersController.syncOrderEdit!.parameters!.distributorsSelected!;

      distributorsSelected
          .sort((a, b) => a.ordemSelected!.compareTo(b.ordemSelected!));

      final params =
          ordersController.syncOrderEdit!.parameters!.paymentTypeParameters!;

      final storeId = params.pdvId;
      final paymentDeadlineId = params.idPrazoPagamento;
      final merchantConditionId = params.condicaoComercialBaseId;
      final orderTypeId = params.tipoDePedido;

      offlineData = await SyncResultItemsModel().getFirst(
          storeId: storeId!,
          paymentDeadlineId: paymentDeadlineId!,
          merchantConditionId: merchantConditionId,
          orderTypeId: orderTypeId!);

      if (offlineData == null) {
        // Log to dynatrace
        DynatraceLogger.logToDynatrace(
            "Order Payment Type Controller: getProductsOfflineEdit (not found)",
            {
              "storeId": storeId,
              "paymentDeadlineId": paymentDeadlineId,
              "merchantConditionId": merchantConditionId,
              "orderTypeId": orderTypeId
            });

        loading.hide();
        SnackbarCustom.snackbarError("Não foi possível encontrar os produtos");
        return null;
      }

      double valorMinimoPedidoCondicaoComercial = globalParams.order
              .parametrization?.condicaoComercialBase?.valorMinimoDePedido ??
          0;
      double valorMinimoPedido =
          distributorsSelected.first.distribuidor?.valorMinimoDePedido ?? 0;
      if (valorMinimoPedido <= valorMinimoPedidoCondicaoComercial) {
        valorMinimoPedido = valorMinimoPedidoCondicaoComercial;
      }

      final productData =
          await getParameterOfflineSynchronizationResult(offlineData!.id!);
      // dynamic productSync = jsonDecode(offlineData!.synchronizationResult!);
      // final productData = ProductsMixModel.fromJson(productSync);
      globalParams.order.setParameters(MixProdutoRequest(
        pdvId: params.pdvId,
        idPrazoPagamento: paymentDeadlineId,
        cNPJ: params.cNPJ!,
        condicaoComercialBaseId: merchantConditionId,
        valorMinimoDePedido: valorMinimoPedido,
        tipoDePedido: orderTypeId,
        utilizaComboOferta: true,
        idsDistribuidores: usaLooping
            ? distributorsSelected
                .map(
                  (x) => IdsDistribuidores(
                    idDistribuidor: x.distribuidorId,
                    ordem: x.ordemSelected ?? 1,
                  ),
                )
                .toList()
            : [
                IdsDistribuidores(
                  idDistribuidor:
                      globalParams.getCurrentDistributor()!.distribuidorId,
                  ordem:
                      globalParams.getCurrentDistributor()!.ordemSelected ?? 1,
                )
              ],
        currentDate:
            DateTime.now().formatDate(formatType: DateFormatType.ddMMyyyy),
      ));

      globalParams.order.orderParameters.paymentTypeParameters =
          globalParams.getParameters()!;
      paramHash = globalParams.getParameters()!.toJsonHash();

      return productData;
    } catch (e, s) {
      await subAction.reportZoneStacktrace(e, s);
      loading.hide();
      log("Error: $e");
    } finally {
      leaveAction();
    }
    return null;
  }

  Future<Tuple2<TabloidProductsResponse?, GetMixSpecialProductsResponse?>>
      getProductsEspOfflineEdit(TabloidProductsRequest parametersEsp) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("getProductsEspOfflineEdit");

    try {
      globalParams.order.orderParameters.paymentTypeParametersSpecial =
          parametersEsp;

      globalParams.order.orderParameters.distributorCurrent =
          globalParams.getCurrentDistributor()!;

      final storeId = parametersEsp.storeId;
      final orderTypeId = globalParams.getTypeOrderId();

      offlineData = await SyncResultItemsModel()
          .getFirst(storeId: storeId!, orderTypeId: orderTypeId!);

      if (offlineData == null) {
        // Log to dynatrace
        DynatraceLogger.logToDynatrace(
            "Order Payment Type Controller: getProductsEspOfflineEdit (not found)",
            {"storeId": storeId, "orderTypeId": orderTypeId});

        SnackbarCustom.snackbarError("Não foi possível encontrar os produtos");
        return const Tuple2(null, null);
      }

      final productsTabloidData =
          await getParameterOfflineSynchronizationTabloidResult(
              offlineData!.id!, parametersEsp.tabloidId!);

      if (productsTabloidData == null) {
        SnackbarCustom.snackbarError(
            "Os dados do tabloide não foram encontrados, faça a sincronização novamente.");
        return const Tuple2(null, null);
      } else {
        ordersController.productsTabloid = productsTabloidData;
        // final hashTabloidPayment =
        //     "${parametersEsp.tabloidId}:${parametersEsp.paymentTermId!}";
        final productsTabloidSpecialData =
            await getParameterOfflineSynchronizationSpecialProductsResult(
                offlineData!.id!,
                parametersEsp.tabloidId!,
                parametersEsp.paymentTermId!);
        // final productsTabloidSpecialData = await GetMixSpecialProductsResponse()
        //     .getFirst(
        //         storeId: offlineData!.storeId!, tabloidId: hashTabloidPayment);

        if (productsTabloidSpecialData == null) {
          SnackbarCustom.snackbarError(
              "Os dados do tabloide não foram encontrados, faça a sincronização novamente.");
          return const Tuple2(null, null);
        }

        return Tuple2(productsTabloidData, productsTabloidSpecialData);
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      log("Error: $e");
      return const Tuple2(null, null);
    } finally {
      leaveAction();
    }
  }

  Future<ProductsMixModel?> getParameterOfflineSynchronizationResult(
      String id) async {
    bool exists = await JsonStorage.exists(id);
    if (exists) {
      final offlineData = await JsonStorage.read<SyncResultItemsModel>(
          id, SyncResultItemsModel.fromJson);

      dynamic productSync = jsonDecode(offlineData.synchronizationResult!);
      final productData = ProductsMixModel.fromJson(productSync);

      return productData;
      /*
          synchronizationIdealMixResult =""
          synchronizationParams =""
          synchronizationResult =""
          synchronizationSpecialProductsResult =null
          synchronizationTabloidResult =nul
       */
    } else {
      return null;
    }
  }

  Future<TabloidProductsResponse?>
      getParameterOfflineSynchronizationTabloidResult(
          String id, int tabloidId) async {
    bool exists = await JsonStorage.exists(id);
    if (exists) {
      final offlineData = await JsonStorage.read<SyncResultItemsModel>(
          id, SyncResultItemsModel.fromJson);

      List<dynamic> productSyncList =
          jsonDecode(offlineData.synchronizationTabloidResult!);
      final productData = productSyncList
          .map((product) => TabloidProductsResponse.fromJson(product))
          .toList();

      final productsTabloidData = productData
          .where((element) => element.idTabloide == tabloidId)
          .firstOrNull;

      return productsTabloidData;
    } else {
      return null;
    }
  }

  Future<GetMixSpecialProductsResponse?>
      getParameterOfflineSynchronizationSpecialProductsResult(
          String id, int tabloidId, int paymentDeadlineId) async {
    bool exists = await JsonStorage.exists(id);
    if (exists) {
      final offlineData = await JsonStorage.read<SyncResultItemsModel>(
          id, SyncResultItemsModel.fromJson);

      List<dynamic> productDistributorsSync =
          jsonDecode(offlineData.synchronizationSpecialProductsResult!);
      final productDistributorData = productDistributorsSync
          .map((product) => GetMixSpecialProductsResponse.fromJson(product))
          .toList();

      final productsTabloidSpecialData = productDistributorData
          .where(
            (element) =>
                element.tabloidId == tabloidId &&
                element.paymentDeadlineId == paymentDeadlineId,
          )
          .firstOrNull;

      return productsTabloidSpecialData;
    } else {
      return null;
    }
  }

  Future<void> reportEventByAdvance() async {
    final reportAll = Dynatrace().enterAction(
        "Log: Advance - Order Payment Type Controller: ${globalParams.order.currentStore?.cNPJ ?? "-"}");
    try {
      final cnpj = globalParams.order.currentStore?.cNPJ ?? "-";
      final razaoSocial = globalParams.order.currentStore?.razaoSocial ?? "-";
      final idPdv = globalParams.order.currentStore?.idPdv.toString() ?? "-";
      final idLoja = globalParams.order.currentStore?.idLoja.toString() ?? "-";
      final paymentDeadline =
          "${globalParams.getDeadlinePayment()?.idPrazoPagamento.toString()} - ${globalParams.getDeadlinePayment()?.descricao ?? ""}";
      final merchantConditionId =
          globalParams.order.parametrization?.condicaoComercialBase?.id;

      globalParams.getParametrization()?.condicaoComercialBase?.id;
      final orderType = getTyperOrderText(globalParams.order.typeOrderId ?? 0);
      final temPedidoLocal =
          globalParams.order.orderLocal != null ? "Sim" : "Não";
      final idTipoPedidoLocal =
          globalParams.order.orderLocal?.parameters?.typeOrderId.toString() ??
              "-";
      final modoPedido =
          globalParams.order.currentStore?.dataExtra?.offlineDateSync != null
              ? "Offline"
              : "Online";

      reportAll.reportStringValue("CNPJ PDV", cnpj);
      reportAll.reportStringValue("Razão Social PDV", razaoSocial);
      reportAll.reportStringValue("ID PDV", idPdv);
      reportAll.reportStringValue("ID Loja", idLoja);
      reportAll.reportStringValue("Tipo do Pedido", orderType.toString());
      reportAll.reportStringValue("Prazo de Pagamento", paymentDeadline);
      reportAll.reportStringValue(
          "Condição Comercial", merchantConditionId.toString());

      if (distributorsSelected.isNotEmpty) {
        for (var distributor in distributorsSelected) {
          reportAll.reportStringValue("Distribuidor",
              "${distributor.distribuidorId} - ${distributor.distribuidor?.nomeFantasia ?? "-"}");
        }
      }

      reportAll.reportStringValue("Possui pedido local", temPedidoLocal);
      reportAll.reportStringValue("Tipo de Pedido Local", idTipoPedidoLocal);
      reportAll.reportStringValue("Pedido", modoPedido);

      appLog("Parametros > Advance", data: {
        "cnpj": cnpj,
        "razaoSocial": razaoSocial,
        "idPdv": idPdv,
        "idLoja": idLoja,
        "tipoPedido": orderType,
        "temPedidoLocal": temPedidoLocal,
        "idTipoPedidoLocal": idTipoPedidoLocal,
        "modoPedido": modoPedido,
        "distribuidores": distributorsSelected
            .map((distributor) => {
                  "distribuidorId": distributor.distribuidorId,
                  "nomeFantasia": distributor.distribuidor?.nomeFantasia ?? "-"
                })
            .toList(),
      });
    } catch (e) {
      reportAll.reportStringValue("Erro ao registrar eventos", e.toString());
    } finally {
      reportAll.leaveAction();
    }
  }

  Future<void> getCommercialConditions() async {
    setCommercialCondition(null);
    var response = await storesApi
        .getCommercialConditions(globalParams.getCurrentStore()!.idLoja!);
    if (response.data != null) {
      commercialConditions = response.data!;
    }
    update();
  }

  Future<void> setCommercialCondition(CommercialConditionModel? value) async {
    var loading = PlkLoading();
    try {
      loading.show();
      commercialConditionSelected = value;
      resetDistributorsSelecteds();
      if (value != null) {
        if (globalParams.getDeadlinePayment() != null) {
          var response = await storesApi.getDistributorsByConditionAndStore(
              commercialConditionId: value.id,
              storeId: globalParams.getCurrentStore()!.idLoja!);
          if (response.data != null) {
            final distributorList = response.data!;
            distributorsCommercialCondition = [];
            distributorList
                .map((e) => {
                      distributorsCommercialCondition.add(
                        DistribuidoresModel(
                          distribuidorId: e.distributorId,
                          isSelected: false,
                          lockId: e.lockId,
                          pdvId: globalParams.getCurrentStore()!.idPdv,
                          distribuidor: DistribuidorModel(
                            ativo: true,
                            habilitaContaCorrente: e.enableCurrentAccount,
                            id: e.distributorId,
                            nomeFantasia: e.tradeName,
                            razaoSocial: e.corporateName,
                            valorMinimoDePedido: value.minimumOrder,
                          ),
                        ),
                      )
                    })
                .toList();
          }
          distributors = distributorsCommercialCondition;
          distributorsSelected = [];
        }
      } else {
        globalParams
            .getDeadlinePayment()!
            .distribuidores!
            .map((e) => e.isSelected = false)
            .toList();
        distributors = globalParams.getDeadlinePayment()!.distribuidores!;
        distributorsSelected = [];
      }
    } catch (e) {
      log('Error in setCommercialCondition: $e');
    } finally {
      loading.hide();
      update();
    }
  }

  void resetDistributorsSelecteds() {
    for (var distributor in distributorsSelected) {
      distributor.isSelected = false;
      distributor.ordemSelected = null;
      distributors.add(distributor);
    }
    distributorsSelected.clear();
    distributors.sort((a, b) =>
        a.distribuidor!.nomeFantasia!.compareTo(b.distribuidor!.nomeFantasia!));
    update();
  }
}
