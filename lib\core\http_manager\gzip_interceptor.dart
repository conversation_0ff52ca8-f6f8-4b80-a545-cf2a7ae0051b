import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';

class GzipInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    // Verifica se o Content-Type é JSON
    if (options.contentType == Headers.jsonContentType) {
      if (options.data != null) {
        try {
          // Converte Map<dynamic, dynamic> para Map<String, dynamic>
          Map<String, dynamic> dataAsStringKeyedMap =
              Map<String, dynamic>.from(options.data);
          String jsonString = json.encode(dataAsStringKeyedMap);
          options.data = gzip.encode(utf8.encode(jsonString));
          // Define o cabeçalho 'Content-Encoding' para 'gzip'
          options.headers['Content-Encoding'] = 'gzip';
        } catch (e) {
          log('Erro ao converter ou comprimir os dados: $e');
        }
      }
    }
    super.onRequest(options, handler);
  }
}
