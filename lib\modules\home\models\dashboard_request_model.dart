import 'package:pharmalink/exports/basic_exports.dart';

class DashboardRequest {
  String? idUsuario;
  String? idPessoa;
  DateTime? dataInicio;
  DateTime? dataTermino;
  String? setor;
  String? representante;
  String? gdn;
  String? gcr;
  String? pdv;
  String? origemPedido;
  String? tipoPedido;
  String? familia;
  String? produto;
  String? dun;
  String? cd;
  String? distribuidor;
  String? statusRetornoProduto;
  String? statusRetornoPedido;
  String? laboratorio;
  String? bandeira;
  int? pagina;
  int? totalItens;

  DashboardRequest({
    this.idUsuario,
    this.idPessoa,
    this.dataInicio,
    this.dataTermino,
    this.setor,
    this.representante,
    this.gdn,
    this.gcr,
    this.pdv,
    this.origemPedido,
    this.tipoPedido,
    this.familia,
    this.produto,
    this.dun,
    this.cd,
    this.distribuidor,
    this.statusRetornoProduto,
    this.statusRetornoPedido,
    this.laboratorio,
    this.bandeira,
    this.pagina,
    this.totalItens,
  });

  DashboardRequest.fromJson(Map<String, dynamic> json) {
    var date = DateTime.now();

    idUsuario = json['IdUsuario'] ?? appController.userLogged!.userId;
    idPessoa = json['IdPessoa'];
    dataInicio = json['DataInicio'] ?? DateTime(date.year, date.month + 1, 0);
    dataTermino = json['DataTermino'] ?? DateTime(date.year, date.month, 1);
    setor = json['Setor'];
    representante = json['Representante'];
    gdn = json['Gdn'];
    gcr = json['Gcr'];
    pdv = json['Pdv'];
    origemPedido = json['OrigemPedido'];
    tipoPedido = json['TipoPedido'];
    familia = json['Familia'];
    produto = json['Produto'];
    dun = json['Dun'];
    cd = json['Cd'];
    distribuidor = json['Distribuidor'];
    statusRetornoProduto = json['StatusRetornoProduto'];
    statusRetornoPedido = json['StatusRetornoPedido'];
    laboratorio = json['Laboratorio'];
    bandeira = json['Bandeira'];
    pagina = json['pagina'];
    totalItens = json['totalItens'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdUsuario'] = idUsuario;
    data['IdPessoa'] = idPessoa;
    data['DataInicio'] = dataInicio?.toIso8601String();
    data['DataTermino'] = dataTermino?.toIso8601String();
    data['Setor'] = setor;
    data['Representante'] = representante;
    data['Gdn'] = gdn;
    data['Gcr'] = gcr;
    data['Pdv'] = pdv;
    data['OrigemPedido'] = origemPedido;
    data['TipoPedido'] = tipoPedido;
    data['Familia'] = familia;
    data['Produto'] = produto;
    data['Dun'] = dun;
    data['Cd'] = cd;
    data['Distribuidor'] = distribuidor;
    data['StatusRetornoProduto'] = statusRetornoProduto;
    data['StatusRetornoPedido'] = statusRetornoPedido;
    data['Laboratorio'] = laboratorio;
    data['Bandeira'] = bandeira;
    data['pagina'] = pagina ?? 0;
    data['totalItens'] = totalItens ?? 2147483647;
    data['UsuarioPedido'] = [];
    data['TipoProduto'] = [];
    return data;
  }
}
