import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/orders_resume/controller/orders_resume_controller.dart';
import 'package:pharmalink/modules/orders_resume/pages/widgets/orders_resume_combos.dart';
import 'package:pharmalink/modules/orders_resume/pages/widgets/orders_resume_products.dart';

class OrdersResumeTabsWidget extends StatelessWidget {
  const OrdersResumeTabsWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrdersResumeController>(builder: (ctrl) {
      return Column(
        children: [
          Container(
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: themesController.getPrimaryColor(),
                  width: 3.0, // Espessura da borda
                ),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: CustomInkWell(
                    onTap: () => ctrl.setTabSelected(0),
                    child: Container(
                      color: ctrl.tabSelected == 0
                          ? themesController.getPrimaryColor()
                          : themesController.getMenuColor(),
                      child: Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 12.0.w, vertical: 16.w),
                          child: LabelWidget(
                            title: "Produtos".toUpperCase(),
                            fontSize: DeviceSize.fontSize(13, 16),
                            fontWeight: FontWeight.w400,
                            textColor: ctrl.tabSelected == 0
                                ? themesController.getMenuColor()
                                : themesController.getPrimaryColor(),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: CustomInkWell(
                    onTap: () => ctrl.setTabSelected(1),
                    child: Container(
                      color: ctrl.tabSelected == 1
                          ? themesController.getPrimaryColor()
                          : themesController.getMenuColor(),
                      child: Center(
                        child: Padding(
                          padding: EdgeInsets.symmetric(
                              horizontal: 12.0.w, vertical: 16.w),
                          child: LabelWidget(
                            title: "Combos".toUpperCase(),
                            fontSize: DeviceSize.fontSize(13, 16),
                            fontWeight: FontWeight.w400,
                            textColor: ctrl.tabSelected == 1
                                ? themesController.getMenuColor()
                                : themesController.getPrimaryColor(),
                          ),
                        ),
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
          if (ctrl.tabSelected == 0) const OrdersResumeProductsPage(),

          // Conteúdo da tab Combos
          if (ctrl.tabSelected == 1) const OrdersResumeCombosPage(),
        ],
      );
    });
  }
}
