import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class SettingsAppDashboardPage extends StatelessWidget {
  const SettingsAppDashboardPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<SettingsAppController>(
        "SettingsAppDashboardPage", builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: LabelWidget(
            title: "Configurações do dashboard",
            fontSize: DeviceSize.fontSize(18, 21),
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              GetC.close();
            },
          ),
        ),
        body: ListView(
          children: [
            20.toHeightSpace(),
            LabelWidget(
              title: 'Visualizar',
              padding: EdgeInsets.symmetric(horizontal: 16.w),
            ),
            ListTileItemSwitchWidget(
              title: 'Valor total faturado (Mês)',
              value: settingsAppController.settings.hasTotalInvoicedThisMonth ??
                  false,
              onChanged: settingsAppController.setTotalInvoicedThisMonth,
            ),
            ListTileItemSwitchWidget(
              title: 'Valor total solicitado (Mês)',
              value:
                  settingsAppController.settings.hasTotalRequestedThisMonth ??
                      false,
              onChanged: settingsAppController.setTotalRequestedThisMonth,
            ),
            ListTileItemSwitchWidget(
              title: 'Total pedidos enviados (Mês)',
              value: settingsAppController.settings.hasSentOrdersThisMonth ??
                  false,
              onChanged: settingsAppController.setSentOrdersThisMonth,
            ),
            ListTileItemSwitchWidget(
              title: 'Total pedidos solicitados PDV (Mês)',
              value: settingsAppController
                      .settings.hasPDVOrdersRequestedThisMonth ??
                  false,
              onChanged: settingsAppController.setPDVOrdersRequestedThisMonth,
            ),
            ListTileItemSwitchWidget(
              title: 'Total pedidos enviados PDV (Mês)',
              value: settingsAppController.settings.hasPDVOrdersSentThisMonth ??
                  false,
              onChanged: settingsAppController.setPDVOrdersSentThisMonth,
            ),
          ],
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
