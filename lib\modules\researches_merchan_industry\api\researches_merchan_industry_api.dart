import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/researches_merchan_industry/models/researches_merchan_industry_model.dart';
import 'package:pharmalink/modules/researches_merchan_industry/models/researches_merchan_industry_send_model.dart';

abstract class IResearchesMerchanIndustryApi {
  Future<HttpResponse<ResearchesMerchanIndustryDataModel>>
      getResearchesMerchanIndustry({required int routeId});

  Future<HttpResponse<ResearchesMerchanIndustrySendModel>>
      sendResearchesMerchanIndustry(
          {required ResearchesMerchanIndustrySendModel model});
}

class ResearchesMerchanIndustryApi extends IResearchesMerchanIndustryApi {
  final HttpManager _httpManager;
  ResearchesMerchanIndustryApi(this._httpManager);

  @override
  Future<HttpResponse<ResearchesMerchanIndustryDataModel>>
      getResearchesMerchanIndustry({required int routeId}) async {
    return await _httpManager.request<ResearchesMerchanIndustryDataModel>(
      path: 'pesquisasMerchandising/obterFiltrosPesquisaMerchandising/$routeId',
      method: HttpMethods.get,
      parser: (data) {
        return ResearchesMerchanIndustryDataModel.fromJson(data);
      },
    );
  }

  @override
  Future<HttpResponse<ResearchesMerchanIndustrySendModel>>
      sendResearchesMerchanIndustry(
          {required ResearchesMerchanIndustrySendModel model}) async {
    return await _httpManager.request<ResearchesMerchanIndustrySendModel>(
      path: 'pesquisasMerchandising/enviar',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        return ResearchesMerchanIndustrySendModel.fromJson(data);
      },
    );
  }
}
