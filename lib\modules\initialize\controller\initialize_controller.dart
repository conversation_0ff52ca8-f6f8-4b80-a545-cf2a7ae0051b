import 'dart:async';
import 'dart:convert';

import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_exports.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/json/json_storage.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/log_trace_monitor/models/log_trace_monitor_model.dart';
import 'package:pharmalink/modules/login/models/login_response_model.dart';
import 'package:pharmalink/modules/visits/models/visits_by_routes_response_model.dart';
import 'package:pharmalink/modules/workspaces/models/workspaces_model.dart';

class InitializeController
    extends GetxControllerInstrumentado<InitializeController> {
  InitializeController();

  Timer? _loginTimer;

  @override
  Future<void> onReady() async {
    // Iniciar o timer para redirecionar após 1 minuto
    _loginTimer = Timer(const Duration(seconds: 60), () async {
      var (leaveAction, subAction) =
          dynatraceAction.subActionReport("timeout initialize - login");
      try {
        await loginController.logoutForce();
        redirectToLogin();
      } catch (e, s) {
        subAction.reportZoneStacktrace(e, s);
        redirectToLogin();
      } finally {
        leaveAction();
      }
    });

    // if (!kIsWeb) {
    //   await requestPermissions();
    // }

    await startApp();
  }

  Future<void> startApp() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport("startApp");
    try {
      await _loadWorkspace();

      //Se não carregar o workspace o app irá redirecionar pra a tela de workspace direto.
      if (appController.workspace == null) {
        _cancelLoginTimer();
        await _setupForFirstTime();
        return;
      }

      if (await loginVerify()) {
        await logsHttpController.withControllerAction(this).purgeLogs();
        await LogTraceMonitorModel().purgeLogs();
        await JsonStorage.cleanupOldFiles();
        await appController.withControllerAction(this).isValidToken();
        if (appController.hasErrorRefreshToken == true) {
          _cancelLoginTimer();
          redirectToLogin();
          return;
        }
        if (appController.userLogged == null) {
          loginController.reset();
          redirectToLogin();
        } else {
          if (appController.workspace != null) {
            final storeExists = await storesModel.exists(
                workspaceId: appController.workspace!.workspaceId!);

            if (storeExists) {
              await VisitsByRoutesResponseModel().removeOldVisits();
              await storeRoutesController
                  .withControllerAction(this)
                  .syncStoreData();

              storeRoutesOfflineSyncService.startStatusSyncOffline();
              await generalParameterizationController
                  .withControllerAction(this)
                  .getSystemParameterization();
              await generalParameterizationController
                  .withControllerAction(this)
                  .getGeneralSettingsOrderDiscountRegistration();
              await generalParameterizationController
                  .withControllerAction(this)
                  .getParameterByKeyOffline();
              _cancelLoginTimer();
              Get.offAllNamed(RoutesPath.navigationPage);
            } else {
              await synchronizationsController
                  .withControllerAction(this)
                  .onReady();
              synchronizationsController
                  .withControllerAction(this)
                  .setHasStores(true);
              _cancelLoginTimer();
              Get.offAllNamed(RoutesPath.synchronizations,
                  arguments: {'all': false});
            }
          }
        }
      } else {
        if (appController.workspace != null) {
          loginController.withControllerAction(this).reset();
          _cancelLoginTimer();
          redirectToLogin();
        }
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      loginController.withControllerAction(this).reset();
      _cancelLoginTimer();
      redirectToLogin();
    } finally {
      leaveAction();
    }
  }

  void _cancelLoginTimer() {
    if (_loginTimer != null && _loginTimer!.isActive) {
      _loginTimer!.cancel();
    }
  }

  // Future<void> requestPermissions() async {
  //   var (leaveAction, subAction) =
  //       dynatraceAction.subActionReport("requestPermissions");
  //   final permissions = [
  //     Permission.notification,
  //     Permission.camera,
  //     Permission.microphone,
  //     Permission.location,
  //     if (Platform.isAndroid) ...[
  //       Permission.requestInstallPackages,
  //       Permission.manageExternalStorage,
  //       Permission.storage,
  //     ]
  //   ];

  //   for (final permission in permissions) {
  //     if (await permission.status.isDenied) {
  //       await permission.request();

  //       await permission.status;
  //     }
  //     subAction.reportValue(
  //         permission.toString(), (await permission.status).name);
  //   }

  //   leaveAction();
  // }

  Future<bool> loginVerify() async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("loginVerify");
    final login = await LoginResponseModel().getFirst();

    if (login != null) {
      subAction.reportEvent("Sessão prévia encontrada");
      appController.withControllerAction(this).userLogged = login;
      appController.withControllerAction(this).hasInitialize = true;
      loginController.withControllerAction(this).setUserLogged(true);
      if (login.expires != null) {
        final duration = DateTime.now().difference(login.expires!);
        if (duration.inSeconds >= 0 && login.isB2c != true) {
          subAction.reportEvent("Sessão expirou");

          await loginController.withControllerAction(this).refreshToken(login);
        } else if (duration.inSeconds >= 0 && login.isB2c == true) {
          appController.withControllerAction(this).hasInitialize = false;
          return false;
        }
      }

      settingsAppController.withControllerAction(this).init();
      await themesController.withControllerAction(this).getData();
      leaveAction();
      return true;
    } else {
      subAction.reportEvent("Não há sessão prévia");
      appController.withControllerAction(this).hasInitialize = false;
      leaveAction();
      return false;
    }
  }

  Future<void> _loadWorkspace() async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("_loadWorkspace");
    try {
      final workspaceBox = await WorkspacesModel().getList();
      if (workspaceBox.isNotEmpty) {
        workspacesController.setDataList(workspaceBox);
      }
      final currentWorkspace = await WorkspacesModel()
          .getFirstByKey(key: DatabaseModels.currentWorkspace);
      if (currentWorkspace != null) {
        appController.workspace = currentWorkspace;
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
    }
    leaveAction();
  }

  Future<void> _setupForFirstTime() async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("_setupForFirstTime");
    final workspaceBox = await WorkspacesModel().getList();
    if (workspaceBox.isNotEmpty) {
      workspacesController.setDataList(workspaceBox);
      _cancelLoginTimer();
      Get.offAllNamed(RoutesPath.begin);
    } else {
      final result = await workspacesApi.getWorkspaces();
      result.when(
        sucess: (data) async {
          subAction.reportValue(
              "_setupForFirstTime API Result", jsonEncode(data));
          await dbContext.withControllerAction(this).addData(
              key: DatabaseModels.workspacesModel,
              data: data,
              clearCurrentData: true);
          workspacesController.setDataList(data);
          subAction.reportEvent("_setupForFirstTime database atualizado");
          _cancelLoginTimer();
          Get.offAllNamed(RoutesPath.begin);
        },
        error: (error) {
          SnackbarCustom.snackbarError(error.error);
          subAction.reportError(error.error, errorCode: error.code);
        },
      );
    }
    leaveAction();
  }

  void redirectToLogin() {
    if (appController.workspace == null) {
      Get.offAllNamed(RoutesPath.workspaces, arguments: {'settings': false});
    } else {
      Get.offAllNamed(RoutesPath.login);
    }
  }
}
