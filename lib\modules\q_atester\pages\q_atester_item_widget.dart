import 'package:pharmalink/exports/basic_exports.dart';

class QaTesterItemWidget extends StatelessWidget {
  const QaTesterItemWidget({
    super.key,
    required this.title,
    required this.value,
  });
  final String title;
  final String value;
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        5.toHeightSpace(),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            LabelWidget(
              title: title,
              fontSize: 16.sp,
            ),
            LabelWidget(
              title: value,
              fontSize: 16.sp,
              fontWeight: FontWeight.bold,
            )
          ],
        ),
        5.toHeightSpace(),
        const Divider(thickness: 2)
      ],
    );
  }
}
