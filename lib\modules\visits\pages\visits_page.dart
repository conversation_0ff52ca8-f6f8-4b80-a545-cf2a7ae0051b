import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/visits/pages/visits_register_page.dart';
import 'package:pharmalink/modules/visits/pages/visits_search_page.dart';

class VisitsPage extends StatelessWidget {
  const VisitsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<VisitsController>("VisitsPage",
        builder: (ctrl) {
      return Scaffold(
        backgroundColor:
            ctrl.currentIndex == 0 ? whiteColor : Colors.grey.shade600,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelWidget(
                title: globalParams.getCurrentStore()?.razaoSocial ?? "-",
                fontSize: DeviceSize.fontSize(14, 17),
                fontWeight: FontWeight.w600,
                textColor: whiteColor,
              ),
              LabelWidget(
                title: globalParams.getCurrentStore()?.cNPJ ?? "-",
                fontSize: DeviceSize.fontSize(11, 14),
                textColor: whiteColor,
              ),
            ],
          ),
          actions: [
            Visibility(
              visible:
                  ctrl.currentIndex == 0 && ctrl.isEnabled() && ctrl.canEdit(),
              child: CustomInkWell(
                onTap: () async {
                  if (ctrl.validateVisit(isSync: true)) {
                    await ctrl.saveLocal();
                    await synchronizationsController.onReady();
                    synchronizationsController.setHasVisits(true);
                    ctrl.currentRouteVisit!.isSync = SyncEnum.finished;
                    Get.toNamed(RoutesPath.synchronizations,
                        arguments: {'all': true, 'autostart': true});
                  }
                },
                child: Padding(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
                  child: Column(
                    children: [
                      Icon(
                        FontAwesomeIcons.paperPlane,
                        size: 16,
                        color: themesController.getMenuColor(),
                      ),
                      LabelWidget(
                        title: "Enviar",
                        fontSize: 10,
                        textColor: themesController.getMenuColor(),
                      )
                    ],
                  ),
                ),
              ),
            ),
          ],
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              GetC.close();
            },
          ),
        ),
        bottomNavigationBar: BottomNavigationBar(
          elevation: 5,
          currentIndex: ctrl.currentIndex,
          onTap: ctrl.onBottomSelected,
          items: const <BottomNavigationBarItem>[
            BottomNavigationBarItem(
              icon: Icon(FontAwesomeIcons.clipboardList),
              label: 'Registro',
            ),
            BottomNavigationBarItem(
              icon: Icon(FontAwesomeIcons.magnifyingGlass),
              label: 'Pesquisas',
            ),
          ],
        ),
        body: IndexedStack(
          index: ctrl.currentIndex,
          children: [
            ctrl.currentRouteVisit == null
                ? Center(
                    child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const CircularProgressIndicator(),
                      if (ctrl.tryAgain) 15.toHeightSpace(),
                      if (ctrl.tryAgain)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 56),
                          child: PrimaryButtonWidget(
                            titleButtom: 'Tentar Novamente',
                            isLoading: false,
                            onTap: () async {
                              await ctrl.loadVisitData(ctrl.routeId!);
                            },
                          ),
                        ),
                    ],
                  ))
                : const VisitsRegisterPage(),
            const VisitsSearchPage(),
          ],
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
