abstract class ILocalDb {
  Future<String> get databasePath;

  // Adiciona um método para definir o caminho do banco de dados
  void setDatabasePath(String path);

  Future<bool> isDatabaseCorrupted();

  Future<void> addData({
    String? key,
    int? workspaceId,
    String? userId,
    int? storeId,
    String? hashCode,
    dynamic data,
    bool? clearCurrentData,
    bool? isOnline,
    bool? isLog,
  });
  Future<List<dynamic>> readAllData({int? limit, int? offset});
  Future<List<dynamic>> readAllByKey({
    String? key,
    int? workspaceId,
    String? userId,
    int? storeId,
    String? hashCode,
    bool? isOnline,
    int? limit,
    int? offset,
  });
  Future<dynamic> getById<T>({int? id});
  Future<void> deleteByKey({
    String? key,
    int? workspaceId,
    String? userId,
    int? storeId,
    String? hashCode,
    bool? isOnline,
    bool? isLog,
  });
  Future<void> clearDataBase();
  Future<int> getTotalCount();
  Future<List<String>> getDistinctKeys();
}
