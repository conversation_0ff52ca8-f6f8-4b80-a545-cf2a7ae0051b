import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionGlobalController extends GetxController {
  @override
  Future<void> onReady() async {
    super.onReady();
    await requestPermissions();
    update();
  }

  // Lista de permissões a serem gerenciadas
  final List<Permission> permissions = [
    // Permission.notification,
    Permission.camera,
    // Permission.microphone,
    Permission.location,
    // if (Platform.isAndroid) ...[
    //   // Permission.requestInstallPackages,
    //   // Permission.manageExternalStorage,
    //   // Permission.storage,
    // ]
  ];

  // Método para solicitar permissões
  Future<bool> requestPermissions() async {
    bool allGranted = true;
    for (final permission in permissions) {
      if (await permission.status.isDenied) {
        final result = await permission.request();
        if (result.isDenied) {
          allGranted = false;
        }
      }
    }
    return allGranted;
  }

  // Método para verificar o status de todas as permissões
  Future<Map<Permission, PermissionStatus>> checkPermissionsStatus() async {
    Map<Permission, PermissionStatus> statusMap = {};
    for (final permission in permissions) {
      statusMap[permission] = await permission.status;
    }
    return statusMap;
  }
}
