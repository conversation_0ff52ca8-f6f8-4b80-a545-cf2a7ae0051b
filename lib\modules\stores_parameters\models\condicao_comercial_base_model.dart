class CondicaoComercialBaseModel {
  int? id;
  String? descricao;
  double? valorMinimoDePedido;

  CondicaoComercialBaseModel({
    this.id,
    this.descricao,
    this.valorMinimoDePedido,
  });

  CondicaoComercialBaseModel.fromJson(Map<String, dynamic> json) {
    id = json['Id'];
    descricao = json['Descricao'];
    valorMinimoDePedido = json['ValorMinimoDePedido'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Id'] = id;
    data['Descricao'] = descricao;
    data['ValorMinimoDePedido'] = valorMinimoDePedido;

    return data;
  }
}
