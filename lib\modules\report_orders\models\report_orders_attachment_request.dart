class ReportOrdersAttachmentRequest {
  final int idPedido;
  final bool pedidoEnviadoParaAprovacao;

  ReportOrdersAttachmentRequest({
    required this.idPedido,
    required this.pedidoEnviadoParaAprovacao,
  });

  factory ReportOrdersAttachmentRequest.fromJson(Map<String, dynamic> json) {
    return ReportOrdersAttachmentRequest(
      idPedido: json['IdPedido'] as int,
      pedidoEnviadoParaAprovacao: json['PedidoEnviadoParaAprovacao'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'IdPedido': idPedido,
      'PedidoEnviadoParaAprovacao': pedidoEnviadoParaAprovacao,
    };
  }
}
