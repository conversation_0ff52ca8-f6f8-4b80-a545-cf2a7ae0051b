import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/synchronizations/controller/synchronizations_controller.dart';

class SynchronizationsCardWidget extends StatelessWidget {
  final String title;
  final String iconSvg;
  final bool switchValue;
  final ValueChanged<bool?> onChanged;
  final Widget? child;
  final bool syncStart;
  const SynchronizationsCardWidget({
    super.key,
    required this.title,
    required this.iconSvg,
    required this.switchValue,
    required this.onChanged,
    this.child,
    required this.syncStart,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilder<SynchronizationsController>(builder: (ctrl) {
      return Card(
        elevation: 1,
        shadowColor: greyColor,
        child: Container(
          color: whiteCardColor,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  children: [
                    SizedBox(
                      width: 36.w,
                      child: SvgPicture.asset(
                        width: 32.w,
                        height: 32.h,
                        colorFilter: ColorFilter.mode(
                          themesController.getBackgroundColor(),
                          BlendMode.srcIn,
                        ),
                        iconSvg,
                      ),
                    ), // Você pode precisar fazer essa imagem dinâmica também
                    10.toWidthSpace(),
                    Expanded(
                      child: LabelWidget(
                        title: title,
                        fontSize: 21.sp,
                        fontWeight: FontWeight.w400,
                        textColor: themesController.getBackgroundColor(),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    Visibility(
                      visible: syncStart,
                      child: const SyncLoading(),
                    ),
                    10.toWidthSpace(),
                    Switch(
                      value: switchValue,
                      onChanged: syncStart ? null : onChanged,
                      activeColor: themesController.getColorButton(),
                    ),
                  ],
                ),
                if (child != null) child!
              ],
            ),
          ),
        ),
      );
    });
  }
}
