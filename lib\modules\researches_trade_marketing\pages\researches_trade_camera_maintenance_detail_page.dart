import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/researches_trade_marketing/pages/components/researches_trade_camera_detail_widget.dart';

class ResearchesTradeMaintenanceDetailPage extends StatelessWidget {
  const ResearchesTradeMaintenanceDetailPage({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ResearchesTradeMarketingController>(
        "ResearchesTradeMaintenanceDetailPage", builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: LabelWidget(
            title:
                "Manutenção ${ctrl.dateMaintenanceSelected!.formatDate(formatType: DateFormatType.ddMMyyyy)}",
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
          leading: Icon<PERSON><PERSON>on(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              GetC.close();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: ctrl.answersSubmittedFiltered!.map((e) {
                    return SizedBox(
                      width: double.infinity,
                      child: Card(
                          child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 16),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            LabelWidget(
                              title: e.question ?? "-",
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                            5.toHeightSpace(),
                            LabelWidget(
                              title: e.answers?.first.toString() ?? "-",
                              fontSize: 24,
                              fontWeight: FontWeight.normal,
                            ),
                            5.toHeightSpace(),
                            LabelWidget(
                              title: e.explainLabel ?? "Observações",
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                            5.toHeightSpace(),
                            LabelWidget(
                              title: e.explainText ?? "Não informado",
                              fontSize: 24,
                              fontWeight: FontWeight.normal,
                            ),
                          ],
                        ),
                      )),
                    );
                  }).toList(),
                ),
              ),
              if (ctrl.maintenanceImagesBefore.isNotEmpty)
                ResearchesTradeCameraDetailWidget(
                  title: "Antes",
                  images: ctrl.maintenanceImagesBefore,
                ),
              if (ctrl.maintenanceImagesAfter.isNotEmpty)
                ResearchesTradeCameraDetailWidget(
                  title: "Depois",
                  images: ctrl.maintenanceImagesAfter,
                ),
            ],
          ),
        ),
      );
    });
  }
}
