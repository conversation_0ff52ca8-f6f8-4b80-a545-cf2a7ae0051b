import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/ilocal_db.dart';
import 'package:pharmalink/core/utils/dynatrace/dynatrace_custom_action.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';

mixin class ILocalDbInstrumentado {

  DynatraceCustomAction? _action;

  String? _tagName;

  DynatraceCustomAction get dynatraceAction => _getDynatraceAction();

  void initDynatraceAction(String? tagName) {
    _tagName ??= tagName ?? runtimeType.toString();
    _action ??= dynatrace.actionReport(runtimeType, _tagName!);
  }

  DynatraceCustomAction _getDynatraceAction() {
    if (_action == null) {
      initDynatraceAction(runtimeType.toString());
    }
    return _action!;
  }

  ILocalDb withAction(DynatraceCustomAction action) {
    _action = action;
    return this as ILocalDb;
  }

  ILocalDb withControllerAction(GetxControllerInstrumentado controller) {
    _action = controller.dynatraceAction;
    return this as ILocalDb;
  }

}
