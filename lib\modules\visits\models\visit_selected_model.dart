import 'package:pharmalink/modules/visits/models/visits_by_routes_response_model.dart';

class VisitSelectedModel {
  late AcompanhamentosVisita role;
  late String name;

  VisitSelectedModel({required this.role, required this.name});

  static VisitSelectedModel empty() {
    return VisitSelectedModel(
      role: AcompanhamentosVisita(descricao: 'Não', idVisitaAcompanhamento: 1),
      name: '',
    );
  }

  static VisitSelectedModel fromJson(Map<String, dynamic> json) {
    return VisitSelectedModel(
      role: AcompanhamentosVisita(
        descricao: json['PerfilAcompanhante'],
        idVisitaAcompanhamento: json['IdVisitaAcompanhamento'],
      ),
      name: json['NomeAcompanhante'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['PerfilAcompanhante'] = role.descricao;
    data['NomeAcompanhante'] = name;
    data['IdVisitaAcompanhamento'] = role.idVisitaAcompanhamento;
    return data;
  }

  VisitSelectedModel copyWith({AcompanhamentosVisita? role, String? name}) {
    return VisitSelectedModel(role: role ?? this.role, name: name ?? this.name);
  }
}
