import 'package:pharmalink/exports/basic_exports.dart';

// ignore: must_be_immutable
class QuestionTextWidget extends StatefulWidget {
  QuestionTextWidget(
      {super.key,
      required this.title,
      this.selectedAwser,
      required this.onAnswerSelected,
      this.hint,
      required this.readonly,
      this.isRequired});

  final bool readonly;
  final String title;
  final String? hint;
  late String? selectedAwser;
  final Function(String?) onAnswerSelected;
  final bool? isRequired;
  @override
  State<QuestionTextWidget> createState() => _QuestionTextWidgetState();
}

class _QuestionTextWidgetState extends State<QuestionTextWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomTextField(
            label: widget.title,
            hint: widget.hint,
            readOnly: widget.readonly,
            controller: TextEditingController(text: widget.selectedAwser ?? ""),
            onChanged: (String? value) {
              widget.onAnswerSelected(value);
            },
          ),
          Divider(
            thickness: 1,
            color:
                widget.isRequired == true ? Colors.red : Colors.grey.shade300,
          )
        ],
      ),
    );
  }
}
