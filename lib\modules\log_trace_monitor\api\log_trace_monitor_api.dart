import 'package:dio/dio.dart';
import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';

abstract class ILogTraceMonitorApi {
  Future<HttpResponse<bool>> sendBackup(String zipFilePath, String fileName);
}

class LogTraceMonitorApi extends ILogTraceMonitorApi {
  final HttpManager _httpManager;
  LogTraceMonitorApi(this._httpManager);

  @override
  Future<HttpResponse<bool>> sendBackup(
      String zipFilePath, String fileName) async {
    MultipartFile file =
        await MultipartFile.fromFile(zipFilePath, filename: fileName);
    return await _httpManager.request<bool>(
      path: '',
      baseUrl: '${appController.apiUrlDataVault}v1/arquivos/salvar',
      method: HttpMethods.post,
      headers: {
        'api-key': 'stub',
      },
      formData: {
        'file': file,
        'userName': appController.userLogged?.userName ?? "not identified"
      },
      parser: (data) {
        return true;
      },
    );
  }
}
