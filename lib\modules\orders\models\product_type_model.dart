import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class TiposProdutosModel extends SqfLiteBase<TiposProdutosModel> {
  int? idTipoProduto;
  String? descricao;

  TiposProdutosModel({this.idTipoProduto, this.descricao})
    : super(DatabaseModels.productTypeModel);

  TiposProdutosModel.fromJson(Map<String, dynamic> json)
    : super(DatabaseModels.productTypeModel) {
    idTipoProduto = json['IdTipoProduto'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdTipoProduto'] = idTipoProduto;
    data['Descricao'] = descricao;
    return data;
  }

  Future<List<TiposProdutosModel>> getList() async {
    var list = await getAll<TiposProdutosModel>(TiposProdutosModel.fromJson);
    return list;
  }
}
