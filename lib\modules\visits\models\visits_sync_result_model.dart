class VisitSyncResultModel {
  int? statusCode;
  int? idRota;
  int? idVisita;
  bool? success;

  VisitSyncResultModel({this.idRota, this.idVisita, this.success});

  VisitSyncResultModel.fromJson(Map<String, dynamic> json) {
    idRota = json['IdRota'];
    idVisita = json['IdVisita'];
    success = json['sucesso'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    data['IdVisita'] = idVisita;
    data['IdRota'] = idRota;
    data['sucesso'] = success;

    return data;
  }
}
