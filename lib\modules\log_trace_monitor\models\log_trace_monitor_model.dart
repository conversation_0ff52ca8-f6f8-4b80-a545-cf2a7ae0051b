import 'dart:developer';

import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:uuid/uuid.dart';

class LogTraceMonitorModel extends SqfLiteBase<LogTraceMonitorModel> {
  int? id;
  String? controllerName;
  String? methodName;
  String? type;
  String? textData;
  String? jsonData;
  DateTime? createAt;

  LogTraceMonitorModel({
    this.id,
    this.controllerName,
    this.methodName,
    this.type,
    this.createAt,
    this.jsonData,
    this.textData,
  }) : super(DatabaseModels.logTraceMonitorModel);

  LogTraceMonitorModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.logTraceMonitorModel) {
    id = json['id'];
    controllerName = json['controllerName'];
    methodName = json['methodName'];
    type = json['type'];
    textData = json['textData'];
    jsonData = json['jsonData'];
    createAt =
        json['createAt'] != null ? DateTime.parse(json['createAt']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['controllerName'] = controllerName;
    data['methodName'] = methodName;
    data['type'] = type;
    data['createAt'] = createAt?.toIso8601String();
    data['jsonData'] = jsonData;
    data['textData'] = textData;
    return data;
  }

  Future<LogTraceMonitorModel> getFirst() async {
    var list = await getAll<LogTraceMonitorModel>(
        workspaceId: appController.workspace?.workspaceId,
        userId: appController.userLogged?.userId,
        LogTraceMonitorModel.fromJson);
    return list.first;
  }

  Future<List<LogTraceMonitorModel>> getList({DateTime? createdAt}) async {
    var list = await getAll<LogTraceMonitorModel>(
        workspaceId: appController.workspace?.workspaceId,
        userId: appController.userLogged?.userId,
        createdAt: createdAt,
        LogTraceMonitorModel.fromJson);
    return list;
  }

  Future<void> logger() async {
    await dbContext.addData(
      key: DatabaseModels.logTraceMonitorModel,
      data: this,
      workspaceId: appController.workspace?.workspaceId,
      userId: appController.userLogged?.userId,
      clearCurrentData: false,
      hashCode: const Uuid().v4(),
      isLog: true,
    );
  }

  Future<void> purgeLogs() async {
    try {
      var httpExpirationDays = DateTime.now().subtract(const Duration(days: 3));
      await dbContext.deleteByKey(
        key: DatabaseModels.logTraceMonitorModel,
        workspaceId: appController.workspace?.workspaceId,
        userId: appController.userLogged?.userId,
        isLog: true,
        createdAt: httpExpirationDays,
      );
    } catch (e) {
      log(e.toString());
    }
  }
}
