class CommercialConditionModel {
  final int id;
  final String description;
  final double minimumOrder;
  final int discountHierarchyId;
  final String hierarchyDescription;
  final int order;
  final int storeId;
  final int networkId;

  CommercialConditionModel({
    required this.id,
    required this.description,
    required this.minimumOrder,
    required this.discountHierarchyId,
    required this.hierarchyDescription,
    required this.order,
    required this.storeId,
    required this.networkId,
  });

  factory CommercialConditionModel.fromJson(Map<String, dynamic> json) {
    return CommercialConditionModel(
      id: json['IdCondicaoComercial'],
      description: json['DescricaoCondicaoComercial'],
      minimumOrder: json['PedidoMinimo'],
      discountHierarchyId: json['IdHierarquiaDesconto'],
      hierarchyDescription: json['DescricaoHierarquia'],
      order: json['Ordem'],
      storeId: json['IdLoja'],
      networkId: json['IdRede'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'IdCondicaoComercial': id,
      'DescricaoCondicaoComercial': description,
      'PedidoMinimo': minimumOrder,
      'IdHierarquiaDesconto': discountHierarchyId,
      'DescricaoHierarquia': hierarchyDescription,
      'Ordem': order,
      'IdLoja': storeId,
      'IdRede': networkId,
    };
  }
}

class CommercialConditionDistributorsModel {
  final int distributorId;
  final String tradeName;
  final String? corporateName;
  final String cnpj;
  final String? stateRegistration;
  final String? postalCode;
  final String? address;
  final String? number;
  final String? complement;
  final String? city;
  final String? state;
  final String? neighborhood;
  final String? phone;
  final String? fax;
  final String? website;
  final String? email;
  final String? contactName;
  final int contactPositionId;
  final String? contactPhone;
  final String? contactCellPhone;
  final String? contactEmail;
  final String? contactExtension;
  final bool status;
  final int? distributionCenterId;
  final bool exception;
  final bool isCampaignOrder;
  final bool isBonusOrder;
  final double minimumOrder;
  final String? inclusionDate;
  final String? alterationDate;
  final bool? acceptsBonus;
  final int toleranceDays;
  final bool? enableCurrentAccount;
  final int? lockId;
  final String? validationResult;

  CommercialConditionDistributorsModel({
    required this.distributorId,
    required this.tradeName,
    this.corporateName,
    required this.cnpj,
    this.stateRegistration,
    this.postalCode,
    this.address,
    this.number,
    this.complement,
    this.city,
    this.state,
    this.neighborhood,
    this.phone,
    this.fax,
    this.website,
    this.email,
    this.contactName,
    required this.contactPositionId,
    this.contactPhone,
    this.contactCellPhone,
    this.contactEmail,
    this.contactExtension,
    required this.status,
    this.distributionCenterId,
    required this.exception,
    required this.isCampaignOrder,
    required this.isBonusOrder,
    required this.minimumOrder,
    this.inclusionDate,
    this.alterationDate,
    this.acceptsBonus,
    required this.toleranceDays,
    this.enableCurrentAccount,
    this.lockId,
    this.validationResult,
  });

  factory CommercialConditionDistributorsModel.fromJson(
      Map<String, dynamic> json) {
    return CommercialConditionDistributorsModel(
      distributorId: json['IdDistribuidor'],
      tradeName: json['NomeFantasia'],
      corporateName: json['RazaoSocial'],
      cnpj: json['CNPJ'],
      stateRegistration: json['IE'],
      postalCode: json['CEP'],
      address: json['Endereco'],
      number: json['Numero'],
      complement: json['Complemento'],
      city: json['Cidade'],
      state: json['Uf'],
      neighborhood: json['Bairro'],
      phone: json['Telefone'],
      fax: json['Fax'],
      website: json['WebSite'],
      email: json['Email'],
      contactName: json['NomeContato'],
      contactPositionId: json['IdCargoContato'],
      contactPhone: json['TelefoneContato'],
      contactCellPhone: json['CelularContato'],
      contactEmail: json['EmailContato'],
      contactExtension: json['RamalContato'],
      status: json['Status'],
      distributionCenterId: json['IdCentroDistribuicao'],
      exception: json['Excecao'],
      isCampaignOrder: json['IsPedidoCampanha'],
      isBonusOrder: json['IsPedidoBonificado'],
      minimumOrder: json['PedidoMinimo'],
      inclusionDate: json['DataInclusao'],
      alterationDate: json['DataAlteracao'],
      acceptsBonus: json['AceitaBonificacao'],
      toleranceDays: json['QtdDiasTolerancia'],
      enableCurrentAccount: json['HabilitarContaCorrente'],
      lockId: json['IdTrava'],
      validationResult: json['ValidationResult'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'IdDistribuidor': distributorId,
      'NomeFantasia': tradeName,
      'RazaoSocial': corporateName,
      'CNPJ': cnpj,
      'IE': stateRegistration,
      'CEP': postalCode,
      'Endereco': address,
      'Numero': number,
      'Complemento': complement,
      'Cidade': city,
      'Uf': state,
      'Bairro': neighborhood,
      'Telefone': phone,
      'Fax': fax,
      'WebSite': website,
      'Email': email,
      'NomeContato': contactName,
      'IdCargoContato': contactPositionId,
      'TelefoneContato': contactPhone,
      'CelularContato': contactCellPhone,
      'EmailContato': contactEmail,
      'RamalContato': contactExtension,
      'Status': status,
      'IdCentroDistribuicao': distributionCenterId,
      'Excecao': exception,
      'IsPedidoCampanha': isCampaignOrder,
      'IsPedidoBonificado': isBonusOrder,
      'PedidoMinimo': minimumOrder,
      'DataInclusao': inclusionDate,
      'DataAlteracao': alterationDate,
      'AceitaBonificacao': acceptsBonus,
      'QtdDiasTolerancia': toleranceDays,
      'HabilitarContaCorrente': enableCurrentAccount,
      'IdTrava': lockId,
      'ValidationResult': validationResult,
    };
  }
}
