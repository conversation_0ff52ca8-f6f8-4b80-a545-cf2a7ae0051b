import 'package:sqlite_explorer/exports/get_exports.dart';

enum Environment {
  production,
  developer,
  staging,
}

enum DeviceType {
  mobile,
  tablet,
}

class AppController extends GetxController {
  AppController();

  bool hasErrorRefreshToken = false;
  String get apiUrlAzure => setApiUrlAzure();

  int get httpExpirationDays => 7;
  Environment currentEnvironment = Environment.staging;

  Map<String, String> urlsAzure = {
    'developer':
        'https://idp-api-gtw.azure-api.net/marketplace-pedido-api-pre/',
    'staging': 'https://idp-api-gtw.azure-api.net/marketplace-pedido-api-pre/',
    'production': 'https://api-mng.interplayers.com.br/marketplace-pedido-api/',
  };

  Map<String, String> urlsSyncOffline = {
    'developer':
        'https://aks-dev.interplayers.com.br/marketplace-appoffline-api/store/',
    'staging':
        'https://idp-api-gtw.azure-api.net/marketplace-appoffline-api-pre/store/',
    'production':
        'https://api-mng.interplayers.com.br/marketplace-appoffline-api/store/',
  };

  bool hasInitialize = true;
  void setHasInitialize(bool v) => hasInitialize = v;

  DeviceType deviceType = DeviceType.mobile;

  void setDevice(bool isTablet) {
    if (isTablet) {
      deviceType = DeviceType.tablet;
    } else {
      deviceType = DeviceType.mobile;
    }
  }

  bool isMobile() {
    return deviceType == DeviceType.mobile;
  }

  bool isTablet() {
    return deviceType == DeviceType.tablet;
  }

  getIsProduction() {
    switch (currentEnvironment) {
      case Environment.production:
        return true;
      case Environment.developer:
      case Environment.staging:
        return false;
    }
  }

  Future<void> loadUrl() async {
    setApiUrlAzure();
  }

  setApiUrlAzure() {
    switch (currentEnvironment) {
      case Environment.production:
        return urlsAzure['production'];
      case Environment.developer:
        return urlsAzure['developer'];
      case Environment.staging:
        return urlsAzure['staging'];
    }
  }

  String extractHostname(String url) {
    final uri = Uri.parse(url);
    return uri.host;
  }
}
