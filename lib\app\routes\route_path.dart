abstract class RoutesPath {
  static const String navigationPage = '/navigationPage';
  static const String settings = '/settings';
  static const String settingsApp = '/settingsApp';
  static const String settingsAppDashboard = '/settingsAppDashboard';
  static const String settingsAppOrders = '/settingsAppOrders';
  static const String home = '/home';
  static const String initialize = '/initialize';
  static const String splashPage = '/splash';
  static const String basePage = '/';
  static const String logsHttp = '/logsHttp';
  static const String environment = '/environment';
  static const String begin = '/begin';
  static const String workspaces = '/workspaces';
  static const String login = '/login';
  static const String logoutB2c = '/logoutB2c';
  static const String synchronizations = '/synchronizations';
  static const String stores = '/stores';
  static const String storesPdv = '/storesPdv';
  static const String reports = '/reports';
  static const String storeDetail = '/storeDetail';
  static const String orderTypes = '/orderTypes';
  static const String orderPaymentType = '/orderPaymentType';
  static const String orders = '/orders';
  static const String ordersIdealMix = '/ordersIdealMix';
  static const String ordersCombo = '/ordersCombo';
  static const String ordersResume = '/ordersResume';
  static const String ordersTabloid = '/ordersTabloid';
  static const String qATester = '/qATester';
  static const String reportContents = '/reportContents';
  static const String reportOrders = '/reportOrders';
  static const String reportOrdersDetail = '/reportOrdersDetail';
  static const String updateApp = '/updateApp';
  static const String visits = '/visits';
  static const String visitsCamera = '/visitsCamera';
  static const String researchesProductIndustry = '/researchesProductIndustry';
  static const String researchesMerchanIndustry = '/researchesMerchanIndustry';
  static const String researchesMerchanIndustryQuestion =
      '/researchesMerchanIndustryQuestion';
  static const String researchesMerchanCompetitive =
      '/researchesMerchanCompetitive';
  static const String researchesMerchanCompetitiveQuestion =
      '/researchesMerchanCompetitiveQuestion';
  static const String content = '/content';
  static const String researchesProductConcurrent =
      '/researchesProductConcurrent';
  static const String researchesTradeMarketing = '/researchesTradeMarketing';
  static const String researchesTradeMaintenanceList =
      '/researchesTradeMaintenanceList';
  static const String researchesTradeMaintenanceDetail =
      '/researchesTradeMaintenanceDetail';
  static const String researchesComplementary = '/researchesComplementary';
  static const String researchesComplementaryQuestion =
      '/researchesComplementaryQuestion';
  static const String researchesShareOfShelf = '/researchesShareOfShelf';
  static const String researchesShareOfShelfPhotoCrop =
      '/researchesShareOfShelfPhotoCrop';
  static const String researchesShareOfShelfPhotoArea =
      '/researchesShareOfShelfPhotoArea';

  static const String cameraPicker = '/cameraPicker';
  static const String cameraPickerPreview = '/cameraPickerPreview';
  static const String permissionRequest = '/permissionRequest';
  static const String reportOrdersSync = '/reportOrdersSync';
  static const String logTraceMonitor = '/logTraceMonitor';
  static const String storeRoutes = '/storeRoutes';
  static const String storeRoutesPanel = '/storeRoutesPanel';
  static const String storeRoutesPlanned = '/storeRoutesPlanned';
  static const String storeRoutesDetails = '/storeRoutesDetails';
  static const String permissionGlobal = '/permissionGlobal';
  static const String cacheConfig = '/cacheConfig';
  static const String reportOrdersUpload = '/reportOrdersUpload';
}
