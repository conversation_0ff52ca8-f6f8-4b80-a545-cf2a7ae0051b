import 'package:pharmalink/exports/basic_exports.dart';

class LabelWidget extends StatelessWidget {
  const LabelWidget({
    super.key,
    required this.title,
    this.textColor,
    this.fontWeight,
    this.fontSize,
    this.padding,
    this.overflow,
    this.maxLines,
    this.textAlign,
  });
  final String title;
  final Color? textColor;
  final FontWeight? fontWeight;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;
  final TextOverflow? overflow;
  final int? maxLines;
  final TextAlign? textAlign;

  List<TextSpan> _buildRichText() {
    final List<TextSpan> spans = [];
    final RegExp exp = RegExp(r'\*(.*?)\*');
    int lastIndex = 0;

    for (final Match match in exp.allMatches(title)) {
      if (match.start > lastIndex) {
        spans.add(TextSpan(text: title.substring(lastIndex, match.start)));
      }
      spans.add(TextSpan(
        text: match.group(1),
        style: const TextStyle(fontWeight: FontWeight.bold),
      ));
      lastIndex = match.end;
    }

    if (lastIndex < title.length) {
      spans.add(TextSpan(text: title.substring(lastIndex)));
    }

    return spans;
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(0),
      child: RichText(
        maxLines: maxLines,
        textAlign: textAlign ?? TextAlign.start,
        text: TextSpan(
          style: TextStyle(
            color: textColor ?? Colors.black,
            fontWeight: fontWeight ?? FontWeight.normal,
            fontSize: fontSize ?? 12,
            overflow: overflow,
          ),
          children: _buildRichText(),
        ),
      ),
    );
  }
}
