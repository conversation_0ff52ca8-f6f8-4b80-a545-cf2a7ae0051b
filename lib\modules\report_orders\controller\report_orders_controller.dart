import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart' show rootBundle;
import 'package:intl/intl.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/models/result_error.dart';
import 'package:pharmalink/core/models/result_error_model.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/notification/models/notification_model.dart';
import 'package:pharmalink/modules/orders/models/product_type_model.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';
import 'package:pharmalink/modules/report_orders/models/report_orders_attachment_request.dart';
import 'package:pharmalink/modules/report_orders/models/report_orders_mirror_response_model.dart';
import 'package:pharmalink/modules/report_orders/models/report_orders_request_model.dart';
import 'package:pharmalink/modules/report_orders/models/report_orders_response_model.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';
import 'package:pharmalink/widgets/pdf/pdf_widget.dart';
import 'package:share_plus/share_plus.dart';

class ReportOrdersController
    extends GetxControllerInstrumentado<ReportOrdersController> {
  ReportOrdersController();

  bool isLoadMore = false;
  ReportOrdersListPedidos? selected;
  ReportOrdersListRequest? request;
  ReportOrdersListResponse? dataList;
  List<ReportOrdersListPedidos> ordersSearchList = [];
  StoresModel? pdvChoice;
  List<StoresModel> storesFull = [];
  List<TiposProdutosModel> productTypes = [];
  List<TiposProdutosModel> selectedProductTypes = [];

  bool isProgress = false;

  String startDateStr = "__/__/____";
  DateTime? startDate;
  String endDateStr = "__/__/____";
  DateTime? endDate;
  TextEditingController pdvController = TextEditingController();
  String? pdvText;
  TextEditingController numberOrderController = TextEditingController();
  String? numberOrder;
  TextEditingController userController = TextEditingController();
  String? selectedUser;

  TextEditingController productTypeController = TextEditingController();

  bool hasPdvList = false;

  List<SyncronizationModel> ordersList = [];
  List<StoresModel> storesAll = [];

  @override
  Future<void> onReady() async {
    super.onReady();
    await initializeData();
  }

  Future<void> initializeData() async {
    _setupDates();
    storesFull = storeRoutesController.storeListFull;
    await updateListOrdersToSync();
    await _getStoresFromLocalBase();
    await verifiyMessages();
    await loadProductTypes();
    update();
  }

  void toggleProductTypeSelection(TiposProdutosModel productType) {
    final isSelected = selectedProductTypes.any(
      (pt) => pt.idTipoProduto == productType.idTipoProduto,
    );
    if (isSelected) {
      selectedProductTypes.removeWhere(
        (pt) => pt.idTipoProduto == productType.idTipoProduto,
      );
    } else {
      selectedProductTypes.add(productType);
    }
    _updateProductTypeControllerText();
    update();
  }

  void _updateProductTypeControllerText() {
    if (selectedProductTypes.isEmpty) {
      productTypeController.text = "";
    } else {
      productTypeController.text = selectedProductTypes
          .map((e) => e.descricao ?? '')
          .join(', ');
    }
  }

  void clearSelectedProductTypes() {
    selectedProductTypes.clear();
    _updateProductTypeControllerText();
    update();
  }

  ///Obtem a lista de lojas através da basel local
  _getStoresFromLocalBase() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "BuscaLojasBaseLocal",
    );
    try {
      subAction.reportEvent('buscando lojas na base local');
      storesAll = await StoresModel().getList();
      subAction.reportEvent('busca lojas na base local concluída');
    } catch (e, stack) {
      subAction.reportZoneStacktrace(e, stack);
    } finally {
      leaveAction();
    }
  }

  ///Verifica se há mensagens e pega a primeira encontrada
  Future<void> verifiyMessages() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "VerificaMensagens",
    );
    try {
      subAction.reportEvent('Buscando se há notificação');
      final message = await NotificationModel().getFirstMessage(1);
      if (message != null) {
        subAction.reportEvent('Notificação encontrada para ser exibida');
        message.isShow = true;
        await dbContext.addData(
          clearCurrentData: true,
          key: DatabaseModels.notificationModel,
          userId: message.id,
          data: message,
          workspaceId: appController.workspace!.workspaceId!,
        );
        await Dialogs.info(
          AppStrings.attention,
          message.message!,
          buttonName: "Entendi",
        );
      }
    } catch (e, stack) {
      subAction.reportZoneStacktrace(e, stack);
    } finally {
      leaveAction();
    }
  }

  /// Buscar lista de pedidos a serem sincronizados e sincronizados recentemente
  /// Filtro por Wordkspace
  Future<void> updateListOrdersToSync() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "ListagemPedidosSincronizar",
    );
    try {
      ordersList.clear();
      final syncList = await SyncronizationModel().getList(
        workspaceId: appController.workspace!.workspaceId!,
        storeId: null,
      );
      if (syncList.isNotEmpty) {
        subAction.reportEvent(
          '${syncList.length} itens encontrados, iniciando mapeamento da classe',
        );

        syncList.map((e) => ordersList.add(e)).toList();
        ordersList.sort((a, b) => b.createAt!.compareTo(a.createAt!));
        subAction.reportEvent('itens mapeados');
      }
    } catch (e, stack) {
      subAction.reportZoneStacktrace(e, stack);
    } finally {
      update();
      leaveAction();
    }
  }

  /// Listar pedidos efetuados
  /// Pelo menos um dos itens abaixo é obrigado para filtro
  /// startDate, endDate, pdvText, numberOrder, selectedUser, selectedProductType
  Future<void> searchOrders({bool? isClear}) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "ListagemHistoricoPedidos",
    );
    try {
      await appController
          .withDynatraceAction(dynatraceAction)
          .isValidToken(noMessages: true);
      if (appController.hasErrorRefreshToken == true) {
        subAction.reportError("Falha ao realizar refresh to token");
        SnackbarCustom.snackbarError(AppStrings.tokenExpired);
        return;
      }
      subAction.reportEvent('Validando filtro');
      var resultFilterValidation = _validateFiltersOnSearchOrders();
      if (resultFilterValidation != null) {
        subAction.reportEvent('Filtro incompleto: $resultFilterValidation');
        SnackbarCustom.snackbarError(resultFilterValidation);
        return;
      }

      //Garante que o cnpjRazaoSocial seja nulo caso não tenha sido selecionado um PDV valido
      if (pdvText != null && pdvText!.isEmpty) {
        pdvText = null;
      }

      if (request == null || isClear == true) {
        subAction.reportEvent(
          'ReportOrdersListRequest = null ou isClear = true',
        );
        ordersSearchList = [];
        update();
        request = ReportOrdersListRequest(
          userId: appController.userLogged!.userId,
          pagina: 1,
          tamanhoPagina: 10,
          perfil: appController.userLogged!.perfil,
          dataInicio:
              startDate?.formatDate(formatType: DateFormatType.yyyyMMdd),
          dataTermino: endDate?.formatDate(formatType: DateFormatType.yyyyMMdd),
          idPedido: numberOrder,
          cnpjRazaoSocial: pdvText,
          usuario: selectedUser,
          tipoProduto:
              selectedProductTypes.isNotEmpty
                  ? selectedProductTypes.map((e) => e.descricao!).toList()
                  : null,
        );
      } else {
        subAction.reportEvent(
          'ReportOrdersListRequest != null ou isClear = false',
        );
        request!.dataInicio = startDate?.formatDate(
          formatType: DateFormatType.yyyyMMdd,
        );
        request!.dataTermino = endDate?.formatDate(
          formatType: DateFormatType.yyyyMMdd,
        );
        request!.idPedido = numberOrder;
        request!.cnpjRazaoSocial = pdvText;
        request!.usuario = selectedUser;
        request!.tipoProduto =
            selectedProductTypes.isNotEmpty
                ? selectedProductTypes.map((e) => e.descricao!).toList()
                : null;
      }
      setIsLoadMode(true);
      subAction.reportEvent('Realizando requisição getReportOrdersAnalitycs');
      final result = await reportOrdersApi.getReportOrdersAnalitycs(
        model: request!,
      );
      subAction.reportEvent('Requisição getReportOrdersAnalitycs finalizada');

      if (result.data != null) {
        subAction.reportEvent('Sucesso na listagem dos pedidos');
        dataList = result.data!;
        if (request!.pagina == 1) ordersSearchList = [];
        ordersSearchList.addAll(result.data!.pedidos ?? []);
      } else {
        if (result.error!.data is ResultErrorModel) {
          subAction.reportError(
            'Falha na listagem de pedidos (getReportOrdersAnalitycs) : ${result.error?.data ?? ''}',
          );
          final error = result.error!.data as ResultErrorModel;
          final String? message = error.modelState?.erros!
              .map((e) => e)
              .join("\n");
          SnackbarCustom.snackbarError(
            message ?? "Ocorreu um erro não identificado!",
          );
        } else if (result.error!.data is ResultError) {
          subAction.reportError(
            'Falha na listagem de pedidos (getReportOrdersAnalitycs) : ${result.error?.data ?? ''}',
          );
          final error = result.error!.data as ResultError;
          final String message = error.error;
          SnackbarCustom.snackbarError(message);
        }
      }
      setIsLoadMode(false);
    } catch (e, stack) {
      subAction.reportZoneStacktrace(e, stack);
    } finally {
      leaveAction();
    }
  }

  /// Cria pdf de espelho de pedidos
  Future<void> createPdfMirror(String orderNumber) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "gerarPdfEspelhoPedido",
    );
    try {
      subAction.reportEvent('RequestAPI informações do espelho do pedido');
      final info = await reportOrdersApi.getMirrorOrder(
        orderNumber: orderNumber,
      );
      if (info.error != null) {
        subAction.reportEvent(
          'RequestAPI informações do espelho do pedido falha, ${info.error!.message!}',
        );
        SnackbarCustom.snackbarError(info.error!.message!);
        return;
      }

      subAction.reportEvent(
        'RequestAPI informações do espelho do pedido sucesso',
      );
      final mirror = info.data!;
      const pageFormat = PdfPageFormat.a4;
      final pdf = pw.Document();

      subAction.reportEvent('Gerando logo para inserir no PDF');
      final image = await rootBundle.load("assets/app/logo.png");
      final imgLogo = pw.MemoryImage(image.buffer.asUint8List());
      subAction.reportEvent('Logo renderizada');
      pdf.addPage(
        pw.MultiPage(
          pageFormat: PdfPageFormat.a4,
          build:
              (pw.Context context) => [
                pw.Header(
                  level: 0,
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text(
                        'Espelho do pedido ${getTypeOrder(mirror.tipoPedido ?? 0)}',
                        style: pw.TextStyle(
                          fontSize: 16,
                          color: const PdfColor.fromInt(0x00497A),
                          fontWeight: pw.FontWeight.bold,
                        ),
                      ),
                      pw.Image(imgLogo, width: 160),
                    ],
                  ),
                ),

                // Nova linha com CNPJ / RAZÃO
                PdvWidget.setInfo(
                  "CNPJ / RAZÃO",
                  "${mirror.cnpj} - ${mirror.razaoSocial}",
                ),
                pw.SizedBox(height: 20),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    PdvWidget.setInfo(
                      "DATA",
                      mirror.data!.formatDate(
                        formatType: DateFormatType.ddMMyyyy,
                        applyTimezone: false,
                      ),
                    ),
                    PdvWidget.setInfo(
                      "HORA",
                      mirror.data!.formatDate(
                        formatType: DateFormatType.hHmmss,
                        applyTimezone: false,
                      ),
                    ),
                    PdvWidget.setInfo("ORIGEM", mirror.origem ?? ""),
                    PdvWidget.setInfo(
                      "LOGIN",
                      mirror.pedidos?.first.nomeUsuario ?? "",
                    ),
                  ],
                ),
                pw.SizedBox(height: 20),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    PdvWidget.setInfo(
                      "CLASSIFICAÇÃO",
                      mirror.classificacao ?? "",
                    ),
                    PdvWidget.setInfo(
                      "PEDIDO ORIGINAL",
                      mirror.pedidoOriginal?.toString() ?? "-",
                    ),
                    PdvWidget.setInfo("STATUS", mirror.status ?? ""),
                    PdvWidget.setInfo("FATURAMENTO", mirror.faturamento ?? ""),
                  ],
                ),
                pw.SizedBox(height: 20),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.start,
                  children: [
                    PdvWidget.setInfo(
                      "PEDIDO MATRIZ",
                      mirror.idPedido!.toString().padLeft(9, '0'),
                    ),
                    pw.SizedBox(width: 60),
                    PdvWidget.setInfo(
                      "NÚMERO DO PEDIDO CLIENTE",
                      mirror.numeroPedidoCliente ?? "",
                    ),
                  ],
                ),

                pw.SizedBox(height: 20),
                pw.Text(
                  "DISTRIBUIDOR SELECIONADO",
                  style: pw.TextStyle(
                    fontWeight: pw.FontWeight.bold,
                    color: const PdfColor.fromInt(0x00497A),
                  ),
                ),
                pw.Column(
                  mainAxisSize: pw.MainAxisSize.min,
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children:
                      mirror.distribuidores!.map((e) {
                        return pw.Text(
                          e.razaoSocial ?? "",
                          style: pw.TextStyle(
                            fontWeight: pw.FontWeight.normal,
                            color: PdfColors.black,
                          ),
                        );
                      }).toList(),
                ),

                if (mirror.observacao != null && mirror.observacao!.isNotEmpty)
                  pw.SizedBox(height: 20),
                if (mirror.observacao != null && mirror.observacao!.isNotEmpty)
                  PdvWidget.setInfo("OBSERVAÇÕES", mirror.observacao!),

                pw.SizedBox(height: 20),
                pw.Column(
                  mainAxisSize: pw.MainAxisSize.min,
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children:
                      mirror.pedidos!.expand((e) {
                        return [
                          pw.Text(
                            "PEDIDO ID ${e.idPedido!}",
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              color: const PdfColor.fromInt(0x00497A),
                            ),
                          ),
                          pw.Text(
                            getDistributor(
                                  e.idDistribuidor!,
                                  mirror,
                                )?.razaoSocial ??
                                "",
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.normal,
                              color: PdfColors.black,
                            ),
                          ),
                          pw.Text(
                            "FORMA DE PAGAMENTO: ${e.descricaoPrazoPagamento ?? mirror.descricaoPrazoPagamento}",
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.normal,
                              color: PdfColors.black,
                            ),
                          ),
                          pw.SizedBox(height: 10),
                          pw.Divider(
                            color: const PdfColor.fromInt(0x00497A),
                            thickness: 0.2,
                          ),
                          pw.SizedBox(height: 10),
                          pw.Text(
                            "ITENS DO PEDIDO",
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              color: const PdfColor.fromInt(0x00497A),
                            ),
                          ),
                          pw.SizedBox(height: 10),
                          //vai o header e body
                          //Aqui será o Header da table
                          pw.Row(
                            children: [
                              pw.SizedBox(
                                width: pageFormat.availableWidth * 0.13,
                                child: PdvWidget.setTableHeader("EAN"),
                              ),
                              pw.SizedBox(
                                width: pageFormat.availableWidth * 0.2,
                                child: PdvWidget.setTableHeader("Produto"),
                              ),
                              pw.SizedBox(
                                width: pageFormat.availableWidth * 0.08,
                                child: pw.Center(
                                  child: PdvWidget.setTableHeader(
                                    "Qtde Solicitada",
                                    textAlign: pw.TextAlign.center,
                                  ),
                                ),
                              ),
                              pw.SizedBox(
                                width: pageFormat.availableWidth * 0.08,
                                child: pw.Center(
                                  child: PdvWidget.setTableHeader(
                                    "Qtde Respondida",
                                    textAlign: pw.TextAlign.center,
                                  ),
                                ),
                              ),
                              pw.SizedBox(
                                width: pageFormat.availableWidth * 0.08,
                                child: pw.Center(
                                  child: PdvWidget.setTableHeader(
                                    "Qtde Faturada",
                                    textAlign: pw.TextAlign.center,
                                  ),
                                ),
                              ),
                              pw.SizedBox(
                                width: pageFormat.availableWidth * 0.08,
                                child: pw.Center(
                                  child: PdvWidget.setTableHeader(
                                    "Desconto Solicitado",
                                    textAlign: pw.TextAlign.center,
                                  ),
                                ),
                              ),
                              pw.SizedBox(
                                width: pageFormat.availableWidth * 0.08,
                                child: pw.Center(
                                  child: PdvWidget.setTableHeader(
                                    "Desconto Faturado",
                                    textAlign: pw.TextAlign.center,
                                  ),
                                ),
                              ),
                              pw.SizedBox(
                                width: pageFormat.availableWidth * 0.07,
                                child: pw.Center(
                                  child: PdvWidget.setTableHeader(
                                    "Preço",
                                    textAlign: pw.TextAlign.center,
                                  ),
                                ),
                              ),
                              pw.SizedBox(
                                width: pageFormat.availableWidth * 0.1,
                                child: pw.Center(
                                  child: PdvWidget.setTableHeader(
                                    "Retorno",
                                    textAlign: pw.TextAlign.center,
                                  ),
                                ),
                              ),
                              pw.SizedBox(
                                width: pageFormat.availableWidth * 0.1,
                                child: pw.Center(
                                  child: PdvWidget.setTableHeader(
                                    "Motivo do Não Atendimento",
                                    textAlign: pw.TextAlign.center,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          pw.SizedBox(height: 5),
                          pw.Divider(
                            color: const PdfColor.fromInt(0x00497A),
                            thickness: 0.2,
                          ),
                          pw.SizedBox(height: 5),
                          //Aqui será o Body da table
                          pw.Column(
                            children: [
                              ...e.itens!.map(
                                (e) => pw.Row(
                                  children: [
                                    pw.SizedBox(
                                      width: pageFormat.availableWidth * 0.13,
                                      child: PdvWidget.setTableBody(
                                        e.eAN ?? "",
                                      ),
                                    ),
                                    pw.SizedBox(
                                      width: pageFormat.availableWidth * 0.2,
                                      child: PdvWidget.setTableBody(
                                        e.apresentacao ?? "",
                                      ),
                                    ),
                                    pw.SizedBox(
                                      width: pageFormat.availableWidth * 0.08,
                                      child: pw.Center(
                                        child: PdvWidget.setTableBody(
                                          (e.qtdeProduto ?? 0).toString(),
                                          textAlign: pw.TextAlign.center,
                                        ),
                                      ),
                                    ),
                                    pw.SizedBox(
                                      width: pageFormat.availableWidth * 0.08,
                                      child: pw.Center(
                                        child: PdvWidget.setTableBody(
                                          (e.qtdeAtendida ?? 0).toString(),
                                          textAlign: pw.TextAlign.center,
                                        ),
                                      ),
                                    ),
                                    pw.SizedBox(
                                      width: pageFormat.availableWidth * 0.08,
                                      child: pw.Center(
                                        child: PdvWidget.setTableBody(
                                          (e.qtdeFaturada ?? 0).toString(),
                                          textAlign: pw.TextAlign.center,
                                        ),
                                      ),
                                    ),
                                    pw.SizedBox(
                                      width: pageFormat.availableWidth * 0.08,
                                      child: pw.Center(
                                        child: PdvWidget.setTableBody(
                                          e.desconto!.formatPercent(),
                                          textAlign: pw.TextAlign.center,
                                        ),
                                      ),
                                    ),
                                    pw.SizedBox(
                                      width: pageFormat.availableWidth * 0.08,
                                      child: pw.Center(
                                        child: PdvWidget.setTableBody(
                                          e.descontoFaturado?.toString() ?? "0",
                                          textAlign: pw.TextAlign.center,
                                        ),
                                      ),
                                    ),
                                    pw.SizedBox(
                                      width: pageFormat.availableWidth * 0.07,
                                      child: pw.Center(
                                        child: PdvWidget.setTableBody(
                                          e.valorUnitario!.formatReal(),
                                          textAlign: pw.TextAlign.center,
                                        ),
                                      ),
                                    ),
                                    pw.SizedBox(
                                      width: pageFormat.availableWidth * 0.10,
                                      child: pw.Center(
                                        child: PdvWidget.setTableBody(
                                          e.retornoItem ?? "-",
                                          textAlign: pw.TextAlign.center,
                                        ),
                                      ),
                                    ),
                                    pw.SizedBox(
                                      width: pageFormat.availableWidth * 0.10,
                                      child: pw.Center(
                                        child: PdvWidget.setTableBody(
                                          e.motivoNaoAtendimento ?? "-",
                                          textAlign: pw.TextAlign.center,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),

                          //fim da body
                          pw.SizedBox(height: 10),
                          pw.Divider(
                            color: const PdfColor.fromInt(0x00497A),
                            thickness: 0.2,
                          ),
                          pw.SizedBox(height: 10),
                          pw.Text(
                            "TOTAIS DO PEDIDO",
                            style: pw.TextStyle(
                              fontWeight: pw.FontWeight.bold,
                              color: const PdfColor.fromInt(0x00497A),
                            ),
                          ),
                          pw.SizedBox(height: 20),

                          pw.Column(
                            mainAxisSize: pw.MainAxisSize.min,
                            children: [
                              pw.Row(
                                mainAxisAlignment:
                                    pw.MainAxisAlignment.spaceBetween,
                                children: [
                                  pw.Flexible(
                                    child: PdvWidget.setInfoRow(
                                      "TOTAL DE UNIDADES",
                                      (e.totalUnidades?.formatNumber() ?? "0")
                                          .toString(),
                                    ),
                                  ),
                                  pw.SizedBox(width: 20),
                                  pw.Flexible(
                                    child: PdvWidget.setInfoRow(
                                      "TOTAL BRUTO R\$",
                                      e.totalBruto!.formatReal().replaceAll(
                                        "R\$",
                                        "",
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          pw.Column(
                            mainAxisSize: pw.MainAxisSize.min,
                            children: [
                              pw.Row(
                                mainAxisAlignment:
                                    pw.MainAxisAlignment.spaceBetween,
                                children: [
                                  pw.Flexible(
                                    child: PdvWidget.setInfoRow(
                                      "TOTAL DE APRESENTAÇÕES",
                                      (e.quantidadeApresentacao
                                                  ?.formatNumber() ??
                                              "0")
                                          .toString(),
                                    ),
                                  ),
                                  pw.SizedBox(width: 20),
                                  pw.Flexible(
                                    child: PdvWidget.setInfoRow(
                                      "TOTAL LIQUIDO R\$",
                                      e.totalLiquido!.formatReal().replaceAll(
                                        "R\$",
                                        "",
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),

                          pw.SizedBox(height: 50),
                        ];
                      }).toList(),
                ),
              ],
        ),
      );

      subAction.reportEvent('Gerando PDF espelho do pedido');
      // Salvar o arquivo PDF
      final output = await getTemporaryDirectory();
      final file = File("${output.path}/order_$orderNumber.pdf");
      await file.writeAsBytes(await pdf.save());
      subAction.reportEvent('PDF Gerado, abrindo arquivo');
      await OpenFile.open(file.path);
    } catch (e, stack) {
      subAction.reportZoneStacktrace(e, stack);
      SnackbarCustom.snackbarError(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }
  }

  _setupDates() {
    if (startDate == null) {
      startDate = findMonday(
        DateTime.now(),
      ).copyWith(hour: 0, minute: 0, second: 0, millisecond: 0, microsecond: 0);
      startDateStr = startDate!.formatDate(formatType: DateFormatType.ddMMyyyy);
    }
    if (endDate == null) {
      endDate = findFriday(
        DateTime.now(),
      ).copyWith(hour: 0, minute: 0, second: 0, millisecond: 0, microsecond: 0);
      endDateStr = endDate!.formatDate(formatType: DateFormatType.ddMMyyyy);
    }
  }

  DateTime findMonday(DateTime date) {
    int daysToMonday = date.weekday - DateTime.monday; // Monday is 1
    return date.subtract(Duration(days: daysToMonday));
  }

  DateTime findFriday(DateTime date) {
    int daysToFriday = DateTime.friday - date.weekday; // Friday is 5
    return date.add(Duration(days: daysToFriday));
  }

  void setStartDate(DateTime? value) {
    startDate = value;
    startDateStr =
        value != null
            ? value.formatDate(formatType: DateFormatType.ddMMyyyy)
            : "__/__/____";
    update();
  }

  void setEndDate(DateTime? value) {
    endDate = value;
    endDateStr =
        value != null
            ? value.formatDate(formatType: DateFormatType.ddMMyyyy)
            : "__/__/____";
    update();
  }

  void setPdvText(String? v) {
    pdvText = v;
    if (v == null || v.isEmpty) {
      pdvText = null;
    }
    if (v != null && v.length >= 3) {
      hasPdvList = true;

      // Remove formatação do CNPJ para comparação
      String cleanInput = v.replaceAll(RegExp(r'[.\/-]'), '');

      // Verifica se a entrada contém apenas números ou caracteres de formatação
      bool isNumericOrFormatted = RegExp(r'^[0-9.\/-]+$').hasMatch(v);

      storesFull =
          storeRoutesController.storeListFull.where((e) {
            if (isNumericOrFormatted) {
              // Se for numérico ou formatado, busca no CNPJ
              String cleanCNPJ = e.cNPJ!.replaceAll(RegExp(r'[.\/-]'), '');
              return cleanCNPJ.contains(cleanInput);
            } else {
              // Se contiver letras, busca na razão social
              return e.razaoSocial!.toLowerCase().contains(v.toLowerCase());
            }
          }).toList();
    } else {
      hasPdvList = false;
      storesFull = storeRoutesController.storeListFull;
    }
    update();
  }

  void setSelectedUser(String? v) {
    selectedUser = v;
    update();
  }

  void setPdvChoice(StoresModel? storesModel) {
    pdvChoice = storesModel;
    pdvText = storesModel?.cNPJ ?? "";
    pdvController.text = storesModel?.cNPJ ?? "";
    hasPdvList = false;
    storesFull = storeRoutesController.storeListFull;
    update();
  }

  void openMenuOrders(
      String orderId, bool canEdit, bool isOnline, bool canIncludeFile) {
    showModalBottomSheet(
      context: Get.context!,
      builder: (BuildContext bc) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              if (canEdit)
                ListTile(
                  leading: const Icon(Icons.edit),
                  title: const Text('Corrigir'),
                  onTap: () async {
                    await changeOrder(orderId, isOnline);
                  },
                ),
              if (canIncludeFile)
                ListTile(
                    leading: const Icon(Icons.file_upload),
                    title: const Text('Incluir Arquivo'),
                    onTap: () async {
                      Get.back();
                      final order = ordersList
                          .firstWhereOrNull((e) => e.transactionKey == orderId);
                      Get.toNamed(RoutesPath.reportOrdersUpload,
                          arguments: {'order': order})?.then((value) async {
                        await updateListOrdersToSync();
                      });
                    }),
              ListTile(
                  leading: const Icon(Icons.delete),
                  title: const Text('Excluir'),
                  onTap: () async {
                    ordersList.removeWhere(
                        (element) => element.transactionKey == orderId);
                    await dbContext.deleteByKey(
                      hashCode: orderId,
                      workspaceId: appController.workspace?.workspaceId,
                      //storeId: orderRemove.storeData?.idPdv,
                      userId: appController.userLogged!.userId,
                      key: DatabaseModels.syncronization,
                    );
                    update();
                    Get.back();
                  }),
            ],
          ),
        );
      },
    );
  }

  Future<void> loadMoreData() async {
    if (request != null) {
      request!.pagina = request!.pagina! + 1;
    }

    await searchOrders();
  }

  void clearPdvText() {
    pdvText = null;
    pdvController.text = "";
    update();
  }

  void setNumberOrder(String? v) {
    numberOrder = v;
  }

  void clearNumberOrder() {
    numberOrder = null;
    numberOrderController.text = "";
    update();
  }

  void clearSelectedUser() {
    selectedUser = null;
    userController.text = "";
    update();
  }

  void clearProductTypeSelection() {
    selectedProductTypes.clear();
    productTypeController.text = "";
    update();
  }

  void setIsProgress(bool state) {
    isProgress = state;
    update();
  }

  void setIsLoadMode(bool state) {
    isLoadMore = state;
    update();
  }

  String? _validateFiltersOnSearchOrders() {
    if (numberOrder == null &&
        startDate == null &&
        endDate == null &&
        pdvText == null &&
        selectedProductTypes.isEmpty &&
        selectedUser == null) {
      return 'Preencha pelo menos um campo para realizar a busca!';
    }
    if (numberOrder == null) {
      if (startDate == null || endDate == null) {
        return 'Datas de início e fim devem ser informadas!';
      } else if (startDate!.isAfter(endDate!)) {
        return 'A data final não pode ser inferior a data inicial.';
      }
    }

    return null;
  }

  String getStoreName(String cnpj) {
    if (storesAll.any((element) => element.cNPJ == cnpj)) {
      final storeName =
          storesAll.where((element) => element.cNPJ == cnpj).first;
      return storeName.razaoSocial ?? "Não encontrado";
    } else {
      return "Não encontrado";
    }
  }

  void openOrdersDetail(ReportOrdersListPedidos item) {
    showModalBottomSheet(
      context: Get.context!,
      builder: (BuildContext bc) {
        return SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(
                  FontAwesomeIcons.solidEye,
                  color: Colors.black,
                ),
                title: const Text('Detalhe do pedido'),
                onTap: () async {
                  selected = item;
                  Get.back();
                  Get.toNamed(RoutesPath.reportOrdersDetail);
                },
              ),
              ListTile(
                leading: const Icon(
                  FontAwesomeIcons.clipboard,
                  color: Colors.black,
                ),
                title: const Text('Espelho do pedido'),
                onTap: () async {
                  selected = item;
                  await getPdfMirror(item.nrPedido!);
                  Get.back();
                  // if (item.itens != null && item.itens!.length >= 40) {
                  //   Get.back();
                  //   await openModalErrorReportOrderItemLengt();
                  // } else {
                  //   await getPdfMirror(item.nrPedido!);
                  //   Get.back();
                  // }
                },
              ),
              ListTile(
                leading: const Icon(
                  FontAwesomeIcons.download,
                  color: Colors.black,
                ),
                title: const Text('Baixar Anexo'),
                onTap: () async {
                  Get.back();
                  await downloadAttachment(int.parse(item.nrPedido!));
                },
              ),
            ],
          ),
        );
      },
    );
  }

  Future<void> openModalErrorReportOrderItemLengt() async {
    await showDialog(
      context: Get.context!,
      barrierDismissible: false,
      builder:
          ((context) => AlertDialog(
            title: const Text("Atenção"),
            content: const Text(
              "Este pedido possui muitos itens, o aplicativo não permite extrair Espelho de Pedidos extensos. Por favor, extraia pelo Portal Web!",
            ),
            actions: [
              TextButton(
                child: const Text('Ok'),
                onPressed: () {
                  Get.back();
                },
              ),
            ],
          )),
    );
  }

  String getLogoPharmalinkBase64() {
    return "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAK8AAAAgCAYAAABkdCh4AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAAZdEVYdFNvZnR3YXJlAHBhaW50Lm5ldCA0LjAuMTCtCgrAAAAda0lEQVR4Xu2bCXhU1dnHE6sCWaBo1br0s2q1rbZ2sbbV+okbGjN37p1AImRmkihotCruu5bY1gVxg7YuiJLcGRYBEReEzExCxB3FT6u2St1bdwtuKLhgvt//3HOTSUhC4Ov3dNH3ec5zJ2d5z3ve8553OycF64f2wgGRGTuXRtJuqdvUWbxUtDiSPqDUTX97i7L0YPWzAzqgqOL6bUudVLQk0lRR4vj7FQxrHGibvoQNAfhW4t60e5Hj1xRHUteVRFOPFUf9FcXR1IfFjv8O3z/D35nUjWNf9h186NQtCgoaNrGjv8Cw15TNSpxUA4xZSXkvv8CwN2HeM/y+ZSBMK6ia8xU7ygCCPYa2dykfgGN2iTPla7bpS+gXtBcOGTFj55Kof7n4DL8/RnDbVYqd1Efw9A3KHymLaWul3+P8fopyZ7EzfVSgVL7IUDZ5AIxphCFrLeM+h0m2BIxUG8x9SFrYjiqQIKMRfh32KXX8CV9q3g0A+Ffk+YfB+8fh3ycBH/3PEdp3qcsWR5pOLnL9HxWVN3596CFThmzp3lZaHJ25TXG0aU/KRPq8gNKYNqjshh0sxi8eDInMGArT2kIh5fsorsANCOZcmPMX/v40YGyqvSiSurKgcvYgjRNDS6LpWcE4fy1ttQbhRsO6bskGQmFgGf7PeDYSNnDeqjmbw7fx8FgW7jPKiyiAG4ucpsNDHvcFRZGmvVAot8L/BUXOtB//89b9T4QSN707gro8EFB/DSf/hG2Gp4rlV5VE0v8Ng5bRZrQw/Z7/avn0HTVugNf4TRj+bCC8qVfli6ER9ijyZpSjUcoNQ8FjJukFZPa0CYyPyd8rivrO4FjTLkYIh7VtWhrzdzPaB1wdJrLyykGieXCk6adDoqmdtGmlh6W/PTiarobWUQXOlKJw7GAnvffgaOpbwlfq3vjtwUbTpWLQPUwazeCzUFyR2rq0PLWv2hlTVuxN/75cKtvcI+jgB+tOH8Hak/DnoKLD0tuaxmSqmAP9A9FvLBbCaurzYIjX+FX4dxbjUgjuPuK7beoXFEenblPiNDWxb4+WuNP3t9VfHChy0ofCvLcC4fRXFUWnO+Ep3h2GszEXWo2s9vdC12GIhC7qG63MeAKK1NWUpxCgD9SPr8yaL4FeVyu0FypAKYn408DxEmWVDg7j8Z39ZRLCQZWzt2f8QrXzfbrEbTKbYwQsmtKBej2Ys+lofv8PZTX9bhlaNWdI4EemmimvlEb9+eA8n0P5GH+/S/mQsoKxCwa7/k8KhjVsKgFjnbfQ7zVDi+O/D66/8D1HFsaQnA8y+WhI1n2rGeP4H4FzNXSv4JsrchsjReWpMurln77I3PSb0XM8oAPCgdxIzVk4KJr+OXP8FbofDXj9BYLiSPpkFh/6XK8XedN/aJsKpAmom0y7Fd7UO6WH+7uZzXNTtbQFfrGT+piNyvOZzTcoTio3xJv1TYvSbFYRmo2xCj7CMZo7f9wrEkSKhEF/P1/kNUIXQu80HWuCmajxwwkmU2+bPjK90CrNWxJt/AVtbKgZy6GwgZCTPwe/TSDUdCx/cxg62jr6MD+HsOnULlpTB9pJnUT/18N+pnRZS+p16LonmBc/FiHf0OAqmRlePOY2t9T+2SsYt8/xZ1I+Yc1TsDhb2qb/bDCalQWHTIfJSzqYjOnlJGMOUy92tDv+gzK322AS2ZQ/5G04xX+NvvKVL6Vo4xAmg/Nd4w+j4ZTesUHKC3k435HvzHcC9X6HUBhhsPgVXWPWFVwWu/5lAW4jfBIYhDj1HN9m3IkKQ7fjxyV4ZqwZ7xO1+zOpvwx67qbO+vFYDif1vv0+Qvkd7dfQT4Ifru3R0siMXQ1PgOJyuQd+cGCC4PZ5xkwN6E/dRd1q0xYKMwJM/99uSDBbv6x+s5qsO74m6/12vQKMImH9J4gnogvXq24jtfi/FxQTvWK6M3YjtJmYaDbCFIKBwIyGm/gpG3SahFD+MAxb0jku9bLyxNtafxOh35OxiqI1djX+7HhpLONHO/5sMdrgdVJPyMQOrcI012M+3RuJqNNHUB9qTVv8P+yAaZWPaMx7Z71M/KTiMuYrm72V1ZC4JE0X0Ba4NE7q7xy4sQWM1RyyHNQ90okjRYTvLzE+7riFA2RtaJdmXWPG41KZIAoYXNa0C3x5Ohhn+LJUvneBc0eRLIq0Xkm06bxwrBnP4YDm6oKG/udla7LOsETGezWZ81YmMu5plbMr+wrgCqVkAp4aeu8ZaOOS/2jA3/0xm4HPGW6kBEqb0iGwQQncipsHHjrtGxonv5cNt8Ga/wHCeWb+5kgYA+E32nMNDP21Tcntx99/C/ByMKQpFZzlAweA/hfRJxSAT8E1TtpkcGzWLuB4wtZr7ukKWuzIAOTqBEGMWQNjpw2JXDvUthbsWNc4ELomhThYx4oiZzrC2amtJMj0edWOX6mLmIKG9k1KnfSZ1EGX4dGTCsjskE7ggNDn5rz534bOYbZ1vSBNW5NzG5M59zOEtz2Z9Vbyvai6zeklh96O8KaTzBEIr/xv1z9OSsR2+M8ENojI25o5FQmuBM4UmCGfDe1b7KQnoPXwW7XB7YXW9L8ZjPMfNxF9HhgNiam3G/ghv0/VpqIhTqSO4Mxou7nBTdG6IGGSxhN+xkrzHap6BW3UGbeCevxh38sXOsGW7sztGGs0K4LzYUm5PyK/z7ZoScbh8oTCm5onem2zgSADErov/ku6oBnizUfrE/wFgsuB9E/vTZsqc8JYe/j8J6Hhu7ZpvTC6JbpnIuc9awTXlkTOXUX5fU0utq4/q9s5LJvZs3BN0dSCkkos0YZB4bC2YZvWT6nfTF9bxxIbNgnrq+ZUdVU0GwHC0dDWsKkp7Rt7S4j5l1CxaBOsIUzvKs/I70nUXUFpKI2mjlRaqsspRgOVRKePpb9u1SToc7pvvrQh7bgVJlhZWRJJV0rD8vdVFJnzteC+yLgKPQB+5YE6NGYzcC2KI6nviV6TWQiCNQndg8GB6grShvR5w479Y4nT+B3bZEABDgFjzrRDR4mbusA2WSAojPojKDpk6rNUZnigN+0bzGnWZA5ukJrq0bdkTqXjQjrv6jXT0B3aCwprctG6RNZ7P194AwH2VqGFJ8bvjXdYEYFiAXh8u6EroFd7+WyRO/NHtku/gIOxWyIXvRA/+yrKKVXLqoa0Q0+81d2fg3M5NF2FC1MhobNDNhicO5wi8Bwd4ItdFs+4+2/cgcC/RJM1wWR7Yv22fjFZQV7E/y3jpJU/Rggv7Z5UL4n534WBfzJ4Hf9vg2L4he2YNyd9LQJlNXuqoWfTZsxgNZtOIGc2/zYJXEFdm8y90nFhIDRz3bxo+ybFbmo07UqHSfPO7Z5rBtfObHbot74JvphtCsAcEv8c2sNDfbuCWGVMoOt+6qR5XyuNNv7cjlgH0Lwab31uf0oQrK4fpInY0KsR1E+6C68pWe8NNv9g211QKJ+bOToC4GBO8Q6L090l6wNwU2qZ90Pm+Bwhba5cWLmVMh7MN5m6T2lbmyCIzNfKGwp1bVVfB89DrOVzvu8nWmPVG6V9zYl1/Ic6F+1PlmDa5l5hq6o5JWzuLCtEEpJfdhdCuRUIiInIJcQmvQbA1N9I4JkLjexP6ykpb1I/0VSKfkFQF01fJfwmSJQfHWiYwI/uvjk/v3IQdF1h+6ylz6VdH7Dg8ugCojO99oSxLPmAGab9ejv/p8pumPReRXpbcC+2tK8Cz5HCZ0d1gKwQYx80+KMm03CKbVovKMvAps6TAHUXXOrXIFQTxtyXl30I/Ouzodcc1rDA5w+K3NRR/fV766fstVk8517APGsQ1k+lZeva6gbiwmwDLTkj0Dn3XQQ8sfGmHpdoUXQPcLxo1/RqstXb1zZtGOjGCcF6xiwYQYTJ4+QS2OZeoaTypq0Y02I2UZE8wkB1l03Ugx36GJ8PLZQNb50w/6MDrWAE5wXd4HW5xSJYK4r4dcJr+iAkIV0KEqnT5YR8urdKIk2V3YVHFxTgXWT6gKM02pS0TQEg7NSfwHptOstvLY3d2sWPLB6e2pq2cH3v87ve8AXrwrw3BYdWh8Nv26IivUMnz9oLja8fTV8eCL6h8++hv94fqMdUJ7NuWyiwHUXCk3WXJpujXWILvWuAP7IGHYIbFGVhmo7twts+QAcino3eZDRs1ltdk/OOl4Y9vq2qxLox51FOibeOUBajR1dJLo/G9CXc1VnvMNbxd60p0eIuT7S6nW9l1oWe5xGjYexwFtpn8NMTmExDaHYd/zllLGyTBbkH/mXgs7dvTdcUEOGrRflS6nTTZXO0/mPMfapu+czTSuMW+CbKD4r/N1yUQxQYFbvyg20GQD7dOvNa/PJzgz7P6ObMNgVQv2yzUoJP+nwiAWPuG8x1ch4ENBJkaX7HfyM4nAFfRCNjgoMl+tHE1B0lAdVhov9M2jq1IAe0ezDbF1Q2H7oFG7uki+Bqo2XOc+5J0sy2a+C+BVmZzoDbFlmGEid9dH81r8w5h+Yeq2FXIGCuhDCZqdg6mYkexKEpq87F9ilbWDZAQhrPjdg5nokdnsxFyxJtFTskM5HvxXPeKOgcl8x4RyZbowdpLRZ9B7CGUzkErMUEoa1jmys7+tS2RLY3+LJuRPMnstHe3LKGTcR0Fhn4hk5qeY9pnx6g2J1+IExbGYzzH9RbYNtkQLleGIf2Qzs5/uelrn+mbTJmjrmUQ+1guBgNvrf5rjRCZetteUJvFALh7dDaylTcP+Cw7sEaLoHTdDhzmiwIOO8d5N64nW00MPQQaebUHPoom/JRkZM6o3vQWKrrVpvOM4c0P1NQtnAA9RON4Br6jAuh56NvW9rs5YcpOqCLg6vf/kHlA5WD2NS7ugsvm76stjW2i+2Ga6OLmOmj7JzhfB3F0AS/+uvzxnORnZn3aStUz8Uz3g8lpHITELaXA23pLlS6rn7ZXpvR7yIE/W3KGwjsLMY8SXmX36toe4/+r/O9oa6tvMv7Edqn0W+t5olnvZvkrqi+rs37ak1W6UHvLdpXgPclArpqM2gdMOazKfA/ERgJw6BI0/a2tU9AE41lUz80gub4s7ck8LNNBuQiwFTdeNGO2Y2kqmyTAaWqYO7pRsisec0rSquh2UySX4LcMsiduZ20DH31PuF9S3PaBHH5oCyIkz6WtehJoYLJRj0jtK0GBlVgZuWPas3R1ApzedBlg43VQCh062f4cl93vmhexl7PGmSt5Ftb2gNBZl4FgeIN/m7quv5YsxAUebPxjRSzwSoIj0z5JUpZ2W4FuuVkzuau8+cVWSiTW+7f3NWLnWHMpUsRafmHEgsrdlCmASE6g7o1CO5naNRJyUyyWC4GgnULNElLf0L/T4zGzrqrg772wGXdz+gzOQzwpLX5e3Fnu3eG1qQDm2yJNdD/I9u2Mp51j4MXvcRfbFhpeWNEaSL8zl+xSfHu5rM3CHKt/q80NvDnujJIGYtSp+kMAi3wpk7q8b0pwhi8xkpdCpNn0e82vn6J6x9T7KQPZgPOBn/DIGUCdK2q26tAq57PxjSYebv7czBCL7PCPuA+qHuUb4JB1z9OtNH3zOBVWr5v1V44yPV/Qvt5JU7jeIT7iJ74opyv/tuEeXSdrKej8+HhFGiugjdKs/2KNZxb4uHTbwA0NBRsguk81wpCILw596+JFq8rnqqGzU2uPeo/3EVobYGW+wd6s8yFUn8gmY390mpNDot7hzShhA2hnSrB1QEK/eBkZvjWCNrSDvqy7jt8Zyaz0RMRyLNpe4K/TcAJ7c8pBac50LTfp/2ZYJz7WbzFPXDsA5VbsLZLmDt0JVYx13lV+NqGsF5BG6vsgko/UzkGpKnCcT2aJYRZpljtRsB6O/3Ul00eIO0ooTKZB0NH3vh8/GrDZ+19XsDQ1tA/2oy70ANtBocZ3zdfpAk5WArSjBWQkAdjg/GitR8BcHcg6j+EDXwr2GQjHEsTC8vWfdQDbvMarkvGiCJfXjl0eGt7rhcSudhlCNanCM9ayoRxfykbULdAaS33bkvHmngu5smVSLQ4P4a+lwNhU0ovdoXciQYE22RLMtEkAiyBFu24GxUK6AtwC6roH+avVx7Z7H0DDT4+rAsE1z3fWeb0S4l+Cf+CEGyqd59McSAA3qK6tmG9POppLyx1ZuyD0BIEWxeCIHGQk97bdlgvyDwzz1wJLhrx45pWr4ZqhNTdHWFaboQt6z5fk3NN8EsgVU3de6Y+5z0ljar6EDQOPH9Vuw5hIhNTtgX/OXpuuCbwtSUy3rGBcJtDsCqZcS+WWxJg6QPCKz/7JzwoKNT1n762Zl3oq60/gEm0c66DR/6VAoH29q6aUHUbdwPz7wvGNHdG5fIr7xinKL83YC+lgRHeDFp4lVyiDdG6owgEmeNxI1QKtnLeAaqvyXrl/L3CCBe+qvxg7WGyxf0Nf+sSBUF0pyofbBBZkItD+xtmXM57oSYb+27wXsOb1im8BGQKzII+wjWpMi/z0CckWyt+wOAxukHR3/HW8h0htqZH8wQov0jbDhsrwDos8Uz0F5zKiu6LFeBjfRN6TrN5RAOJRRXbsrBxSsvYqn8LkPCNzro/MWmljQSZbLTTPWZzs969CmpsU29QuHnMvJa7uDQyreP5Zn8An3Q/eP+mFaQnq7ORXZUF0H6gGc1NH78bJYDmxi3nzlcd9H2k/bFoOiDZ4o1l3AemT85dJtprF8Z24e/7DC6CUdZkBFcFN+m12pbYT+3wvqGqveorSP4ZxiQgxBJI+TP4HzdKePX8Tg9A6uV70Gau9Fo8P5FxDjUawGjJ+s2sKSuseqpqc22YivJ29bmqISYypp98J+FRAMCiJjLnZXLGVTqS2fRLNnsx2l5nXk9VJh2TiSlwWX4UZkimTbiFR5pYgY3mFWPiCyJDQ0GR36W/RX8YrYqWKmhSUT+r/QtEg/qFY8M1VeUOGSK8+lu4wsOmebU2aYgwxaM+4pfJz7Kxwi2flU2Z1+UmbCMAnuzL5r+ET/licrH7M1u9PkC59ODH9wH4qHUIoQmYkItbxScJKry/NhAwArZc7EytX0qGvx8L+iJ0ze6BFk0IuAfeJAR8rdWyc7XPiZyzN/gCPznrvoXwHk3Jf3x0XahI+wRzglq8JnyMmSJKm5PMRM9StKj7bCY+h3I5xDUk26LfCnwV93nzzbknxe+NDOWbqGnxTteEOmnxTGwvzMIoyh9ECIQdrAS3HnqA91LwnQaOGRwSR2OFy5vvmcc8EjhwnMW4Z+g3RgKghDh1D/P3fGlemHs8OCeD4xoW7yp/iA92DkHA+UF99Jf15tFH7Aj+vkp12hQdNmkWGHmlfCrGnq9TLlMGHReD//dakw6tAgrjd6k+qwjYHcMaJycz3lkm8m6JDse/uxRcE6EtJr6xvjrhZMwV9L+8dmF0T9ZxO2VJYlGft0frBQlLPOsew9rfokzrh/btEcQDmXzhs1UdYPa+Bd4Ezy8lcBOl3BJtZTvAa3PTx/rkl45U/0TWOZi/XzH1CLGso0FkwQr9bUZwlfrKemdrXiklfutpp7T7I8jbTvD4aH7bDIf3erwldrhRen2BCQhy7lJtAETPxsRtp03Ri6Fkq3cG9RNrWyq3N5udQWhaiB7ZEKU2mGxOdbPzHcbfJWEclYvtxvd6xsepu0ORqIkqs+4t8cwIuQnL9ZgaQTsE4m8F99nguE79Qs1rmBu8WrpNwihtyPhLWOAC+l6YbJaLwzdT8T1cm9Npuxk69+X7pA5cTTY6gvZ5AUPck0e3jNwTHPswb0pBhqG5Ff+N8azjj2Ik89wCbSfzezfa7060xg6116O36WaHNT1Kn/Gjm6PDqZtbpzVnvema14zJer5cKfjXDO0TggPm3l3dHPspCmE+LtKRbNp634msD2QdDC+IznVN25MA9gW1d0a2Zy0N0NYsntvqDjAKJuPOC4TKPLsco/qa3MgfsW4Ulqn/85FoTtMfJWGFUnnd+d0DrOrsiF3hp314476jWzjxgf5YepsOQ7ir26q/Zt5NdN4mKq89U1rfouoZEtkKbc7SxBJ8SrSUELMJV6PtpKEepu2aONqGSa5lA4+j7VQJsjZN2hpBraFtBpPdgFYai7YdB6GXYy5PFf7gms+bq9whGvb2qjm7b27SPzn3OXC9iMM/On8Tqu9wvgYd10oQ0cxXSxihQ9r6imRL7MRka+QHzDeetgtgTBYhnaTXSPS/ydy9Z2MjcGuu0jUl85zEWOUar0FrXQOOMxkzTeZfpp75Z5o1Zb0/QeOv1Q49beCPsI5ZrHE/3bfTdutYXAHGHkCfCRy6sdQths7zmP9c+t1oTGHObVW0feTdI3YF552qY+6Fxh37B4GxKC3Mq/xp1j1Km96bEKu+DosWWEzxwn0AumzWwr2/yy0dYCwQBzVo917Weo0b1+JF+W39UjczumX0NrKI4JpIf9wIbw3lwtAFC0HBGn2se8AXhaFnkOzF75g/vMyYcvycwG00V8nmwY/cEO8D6DmmzwMaz7gn0DktFV3bOmJ/iHhINyZWCz3C5rmJXMXeVmDlzKckcDIRTLBABa1VCXH3iRDqlfebBeFHg75Qb0ElWIyTWR8f5P5wG9COaMIxEHt1fmQpTac5FOTocEjApOX5PRXhr2fcDWjF0+X3MU8O+k+CznFyPQLT7Z4j82pMLFoqSNV4F6mefseDL635qBvJ+Dn4kPVsQFvtougeNa24CmgmuT30m1qTc/6LPgcbGjHTrOlYpXQ4iCdSd31tSxStjnvEAY7noh44b5efHOcAiW5oKqNubj1+s13ePwSM9kKg4P2dlCXw9Xw2Pmp41szhbsNaYEnjcuWwpvBBV72hH4sW9J6m7mT58xalAdZxGH1eUz/al0pzKt4Q76jTC7PPZZHEi6MWlm1Fn/m0SUsqNztSsYdFZWKXQC7cdzQn7UukIHWYqLtTuPiu5rCfrvVoiDQtdN+k+U3Jui/Z4K1nvx0GT2LAWfqtYApi5rG4eVqYTghMmcnk8omn2pP5AIucbHxdiIA5C6ubR+A6eDDIvViESEggrhUhncBG3xPHkWeOiQi/I9MHvivRqKdLOCByXs3iWMe/xXBwqsEzdRTuDN8W5miStmWM3IYDWLACh5l8L2EOOfkjdSiSzU5Mvir0TsKkOslm3cPjDuigGG3inkTQtB3rm6/1sAnPQE+jhBs8Wfr8jr53gesYaQzqrhK+RCaWFIO1YeC9MLnYG6n1Mu/tJpmPCYYf5eA8W7SZTct5v9Laq6lnvieTLRXDWdpGZWZ6A2kke7hOgRbdVCnX+hI0LKe8yLzv0LYa+oJ/HwoEUte382Rtu2c/AnyxMbSHN2u3JBYmBptDa94ZGDy6Gr7ACDQ+PPVPBX29V+zDmY41miA7iIusW4FCg5+jugR53nt6wKO57bACKRFoCIM3+co3a5xt7grmhkQ5Owvy5/QQQwhNVgAhkHYxphatWZtx9wfpSLOZnHRtvkxZbau7v3wq4ZDfKocbgo+ua3O+I4GuA6cEXjjkt4rx+l3NyVLUaiYHkpnKnWR6hYMD8zP5jzB1y8Qi779dAgAJtfBKYDR21CKY0Yb/azIiAW4FmvbO/QjRKk0qZgcuB5q0uWI4Qn57oiV2sgJEzc9BPU6aR9p7LKZY6xIPzIup1hE76ndt68g9QjOtuWHy8cy7jzIdekkl/1+5T2lkpZiq5qAA4N0/0m3oDsqJ18ED8QSBbcQqPszvZxGM51UQsOV8F9M2wcQpCKQd2gWkNWuy5QSuFQldTMjl0TpNxsbsuVsrxaJHOzqgRnkFcVKCb7S7gGmsiTHk0oFT/FKd/iMjHKcLDhvkdR5sHX5ZX5QG+5OEv15fmZo+NYJ8kW5RX2EYXOm3/Qq64oEIEWv/EnQh0P4SFK7zdyeEv7v0MUwNaQrqexwvOkMa9EVAq9gIzLl3HSf6Gh0g0xEw/TrnyMfZI2717eLj5a8h77fo7H7Z8v8BWqsOXtWC8q8reJSVNBZiSXQnHWzWJ9PcNx3QLXoNb7utp6M+D8K63tan+o5xnfg66kx9L9CfPl8okGaWBVGO2OQR8zfoS/gXh4KC/wWbiiBXxzUxPQAAAABJRU5ErkJggg==";
  }

  MirrorOrderDistribuidores? getDistributor(
    int distributorId,
    MirrorOrderResponse mirror,
  ) {
    return mirror.distribuidores!.firstWhereOrNull(
      (element) => element.idDistribuidor == distributorId,
    );
  }

  String getTypeOrder(int id) {
    switch (id) {
      case 2:
        return "Especial";
      case 4:
        return "Rep";
      case 1:
      default:
        return "Padrão";
    }
  }

  String getTypeOrderName(int? id) {
    switch (id) {
      case 2:
        return "Pedido Especial";
      case 4:
        return "Pedido Representante";
      case 1:
        return "Pedido Padrão";
      default:
        return "";
    }
  }

  Future<void> changeOrder(String orderId, bool isOnline) async {
    if (isOnline) {
      bool isConnected = await appController.checkConnectivity();
      if (!isConnected) {
        SnackbarCustom.snackbarError(AppStrings.noInternet);
        return;
      }
      await appController.isValidToken(noMessages: true);
      if (appController.hasErrorRefreshToken == true) {
        SnackbarCustom.snackbarError(AppStrings.tokenExpired);
        Get.offAndToNamed(RoutesPath.login);
        return;
      }
    }
    Get.back();
    Get.toNamed(RoutesPath.orders, arguments: {'edit': orderId})?.then((
      value,
    ) async {
      await updateListOrdersToSync();
    });
  }

  String getOrderTotal(SyncronizationModel data) {
    double total = 0.0;

    if (data.payLoad?.orderInfo != null) {
      for (var order in data.payLoad!.orderInfo!) {
        if (order.payLoad != null) {
          for (var item in order.payLoad!) {
            total = item.totalLiquidoSKU ?? 0.0;
          }
        }
      }
    }

    // Formata o total para o padrão brasileiro (R$ 0,00)
    return NumberFormat.currency(
      locale: 'pt_BR',
      symbol: 'R\$',
      decimalDigits: 2,
    ).format(total);
  }

  Future<void> downloadAttachment(int idPedido) async {
    final loading = PlkLoading();

    loading.show(title: AppStrings.load);
    try {
      ReportOrdersAttachmentRequest request = ReportOrdersAttachmentRequest(
        idPedido: idPedido,
        pedidoEnviadoParaAprovacao: false,
      );

      final response = await reportOrdersApi.downloadAttachment(model: request);

      if (response.error != null) {
        loading.hide();
        SnackbarCustom.snackbarError("Não há anexos associados ao pedido");
        return;
      }
      final fileData = response.data!;
      final bytes = base64Decode(fileData.arquivo!);
      final tempDir = await getTemporaryDirectory();
      final file = File('${tempDir.path}/${fileData.nomeArquivo}');
      await file.writeAsBytes(bytes);
      loading.hide();

      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Anexo do pedido',
        sharePositionOrigin:
            const Rect.fromLTWH(0, 0, 1, 1), // Fallback para casos raros
      );
    } catch (e) {
      loading.hide();
      SnackbarCustom.snackbarError(e.toString());
      rethrow;
    }
  }

  Future<void> getPdfMirror(String orderNumber) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "gerarPdfEspelhoPedido",
    );
    try {
      subAction.reportEvent('RequestAPI informações do espelho do pedido');
      final response = await reportOrdersApi.getMirrorOrderPdf(
        orderNumber: orderNumber,
      );
      if (response.error != null) {
        subAction.reportEvent(
          'RequestAPI informações do espelho do pedido falha, ${response.error!.message!}',
        );
        SnackbarCustom.snackbarError(response.error!.message!);
        return;
      }

      subAction.reportEvent('Gerando PDF espelho do pedido');

      final pdfData = base64Decode(response.data!.filePdf!);

      // Salvar o arquivo PDF
      final output = await getTemporaryDirectory();
      final fileName = response.data!.fileName ?? 'order_$orderNumber.pdf';
      final file = File('${output.path}/$fileName');
      await file.writeAsBytes(pdfData);

      subAction.reportEvent('PDF Gerado, abrindo arquivo');
      await OpenFile.open(file.path);
    } catch (e, stack) {
      subAction.reportZoneStacktrace(e, stack);
      SnackbarCustom.snackbarError(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> loadProductTypes() async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport(
      "loadProductTypes",
    );

    try {
      subAction.reportEvent('Carregando tipos de produtos do banco local');

      productTypes = await TiposProdutosModel().getList();

      if (productTypes.isEmpty && connectivityController.isConnected) {
        subAction.reportEvent('Banco local vazio, buscando da API');
        final result = await reportOrdersApi.getProductTypes();

        if (result.data != null) {
          productTypes = result.data!;

          for (var productType in productTypes) {
            await dbContext
                .withControllerAction(this)
                .addData(
                  key: DatabaseModels.productTypeModel,
                  data: productType,
                  clearCurrentData: false,
                );
          }
        }
      }

      subAction.reportEvent(
        'Tipos de produtos carregados: ${productTypes.length}',
      );
      update();
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
    } finally {
      leaveAction();
    }
  }
}
