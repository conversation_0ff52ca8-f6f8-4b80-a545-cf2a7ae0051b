import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class NotificationModel extends SqfLiteBase<NotificationModel> {
  bool? isShow;
  int? type;
  String? message;
  String? id;

  NotificationModel({this.id, this.isShow, this.type, this.message})
      : super(DatabaseModels.notificationModel);

  NotificationModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.notificationModel) {
    isShow = json['isShow'];
    id = json['id'];
    type = json['type'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['isShow'] = isShow;
    data['type'] = type;
    data['id'] = id;
    data['message'] = message;
    return data;
  }

  Future<List<NotificationModel>> getList(int type) async {
    var list = await getAll<NotificationModel>(
        workspaceId: appController.workspace!.workspaceId!,
        NotificationModel.fromJson);
    return list
            .any((element) => element.isShow == false && element.type == type)
        ? list
            .where((element) => element.isShow == false && element.type == type)
            .toList()
        : [];
  }

  Future<NotificationModel?> getFirstMessage(int type) async {
    var list = await getAll<NotificationModel>(
        workspaceId: appController.workspace!.workspaceId!,
        NotificationModel.fromJson);
    return list
            .any((element) => element.isShow == false && element.type == type)
        ? list
            .where((element) => element.isShow == false && element.type == type)
            .first
        : null;
  }
}
