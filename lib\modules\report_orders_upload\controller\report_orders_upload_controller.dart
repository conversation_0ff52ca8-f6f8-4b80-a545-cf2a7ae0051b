import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/orders_resume/models/file_attachment.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';

class ReportOrdersUploadController extends GetxController {
  File? selectedFile;
  String? fileName;
  String? fileSize;

  List<String> allowedExtensions = [
    'pdf',
    'doc',
    'docx',
    'xls',
    'xlsx',
    'jpg',
    'jpeg',
    'png',
    'txt',
    'csv',
    'webp'
  ];

  SyncronizationModel? order;

  @override
  void onInit() {
    super.onInit();
    if (Get.arguments != null && Get.arguments['order'] != null) {
      order = Get.arguments['order'];

      if (order != null && order!.fileAttachment != null) {
        selectedFile = File(order!.fileAttachment!.selectedFilePath!);
        fileName = order!.fileAttachment!.fileName;
        fileSize = order!.fileAttachment!.fileSize;
      }
    }
    update();
  }

  Future<void> pickFile(FileType fileType) async {
    if (selectedFile != null) return;

    try {
      FilePickerResult? result;

      result = await FilePicker.platform.pickFiles(
        type: fileType,
        allowedExtensions:
            fileType == FileType.media ? null : allowedExtensions,
        dialogTitle: "Selecione um arquivo",
        lockParentWindow: true,
      );

      if (result != null) {
        File file = File(result.files.single.path!);
        int sizeInBytes = await file.length();
        double sizeInMb = sizeInBytes / (1024 * 1024);

        if (sizeInMb > getAttachmentMbAllowed()) {
          SnackbarCustom.snackbarError(
              'Não foi possível anexar o documento, o tamanho máximo permitido é de ${getAttachmentMbAllowed()} MB');
          return;
        }

        String fileExtension =
            result.files.single.extension?.toLowerCase() ?? '';

        if (!allowedExtensions.contains(fileExtension)) {
          SnackbarCustom.snackbarError(
              'Não foi possível anexar o arquivo. As extensões permitidas são: ${allowedExtensions.join(", ")}');
          return;
        }

        selectedFile = file;
        fileName = result.files.single.name;
        fileSize = '${sizeInMb.toStringAsFixed(2)} MB';

        order!.fileAttachment = FileAttachment(
          selectedFilePath: selectedFile?.path,
          fileName: fileName,
          fileSize: fileSize,
          status: FileAttachmentStatus.waiting,
        );

        update();

        SnackbarCustom.snackbarSucess('Arquivo', 'Arquivo anexado com sucesso');
      }
    } catch (e) {
      SnackbarCustom.snackbarError('Erro ao selecionar arquivo');
    }
  }

  Future<void> removeFile() async {
    await GetC.closeSnack();
    Dialogs.confirm(
      AppStrings.confirmation,
      'Deseja remover o arquivo anexado?',
      buttonNameOk: "Confirmar".toUpperCase(),
      buttonNameCancel: "Cancelar".toUpperCase(),
      onPressedCancel: () {
        GetC.close();
      },
      onPressedOk: () {
        if (selectedFile != null) {
          selectedFile = null;
          fileName = null;
          fileSize = null;
          order!.fileAttachment = null;
          update();
          GetC.close();
          SnackbarCustom.snackbarSucess(
              'Arquivo', 'Arquivo removido com sucesso');
        } else {
          GetC.close();
        }
      },
    );
  }

  double getAttachmentMbAllowed() {
    return generalParameterizationController
            .generalSettingsOrderDiscountRegistration
            ?.incluirAnexoPedidoTamanhoMb ??
        5;
  }

  Future<void> openCameraPicker() async {
    if (!await appController.checkCameraPermission()) {
      Get.toNamed(RoutesPath.permissionRequest)?.then((data) async {
        if (data) {
          await _handleCameraPicker();
        }
      });
    } else {
      await _handleCameraPicker();
    }
  }

  Future<void> _handleCameraPicker() async {
    Get.toNamed(RoutesPath.cameraPicker)?.then((data) async {
      if (data != null) {
        final xFile = data.file;
        selectedFile = File(xFile.path);
        fileName = xFile.name;
        final sizeInMb = selectedFile!.lengthSync() / (1024 * 1024);

        if (sizeInMb > getAttachmentMbAllowed()) {
          SnackbarCustom.snackbarError(
              'Não foi possível anexar o documento, o tamanho máximo permitido é de ${getAttachmentMbAllowed()} MB');
          selectedFile = null;
          fileName = null;
          fileSize = null;
          order!.fileAttachment = null;
          update();
          return;
        }

        fileSize = '${sizeInMb.toStringAsFixed(4)} MB';

        order!.fileAttachment = FileAttachment(
          selectedFilePath: selectedFile?.path,
          fileName: fileName,
          fileSize: fileSize,
          status: FileAttachmentStatus.waiting,
        );

        update();
        SnackbarCustom.snackbarSucess('Arquivo', 'Arquivo anexado com sucesso');
      }
    });
  }

  Future<void> saveFile() async {
    await dbContext.addData(
      clearCurrentData: true,
      data: order!,
      hashCode: order!.transactionKey,
      workspaceId: appController.workspace?.workspaceId,
      storeId: order!.payLoad!.pdvId,
      userId: appController.userLogged!.userId,
      key: DatabaseModels.syncronization,
    );
    await GetC.close();
  }
}
