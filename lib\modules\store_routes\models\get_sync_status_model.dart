class GetSyncStatusModel {
  int? status;
  int? pageSize;
  int? currentPage;
  int? totalPages;

  GetSyncStatusModel(
      {this.status, this.pageSize, this.currentPage, this.totalPages});

  GetSyncStatusModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    pageSize = json['pageSize'];
    currentPage = json['currentPage'];
    totalPages = json['totalPages'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['pageSize'] = pageSize;
    data['currentPage'] = currentPage;
    data['totalPages'] = totalPages;
    return data;
  }
}
