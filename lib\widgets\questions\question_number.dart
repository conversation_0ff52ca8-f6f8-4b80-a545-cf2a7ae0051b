import 'package:pharmalink/exports/basic_exports.dart';

// ignore: must_be_immutable
class QuestionNumberWidget extends StatefulWidget {
  QuestionNumberWidget(
      {super.key,
      required this.title,
      this.selectedAwser,
      required this.onAnswerSelected,
      this.hint,
      required this.readonly,
      this.isRequired});

  final bool readonly;
  final String title;
  final String? hint;
  late String? selectedAwser;
  final Function(double?) onAnswerSelected;
  final bool? isRequired;
  @override
  State<QuestionNumberWidget> createState() => _QuestionNumberWidgetState();
}

class _QuestionNumberWidgetState extends State<QuestionNumberWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomTextField(
            label: widget.title,
            hint: widget.hint,
            readOnly: widget.readonly,
            controller: TextEditingController(text: widget.selectedAwser ?? ""),
            keyboardType: TextInputType.number,
            onChanged: widget.readonly
                ? null
                : (String? value) {
                    widget.onAnswerSelected(value!.toDouble());
                  },
          ),
          Divider(
            thickness: 1,
            color:
                widget.isRequired == true ? Colors.red : Colors.grey.shade300,
          )
        ],
      ),
    );
  }
}
