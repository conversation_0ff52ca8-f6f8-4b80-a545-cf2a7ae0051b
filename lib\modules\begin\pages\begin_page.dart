import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/begin/controller/begin_controller.dart';

class BeginPage extends StatelessWidget {
  const BeginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<BeginController>(
        "BeginPage",
        builder: (ctrl)
    {
      return Scaffold(
        body: Column(
          children: [
            Container(
                height: MediaQuery
                    .of(context)
                    .size
                    .height / 2,
                width: MediaQuery
                    .of(context)
                    .size
                    .width,
                color: mi<PERSON><PERSON><PERSON>,
                child: const Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(
                          top: 100, left: 30, right: 30, bottom: 30),
                      child: Column(
                        textDirection: TextDirection.ltr,
                        children: [
                          Row(
                            children: [
                              Text(
                                'Olá,',
                                style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.w800,
                                    fontSize: 32),
                              ),
                            ],
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 15),
                            child: Text(
                              'O App Pharmalink mudou bastante nessa última versão e com isso tivemos diversas melhorias para você poder utilizar todo o poder da ferramenta.',
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 16,
                                  fontWeight: FontWeight.normal),
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                )),
            Flexible(
              child: Container(
                  height: MediaQuery
                      .of(context)
                      .size
                      .height / 2,
                  width: MediaQuery
                      .of(context)
                      .size
                      .width,
                  color: mizuasagi,
                  child: Stack(
                    children: [
                      SizedBox(
                        width: MediaQuery
                            .of(context)
                            .size
                            .width,
                        height: MediaQuery
                            .of(context)
                            .size
                            .height,
                        child: SvgPicture.asset('assets/images/cidade.svg',
                            fit: BoxFit.contain),
                      ),
                    ],
                  )),
            )
          ],
        ),
        floatingActionButton: FloatingActionButton.extended(
            label: const Text(
              'Avançar',
              style: TextStyle(color: whiteColor),
            ),
            backgroundColor: paletaCorBotaoDefault,
            onPressed: () {
              Get.toNamed(
                  RoutesPath.workspaces, arguments: {'settings': false});
            }),
      );
    });
  }
}
