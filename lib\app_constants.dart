import 'dart:convert';
import 'dart:developer';

import 'package:pharmalink/app/app_exports.dart';
import 'package:pharmalink/app/controller/parameter_controller.dart';
import 'package:pharmalink/app/controller/theme_controller.dart';
import 'package:pharmalink/config/databases/ilocal_db.dart';
import 'package:pharmalink/core/utils/dynatrace/dynatrace_custom.dart';
import 'package:pharmalink/core/utils/dynatrace/dynatrace_custom_sub_action.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/home/<USER>/home_api.dart';
import 'package:pharmalink/modules/home/<USER>/home_controller.dart';

final navigationPageController = Get.find<NavigationPageController>();
final dbContext = Get.find<ILocalDb>();
final storage = Get.find<StorageService>();
final appController = Get.find<AppController>();
final globalParams = Get.find<ParameterController>();
final themeController = Get.find<ThemeController>();
final splashController = Get.find<SplashController>();
// final initializeController1 = Get.find<InitializeController>();
final beginController = Get.find<BeginController>();
final loginController = Get.find<LoginController>();
final loginApi = Get.find<ILoginApi>();
final workspacesController = Get.find<WorkspacesController>();
final workspacesApi = Get.find<IWorkspacesApi>();

final synchronizationsController = Get.find<SynchronizationsController>();

final themesController = Get.find<ThemesController>();
final themesApi = Get.find<IThemesApi>();

// final storesController = Get.find<StoresController>();
final storesApi = Get.find<IStoresApi>();

final storeParametersController = Get.find<StoreParametersController>();
final storeParametersApi = Get.find<IStoreParametersApi>();

final settingsAppController = Get.find<SettingsAppController>();

final routesController = Get.find<RoutesController>();
final routesApi = Get.find<IRoutesApi>();

OrderTypesController orderTypesController = Get.find<OrderTypesController>();
OrderPaymentTypeController orderPaymentTypeController =
    Get.find<OrderPaymentTypeController>();
OrdersController ordersController = Get.find<OrdersController>();

final ordersApi = Get.find<IOrdersApi>();
final generalParameterizationController =
    Get.find<GeneralParameterizationController>();
final generalParameterizationApi = Get.find<IGeneralParameterizationApi>();

OrdersIdealMixController ordersIdealMixController =
    Get.find<OrdersIdealMixController>();
final ordersIdealMixApi = Get.find<IOrdersIdealMixApi>();
OrdersComboController ordersComboController = Get.find<OrdersComboController>();

final ordersResumeApi = Get.find<IOrdersResumeApi>();
OrdersResumeController ordersResumeController =
    Get.find<OrdersResumeController>();

final connectivityController = Get.find<ConnectivityController>();
OrdersTabloidController ordersTabloidController =
    Get.find<OrdersTabloidController>();
QATesterController qATesterController = Get.find<QATesterController>();

final reportContentsApi = Get.find<IReportContentsApi>();
ReportContentsController reportContentsController =
    Get.find<ReportContentsController>();

final reportOrdersApi = Get.find<IReportOrdersApi>();
ReportOrdersController reportOrdersController =
    Get.find<ReportOrdersController>();

ReportsController reportsController = Get.find<ReportsController>();
final visitsApi = Get.find<IVisitsApi>();

final researchesMerchanIndustryApi = Get.find<IResearchesMerchanIndustryApi>();
ResearchesMerchanIndustryController researchesMerchanIndustryController =
    Get.find<ResearchesMerchanIndustryController>();

final researchesMerchanCompetitiveApi =
    Get.find<IResearchesMerchanCompetitiveApi>();
ResearchesMerchanCompetitiveController researchesMerchanCompetitiveController =
    Get.find<ResearchesMerchanCompetitiveController>();

final researchesProductIndustryApi = Get.find<IResearchesProductIndustryApi>();
ResearchesProductIndustryController researchesProductIndustryController =
    Get.find<ResearchesProductIndustryController>();

final researchesProductConcurrentApi =
    Get.find<IResearchesProductConcurrentApi>();
ResearchesProductConcurrentController researchesProductConcurrentController =
    Get.find<ResearchesProductConcurrentController>();

final researchesTradeMarketingApi = Get.find<IResearchesTradeMarketingApi>();
ResearchesTradeMarketingController researchesTradeMarketingController =
    Get.find<ResearchesTradeMarketingController>();

final researchesComplementaryApi = Get.find<IResearchesComplementaryApi>();
ResearchesComplementaryController researchesComplementaryController =
    Get.find<ResearchesComplementaryController>();

final researchesShareOfShelfApi = Get.find<IResearchesShareOfShelfApi>();
ResearchesShareOfShelfController researchesShareOfShelfController =
    Get.find<ResearchesShareOfShelfController>();

final homeApi = Get.find<IHomeApi>();
final homeController = Get.find<HomeController>();

LogsHttpController logsHttpController = Get.find<LogsHttpController>();

final synchronizationsOfflineApi = Get.find<ISynchronizationsOfflineApi>();
final dynatrace = Get.find<DynatraceCustom>();

final logTraceMonitorApi = Get.find<ILogTraceMonitorApi>();

StoreRoutesController storeRoutesController = Get.find<StoreRoutesController>();
StoreRoutesPanelController storeRoutesPanelController =
    Get.find<StoreRoutesPanelController>();
StoreRoutesPlannedController storeRoutesPlannedController =
    Get.find<StoreRoutesPlannedController>();

StoreRoutesSyncService storeRoutesSyncService =
    Get.find<StoreRoutesSyncService>();

StoreRoutesOfflineSyncService storeRoutesOfflineSyncService =
    Get.find<StoreRoutesOfflineSyncService>();

void appLog(String eventName, {dynamic data}) {
  try {
    DBLogger.log(eventName, jsonData: data != null ? jsonEncode(data) : null);
  } catch (e) {
    log(e.toString());
  }
}

void appLogWithDynatrace(DynatraceCustomSubAction subAction, String eventName,
    {String? data}) {
  try {
    subAction.reportEvent(eventName);
    DBLogger.log(eventName, textData: data);
  } catch (e) {
    log(e.toString());
  }
}

void appErrorWithDynatrace(
    DynatraceCustomSubAction subAction, dynamic error, StackTrace stacktrace) {
  try {
    subAction.reportZoneStacktrace(error, stacktrace);
    DBLogger.log("Erro:",
        textData: jsonEncode(
            {"error": error.toString(), "stacktrace": stacktrace.toString()}));
  } catch (e) {
    log(e.toString());
  }
}

void appError(dynamic error, StackTrace stacktrace) {
  try {
    DBLogger.log("Erro:",
        textData: jsonEncode(
            {"error": error.toString(), "stacktrace": stacktrace.toString()}));
  } catch (e) {
    log(e.toString());
  }
}
