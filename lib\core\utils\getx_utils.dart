import 'dart:developer';

import 'package:get/get.dart';

class GetC {
  /// <PERSON><PERSON> o snackbar, se houver, e em seguida fecha o dialog, bottomSheet ou página atual.
  static Future<void> close() async {
    // Se existir um snackbar aberto, fecha-o.
    try {
      if (Get.isSnackbarOpen) {
        await Get.closeCurrentSnackbar();
      }
      // Em seguida, fecha a rota (dialog, bottomSheet ou página).
      Get.back();
    } catch (e) {
      log(e.toString());
    }
  }

  static Future<void> closeSnack() async {
    // Se existir um snackbar aberto, fecha-o.
    if (Get.isSnackbarOpen) {
      await Get.closeCurrentSnackbar();
    }
  }
}
