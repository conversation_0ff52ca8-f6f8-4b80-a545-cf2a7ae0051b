import 'package:pharmalink/modules/stores_parameters/models/distribuidores_model.dart';

class PrazoPagamentoModel {
  int? idPrazoPagamento;
  String? codigo;
  String? descricao;
  int? prazo;
  bool? rep;
  bool? padrao;
  bool? especial;
  bool? repDefault;
  bool? padraoDefault;
  bool? especialDefault;
  bool? selecionado;
  List<DistribuidoresModel>? distribuidores;

  PrazoPagamentoModel({
    this.idPrazoPagamento,
    this.codigo,
    this.descricao,
    this.prazo,
    this.rep,
    this.padrao,
    this.especial,
    this.repDefault,
    this.padraoDefault,
    this.especialDefault,
    this.selecionado,
    this.distribuidores,
  });

  PrazoPagamentoModel.fromJson(Map<String, dynamic> json) {
    idPrazoPagamento = json['IdPrazoPagamento'];
    codigo = json['Codigo'];
    descricao = json['Descricao'];
    prazo = json['Prazo'];
    rep = json['Rep'];
    padrao = json['Padrao'];
    especial = json['Especial'];
    repDefault = json['RepDefault'];
    padraoDefault = json['PadraoDefault'];
    especialDefault = json['EspecialDefault'];
    selecionado = json['Selecionado'];
    if (json['Distribuidores'] != null) {
      distribuidores = <DistribuidoresModel>[];
      json['Distribuidores'].forEach((v) {
        distribuidores!.add(DistribuidoresModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPrazoPagamento'] = idPrazoPagamento;
    data['Codigo'] = codigo;
    data['Descricao'] = descricao;
    data['Prazo'] = prazo;
    data['Rep'] = rep;
    data['Padrao'] = padrao;
    data['Especial'] = especial;
    data['RepDefault'] = repDefault;
    data['PadraoDefault'] = padraoDefault;
    data['EspecialDefault'] = especialDefault;
    data['Selecionado'] = selecionado;
    if (distribuidores != null) {
      data['Distribuidores'] = distribuidores!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
