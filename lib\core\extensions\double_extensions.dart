import 'package:intl/intl.dart';
import 'package:pharmalink/exports/basic_exports.dart';

extension DoubleExtensions on double {
  String formatReal() {
    return UtilBrasilFields.obterReal(this);
  }

  String formatarDouble() {
    NumberFormat formatoBrasileiro = NumberFormat("##0.##", "pt_BR");
    return formatoBrasileiro.format(this);
  }

  String formatPercent() {
    NumberFormat currencyFormat =
        NumberFormat.currency(locale: 'pt_BR', decimalDigits: 2, symbol: '');

    // Formate o valor como percentual
    return "${currencyFormat.format(this)} %";
  }

  String formatNumber() {
    if (this == roundToDouble()) {
      return toInt().toString();
    } else {
      return toStringAsFixed(2).replaceAll('.', ',');
    }
  }

  String formatPercentWithSymbol() {
    NumberFormat currencyFormat =
        NumberFormat.currency(locale: 'pt_BR', decimalDigits: 2, symbol: '');

    // Formate o valor como percentual
    return "${currencyFormat.format(this)} %";
  }
}
