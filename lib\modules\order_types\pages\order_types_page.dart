import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class OrderTypesPage extends StatelessWidget {
  const OrderTypesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<OrderTypesController>("OrderTypesPage",
        builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelWidget(
                title: globalParams.getCurrentStore()?.razaoSocial ?? "-",
                fontSize: DeviceSize.fontSize(14, 18),
                fontWeight: FontWeight.w600,
                textColor: whiteColor,
              ),
              LabelWidget(
                title: globalParams.getCurrentStore()?.cNPJ ?? "-",
                fontSize: DeviceSize.fontSize(11, 13),
                textColor: whiteColor,
              ),
            ],
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              GetC.close();
            },
          ),
        ),
        body: ListView.builder(
          itemCount: ctrl.types.length, // por exemplo, 10 itens
          itemBuilder: (context, index) {
            final item = ctrl.types[index];
            return CustomListTile(
              leading: item.icon!,
              title: LabelWidget(
                title: item.title!,
                fontSize: DeviceSize.fontSize(16, 20),
              ),
              trailing: Icon(
                Icons.navigate_next,
                color: Colors.grey.shade400,
              ),
              onTap: () async {
                ctrl.dynatraceAction.reportEvent("Click: ${item.title}");
                await ctrl.advance(item.typeOrderStrEnum!, item.typeOrderEnum!);
              },
            );
          },
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
