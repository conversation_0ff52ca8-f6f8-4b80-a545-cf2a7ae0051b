class StartSyncStoresRequestModel {
  int? storeId;
  String? cnpj;

  StartSyncStoresRequestModel({this.storeId, this.cnpj});

  StartSyncStoresRequestModel.fromJson(Map<String, dynamic> json) {
    storeId = json['storeId'];
    cnpj = json['cnpj'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['storeId'] = storeId;
    data['cnpj'] = cnpj;
    return data;
  }
}
