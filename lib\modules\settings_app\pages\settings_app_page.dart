import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';

class SettingsAppPage extends StatelessWidget {
  const SettingsAppPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: whiteColor,
      appBar: AppBar(
        backgroundColor: themesController.getPrimaryColor(),
        title: LabelWidget(
          title: "Configurações do aplicativo",
          fontSize: DeviceSize.fontSize(18, 21),
          fontWeight: FontWeight.w600,
          textColor: whiteColor,
        ),
        leading: Icon<PERSON>utton(
          icon: const Icon(
            Icons.arrow_back,
            color: whiteColor,
          ),
          onPressed: () {
            GetC.close();
          },
        ),
      ),
      body: ListView(
        children: [
          20.toHeightSpace(),
          LabelWidget(
            title: 'Dashboard',
            padding: EdgeInsets.symmetric(horizontal: 16.w),
          ),
          ListTileItemWidget(
            title: 'Configurações',
            onTap: () async {
              await settingsAppController.init();
              Get.toNamed(RoutesPath.settingsAppDashboard);
            },
          ),
          const Divider(),
          LabelWidget(
            title: 'Pedido',
            padding: EdgeInsets.symmetric(horizontal: 16.w),
          ),
          ListTileItemWidget(
            title: 'Configurações',
            onTap: () async {
              await settingsAppController.init();
              Get.toNamed(RoutesPath.settingsAppOrders);
            },
          ),
          const Divider(),
          LabelWidget(
            title: 'Logs',
            padding: EdgeInsets.symmetric(horizontal: 16.w),
          ),
          ListTileItemWidget(
            title: 'Erros de comunicação',
            onTap: () async {
              logsHttpController.openLogs();
            },
          ),
          ListTileItemWidget(
            title: 'Auditoria',
            onTap: () async {
              Get.toNamed(RoutesPath.logTraceMonitor);
            },
          ),
          const Divider(),
          ListTileItemWidget(
            title: 'Cache',
            onTap: () async {
              Get.toNamed(RoutesPath.cacheConfig);
            },
          ),
        ],
      ),
      bottomSheet: const VersionWidget(),
    );
  }
}
