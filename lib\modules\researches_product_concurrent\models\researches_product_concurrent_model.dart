import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';

class ResearchesProductConcurrentModel
    extends SqfLiteBase<ResearchesProductConcurrentModel> {
  int? workspaceId;
  int? routeId;
  int? isSync;
  int? idRota;
  int? idParametrizacaoPesquisa;
  bool? enviado;
  List<ResearchesProductConcurrentProdutos>? produtos;
  List<ResearchesProductConcurrentPosicionamentos>? posicionamentos;
  List<ResearchesProductConcurrentDistribuicoesNumericas>?
      distribuicoesNumericas;
  List<ResearchesProductConcurrentParametrizacaoCampos>? parametrizacaoCampos;
  String? mensagem;
  String? tipoPesquisa;

  ResearchesProductConcurrentModel(
      {this.workspaceId,
      this.routeId,
      this.isSync,
      this.idRota,
      this.idParametrizacaoPesquisa,
      this.enviado,
      this.produtos,
      this.posicionamentos,
      this.distribuicoesNumericas,
      this.parametrizacaoCampos,
      this.mensagem,
      this.tipoPesquisa})
      : super(DatabaseModels.researchesProductConcurrentModel);

  ResearchesProductConcurrentModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.researchesProductConcurrentModel) {
    idRota = json['IdRota'];
    workspaceId = json['workspaceId'];
    routeId = json['routeId'];
    isSync = json['isSync'] ?? SyncEnum.none;
    idParametrizacaoPesquisa = json['IdParametrizacaoPesquisa'];
    enviado = json['Enviado'];
    if (json['Produtos'] != null) {
      produtos = <ResearchesProductConcurrentProdutos>[];
      json['Produtos'].forEach((v) {
        produtos!.add(ResearchesProductConcurrentProdutos.fromJson(v));
      });
    }
    if (json['Posicionamentos'] != null) {
      posicionamentos = <ResearchesProductConcurrentPosicionamentos>[];
      json['Posicionamentos'].forEach((v) {
        posicionamentos!
            .add(ResearchesProductConcurrentPosicionamentos.fromJson(v));
      });
    }
    if (json['DistribuicoesNumericas'] != null) {
      distribuicoesNumericas =
          <ResearchesProductConcurrentDistribuicoesNumericas>[];
      json['DistribuicoesNumericas'].forEach((v) {
        distribuicoesNumericas!
            .add(ResearchesProductConcurrentDistribuicoesNumericas.fromJson(v));
      });
    }
    if (json['ParametrizacaoCampos'] != null) {
      parametrizacaoCampos =
          <ResearchesProductConcurrentParametrizacaoCampos>[];
      json['ParametrizacaoCampos'].forEach((v) {
        parametrizacaoCampos!
            .add(ResearchesProductConcurrentParametrizacaoCampos.fromJson(v));
      });
    }
    mensagem = json['Mensagem'];
    tipoPesquisa = json['tipoPesquisa'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workspaceId'] = workspaceId;
    data['routeId'] = routeId;
    data['isSync'] = isSync;
    data['IdRota'] = idRota;
    data['IdParametrizacaoPesquisa'] = idParametrizacaoPesquisa;
    data['Enviado'] = enviado;
    if (produtos != null) {
      data['Produtos'] = produtos!.map((v) => v.toJson()).toList();
    }
    if (posicionamentos != null) {
      data['Posicionamentos'] =
          posicionamentos!.map((v) => v.toJson()).toList();
    }
    if (distribuicoesNumericas != null) {
      data['DistribuicoesNumericas'] =
          distribuicoesNumericas!.map((v) => v.toJson()).toList();
    }
    if (parametrizacaoCampos != null) {
      data['ParametrizacaoCampos'] =
          parametrizacaoCampos!.map((v) => v.toJson()).toList();
    }
    data['Mensagem'] = mensagem;
    data['tipoPesquisa'] = tipoPesquisa;
    return data;
  }

  Future<List<ResearchesProductConcurrentModel>> getList(
      {required int workspaceId, required int routeId}) async {
    var list = await getAll<ResearchesProductConcurrentModel>(
        workspaceId: workspaceId,
        storeId: routeId,
        ResearchesProductConcurrentModel.fromJson);
    return list;
  }

  Future<List<ResearchesProductConcurrentModel>> getListToSync(
      {required int workspaceId}) async {
    var list = await getAll<ResearchesProductConcurrentModel>(
        workspaceId: workspaceId, ResearchesProductConcurrentModel.fromJson);
    return (list.isNotEmpty &&
            list.any((element) => element.isSync == SyncEnum.awaited))
        ? list.where((element) => element.isSync == SyncEnum.awaited).toList()
        : [];
  }
}

class ResearchesProductConcurrentProdutos {
  int? idPesquisaProduto;
  int? idProduto;
  String? apresentacao;
  String? descricao;
  String? foto;
  String? caminhoFoto;
  int? idPesquisaProdutoDistribuicaoNumericaSelecionado;
  int? idPesquisaPosicionamentoSelecionado;
  double? facing;
  double? preco;
  double? desconto;
  int? estoqueSistema;
  int? estoqueFisico;
  bool? informado;
  bool? enviadoRuptura;
  List<ResearchesProductConcurrentAnswers>? answers;
  ResearchesProductConcurrentProdutos({
    this.idPesquisaProduto,
    this.idProduto,
    this.apresentacao,
    this.descricao,
    this.foto,
    this.caminhoFoto,
    this.idPesquisaProdutoDistribuicaoNumericaSelecionado,
    this.idPesquisaPosicionamentoSelecionado,
    this.facing,
    this.preco,
    this.desconto,
    this.estoqueSistema,
    this.estoqueFisico,
    this.informado,
    this.enviadoRuptura,
    this.answers,
  });

  ResearchesProductConcurrentProdutos.fromJson(Map<String, dynamic> json) {
    idPesquisaProduto = json['IdPesquisaProduto'];
    idProduto = json['IdProduto'];
    apresentacao = json['Apresentacao'];
    descricao = json['Descricao'];
    foto = json['Foto'];
    caminhoFoto = json['CaminhoFoto'];
    idPesquisaProdutoDistribuicaoNumericaSelecionado =
        json['IdPesquisaProdutoDistribuicaoNumericaSelecionado'];
    idPesquisaPosicionamentoSelecionado =
        json['IdPesquisaPosicionamentoSelecionado'];
    facing = json['Facing'];
    preco = json['Preco'];
    desconto = json['Desconto'];
    estoqueSistema = json['EstoqueSistema'];
    estoqueFisico = json['EstoqueFisico'];
    informado = json['Informado'];
    enviadoRuptura = json['EnviadoRuptura'];
    if (json['Answers'] != null) {
      answers = <ResearchesProductConcurrentAnswers>[];
      json['Answers'].forEach((v) {
        answers!.add(ResearchesProductConcurrentAnswers.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPesquisaProduto'] = idPesquisaProduto;
    data['IdProduto'] = idProduto;
    data['Apresentacao'] = apresentacao;
    data['Descricao'] = descricao;
    data['Foto'] = foto;
    data['CaminhoFoto'] = caminhoFoto;
    data['IdPesquisaProdutoDistribuicaoNumericaSelecionado'] =
        idPesquisaProdutoDistribuicaoNumericaSelecionado;
    data['IdPesquisaPosicionamentoSelecionado'] =
        idPesquisaPosicionamentoSelecionado;
    data['Facing'] = facing;
    data['Preco'] = preco;
    data['Desconto'] = desconto;
    data['EstoqueSistema'] = estoqueSistema;
    data['EstoqueFisico'] = estoqueFisico;
    data['Informado'] = informado;
    data['EnviadoRuptura'] = enviadoRuptura;
    if (answers != null) {
      data['Answers'] = answers!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResearchesProductConcurrentAnswers {
  int? idPesquisaCampo;
  String? response;

  ResearchesProductConcurrentAnswers({this.idPesquisaCampo, this.response});

  ResearchesProductConcurrentAnswers.fromJson(Map<String, dynamic> json) {
    idPesquisaCampo = json['IdPesquisaCampo'];
    response = json['Response'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPesquisaCampo'] = idPesquisaCampo;
    data['Response'] = response;
    return data;
  }
}

class ResearchesProductConcurrentPosicionamentos {
  int? idPesquisaPosicionamento;
  String? descricao;

  ResearchesProductConcurrentPosicionamentos(
      {this.idPesquisaPosicionamento, this.descricao});

  ResearchesProductConcurrentPosicionamentos.fromJson(
      Map<String, dynamic> json) {
    idPesquisaPosicionamento = json['IdPesquisaPosicionamento'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPesquisaPosicionamento'] = idPesquisaPosicionamento;
    data['Descricao'] = descricao;
    return data;
  }
}

class ResearchesProductConcurrentDistribuicoesNumericas {
  int? idPesquisaProdutoDistribuicaoNumerica;
  String? descricao;
  bool? selected;

  ResearchesProductConcurrentDistribuicoesNumericas(
      {this.idPesquisaProdutoDistribuicaoNumerica, this.descricao});

  ResearchesProductConcurrentDistribuicoesNumericas.fromJson(
      Map<String, dynamic> json) {
    idPesquisaProdutoDistribuicaoNumerica =
        json['IdPesquisaProdutoDistribuicaoNumerica'];
    descricao = json['Descricao'];
    selected = json['Selected'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPesquisaProdutoDistribuicaoNumerica'] =
        idPesquisaProdutoDistribuicaoNumerica;
    data['Descricao'] = descricao;
    data['Selected'] = selected;
    return data;
  }
}

class ResearchesProductConcurrentParametrizacaoCampos {
  int? idPesquisaParametrizacaoCampo;
  int? idPesquisaParametrizacao;
  int? idPesquisaCampo;
  String? descricaoCampo;
  bool? visivel;
  bool? obrigatorio;
  int? ordem;

  ResearchesProductConcurrentParametrizacaoCampos(
      {this.idPesquisaParametrizacaoCampo,
      this.idPesquisaParametrizacao,
      this.idPesquisaCampo,
      this.descricaoCampo,
      this.visivel,
      this.obrigatorio,
      this.ordem});

  ResearchesProductConcurrentParametrizacaoCampos.fromJson(
      Map<String, dynamic> json) {
    idPesquisaParametrizacaoCampo = json['IdPesquisaParametrizacaoCampo'];
    idPesquisaParametrizacao = json['IdPesquisaParametrizacao'];
    idPesquisaCampo = json['IdPesquisaCampo'];
    descricaoCampo = json['DescricaoCampo'];
    visivel = json['Visivel'];
    obrigatorio = json['Obrigatorio'];
    ordem = json['Ordem'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPesquisaParametrizacaoCampo'] = idPesquisaParametrizacaoCampo;
    data['IdPesquisaParametrizacao'] = idPesquisaParametrizacao;
    data['IdPesquisaCampo'] = idPesquisaCampo;
    data['DescricaoCampo'] = descricaoCampo;
    data['Visivel'] = visivel;
    data['Obrigatorio'] = obrigatorio;
    data['Ordem'] = ordem;
    return data;
  }
}
