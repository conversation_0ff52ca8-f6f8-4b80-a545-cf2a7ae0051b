class DatabaseItem {
  final String id;
  final String userName;
  final DateTime createdAt;
  final String fileName;
  final bool isDownloaded;

  DatabaseItem({
    required this.id,
    required this.userName,
    required this.createdAt,
    required this.fileName,
    required this.isDownloaded,
  });

  factory DatabaseItem.fromJson(Map<String, dynamic> json) {
    return DatabaseItem(
      id: json['id'],
      userName: json['userName'],
      createdAt: DateTime.parse(json['createdAt']),
      fileName: json['fileName'],
      isDownloaded: json['isDownloaded'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userName': userName,
      'createdAt': createdAt.toIso8601String(),
      'fileName': fileName,
      'isDownloaded': isDownloaded,
    };
  }
}
