import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/researches_merchan_competitive/models/researches_merchan_competitive_model.dart';
import 'package:pharmalink/modules/researches_merchan_competitive/models/researches_merchan_competitive_send_model.dart';

abstract class IResearchesMerchanCompetitiveApi {
  Future<HttpResponse<ResearchesMerchanCompetitiveDataModel>>
      getResearchesMerchanCompetitive({required int routeId});

  Future<HttpResponse<ResearchesMerchanCompetitiveSendModel>>
      sendResearchesMerchanCompetitive(
          {required ResearchesMerchanCompetitiveSendModel model});
}

class ResearchesMerchanCompetitiveApi extends IResearchesMerchanCompetitiveApi {
  final HttpManager _httpManager;
  ResearchesMerchanCompetitiveApi(this._httpManager);

  @override
  Future<HttpResponse<ResearchesMerchanCompetitiveDataModel>>
      getResearchesMerchanCompetitive({required int routeId}) async {
    return await _httpManager.request<ResearchesMerchanCompetitiveDataModel>(
      path:
          'pesquisasMerchandising/obterFiltrosPesquisaMerchandisingConcorrencia/$routeId',
      method: HttpMethods.get,
      parser: (data) {
        return ResearchesMerchanCompetitiveDataModel.fromJson(data);
      },
    );
  }

  @override
  Future<HttpResponse<ResearchesMerchanCompetitiveSendModel>>
      sendResearchesMerchanCompetitive(
          {required ResearchesMerchanCompetitiveSendModel model}) async {
    return await _httpManager.request<ResearchesMerchanCompetitiveSendModel>(
      path: 'pesquisasMerchandising/enviarConcorrente',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        return ResearchesMerchanCompetitiveSendModel.fromJson(data);
      },
    );
  }
}
