import 'dart:developer';

import 'package:flutter_masked_text2/flutter_masked_text2.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/researches_product_industry/models/researches_product_industry_model.dart';
import 'package:pharmalink/modules/researches_product_industry/models/researches_product_industry_save_model.dart';

class ResearchesProductIndustryController
    extends GetxControllerInstrumentado<ResearchesProductIndustryController> {
  ResearchesProductIndustryController();

  int? currentRouteId;

  int productIndex = 0;
  int productLength = 0;
  int questionLength = 0;
  int questionIndex = 0;
  MoneyMaskedTextController currencyController = MoneyMaskedTextController(
    decimalSeparator: ',',
    thousandSeparator: '.',
  );

  ResearchesProductIndustryDataModel? productIndustryDataModel;
  List<ResearchesProductIndustryDataModel> productIndustryDataList = [];

  Future<void> getData(int routeId) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport("getData");
    currentRouteId = routeId;
    productIndex = 0;
    questionIndex = 0;

    try {
      final researchesBox = await ResearchesProductIndustryDataModel().getList(
          workspaceId: appController.workspace!.workspaceId!,
          routeId: currentRouteId!);
      if (researchesBox.isNotEmpty) {
        productIndustryDataModel = researchesBox.first;
        productLength = productIndustryDataModel!.produtos!.length;
        questionLength = productIndustryDataModel!.parametrizacaoCampos!.length;
        Get.toNamed(RoutesPath.researchesProductIndustry);
        return;
      }

      final response = await researchesProductIndustryApi.getProductIndustry(
          routeId: routeId);
      if (response.data != null) {
        productIndustryDataModel = response.data!;

        productLength = productIndustryDataModel!.produtos!.length;
        questionLength = productIndustryDataModel!.parametrizacaoCampos!.length;
      } else {}

      if (productLength == 0) {
        Dialogs.info(
          "Atenção",
          "Não há produtos para pesquisa de indústria.",
        );
      } else {
        Get.toNamed(RoutesPath.researchesProductIndustry);
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> getDataSync(int routeId) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("getDataSync");

    try {
      final response = await researchesProductIndustryApi.getProductIndustry(
          routeId: routeId);
      if (response.data != null) {
        response.data!.isSync =
            response.data!.enviado == null ? SyncEnum.none : SyncEnum.finished;
        await dbContext.withControllerAction(this).addData(
            key: DatabaseModels.researchesProductIndustryDataModel,
            data: response.data!,
            storeId: routeId,
            workspaceId: appController.workspace!.workspaceId,
            clearCurrentData: true);
      }
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }
  }

  void setDistribuicoesNumericas(DistribuicoesNumericas item) {
    productIndustryDataModel!.produtos![productIndex]
            .idPesquisaProdutoDistribuicaoNumericaSelecionado =
        item.idPesquisaProdutoDistribuicaoNumerica;

    next();
  }

  bool isDistribuicoesNumericasSelected(DistribuicoesNumericas item) {
    if (productIndustryDataModel!.produtos![productIndex]
            .idPesquisaProdutoDistribuicaoNumericaSelecionado ==
        null) {
      return false;
    }

    return productIndustryDataModel!.produtos![productIndex]
            .idPesquisaProdutoDistribuicaoNumericaSelecionado ==
        item.idPesquisaProdutoDistribuicaoNumerica;
  }

  void setNaoInformado(bool value) {
    productIndustryDataModel!.produtos![productIndex].informado = value;

    update();
  }

  bool isNaoInformado() {
    if (productIndustryDataModel!.produtos![productIndex].informado == null) {
      return false;
    }

    return productIndustryDataModel!.produtos![productIndex].informado!;
  }

  void setValue(String value, String typeQuestion) {
    if (value.isEmpty) return;
    switch (typeQuestion) {
      case "Preço":
        productIndustryDataModel!.produtos![productIndex].preco =
            value.toDouble();
        break;
      case "Desconto":
        productIndustryDataModel!.produtos![productIndex].desconto =
            value.toDouble();
        break;
      case "Facing":
        productIndustryDataModel!.produtos![productIndex].facing =
            value.toDouble();
        break;
      case "Estoque Físico":
        productIndustryDataModel!.produtos![productIndex].estoqueFisico =
            int.parse(value);
        break;
      case "Estoque Sistema":
        productIndustryDataModel!.produtos![productIndex].estoqueSistema =
            int.parse(value);
        break;
    }

    //update();
  }

  String getValue(String typeQuestion) {
    switch (typeQuestion) {
      case "Preço":
        currencyController.updateValue(
            productIndustryDataModel!.produtos![productIndex].preco ?? 0);
        break;
      case "Desconto":
        currencyController.updateValue(
            productIndustryDataModel!.produtos![productIndex].desconto ?? 0);
        break;
      case "Facing":
        return productIndustryDataModel!.produtos![productIndex].facing
                ?.toString() ??
            "0";
      case "Estoque Físico":
        return productIndustryDataModel!.produtos![productIndex].estoqueFisico
                ?.toString() ??
            "0";
      case "Estoque Sistema":
        return productIndustryDataModel!.produtos![productIndex].estoqueSistema
                ?.toString() ??
            "0";
    }

    return "";
  }

  void setPosicionamentos(Posicionamentos item) {
    productIndustryDataModel!.produtos![productIndex]
        .idPesquisaPosicionamentoSelecionado = item.idPesquisaPosicionamento;

    next();
  }

  bool isPosicionamentosSelected(Posicionamentos item) {
    return productIndustryDataModel!
                .produtos![productIndex].idPesquisaPosicionamentoSelecionado ==
            null
        ? false
        : productIndustryDataModel!
                .produtos![productIndex].idPesquisaPosicionamentoSelecionado ==
            item.idPesquisaPosicionamento;
  }

  bool canNext() {
    // final isFieldRequired = productIndustryDataModel!
    //     .parametrizacaoCampos![questionIndex].obrigatorio;

    // final currentFieldId = productIndustryDataModel!
    //     .parametrizacaoCampos![questionIndex].idPesquisaCampo;

    // if (isFieldRequired!) {
    //   final hasEmptyAnswer = productAnswers.any((element) =>
    //       element.idPesquisaCampo == currentFieldId &&
    //           element.response!.isEmpty ||
    //       element.response! == "0,00");

    //   if (hasEmptyAnswer) {
    //     return false;
    //   }
    // }

    return true;
  }

  Future<void> next() async {
    if (!canNext()) {
      SnackbarCustom.snackbarError("O seguinte campo é obrigatório.");
      return;
    }
    if (questionIndex < questionLength - 1) {
      questionIndex++;
      FocusScope.of(Get.context!).unfocus();

      update();
    } else {
      if (productIndex < productLength - 1) {
        productIndex++;
        questionIndex = 0;
        FocusScope.of(Get.context!).unfocus();

        update();
      } else {
        await saveQuestion();
      }
    }
  }

  void setAnswers() {
    for (var prod in productIndustryDataModel!.produtos!) {
      prod.idPesquisaPosicionamentoSelecionado = parseResponse<int>(prod, 2);
      prod.idPesquisaProdutoDistribuicaoNumericaSelecionado =
          parseResponse<int>(prod, 1);
      prod.facing = parseResponse<double>(prod, 3);
      prod.preco = parseResponse<double>(prod, 4);
      prod.desconto = parseResponse<double>(prod, 5);
      prod.estoqueSistema = parseResponse<int>(prod, 6);
      prod.estoqueFisico = parseResponse<int>(prod, 7);
      prod.informado = parseResponse<bool>(prod, 8);
    }
  }

  T? parseResponse<T>(var product, int idCampo) {
    if (!product.answers!.any((e) => e.idPesquisaCampo == idCampo)) {
      return null;
    }
    var response = product.answers!
        .where((element) => element.idPesquisaCampo == idCampo)
        .first
        ?.response;

    if (response == null) return null;

    if (T == int) {
      return int.parse(response) as T?;
    } else if (T == bool) {
      return bool.parse(response) as T?;
    } else if (T == double) {
      try {
        return response.toDouble() as T?;
      } catch (e) {
        rethrow;
      }
    }
    return null;
  }

  Future<void> saveQuestion() async {
    productIndustryDataModel!.workspaceId =
        appController.workspace?.workspaceId;
    productIndustryDataModel!.routeId = currentRouteId;
    productIndustryDataModel!.isSync = SyncEnum.awaited;
    //setAnswers();
    productIndustryDataList.add(productIndustryDataModel!);
    await dbContext.withControllerAction(this).addData(
        key: DatabaseModels.researchesProductIndustryDataModel,
        data: productIndustryDataList,
        workspaceId: appController.workspace!.workspaceId,
        storeId: currentRouteId,
        clearCurrentData: true);
    final visitController = Get.find<VisitsController>();
    visitController.refreshVisitsSearch();

    update();
    Get.back();
    SnackbarCustom.snackbarSucess("Produto Indústria",
        "Pesquisa do Produto Indústria salva com sucesso!");
  }

  void back() {
    if (questionIndex > 0) {
      questionIndex--;
      FocusScope.of(Get.context!).unfocus();
      update();
    } else {
      if (productIndex > 0) {
        productIndex--;
        questionIndex = questionLength - 1;
        FocusScope.of(Get.context!).unfocus();
        update();
      } else {
        Get.back();
      }
    }
  }

  Future<void> syncResearches() async {
    final listResearches = await ResearchesProductIndustryDataModel()
        .getListToSync(workspaceId: appController.workspace!.workspaceId!);

    final listFiltered = listResearches.where((e) => e.idRota != null).toList();
    if (listFiltered.isEmpty) return;

    for (var itemSync in listResearches) {
      final model = ResearchesProductIndustrySaveSync(
        idRota: itemSync.idRota,
        idParametrizacaoPesquisa: itemSync.idParametrizacaoPesquisa,
        enviado: true,
        tipoPesquisa: "Industria",
        produtos: itemSync.produtos!
            .map(
              (e) => ResearchesProductIndustrySaveProdutos(
                apresentacao: e.apresentacao,
                caminhoFoto: e.caminhoFoto,
                desconto: e.desconto,
                descricao: e.descricao,
                estoqueFisico: e.estoqueFisico,
                estoqueSistema: e.estoqueSistema,
                facing: e.facing,
                foto: e.foto,
                idPesquisaPosicionamentoSelecionado:
                    e.idPesquisaPosicionamentoSelecionado,
                idPesquisaProduto: e.idPesquisaProduto,
                idPesquisaProdutoDistribuicaoNumericaSelecionado:
                    e.idPesquisaProdutoDistribuicaoNumericaSelecionado,
                idProduto: e.idProduto,
                informado: e.informado,
                preco: e.preco,
                enviadoRuptura: e.enviadoRuptura,
              ),
            )
            .toList(),
      );
      final result =
          await researchesProductIndustryApi.sendProductIndustry(model: model);
      if (result.error != null) {
        SnackbarCustom.snackbarError(
            "Erro ao sincronizar pesquisa de produto indústria");
        return;
      }
    }
  }

  Future<void> saveDebugTest() async {
    for (var e in productIndustryDataModel!.produtos!) {
      e.idPesquisaProdutoDistribuicaoNumericaSelecionado = 1;
      e.idPesquisaPosicionamentoSelecionado = 1;
      e.facing = 1;
      e.desconto = 2;
      e.estoqueFisico = 3;
      e.estoqueSistema = 4;
      e.informado = true;
      e.preco = 5;
    }
    await saveQuestion();
  }
}
