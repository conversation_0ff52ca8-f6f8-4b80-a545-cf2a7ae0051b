import 'dart:io';

import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';

class PhotoViewPreview extends StatelessWidget {
  final String imagePath;
  final Orientation orientation;
  const PhotoViewPreview({
    super.key,
    required this.imagePath,
    required this.orientation,
  });

  @override
  Widget build(BuildContext context) {
    final isLandscape = orientation == Orientation.landscape;

    Widget photoView = PhotoView(
      imageProvider: FileImage(File(imagePath)),
      minScale: PhotoViewComputedScale.contained,
      maxScale: PhotoViewComputedScale.covered * 2,
      initialScale: PhotoViewComputedScale.contained,
      basePosition: Alignment.center,
      filterQuality: FilterQuality.high,
      enableRotation: false,
    );

    if (isLandscape) {
      return RotatedBox(
        quarterTurns: 3,
        child: photoView,
      );
    } else {
      return photoView;
    }
  }
}
