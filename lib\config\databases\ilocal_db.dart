import 'package:pharmalink/core/utils/dynatrace/instrumentado.dart';

abstract class ILocalDb with ILocalDbInstrumentado {
  Future<String> get databasePath;
  Future<void> addData({
    String? key,
    int? workspaceId,
    String? userId,
    int? storeId,
    String? hashCode,
    dynamic data,
    bool? clearCurrentData,
    bool? isOnline,
    bool? isLog,
  });
  Future<List<dynamic>> readAllData();
  Future<List<dynamic>> readAllByKey({
    String? key,
    int? workspaceId,
    String? userId,
    int? storeId,
    String? hashCode,
    bool? isOnline,
    DateTime? createdAt,
  });
  Future<dynamic> getById<T>({int? id});
  Future<void> deleteByKey({
    String? key,
    int? workspaceId,
    String? userId,
    int? storeId,
    String? hashCode,
    bool? isOnline,
    bool? isLog,
    DateTime? createdAt,
  });
  Future<void> clearDataBase();
}
