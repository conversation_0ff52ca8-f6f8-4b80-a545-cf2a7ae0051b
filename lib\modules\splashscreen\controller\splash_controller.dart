import 'package:permission_handler/permission_handler.dart';
import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/exports/get_exports.dart';

class SplashController extends GetxControllerInstrumentado<SplashController> {
  SplashController();

  @override
  Future<void> onReady() async {
    super.onReady();
    dynatrace.collectAndReportSessionData();
    var action = dynatrace.actionReport(runtimeType, "Iniciando tela");

    if (await _hasAllPermissions()) {
      Get.toNamed(RoutesPath.initialize);
    } else {
      Get.toNamed(RoutesPath.permissionGlobal);
    }

    action.leaveRemainingActions();
  }

  Future<bool> _hasAllPermissions() async {
    final permissions = [
      Permission.camera,
      // Permission.microphone,
      Permission.location,
      // if (Platform.isAndroid) ...[
      //   // Permission.requestInstallPackages,
      //   // Permission.manageExternalStorage,
      //   // Permission.storage,
      // ]
    ];

    for (final permission in permissions) {
      if (await permission.status.isDenied) {
        return false;
      }
    }
    return true;
  }
}
