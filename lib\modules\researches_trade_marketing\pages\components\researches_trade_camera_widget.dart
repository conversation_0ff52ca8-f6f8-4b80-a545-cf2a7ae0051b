import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/widgets/buttons/button_with_icon.dart';

class ResearchesTradeCameraWidget extends StatelessWidget {
  const ResearchesTradeCameraWidget(
      {super.key,
      required this.title,
      required this.onPressed,
      required this.categoryId});

  final String title;
  final GestureTapCallback onPressed;
  final int categoryId;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<ResearchesTradeMarketingController>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  LabelWidget(
                    title: title,
                    fontSize: 14.sp,
                    fontWeight: FontWeight.normal,
                  ),
                  ButtonWithIcon(
                    onPressed: onPressed,
                    fontSize: 12.sp,
                    title: "Adicionar".toUpperCase(),
                    icon: Icon(
                      FontAwesomeIcons.solidImage,
                      size: 14.w,
                      color: Colors.white,
                    ),
                  )
                ],
              ),
              10.toHeightSpace(),
              _buildImage(ctrl)
            ],
          ),
        ),
      );
    });
  }

  Widget _buildImage(ResearchesTradeMarketingController ctrl) {
    var images = ctrl.getImages(categoryId);

    if (images.isNotEmpty) {
      return SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: images.map((e) {
            return SizedBox(
              width: 86.w,
              child: Column(
                children: [
                  e.imageId == null
                      ? Image.file(
                          File(e.imageVirtualPath!),
                          width: 72.w,
                        )
                      : CachedNetworkImage(
                          imageUrl: e.imageVirtualPath!,
                          placeholder: (context, url) =>
                              const CircularProgressIndicator(),
                          errorWidget: (context, url, error) => Container(
                            color: Colors.grey,
                            width: 72.w,
                            height: 92.h,
                          ),
                        ),
                  5.toHeightSpace(),
                  IconButtonWidget(
                    colorButton: Colors.red,
                    width: 72.w,
                    icon: const Icon(
                      FontAwesomeIcons.trash,
                      color: Colors.white,
                      size: 16,
                    ),
                    onTap: () async {
                      await ctrl.removePicture(e);
                    },
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      );
    }

    return const SizedBox();
  }
}
