import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/services.dart';
import 'package:pharmalink/core/enuns/product_state_enum.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders/models/orders_products_list_model.dart';
import 'package:pharmalink/modules/stores_parameters/enuns/type_order_enum.dart';
import 'package:pharmalink/widgets/label/label_ritch_widget.dart';
import 'package:pharmalink/widgets/tiles/tiles_info_edit_widget.dart';

class OrdersProductsItem extends StatelessWidget {
  const OrdersProductsItem({super.key, required this.data});

  final OrdersProductsListModel data;

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrdersController>(
      builder: (ctrl) {
        return Padding(
          padding: EdgeInsets.only(bottom: 12.h),
          child: Card(
            elevation: 5,
            child: Padding(
              padding: EdgeInsets.only(bottom: 12.h),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        color:
                            data.states! == ProductStateEnum.done
                                ? Colors.green
                                : data.states! == ProductStateEnum.alert
                                ? Colors.yellow.shade600
                                : Colors.red,
                        width: 30.w,
                        height: DeviceSize.height(110, 150),
                        child: Icon(
                          data.states! == ProductStateEnum.done
                              ? FontAwesomeIcons.check
                              : data.states! == ProductStateEnum.alert
                              ? FontAwesomeIcons.exclamation
                              : FontAwesomeIcons.lock,
                          color: Colors.white,
                          size: 18.w,
                        ),
                      ),
                      Flexible(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CustomInkWell(
                                onTap: () => ctrl.showProductBottomSheet(data),
                                child: Row(
                                  children: [
                                    SizedBox(
                                      width: 60,
                                      child:
                                          data.photo != null
                                              ? CachedNetworkImage(
                                                imageUrl: data.photo ?? "",
                                                placeholder:
                                                    (context, url) =>
                                                        Image.asset(
                                                          AppImages
                                                              .productGeneric,
                                                        ),
                                                errorWidget:
                                                    (context, url, error) =>
                                                        Image.asset(
                                                          AppImages
                                                              .productGeneric,
                                                        ),
                                              )
                                              : Image.asset(
                                                AppImages.productGeneric,
                                              ),
                                    ),
                                    const Gap(5),
                                    Flexible(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          LabelWidget(
                                            title: data.name ?? "-",
                                            fontSize: DeviceSize.fontSize(
                                              15,
                                              20,
                                            ),
                                            fontWeight: FontWeight.bold,
                                            overflow: TextOverflow.ellipsis,
                                            maxLines: 5,
                                          ),
                                          LabelWidget(
                                            title:
                                                "${data.productCodeLabel!}: ${data.productCode!}",
                                            fontSize: DeviceSize.fontSize(
                                              13,
                                              16,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      LabelWidget(
                                        title: "De",
                                        fontSize: DeviceSize.fontSize(12, 14),
                                        fontWeight: FontWeight.bold,
                                      ),
                                      LabelRitchTextWidget(
                                        title:
                                            data.productDunId != null
                                                ? data.priceOrder!.formatReal()
                                                : (data.qtdy! > 0
                                                    ? data.priceOrder!
                                                        .formatReal()
                                                    : data.price!.formatReal()),
                                        fontSize: DeviceSize.fontSize(14, 18),
                                      ),
                                    ],
                                  ),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      LabelWidget(
                                        title: "Por",
                                        fontSize: DeviceSize.fontSize(12, 14),
                                        fontWeight: FontWeight.bold,
                                      ),
                                      LabelWidget(
                                        title: data.totalOrder!.formatReal(),
                                        fontSize: DeviceSize.fontSize(14, 18),
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(
                        width: DeviceSize.width(40, 100),
                        //height: 130.h,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            IconButton(
                              onPressed:
                                  data.states! != ProductStateEnum.block
                                      ? () {
                                        ctrl.setQtdyUp(data);
                                      }
                                      : null,
                              icon: Icon(FontAwesomeIcons.plus, size: 18.w),
                            ),
                            Center(
                              child: TextField(
                                focusNode: data.focusNode!,
                                keyboardType: TextInputType.number,
                                controller: data.qtdyController,
                                maxLength: 5,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly,
                                ],
                                style: TextStyle(
                                  fontSize: DeviceSize.fontSize(14, 20),
                                ),
                                textAlign: TextAlign.center,
                                readOnly:
                                    data.states! == ProductStateEnum.block,
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                  counterText: "",
                                ),
                                onChanged: (value) {
                                  if (value.isEmpty) value = '0';
                                  ctrl.setQtdyEdit(data, int.parse(value));
                                },
                                onSubmitted: (value) {
                                  if (value.isEmpty) value = '0';
                                  ctrl.setQtdyEdit(data, int.parse(value));
                                },
                              ),
                            ),
                            IconButton(
                              onPressed:
                                  data.states! != ProductStateEnum.block
                                      ? () {
                                        ctrl.setQtdyDown(data);
                                      }
                                      : null,
                              icon: Icon(FontAwesomeIcons.minus, size: 18.w),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  8.toHeightSpace(),
                  if (globalParams.getTypeOrderId() == TyperOrderEnum.padrao)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: TilesInfoWidget(
                        title: "Desc.",
                        valueList: data.getDiscountRangeList(),
                        titleFontSize: DeviceSize.fontSize(10, 13),
                        valueFontSize: DeviceSize.fontSize(12, 16),
                        hasDivider: false,
                        valueFontWeight: FontWeight.bold,
                      ),
                    ),
                  // Column(
                  //   crossAxisAlignment: CrossAxisAlignment.start,
                  //   children: [

                  //     Padding(
                  //       padding: EdgeInsets.symmetric(horizontal: 20.w),
                  //       child: LabelWidget(
                  //         title: "Desc.",
                  //         fontSize: DeviceSize.fontSize(12, 14),
                  //         fontWeight: FontWeight.bold,
                  //       ),
                  //     ),
                  //     Padding(
                  //       padding: EdgeInsets.symmetric(horizontal: 20.w),
                  //       child: LabelWidget(
                  //         title: data.getDiscountRange(),
                  //         fontSize: DeviceSize.fontSize(14, 18),
                  //         fontWeight: FontWeight.bold,
                  //       ),
                  //     ),
                  //   ],
                  // ),
                  if (globalParams.getTypeOrderId() == TyperOrderEnum.rep)
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                      child: Row(
                        children: [
                          IconButton(
                            onPressed: () async {
                              await ctrl.setEnableDiscountRep(data);
                            },
                            icon: Icon(
                              FontAwesomeIcons.penToSquare,
                              size: 18,
                              color: Colors.grey.shade600,
                            ),
                          ),
                          Spacer(),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              TilesInfoWidget(
                                title: "Desc. Total:",
                                value:
                                    data.discountTotal?.formatPercent() ?? "-",
                                titleFontSize: DeviceSize.fontSize(10, 13),
                                valueFontSize: DeviceSize.fontSize(13, 16),
                                hasDivider: false,
                                valueFontWeight: FontWeight.bold,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  if (globalParams.getTypeOrderId() == TyperOrderEnum.rep)
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (data.isEditDiscount == true)
                            SizedBox(
                              width: 60,
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: TilesInfoEditWidget(
                                  title: "Desc.",
                                  value:
                                      data.discountChange?.formatPercent() ??
                                      "-",
                                  titleFontSize: DeviceSize.fontSize(10, 12),
                                  hasDivider: false,
                                  valueController: data.valueController!,
                                  textInputType: TextInputType.number,
                                  onChanged: (String? value) async {
                                    if (value == null || value.isEmpty) {
                                      ctrl.setDiscountRep(data, '0');
                                    } else {
                                      ctrl.setDiscountRep(data, value);
                                    }
                                  },
                                  onSubmitted: (String? value) async {
                                    if (value == null || value.isEmpty) {
                                      value = '0';
                                    }
                                    ctrl.setDiscountRep(data, value);
                                  },
                                  inputFormatters: [
                                    FilteringTextInputFormatter.allow(
                                      RegExp(r'[0-9.,]+'),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          if (data.isEditDiscount == false)
                            Flexible(
                              child: TilesInfoWidget(
                                title: "Desc.",
                                valueList: data.getDiscountRangeList(),
                                titleFontSize: DeviceSize.fontSize(10, 13),
                                valueFontSize: DeviceSize.fontSize(12, 16),
                                hasDivider: false,
                                valueFontWeight: FontWeight.bold,
                              ),
                            ),
                          TilesInfoWidget(
                            title: "Desc. Gestor",
                            value: data.discountManager?.formatPercent() ?? "-",
                            titleFontSize: DeviceSize.fontSize(10, 13),
                            valueFontSize: DeviceSize.fontSize(12, 16),
                            hasDivider: false,
                            valueFontWeight: FontWeight.bold,
                          ),
                          TilesInfoWidget(
                            title: "Desc. disp.",
                            value: data.discountApply?.formatPercent() ?? "-",
                            titleFontSize: DeviceSize.fontSize(10, 13),
                            valueFontSize: DeviceSize.fontSize(12, 16),
                            hasDivider: false,
                            valueFontWeight: FontWeight.bold,
                          ),
                          SizedBox(
                            width: 60,
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: TilesInfoEditWidget(
                                title: "Desc. adicional",
                                value:
                                    data.discountAdditional?.formatPercent() ??
                                    "-",
                                titleFontSize: DeviceSize.fontSize(10, 13),
                                hasDivider: false,
                                focusNode: data.focusNodeDiscountChange!,
                                valueController: data.discountChangeController!,
                                enabled:
                                    data.discountManager == 0 &&
                                            data.discountApply == 0
                                        ? false
                                        : data.isEditDiscount == false,
                                textInputType: TextInputType.number,
                                onChanged: (String? value) async {
                                  if (value == null || value.isEmpty) {
                                    ctrl.setDiscountAddRep(
                                      data,
                                      '0',
                                      isEdit: true,
                                    );
                                  } else {
                                    if (data.discountManager == 0 &&
                                        data.discountApply == 0) {
                                      await Dialogs.info(
                                        AppStrings.attention,
                                        AppStrings.discountMax1,
                                        buttonName: "Entendi".toUpperCase(),
                                      );
                                      data.discountChangeController!.text = "0";
                                      return;
                                    }
                                    ctrl.setDiscountAddRep(
                                      data,
                                      value,
                                      isEdit: true,
                                    );
                                  }
                                },
                                onSubmitted: (String? value) async {
                                  if (value == null || value.isEmpty) {
                                    value = '0';
                                  }
                                  if (data.discountManager == 0 &&
                                      data.discountApply == 0) {
                                    await Dialogs.info(
                                      AppStrings.attention,
                                      AppStrings.discountMax1,
                                      buttonName: "Entendi".toUpperCase(),
                                    );
                                    return;
                                  }
                                  ctrl.setDiscountAddRep(
                                    data,
                                    value,
                                    isEdit: true,
                                  );
                                },
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                    RegExp(r'[0-9.,]+'),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (globalParams.getTypeOrderId() == TyperOrderEnum.especial)
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 12.w),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TilesInfoWidget(
                            title: "Desc. Min.",
                            value:
                                data.discountRangeCurrent!.discountMin
                                    ?.formatPercent() ??
                                "-",
                            titleFontSize: DeviceSize.fontSize(10, 13),
                            valueFontSize: DeviceSize.fontSize(13, 16),
                            hasDivider: false,
                            valueFontWeight: FontWeight.bold,
                          ),
                          TilesInfoWidget(
                            title: "Desc. Max",
                            value:
                                data.discountRangeCurrent!.discountMax
                                    ?.formatPercent() ??
                                "-",
                            titleFontSize: DeviceSize.fontSize(10, 13),
                            valueFontSize: DeviceSize.fontSize(13, 16),
                            hasDivider: false,
                            valueFontWeight: FontWeight.bold,
                          ),
                          SizedBox(
                            width: 120,
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: TilesInfoEditWidget(
                                title: "Desc. adicional",
                                value:
                                    data.discountChange?.formatPercent() ?? "-",
                                titleFontSize: DeviceSize.fontSize(10, 13),
                                hasDivider: false,
                                focusNode: data.focusNodeDiscountChange!,
                                valueController: data.discountChangeController!,
                                textInputType: TextInputType.number,
                                onChanged: (String? value) async {
                                  if (value == null || value.isEmpty) {
                                    ctrl.setDiscountAddEsp(data, '0');
                                  } else {
                                    ctrl.setDiscountAddEsp(data, value);
                                  }
                                },
                                onSubmitted: (String? value) async {
                                  if (value == null || value.isEmpty) {
                                    value = '0';
                                  }
                                  ctrl.setDiscountAddEsp(data, value);
                                },
                                inputFormatters: [
                                  FilteringTextInputFormatter.allow(
                                    RegExp(r'[0-9.,]+'),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
