import 'package:pharmalink/modules/researches_trade_marketing/models/researches_trade_marketing_model.dart';

class ResearchesTradeMarketingSendModel {
  List<ResearchesTradeMarketingSendPesquisas>? pesquisas;

  ResearchesTradeMarketingSendModel({this.pesquisas});

  ResearchesTradeMarketingSendModel.fromJson(Map<String, dynamic> json) {
    if (json['pesquisas'] != null) {
      pesquisas = <ResearchesTradeMarketingSendPesquisas>[];
      json['pesquisas'].forEach((v) {
        pesquisas!.add(ResearchesTradeMarketingSendPesquisas.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (pesquisas != null) {
      data['pesquisas'] = pesquisas!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResearchesTradeMarketingSendPesquisas {
  String? cnpj;
  int? idLoja;
  String? idSurvey;
  String? surveyVersion;
  List<ResearchesTradeMarketingSendSteps>? steps;
  List<ResearchesTradeMarketingSendImages>? images;

  ResearchesTradeMarketingSendPesquisas(
      {this.cnpj,
      this.idLoja,
      this.idSurvey,
      this.surveyVersion,
      this.steps,
      this.images});

  ResearchesTradeMarketingSendPesquisas.fromJson(Map<String, dynamic> json) {
    cnpj = json['cnpj'];
    idLoja = json['idLoja'];
    idSurvey = json['idSurvey'];
    surveyVersion = json['surveyVersion'];
    if (json['steps'] != null) {
      steps = <ResearchesTradeMarketingSendSteps>[];
      json['steps'].forEach((v) {
        steps!.add(ResearchesTradeMarketingSendSteps.fromJson(v));
      });
    }
    if (json['images'] != null) {
      images = <ResearchesTradeMarketingSendImages>[];
      json['images'].forEach((v) {
        images!.add(ResearchesTradeMarketingSendImages.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['cnpj'] = cnpj;
    data['idLoja'] = idLoja;
    data['idSurvey'] = idSurvey;
    data['surveyVersion'] = surveyVersion;
    if (steps != null) {
      data['steps'] = steps!.map((v) => v.toJson()).toList();
    }
    if (images != null) {
      data['images'] = images!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResearchesTradeMarketingSendSteps {
  String? stepIdentifier;
  int? idRota;
  String? stepName;
  int? stepIndex;
  bool? readonly;
  int? imageNumMin;
  int? imageNumMax;
  List<ResearchesTradeMarketingSendQuestions>? questions;
  List<ResearchesTradeMarketingSendAnswersSubmitted>? answersSubmitted;

  ResearchesTradeMarketingSendSteps(
      {this.stepIdentifier,
      this.idRota,
      this.stepName,
      this.stepIndex,
      this.readonly,
      this.imageNumMin,
      this.imageNumMax,
      this.questions,
      this.answersSubmitted});

  ResearchesTradeMarketingSendSteps.fromJson(Map<String, dynamic> json) {
    idRota = json['idRota'];
    stepIdentifier = json['stepIdentifier'];
    stepName = json['stepName'];
    stepIndex = json['stepIndex'];
    readonly = json['readonly'];
    imageNumMin = json['imageNumMin'];
    imageNumMax = json['imageNumMax'];
    if (json['questions'] != null) {
      questions = <ResearchesTradeMarketingSendQuestions>[];
      json['questions'].forEach((v) {
        questions!.add(ResearchesTradeMarketingSendQuestions.fromJson(v));
      });
    }
    if (json['answersSubmitted'] != null) {
      answersSubmitted = <ResearchesTradeMarketingSendAnswersSubmitted>[];
      json['answersSubmitted'].forEach((v) {
        answersSubmitted!
            .add(ResearchesTradeMarketingSendAnswersSubmitted.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idRota'] = idRota;
    data['stepIdentifier'] = stepIdentifier;
    data['stepName'] = stepName;
    data['stepIndex'] = stepIndex;
    data['readonly'] = readonly;
    data['imageNumMin'] = imageNumMin;
    data['imageNumMax'] = imageNumMax;
    if (questions != null) {
      data['questions'] = questions!.map((v) => v.toJson()).toList();
    }
    if (answersSubmitted != null) {
      data['answersSubmitted'] =
          answersSubmitted!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResearchesTradeMarketingSendQuestions {
  int? questionId;
  int? step;
  String? question;
  String? dataType;
  dynamic answers;
  ResearchesTradeMarketingSendMetadata? metadata;
  bool? explain;
  String? explainLabel;
  String? explainText;

  ResearchesTradeMarketingSendQuestions(
      {this.questionId,
      this.step,
      this.question,
      this.dataType,
      this.answers,
      this.metadata,
      this.explain,
      this.explainLabel,
      this.explainText});

  ResearchesTradeMarketingSendQuestions.fromJson(Map<String, dynamic> json) {
    questionId = json['questionId'];
    step = json['step'];
    question = json['question'];
    dataType = json['dataType'];
    if (json['answers'] != null) {
      var value = json['answers'];
      if (value is List) {
        if (value.isNotEmpty) {
          if (value[0] is int) {
            answers = value.cast<int>();
          } else if (value[0] is String) {
            answers = value.cast<String>();
          } else if (value[0] is double) {
            answers = value.cast<double>();
          } else if (value[0] is Map) {
            answers = value
                .map((v) => ResearchesTradeMarketingAnswers.fromJson(v))
                .toList();
          }
        } else {
          answers = [];
        }
      } else {
        answers = value;
      }
    }
    metadata = json['metadata'] != null
        ? ResearchesTradeMarketingSendMetadata.fromJson(json['metadata'])
        : null;
    explain = json['explain'];
    explainLabel = json['explainLabel'];
    explainText = json['explainText'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['questionId'] = questionId;
    data['step'] = step;
    data['question'] = question;
    data['dataType'] = dataType;
    if (answers != null) {
      if (answers is List<ResearchesTradeMarketingAnswers>) {
        data['answers'] = (answers as List<ResearchesTradeMarketingAnswers>)
            .map((v) => v.toJson())
            .toList();
      } else {
        data['answers'] = answers;
      }
    }
    if (metadata != null) {
      data['metadata'] = metadata!.toJson();
    }
    data['explain'] = explain;
    data['explainLabel'] = explainLabel;
    data['explainText'] = explainText;
    return data;
  }
}

class ResearchesTradeMarketingSendMetadata {
  bool? required;
  bool? multiple;
  String? placeholder;
  String? trueLabel;
  String? falseLabel;
  List<ResearchesTradeMarketingSendOptions>? options;
  String? maxLength;
  String? minLength;
  bool? isRequired;
  ResearchesTradeMarketingSendMetadata(
      {this.required,
      this.isRequired,
      this.multiple,
      this.placeholder,
      this.trueLabel,
      this.falseLabel,
      this.options,
      this.maxLength,
      this.minLength});

  ResearchesTradeMarketingSendMetadata.fromJson(Map<String, dynamic> json) {
    required = json['required'];
    isRequired = json['isRequired'];
    multiple = json['multiple'];
    placeholder = json['placeholder'];
    trueLabel = json['trueLabel'];
    falseLabel = json['falseLabel'];

    maxLength = json['maxLength'];
    minLength = json['minLength'];
    if (json['options'] != null) {
      options = <ResearchesTradeMarketingSendOptions>[];
      json['options'].forEach((v) {
        options!.add(ResearchesTradeMarketingSendOptions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['required'] = required;
    data['isRequired'] = isRequired;
    data['multiple'] = multiple;
    data['placeholder'] = placeholder;
    data['trueLabel'] = trueLabel;
    data['falseLabel'] = falseLabel;

    data['maxLength'] = maxLength;
    data['minLength'] = minLength;
    if (options != null) {
      data['options'] = options!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ResearchesTradeMarketingSendOptions {
  String? text;
  String? value;

  ResearchesTradeMarketingSendOptions({this.text, this.value});

  ResearchesTradeMarketingSendOptions.fromJson(Map<String, dynamic> json) {
    text = json['text'];
    value = json['value'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['text'] = text;
    data['value'] = value;
    return data;
  }
}

class ResearchesTradeMarketingSendAnswersSubmitted {
  int? id;
  String? stepId;
  String? question;
  String? dataType;
  List<String>? answers;
  String? date;
  bool? explain;
  String? explainText;
  String? explainLabel;

  ResearchesTradeMarketingSendAnswersSubmitted(
      {this.id,
      this.stepId,
      this.question,
      this.dataType,
      this.answers,
      this.date,
      this.explain,
      this.explainText,
      this.explainLabel});

  ResearchesTradeMarketingSendAnswersSubmitted.fromJson(
      Map<String, dynamic> json) {
    id = json['id'];
    stepId = json['stepId'];
    question = json['question'];
    dataType = json['dataType'];
    answers = json['answers'].cast<String>();
    date = json['date'];
    explain = json['explain'];
    explainText = json['explainText'];
    explainLabel = json['explainLabel'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['stepId'] = stepId;
    data['question'] = question;
    data['dataType'] = dataType;
    data['answers'] = answers;
    data['date'] = date;
    data['explain'] = explain;
    data['explainText'] = explainText;
    data['explainLabel'] = explainLabel;
    return data;
  }
}

class ResearchesTradeMarketingSendImages {
  String? imageId;
  String? imageName;
  String? stepIdentifier;
  int? stepIndex;
  int? categoryId;
  int? storeId;
  String? cnpj;
  String? imageVirtualPath;
  String? contentType;

  ResearchesTradeMarketingSendImages(
      {this.imageId,
      this.imageName,
      this.stepIdentifier,
      this.stepIndex,
      this.categoryId,
      this.storeId,
      this.cnpj,
      this.imageVirtualPath,
      this.contentType});

  ResearchesTradeMarketingSendImages.fromJson(Map<String, dynamic> json) {
    imageId = json['imageId'];
    imageName = json['imageName'];
    stepIdentifier = json['stepIdentifier'];
    stepIndex = json['stepIndex'];
    categoryId = json['categoryId'];
    storeId = json['storeId'];
    cnpj = json['cnpj'];
    imageVirtualPath = json['imageVirtualPath'];
    contentType = json['contentType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['imageId'] = imageId;
    data['imageName'] = imageName;
    data['stepIdentifier'] = stepIdentifier;
    data['stepIndex'] = stepIndex;
    data['categoryId'] = categoryId;
    data['storeId'] = storeId;
    data['cnpj'] = cnpj;
    data['imageVirtualPath'] = imageVirtualPath;
    data['contentType'] = contentType;
    return data;
  }
}
