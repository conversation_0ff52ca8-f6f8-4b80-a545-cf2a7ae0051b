class MixIdealRequestModel {
  int? idLoja;
  List<int>? idsDistribuidores;

  MixIdealRequestModel({this.idLoja, this.idsDistribuidores});

  MixIdealRequestModel.fromJson(Map<String, dynamic> json) {
    idLoja = json['idLoja'];
    idsDistribuidores = json['IdsDistribuidores'].cast<int>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idLoja'] = idLoja;
    data['IdsDistribuidores'] = idsDistribuidores;
    return data;
  }
}
