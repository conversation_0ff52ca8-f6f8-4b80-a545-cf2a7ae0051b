class ReportOrdersAttachmentResponse {
  final String? agrupadorPedido;
  final String? nomeArquivo;
  final String? caminhoArquivo;
  final String? tipoArquivo;
  final DateTime? dataUpload;
  final String? arquivo;

  ReportOrdersAttachmentResponse({
    this.agrupadorPedido,
    this.nomeArquivo,
    this.caminhoArquivo,
    this.tipoArquivo,
    this.dataUpload,
    this.arquivo,
  });

  factory ReportOrdersAttachmentResponse.fromJson(Map<String, dynamic> json) {
    return ReportOrdersAttachmentResponse(
      agrupadorPedido: json['AgrupadorPedido'] as String?,
      nomeArquivo: json['NomeArquivo'] as String?,
      caminhoArquivo: json['CaminhoArquivo'] as String?,
      tipoArquivo: json['TipoArquivo'] as String?,
      dataUpload: json['DataUpload'] != null
          ? DateTime.parse(json['DataUpload'] as String)
          : null,
      arquivo: json['Arquivo'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'AgrupadorPedido': agrupadorPedido,
      'NomeArquivo': nomeArquivo,
      'CaminhoArquivo': caminhoArquivo,
      'TipoArquivo': tipoArquivo,
      'DataUpload': dataUpload?.toIso8601String(),
      'Arquivo': arquivo,
    };
  }
}
