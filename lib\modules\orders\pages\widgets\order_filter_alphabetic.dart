import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class OrdersFilterAlphabeticWidget extends StatelessWidget {
  const OrdersFilterAlphabeticWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrdersController>(builder: (ctrl) {
      return ListView.builder(
        itemCount: ctrl.alphabet.length,
        itemBuilder: (item, i) => Center(
          child: CustomInkWell(
            splashColor: themesController.getPrimaryColor().withOpacity(0.2),
            onTap: ctrl.alphabet[i].hasProduct!
                ? () {
                    ctrl.filterProductsByLetter(ctrl.alphabet[i].letter!);
                  }
                : () {},
            child: Card(
              child: Padding(
                padding: const EdgeInsets.all(10.0),
                child: LabelWidget(
                    title: ctrl.alphabet[i].letter!.toUpperCase(),
                    fontSize: DeviceSize.fontSize(18, 20),
                    textColor: ctrl.alphabet[i].hasProduct!
                        ? themesController.getPrimaryColor()
                        : themesController.getPrimaryColor().withOpacity(0.5),
                    fontWeight: ctrl.selectedLetter ==
                            ctrl.alphabet[i].letter!.toUpperCase()
                        ? FontWeight.bold
                        : FontWeight.normal),
              ),
            ),
          ),
        ),
      );
    });
  }
}
