import 'dart:developer';
import 'dart:io';
import 'dart:ui';

import 'package:crop_image/crop_image.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class ResearchesShareOfShelfPhotoCropPage extends StatefulWidget {
  const ResearchesShareOfShelfPhotoCropPage({super.key});

  @override
  State<ResearchesShareOfShelfPhotoCropPage> createState() =>
      _ResearchesShareOfShelfPhotoCropPageState();
}

class _ResearchesShareOfShelfPhotoCropPageState
    extends State<ResearchesShareOfShelfPhotoCropPage> {
  final controller = CropController(
    aspectRatio: null,
    defaultCrop: const Rect.fromLTRB(0.1, 0.1, 0.9, 0.9),
  );

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ResearchesShareOfShelfController>(
        "ResearchesShareOfShelfPhotoCropPage", builder: (ctrl) {
      return SafeArea(
        child: Scaffold(
          backgroundColor: whiteColor,
          appBar: AppBar(
            backgroundColor: themesController.getPrimaryColor(),
            title: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LabelWidget(
                  title: globalParams.getCurrentStore()?.razaoSocial ?? "-",
                  fontSize: DeviceSize.fontSize(14, 18),
                  fontWeight: FontWeight.w600,
                  textColor: whiteColor,
                ),
                LabelWidget(
                  title: globalParams.getCurrentStore()?.cNPJ ?? "-",
                  fontSize: DeviceSize.fontSize(11, 13),
                  textColor: whiteColor,
                ),
              ],
            ),
            actions: [
              IconButton(
                  onPressed: () async {
                    await _finished(ctrl);
                  },
                  icon: const Icon(
                    Icons.refresh,
                    color: whiteColor,
                  )),
              IconButton(
                  onPressed: () async {
                    await _finished(ctrl);
                  },
                  icon: const Icon(
                    Icons.send,
                    color: whiteColor,
                  ))
            ],
          ),
          body: CropImage(
            controller: controller,
            image: Image.file(
              File(ctrl.photoPicturePath!),
              fit: BoxFit.fill,
            ),
            paddingSize: 0.0,
            alwaysMove: true,
          ),
        ),
      );
    });
  }

  Future<void> _finished(ResearchesShareOfShelfController ctrl) async {
    // Loading.show();
    var (leaveAction, subAction) =
        ctrl.dynatraceAction.subActionReport("loadInfoEditOrderOffline");

    try {
      final bitmap = await controller.croppedBitmap();
      final data = await bitmap.toByteData(format: ImageByteFormat.png);
      final bytes = data!.buffer.asUint8List();
      final directory = await getApplicationDocumentsDirectory();
      final file = File(
          '${directory.path}/${DateTime.now().toIso8601String()}_temp.png');
      file.writeAsBytesSync(bytes);

      ctrl.photoPictureCropPath = file.path;
      // loading.hide();
      await ctrl.closeCropAndOpenMarking();
    } catch (e, s) {
      subAction.reportZoneStacktrace(e, s);
      log(e.toString());
      rethrow;
    } finally {
      leaveAction();
    }
  }
}
