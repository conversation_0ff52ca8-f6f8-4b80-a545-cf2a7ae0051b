import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/home/<USER>/home_controller.dart';
import 'package:pharmalink/modules/home/<USER>/home_dashboard_card.dart';
import 'package:pharmalink/modules/home/<USER>/home_pdv_selected_widget.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);

    return Scaffold(
      body: GetBuilderInstrumentado<HomeController>("HomePage",
          init: homeController, initState: (state) {
        Future.delayed(const Duration(milliseconds: 300), () async {
          if (navigationPageController.selectedIndex == 0) {
            await homeController.setDashboard();
          }
        });
      }, builder: (controller) {
        return CustomScrollView(
          slivers: [
            SliverAppBar(
                expandedHeight: 170.0.h,
                floating: false,
                pinned: true,
                flexibleSpace: LayoutBuilder(
                  builder: (BuildContext context, BoxConstraints constraints) {
                    bool shouldShowTitle = 150.0 - constraints.maxHeight <= 20;
                    return FlexibleSpaceBar(
                      title: !shouldShowTitle
                          ? LabelWidget(
                              title: "Olá ${appController.userLogged!.nome!}",
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            )
                          : null,
                      background: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 20.h),
                            LabelWidget(
                              title: "Olá ${appController.userLogged!.nome!}",
                              fontSize: 23.sp,
                              fontWeight: FontWeight.w600,
                              textColor: whiteColor,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            SizedBox(height: 30.h),
                            const HomePdvSelectedWidget()
                          ],
                        ),
                      ),
                    );
                  },
                ),
                backgroundColor: themesController.getPrimaryColor()),
            SliverList(
              delegate: SliverChildListDelegate([
                if (settingsAppController.hasConfiguration()) ...[
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 13.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(height: 10.h),
                        LabelWidget(
                          title: 'Todos os pedidos',
                          fontSize: 14.sp,
                        ),
                        Visibility(
                          visible: settingsAppController
                              .settings.hasSentOrdersThisMonth!,
                          child: HomeDashboardCard(
                            label: 'Total de Pedidos Enviados / Mês',
                            value:
                                controller.totalSentOrdersThisMonth.toString(),
                            width: size.width,
                            cardBackgroundColor:
                                themesController.getSecondaryColor(),
                          ),
                        ),
                        Visibility(
                          visible: settingsAppController
                              .settings.hasTotalInvoicedThisMonth!,
                          child: HomeDashboardCard(
                            label: 'Valor Total Faturado / Mês',
                            value: controller.totalInvoicedThisMonthParsed!,
                            width: size.width,
                            cardBackgroundColor:
                                themesController.getPrimaryColor(),
                          ),
                        ),
                        Visibility(
                          visible: settingsAppController
                              .settings.hasTotalRequestedThisMonth!,
                          child: HomeDashboardCard(
                            label: 'Valor Total Solicitado / Mês',
                            value:
                                controller.totalValueRequestedThisMonthParsed!,
                            width: size.width,
                            cardBackgroundColor:
                                themesController.getPrimaryColor(),
                          ),
                        ),
                        SizedBox(height: 10.h),
                        if (settingsAppController
                                .settings.hasPDVOrdersSentThisMonth! ||
                            settingsAppController
                                .settings.hasPDVOrdersRequestedThisMonth!) ...[
                          LabelWidget(
                            title:
                                'Pedidos do PDV ${globalParams.getCurrentStore() != null ? globalParams.getCurrentStore()!.nomeFantasia : ''}',
                            fontSize: 14.sp,
                          ),
                          SizedBox(height: 10.h),
                          Row(
                            children: [
                              Visibility(
                                visible: settingsAppController
                                    .settings.hasPDVOrdersSentThisMonth!,
                                child: Flexible(
                                  child: HomeDashboardCard(
                                    width: size.width / 2,
                                    label:
                                        'Total de Pedidos Enviados PDV / Mês',
                                    value: controller
                                        .totalPDVOrdersSentThisMonth
                                        .toString(),
                                    cardBackgroundColor:
                                        themesController.getSecondaryColor(),
                                  ),
                                ),
                              ),
                              Visibility(
                                visible: settingsAppController
                                    .settings.hasPDVOrdersRequestedThisMonth!,
                                child: Flexible(
                                  child: HomeDashboardCard(
                                    width: size.width / 2,
                                    label: 'Valor Total Solicitado PDV / Mês',
                                    value: controller
                                        .totalValuePDVRequestedThisMonthParsed!,
                                    cardBackgroundColor:
                                        themesController.getPrimaryColor(),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
                  ),
                ] else
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Gap(20),
                      Center(
                        child: LabelWidget(
                          title: 'Habilite as dashboards nas configurações',
                          fontSize: DeviceSize.fontSize(14, 18),
                        ),
                      ),
                    ],
                  ),
              ]),
            ),
          ],
        );
      }),
      bottomSheet: const VersionWidget(),
    );
  }
}
