import 'package:pharmalink/core/extensions/export.dart';
import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/core/http_result/http_response.dart';
import 'package:pharmalink/core/http_result/http_result.dart';
import 'package:pharmalink/core/utils/constants.dart';
import 'package:pharmalink/modules/orders_ideal_mix/models/orders_ideal_mix_verify_model.dart';
import 'package:pharmalink/modules/orders_ideal_mix/models/orders_ideal_mix_model.dart';
import 'package:pharmalink/modules/orders_ideal_mix/models/orders_ideal_mix_request_model.dart';

abstract class IOrdersIdealMixApi {
  Future<HttpResult<MixIdealModel>> getIdealMix(
      {required MixIdealRequestModel model});

  Future<HttpResponse<MixIdealVerifiyModel>> getIdealMixVerify(
      {required MixIdealRequestModel model});
}

class OrdersIdealMixApi extends IOrdersIdealMixApi {
  final HttpManager _httpManager;
  OrdersIdealMixApi(this._httpManager);

  @override
  Future<HttpResult<MixIdealModel>> getIdealMix(
      {required MixIdealRequestModel model}) async {
    final result = await _httpManager.restRequest(
        url: 'mixideal/condicao/listar/porloja',
        method: HttpMethods.post,
        body: model.toJson());

    if (result.data.isNotEmpty && result.statusCode!.isStatusOk()) {
      return HttpResult.sucess(MixIdealModel.fromJson(result.data));
    } else {
      return HttpResult.error(getOthersStatusCodes(result));
    }
  }

  @override
  Future<HttpResponse<MixIdealVerifiyModel>> getIdealMixVerify(
      {required MixIdealRequestModel model}) async {
    return await _httpManager.request<MixIdealVerifiyModel>(
      path: 'mixideal/condicao/verificar/porloja',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        return MixIdealVerifiyModel.fromJson(data);
      },
    );
  }
}
