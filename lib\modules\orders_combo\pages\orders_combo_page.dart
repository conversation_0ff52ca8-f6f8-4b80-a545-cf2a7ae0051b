import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders/pages/widgets/footer_item.dart';
import 'package:pharmalink/modules/orders_combo/pages/widgets/orders_combo_combo_widget.dart';

class OrdersComboPage extends StatelessWidget {
  const OrdersComboPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<OrdersComboController>("Orders Combo",
        builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelWidget(
                title: ordersController.isEditOrder == true
                    ? ordersController.pdvName!
                    : globalParams.getCurrentStore()?.razaoSocial ?? "-",
                fontSize: DeviceSize.fontSize(14, 18),
                fontWeight: FontWeight.w600,
                textColor: whiteColor,
              ),
              LabelWidget(
                title: ordersController.isEditOrder == true
                    ? ordersController.pdvCnpj!
                    : globalParams.getCurrentStore()?.cNPJ ?? "-",
                fontSize: DeviceSize.fontSize(11, 13),
                textColor: whiteColor,
              ),
            ],
          ),
          actions: [
            CustomInkWell(
              onTap: () async => ctrl.advance(),
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 8, vertical: 16),
                child: LabelWidget(
                  title: "Avançar".toUpperCase(),
                  fontSize: 16.sp,
                  textColor: whiteColor,
                ),
              ),
            ),
          ],
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              GetC.close();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Column(
            children: [
              OrdersCardInfoWidget(
                orderType: globalParams.getTypeOrderName(),
                tabloidName: globalParams.order.tabloidName,
                distributors: globalParams.order.currentDistributors
                    ?.map((e) =>
                        e.distribuidor?.razaoSocial ??
                        e.distribuidor?.nomeFantasia ??
                        "Sem identificação")
                    .toList(),
                paymentType: globalParams.order.deadlinePayment?.descricao,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: SearchTextFieldWidget(
                  label: "Pesquisa de combo",
                  hintText: 'Pesquise pelo nome ou EAN do produto',
                  borderColor: themesController.getIconColor(),
                  iconColor: themesController.getIconColor(),
                  iconSize: 18,
                  labelColor: Colors.grey,
                  labelFontSize: DeviceSize.fontSize(14, 18),
                  keyboardType: TextInputType.text,
                  controller: ctrl.searchController,
                  onChanged: ctrl.setSearchFilter,
                  trailingIcon: (ctrl.searchFilter != null &&
                          ctrl.searchFilter!.isNotEmpty)
                      ? FontAwesomeIcons.xmark
                      : null,
                  trailingTap: () {
                    ctrl.setSearchFilter(null);
                  },
                ),
              ),
              24.toHeightSpace(),
              ...ctrl.combosOferta.map((e) => OrdersComboCardWidget(combo: e)),
            ],
          ),
        ),
        bottomNavigationBar: Container(
          color: themesController.getPrimaryColor(),
          height: DeviceSize.height(70, 90),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal, // Habilita a rolagem horizontal
            child: Padding(
              padding: EdgeInsets.symmetric(
                  horizontal: DeviceSize.width(8, 16),
                  vertical: DeviceSize.height(10, 16)),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(
                    width: DeviceSize.width(85, 105),
                    child: OrderFooterItem(
                      title: AppStrings.orderFooterTotalApresentation,
                      value: ctrl.footer.totalApresentation!.toString(),
                    ),
                  ),
                  SizedBox(
                    width: DeviceSize.width(75, 95),
                    child: OrderFooterItem(
                      title: AppStrings.orderFooterTotalUnits,
                      value: ctrl.footer.totalUnits!.toString(),
                    ),
                  ),
                  SizedBox(
                    width: DeviceSize.width(75, 95),
                    child: OrderFooterItem(
                      title: AppStrings.orderFooterQtdyReal,
                      value: ctrl.footer.qtyReal!.toString(),
                    ),
                  ),
                  if (settingsAppController.settings.hasAverageDiscount == true)
                    SizedBox(
                      width: DeviceSize.width(90, 145),
                      child: OrderFooterItem(
                        title: AppStrings.orderFooterDiscount,
                        value: ctrl.footer.discount!.formatPercent(),
                      ),
                    ),
                  SizedBox(
                    width: DeviceSize.width(120, 165),
                    child: OrderFooterItem(
                      title: AppStrings.orderFooterTotalNet,
                      value: ctrl.footer.totalGross!.formatReal(),
                    ),
                  ),
                  SizedBox(
                    width: DeviceSize.width(120, 165),
                    child: OrderFooterItem(
                      title: AppStrings.orderFooterTotalNet2,
                      value: ctrl.footer.totalNet!.formatReal(),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}
