import 'package:dio/dio.dart' as dio;
import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/core/models/result_error.dart';

extension BoolExtensionsString on String {}

extension BoolExtensionsInt on int {
  bool isStatusOk() {
    return (this >= 200 && this < 205);
  }

  bool isStatus400() {
    return (this == 400);
  }

  bool isStatus404() {
    return (this == 404);
  }

  bool isStatus401() {
    return (this == 401);
  }

  bool isStatus500() {
    return (this == 500);
  }

  String formatVersionCode() {
    String versionString = toString();
    return '${versionString.substring(0, 1)}.${versionString.substring(1, 3)}.${versionString.substring(3)}';
  }
}

extension ErrorExtensionsResponse on dio.Response {
  ResultError getOthers() {
    switch (statusCode!) {
      case 401:
        // authController.signOut();
        Future.delayed(Duration.zero, () async {
          await loginController.logoutForce();
        });
        return ResultError(
            error: 'Sem autorização, autentique-se novamente',
            code: statusCode!);
      case 302:
      case 404:
        Future.delayed(Duration.zero, () async {
          await loginController.logoutForce();
        });
        return ResultError(error: statusMessage!, code: statusCode!);
      default:
        return ResultError(error: statusMessage!, code: statusCode!);
    }
  }
}
