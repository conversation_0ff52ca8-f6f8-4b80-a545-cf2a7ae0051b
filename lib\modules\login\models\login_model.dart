class LoginModel {
  String? userName;
  String? password;
  String? grantType;

  LoginModel({
    this.userName,
    this.password,
    this.grantType,
  });

  LoginModel.fromJson(Map<String, dynamic> json) {
    userName = json['userName'];
    password = json['password'];
    grantType = json['grant_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['UserName'] = userName;
    data['Password'] = password;
    data['grant_type'] = grantType ?? 'password';

    return data;
  }
}
