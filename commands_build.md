# Guia de Configuração e Build do Projeto Flutter

Este guia tem como objetivo ajudar usuários que não estão familiarizados com Flutter a rodar o projeto e gerar a APK (tanto para **release** quanto para **debug**).
**Atenção:** Certifique-se de que sua instalação do Flutter está na versão **3.22.2** e o Dart SDK na versão **3.4.3**.
Você pode verificar a versão instalada executando:

```bash
flutter --version
```

## Estrutura de Pastas e Arquivos Necessários

Antes de iniciar, verifique se o código-fonte do projeto contém os seguintes arquivos e pastas:

### Na pasta raiz do projeto:

Deve existir uma pasta chamada `keystore` contendo os arquivos:
- `keysInterPlayers.jks`
- `pharmalink.jks`

### Na pasta android:

Deve conter o arquivo `key.properties` com o seguinte conteúdo:


## Pré-Requisitos

- **Flutter SDK:** Versão 3.22.2
- **Dart SDK:** Versão 3.4.3

Se você ainda não possui o Flutter instalado, acesse a [documentação oficial do Flutter](https://docs.flutter.dev/get-started/install) e siga as instruções de instalação para o seu sistema operacional.

Para verificar sua instalação, execute:

## Passo a Passo para Build do Projeto
### 1. Baixando o Projeto

### Opção 1: Clonar o Repositório
Utilize o Git para clonar o repositório:
```
git clone <URL_DO_REPOSITORIO>
```
---
### Opção 2: Download do ZIP
Faça o download do arquivo ZIP do projeto e extraia-o em uma pasta no seu computador.

### 2. Preparando o Projeto
Abra o Terminal:
Você pode usar o Prompt de Comando, PowerShell ou o terminal integrado do VS Code.

Navegue até a Pasta Raiz do Projeto:
Certifique-se de que o arquivo pubspec.yaml está presente nessa pasta.

Limpe Builds Anteriores:

Execute:

```
flutter clean
```

Atualize as Dependências:


Execute:

```
flutter pub get
```
---
### 3. Gerando a APK
Você pode gerar a APK para release (versão final) ou para debug (para testes). Use os comandos abaixo:

Para gerar a APK em modo Release:
```
flutter build apk
```
### Localização da APK gerada:
Geralmente, o arquivo estará em:
build/app/outputs/apk/release/app-release.apk
Para gerar a APK em modo Debug:
```
flutter build apk --debug
```
Localização da APK gerada:
Geralmente, o arquivo estará em:
```
build/app/outputs/apk/debug/app-debug.apk
```

---

### Dicas Finais
Verifique as Versões:
Garanta que o Flutter esteja na versão 3.22.2 e o Dart na versão 3.4.3. Caso contrário, atualize sua instalação conforme necessário.

Problemas na Build:
Se ocorrerem erros, verifique as mensagens do terminal e execute:

```
flutter doctor
```
para identificar problemas de configuração.

### Solicitação de Arquivos:
Se algum dos arquivos obrigatórios (como o key.properties ou os arquivos da pasta keystore) estiver faltando, entre em contato com o responsável pelo projeto para obtê-los.

---
