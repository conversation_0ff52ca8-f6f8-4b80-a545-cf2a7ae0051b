import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class StoreRoutesTabWidget extends StatelessWidget {
  const StoreRoutesTabWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StoreRoutesController>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              color: Colors.white,
              child: Row(
                children: [
                  Expanded(
                    child: _buildTabItem(
                      title:
                          "Meu Painel ${ctrl.cnpjPanel != null ? " (${ctrl.cnpjPanel})" : ""}",
                      isSelected: ctrl.selectedTab == 0,
                      onTap: () => ctrl.changeTab(0),
                    ),
                  ),
                  const Gap(10),
                  Expanded(
                    child: _buildTabItem(
                      title:
                          "Planejados ${ctrl.cnpjPlanned != null ? " (${ctrl.cnpjPlanned})" : ""}",
                      isSelected: ctrl.selectedTab == 1,
                      onTap: () => ctrl.changeTab(1),
                    ),
                  ),
                ],
              ),
            ),
            const Gap(10),
            // Tab Content
            Expanded(
              child: IndexedStack(
                index: ctrl.selectedTab,
                children: const [
                  StoreRoutesPanelPage(),
                  StoreRoutesPlannedPage(),
                ],
              ),
            ),
          ],
        ),
      );
    });
  }

  Widget _buildTabItem({
    required String title,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 16),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: isSelected
                  ? themesController.getPrimaryColor()
                  : Colors.grey.shade400,
              width: 2,
            ),
          ),
        ),
        child: Center(
          // Center the title
          child: LabelWidget(
            title: title,
            fontSize: DeviceSize.fontSize(14, 16),
            textColor: isSelected
                ? themesController.getPrimaryColor()
                : Colors.grey.shade400,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }
}
