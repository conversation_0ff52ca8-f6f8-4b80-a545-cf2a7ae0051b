import 'dart:developer';
import 'dart:io';

import 'package:flutter/services.dart';

class MemoryInfo {
  static const MethodChannel _channel = MethodChannel('memory_info');

  static Future<String> getMemoryUsage() async {
    try {
      if (Platform.isAndroid) {
        final int memoryUsage = await _channel.invokeMethod('getMemoryUsage');
        return 'RAM: ${(memoryUsage / (1024 * 1024)).toStringAsFixed(2)} MB';
      } else if (Platform.isIOS) {
        final memoryUsage = await _channel.invokeMethod('getMemoryUsage');
        return 'RAM: ${(memoryUsage / (1024 * 1024)).toStringAsFixed(2)} MB';
      }
    } on PlatformException catch (e) {
      log("Failed to get memory usage: '${e.message}'.");
    }
    return 'RAM: 0 MB';
  }
}
