class PlanejamentoVisitaStatus {
  String? descricao;
  int? idPlanejamentoVisitaStatus;

  PlanejamentoVisitaStatus({this.descricao, this.idPlanejamentoVisitaStatus});

  PlanejamentoVisitaStatus.fromJson(Map<String, dynamic> json) {
    descricao = json['Descricao'];
    idPlanejamentoVisitaStatus = json['IdPlanejamentoVisitaStatus'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Descricao'] = descricao;
    data['IdPlanejamentoVisitaStatus'] = idPlanejamentoVisitaStatus;
    return data;
  }
}
