import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/core/http_result/http_response.dart';
import 'package:pharmalink/modules/themes/models/themes_model.dart';

abstract class IThemesApi {
  Future<HttpResponse<List<ThemesModel>>> getThemes();
}

class ThemesApi extends IThemesApi {
  final HttpManager _httpManager;
  ThemesApi(this._httpManager);

  @override
  Future<HttpResponse<List<ThemesModel>>> getThemes() async {
    return await _httpManager.request<List<ThemesModel>>(
      path: 'temas',
      method: HttpMethods.get,
      parser: (data) {
        //se não for lista
        //return ThemesModel.fromJson(data);
        if (data is List) {
          return data.map((item) => ThemesModel.fromJson(item)).toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }
}
