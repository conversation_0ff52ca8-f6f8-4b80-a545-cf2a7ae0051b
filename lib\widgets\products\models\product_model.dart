import 'package:pharmalink/modules/orders/models/orders_products_model.dart';

class ProductDetailModel {
  final String name;
  final String code;
  final String codeLabel;
  final String? photo;
  final int? qtyMin;
  final int? qtyReal;
  final double? discountRep;
  final double? discountManager;
  final double? discountApply;
  final String? priceDistributor;
  final int? stock;
  final String? discountRange;
  final double? discountBase;
  final double? discountNegotiated;
  final double? discountNegotiation;
  final MetricaMdtr? metricaMdtr;
  ProductDetailModel({
    required this.name,
    required this.code,
    required this.codeLabel,
    this.photo,
    this.qtyMin,
    this.qtyReal,
    this.discountRep,
    this.discountManager,
    this.discountApply,
    this.priceDistributor,
    this.stock,
    this.discountRange,
    this.discountBase,
    this.discountNegotiated,
    this.discountNegotiation,
    this.metricaMdtr,
  });
}
