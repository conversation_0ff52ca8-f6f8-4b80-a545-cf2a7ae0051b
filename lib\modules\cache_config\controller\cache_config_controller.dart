import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';

class CacheConfigController extends GetxController {
  CacheConfigController();

  Future<void> resetCache() async {
    await dbContext.clearDataBase();
    appController.workspace = null;
    appController.userLogged = null;
    appController.userLogout = null;
    Get.offAllNamed(RoutesPath.initialize);
  }
}
