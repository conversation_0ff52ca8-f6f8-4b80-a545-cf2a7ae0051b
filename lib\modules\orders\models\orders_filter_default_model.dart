import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class ProductFilterDefaultModel extends SqfLiteBase<ProductFilterDefaultModel> {
  String? descricao;
  int? idCategoriaFiltrosPersonalizados;
  List<FiltrosPersonalizados>? filtrosPersonalizados;

  ProductFilterDefaultModel(
      {this.descricao,
      this.idCategoriaFiltrosPersonalizados,
      this.filtrosPersonalizados})
      : super(DatabaseModels.productFilterDefaultModel);

  ProductFilterDefaultModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.productFilterDefaultModel) {
    descricao = json['Descricao'];
    idCategoriaFiltrosPersonalizados = json['IdCategoriaFiltrosPersonalizados'];
    if (json['FiltrosPersonalizados'] != null) {
      filtrosPersonalizados = <FiltrosPersonalizados>[];
      json['FiltrosPersonalizados'].forEach((v) {
        filtrosPersonalizados!.add(FiltrosPersonalizados.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Descricao'] = descricao;
    data['IdCategoriaFiltrosPersonalizados'] = idCategoriaFiltrosPersonalizados;
    if (filtrosPersonalizados != null) {
      data['FiltrosPersonalizados'] =
          filtrosPersonalizados!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  Future<ProductFilterDefaultModel?> getFirst(
      {required int workspaceId}) async {
    var list = await getAll<ProductFilterDefaultModel>(
        workspaceId: workspaceId, ProductFilterDefaultModel.fromJson);
    return list.isEmpty ? null : list.first;
  }

  Future<bool> exists({required int workspaceId}) async {
    var list = await getAll<ProductFilterDefaultModel>(
        workspaceId: workspaceId, ProductFilterDefaultModel.fromJson);
    return list.isNotEmpty;
  }

  Future<List<ProductFilterDefaultModel>> getList(
      {required int workspaceId}) async {
    var list = await getAll<ProductFilterDefaultModel>(
        workspaceId: workspaceId, ProductFilterDefaultModel.fromJson);
    return list;
  }
}

class FiltrosPersonalizados {
  bool? ativo;
  String? codigo;
  String? descricao;
  String? descricaoCategoria;
  String? icone;
  int? idCategoriaFiltroPersonalizado;
  int? idFiltroPersonalizado;

  FiltrosPersonalizados(
      {this.ativo,
      this.codigo,
      this.descricao,
      this.descricaoCategoria,
      this.icone,
      this.idCategoriaFiltroPersonalizado,
      this.idFiltroPersonalizado});

  FiltrosPersonalizados.fromJson(Map<String, dynamic> json) {
    ativo = json['Ativo'];
    codigo = json['Codigo'];
    descricao = json['Descricao'];
    descricaoCategoria = json['DescricaoCategoria'];
    icone = ""; //json['Icone'];
    idCategoriaFiltroPersonalizado = json['IdCategoriaFiltroPersonalizado'];
    idFiltroPersonalizado = json['IdFiltroPersonalizado'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Ativo'] = ativo;
    data['Codigo'] = codigo;
    data['Descricao'] = descricao;
    data['DescricaoCategoria'] = descricaoCategoria;
    data['Icone'] = icone;
    data['IdCategoriaFiltroPersonalizado'] = idCategoriaFiltroPersonalizado;
    data['IdFiltroPersonalizado'] = idFiltroPersonalizado;
    return data;
  }
}
