import 'package:dynatrace_flutter_plugin/dynatrace_flutter_plugin.dart';
import 'package:pharmalink/app_constants.dart';

import 'dynatrace_custom_sub_action.dart';

class DynatraceCustomAction {
  DynatraceRootAction rootAction;
  String prefix;

  final List<DynatraceAction> _subActionsList = [];

  DynatraceCustomAction(this.rootAction, this.prefix);

  void reportEvent(String eventName) {
    rootAction.reportEvent(eventName);
  }

  Future<void> reportClickEvent(
      String eventName, Future<void> Function()? onClick) async {
    reportEvent("Click - $eventName");
    if (onClick != null) {
      await onClick();
    }
  }

  (Function leaveAction, DynatraceCustomSubAction subAction) subActionReport(
      String tagIdentifier,
      {String? valueName,
        String? value}) {
    var subAction = rootAction.enterAction(tagIdentifier);

    if (valueName != null) {
      subAction.reportStringValue(valueName, value);
    }

    _subActionsList.add(subAction);

    var customSubAction = DynatraceCustomSubAction(this, subAction);

    leaveAction() {
      _subActionsList.remove(subAction);
      subAction.leaveAction();
    }

    return (leaveAction, customSubAction);
  }

  void leaveRemainingActions() {
    for (var element in _subActionsList) {
      element.leaveAction();
    }
    rootAction.leaveAction();
    _subActionsList.clear();
  }

  Future<void> reportZoneStacktrace(
      dynamic error, StackTrace stacktrace) async {
    await dynatrace.reportZoneStacktrace(error, stacktrace);
  }


  void reportError(String? errorName, {int? errorCode}) {
    rootAction.reportError(errorName, errorCode);
  }
}