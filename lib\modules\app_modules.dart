export 'package:pharmalink/config/databases/call_context.dart';
export 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
export 'package:pharmalink/modules/begin/controller/begin_controller.dart';
export 'package:pharmalink/modules/begin/pages/begin_page.dart';
export 'package:pharmalink/modules/cache_config/bindings/cache_config_bindings.dart';
export 'package:pharmalink/modules/cache_config/controller/cache_config_controller.dart';
export 'package:pharmalink/modules/cache_config/pages/cache_config_page.dart';
export 'package:pharmalink/modules/camera_picker/binding/camera_picker_binding.dart';
export 'package:pharmalink/modules/camera_picker/pages/camera_picker_page.dart';
export 'package:pharmalink/modules/camera_picker/pages/camera_picker_preview_page.dart';
export 'package:pharmalink/modules/connectivity/controller/connectivity_controller.dart';
export 'package:pharmalink/modules/general_parameterization/api/general_parameterization_api.dart';
export 'package:pharmalink/modules/general_parameterization/controller/general_parameterization_controller.dart';
export 'package:pharmalink/modules/home/<USER>/home_page.dart';
export 'package:pharmalink/modules/initialize/bindings/initialize_bindings.dart';
export 'package:pharmalink/modules/initialize/controller/initialize_controller.dart';
export 'package:pharmalink/modules/initialize/pages/initialize_page.dart';
export 'package:pharmalink/modules/log_trace_monitor/api/log_trace_monitor_api.dart';
export 'package:pharmalink/modules/log_trace_monitor/bindings/log_trace_monitor_bindings.dart';
export 'package:pharmalink/modules/log_trace_monitor/controller/log_trace_monitor_controller.dart';
export 'package:pharmalink/modules/log_trace_monitor/pages/log_trace_monitor_page.dart';
export 'package:pharmalink/modules/login/api/login_api.dart';
export 'package:pharmalink/modules/login/controller/login_controller.dart';
export 'package:pharmalink/modules/login/pages/login_page.dart';
export 'package:pharmalink/modules/login/pages/logout_b2c.dart';
export 'package:pharmalink/modules/logs_http/controller/logs_http_controller.dart';
export 'package:pharmalink/modules/logs_http/pages/logs_http_page.dart';
export 'package:pharmalink/modules/navigation_page/controller/navigation_page_controller.dart';
export 'package:pharmalink/modules/navigation_page/pages/navigation_page_page.dart';
export 'package:pharmalink/modules/order_payment_type/bindings/order_payment_type_bindings.dart';
export 'package:pharmalink/modules/order_payment_type/controller/order_payment_type_controller.dart';
export 'package:pharmalink/modules/order_payment_type/pages/order_payment_type_page.dart';
export 'package:pharmalink/modules/order_types/bindings/order_types_bindings.dart';
export 'package:pharmalink/modules/order_types/controller/order_types_controller.dart';
export 'package:pharmalink/modules/order_types/pages/order_types_page.dart';
export 'package:pharmalink/modules/orders/api/orders_api.dart';
export 'package:pharmalink/modules/orders/bindings/orders_bindings.dart';
export 'package:pharmalink/modules/orders/controller/orders_controller.dart';
export 'package:pharmalink/modules/orders/extensions/orders_extensions.dart';
export 'package:pharmalink/modules/orders/pages/orders_page.dart';
export 'package:pharmalink/modules/orders_combo/bindings/orders_combo_bindings.dart';
export 'package:pharmalink/modules/orders_combo/controller/orders_combo_controller.dart';
export 'package:pharmalink/modules/orders_combo/pages/orders_combo_page.dart';
export 'package:pharmalink/modules/orders_ideal_mix/api/orders_ideal_mix_api.dart';
export 'package:pharmalink/modules/orders_ideal_mix/bindings/orders_ideal_mix_bindings.dart';
export 'package:pharmalink/modules/orders_ideal_mix/controller/orders_ideal_mix_controller.dart';
export 'package:pharmalink/modules/orders_ideal_mix/pages/orders_ideal_mix_page.dart';
export 'package:pharmalink/modules/orders_resume/api/orders_resume_api.dart';
export 'package:pharmalink/modules/orders_resume/bindings/orders_resume_bindings.dart';
export 'package:pharmalink/modules/orders_resume/controller/orders_resume_controller.dart';
export 'package:pharmalink/modules/orders_resume/pages/orders_resume_page.dart';
export 'package:pharmalink/modules/orders_tabloid/bindings/orders_tabloid_bindings.dart';
export 'package:pharmalink/modules/orders_tabloid/controller/orders_tabloid_controller.dart';
export 'package:pharmalink/modules/orders_tabloid/pages/orders_tabloid_page.dart';
export 'package:pharmalink/modules/permission_global/bindings/permission_global_bindings.dart';
export 'package:pharmalink/modules/permission_global/controller/permission_global_controller.dart';
export 'package:pharmalink/modules/permission_global/pages/permission_global_page.dart';
export 'package:pharmalink/modules/permission_request/bindings/permission_request_bindings.dart';
export 'package:pharmalink/modules/permission_request/controller/permission_request_controller.dart';
export 'package:pharmalink/modules/permission_request/pages/permission_request_page.dart';
export 'package:pharmalink/modules/q_atester/bindings/q_atester_bindings.dart';
export 'package:pharmalink/modules/q_atester/controller/q_atester_controller.dart';
export 'package:pharmalink/modules/q_atester/pages/q_atester_page.dart';
export 'package:pharmalink/modules/report_contents/api/report_contents_api.dart';
export 'package:pharmalink/modules/report_contents/bindings/report_contents_bindings.dart';
export 'package:pharmalink/modules/report_contents/controller/report_contents_controller.dart';
export 'package:pharmalink/modules/report_contents/pages/report_contents_page.dart';
export 'package:pharmalink/modules/report_orders/api/report_orders_api.dart';
export 'package:pharmalink/modules/report_orders/bindings/report_orders_bindings.dart';
export 'package:pharmalink/modules/report_orders/controller/report_orders_controller.dart';
export 'package:pharmalink/modules/report_orders/pages/report_orders_detail_page.dart';
export 'package:pharmalink/modules/report_orders/pages/report_orders_page.dart';
export 'package:pharmalink/modules/report_orders_sync/bindings/report_orders_sync_bindings.dart';
export 'package:pharmalink/modules/report_orders_sync/controller/report_orders_sync_controller.dart';
export 'package:pharmalink/modules/report_orders_sync/pages/report_orders_sync_page.dart';
export 'package:pharmalink/modules/report_orders_upload/bindings/report_orders_upload_bindings.dart';
export 'package:pharmalink/modules/report_orders_upload/controller/report_orders_upload_controller.dart';
export 'package:pharmalink/modules/report_orders_upload/pages/report_orders_upload_page.dart';
export 'package:pharmalink/modules/reports/controller/reports_controller.dart';
export 'package:pharmalink/modules/reports/pages/reports_page.dart';
export 'package:pharmalink/modules/researches_complementary/api/researches_complementary_api.dart';
export 'package:pharmalink/modules/researches_complementary/bindings/researches_complementary_bindings.dart';
export 'package:pharmalink/modules/researches_complementary/controller/researches_complementary_controller.dart';
export 'package:pharmalink/modules/researches_complementary/pages/researches_complementary_page.dart';
export 'package:pharmalink/modules/researches_complementary/pages/researches_complementary_question_page.dart';
export 'package:pharmalink/modules/researches_merchan_competitive/api/researches_merchan_competitive_api.dart';
export 'package:pharmalink/modules/researches_merchan_competitive/bindings/researches_merchan_competitive_bindings.dart';
export 'package:pharmalink/modules/researches_merchan_competitive/controller/researches_merchan_competitive_controller.dart';
export 'package:pharmalink/modules/researches_merchan_competitive/pages/researches_merchan_competitive_page.dart';
export 'package:pharmalink/modules/researches_merchan_competitive/pages/researches_merchan_competitive_question_page.dart';
export 'package:pharmalink/modules/researches_merchan_industry/api/researches_merchan_industry_api.dart';
export 'package:pharmalink/modules/researches_merchan_industry/bindings/researches_merchan_industry_bindings.dart';
export 'package:pharmalink/modules/researches_merchan_industry/controller/researches_merchan_industry_controller.dart';
export 'package:pharmalink/modules/researches_merchan_industry/pages/researches_merchan_industry_page.dart';
export 'package:pharmalink/modules/researches_merchan_industry/pages/researches_merchan_industry_question_page.dart';
export 'package:pharmalink/modules/researches_product_concurrent/api/researches_product_concurrent_api.dart';
export 'package:pharmalink/modules/researches_product_concurrent/bindings/researches_product_concurrent_bindings.dart';
export 'package:pharmalink/modules/researches_product_concurrent/controller/researches_product_concurrent_controller.dart';
export 'package:pharmalink/modules/researches_product_concurrent/pages/researches_product_concurrent_page.dart';
export 'package:pharmalink/modules/researches_product_industry/api/researches_product_industry_api.dart';
export 'package:pharmalink/modules/researches_product_industry/bindings/researches_product_industry_bindings.dart';
export 'package:pharmalink/modules/researches_product_industry/controller/researches_product_industry_controller.dart';
export 'package:pharmalink/modules/researches_product_industry/pages/researches_product_industry_page.dart';
export 'package:pharmalink/modules/researches_share_of_shelf/api/researches_share_of_shelf_api.dart';
export 'package:pharmalink/modules/researches_share_of_shelf/bindings/researches_share_of_shelf_bindings.dart';
export 'package:pharmalink/modules/researches_share_of_shelf/controller/researches_share_of_shelf_controller.dart';
export 'package:pharmalink/modules/researches_share_of_shelf/pages/researches_share_of_shelf_page.dart';
export 'package:pharmalink/modules/researches_share_of_shelf/pages/researches_share_of_shelf_photo_area_page.dart';
export 'package:pharmalink/modules/researches_share_of_shelf/pages/researches_share_of_shelf_photo_crop_page.dart';
export 'package:pharmalink/modules/researches_trade_marketing/api/researches_trade_marketing_api.dart';
export 'package:pharmalink/modules/researches_trade_marketing/bindings/researches_trade_marketing_bindings.dart';
export 'package:pharmalink/modules/researches_trade_marketing/controller/researches_trade_marketing_controller.dart';
export 'package:pharmalink/modules/researches_trade_marketing/pages/researches_trade_camera_maintenance_detail_page.dart';
export 'package:pharmalink/modules/researches_trade_marketing/pages/researches_trade_camera_maintenance_list_page.dart';
export 'package:pharmalink/modules/researches_trade_marketing/pages/researches_trade_marketing_page.dart';
export 'package:pharmalink/modules/routes/api/routes_api.dart';
export 'package:pharmalink/modules/routes/controller/routes_controller.dart';
export 'package:pharmalink/modules/settings/pages/settings_page.dart';
export 'package:pharmalink/modules/settings_app/controller/settings_app_controller.dart';
export 'package:pharmalink/modules/settings_app/pages/settings_app_dashboard_page.dart';
export 'package:pharmalink/modules/settings_app/pages/settings_app_orders_page.dart';
export 'package:pharmalink/modules/settings_app/pages/settings_app_page.dart';
export 'package:pharmalink/modules/splashscreen/controller/splash_controller.dart';
export 'package:pharmalink/modules/splashscreen/pages/splash_page.dart';
export 'package:pharmalink/modules/store_routes/api/synchronizations_offline_api.dart';
export 'package:pharmalink/modules/store_routes/bindings/store_routes_bindings.dart';
export 'package:pharmalink/modules/store_routes/controller/store_routes_controller.dart';
export 'package:pharmalink/modules/store_routes/pages/store_routes_page.dart';
export 'package:pharmalink/modules/store_routes/services/store_routes_offline_services.dart';
export 'package:pharmalink/modules/store_routes/services/store_routes_sync_services.dart';
export 'package:pharmalink/modules/store_routes_details/bindings/store_routes_details_bindings.dart';
export 'package:pharmalink/modules/store_routes_details/controller/store_routes_details_controller.dart';
export 'package:pharmalink/modules/store_routes_details/pages/store_routes_details_page.dart';
export 'package:pharmalink/modules/store_routes_panel/bindings/store_routes_panel_bindings.dart';
export 'package:pharmalink/modules/store_routes_panel/controller/store_routes_panel_controller.dart';
export 'package:pharmalink/modules/store_routes_panel/pages/store_routes_panel_page.dart';
export 'package:pharmalink/modules/store_routes_planned/bindings/store_routes_planned_bindings.dart';
export 'package:pharmalink/modules/store_routes_planned/controller/store_routes_planned_controller.dart';
export 'package:pharmalink/modules/store_routes_planned/pages/store_routes_planned_page.dart';
export 'package:pharmalink/modules/stores/api/stores_api.dart';
export 'package:pharmalink/modules/stores_parameters/api/store_parameters_api.dart';
export 'package:pharmalink/modules/stores_parameters/controller/store_parameters_controller.dart';
export 'package:pharmalink/modules/synchronizations/controller/synchronizations_controller.dart';
export 'package:pharmalink/modules/synchronizations/pages/synchronizations_page.dart';
export 'package:pharmalink/modules/themes/api/themes_api.dart';
export 'package:pharmalink/modules/themes/controller/themes_controller.dart';
export 'package:pharmalink/modules/visits/api/visits_api.dart';
export 'package:pharmalink/modules/visits/bindings/visits_bindings.dart';
export 'package:pharmalink/modules/visits/controller/visits_controller.dart';
export 'package:pharmalink/modules/visits/pages/components/visitis_camera.dart';
export 'package:pharmalink/modules/visits/pages/visits_page.dart';
export 'package:pharmalink/modules/workspaces/api/workspaces_api.dart';
export 'package:pharmalink/modules/workspaces/controller/workspaces_controller.dart';
export 'package:pharmalink/modules/workspaces/pages/workspaces_page.dart';
