import 'dart:async';

import 'package:pharmalink/app/app_exports.dart';
import 'package:pharmalink/core/os/memory_info.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';

class VersionWidget extends StatefulWidget {
  const VersionWidget({
    super.key,
    this.showWorkspace = false,
    this.showVersion = true,
    this.showInternetStatus = true,
  });

  final bool showWorkspace;
  final bool showVersion;
  final bool showInternetStatus;

  @override
  State<VersionWidget> createState() => _VersionWidgetState();
}

class _VersionWidgetState extends State<VersionWidget> {
  String memoryUsage = 'RAM: 0 MB';
  Timer? _timer;

  @override
  void initState() {
    getMemoryUsage();
    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel(); // Cancel the timer
    super.dispose();
  }

  Future<void> getMemoryUsage() async {
    if (appController.currentEnvironment != Environment.production) {
      final currentMemoryUsage = await MemoryInfo.getMemoryUsage();
      setState(() {
        memoryUsage = currentMemoryUsage;
      });

      _timer = Timer.periodic(const Duration(seconds: 15), (timer) async {
        final currentMemoryUsage = await MemoryInfo.getMemoryUsage();
        if (mounted) {
          // Check if the widget is still mounted
          setState(() {
            memoryUsage = currentMemoryUsage;
          });
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.showWorkspace && widget.showVersion
        ? Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              widget.showVersion
                  ? LabelWidget(
                      title: loginController.versionApp,
                      textColor: loginController.versionColor,
                      fontWeight: FontWeight.bold,
                    )
                  : 1.horizontalSpace,
              const Gap(20),
              widget.showWorkspace
                  ? Flexible(
                      child: LabelWidget(
                        title: "Workspace: ${appController.workspace?.name!}",
                      ),
                    )
                  : 1.horizontalSpace,
              const Gap(20),
              IconButton(
                onPressed: () {
                  Get.toNamed(RoutesPath.logTraceMonitor);
                },
                icon: const Icon(
                  FontAwesomeIcons.squarePollHorizontal,
                  color: Colors.grey,
                ),
              ),
            ],
          )
        : Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                widget.showVersion
                    ? LabelWidget(
                        title: loginController.versionApp,
                        textColor: loginController.versionColor,
                        fontWeight: FontWeight.bold,
                      )
                    : 1.horizontalSpace,
                if (appController.currentEnvironment != Environment.production)
                  LabelWidget(
                    title: memoryUsage,
                    textColor: loginController.versionColor,
                    fontWeight: FontWeight.bold,
                  ),
                widget.showInternetStatus
                    ? GetBuilder<AppController>(builder: (ctrl) {
                        return LabelWidget(
                          title: ctrl.internetMode,
                          textColor: loginController.versionColor,
                          fontWeight: FontWeight.bold,
                        );
                      })
                    : 1.horizontalSpace,
              ],
            ),
          );
  }
}
