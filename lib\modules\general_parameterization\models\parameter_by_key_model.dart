import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class ParameterByKeyModel extends SqfLiteBase<ParameterByKeyModel> {
  String? chave;
  String? valor;
  bool? enabled;

  ParameterByKeyModel({
    this.chave,
    this.valor,
    this.enabled,
  }) : super(DatabaseModels.parameterByKeyModel);

  ParameterByKeyModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.parameterByKeyModel) {
    chave = json['Chave'];
    valor = json['Valor'];
    enabled = json['Valor'] == "1" ? true : false;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['Chave'] = chave;
    data['Valor'] = valor;
    return data;
  }

  Future<ParameterByKeyModel?> getFirst({required String key}) async {
    var list = await getAll<ParameterByKeyModel>(
        workspaceId: appController.workspace?.workspaceId,
        hashCode: key,
        userId: appController.userLogged!.userId,
        ParameterByKeyModel.fromJson);
    return list.isEmpty ? null : list.first;
  }

  Future<bool> exists({required String key}) async {
    var list = await getAll<ParameterByKeyModel>(
        workspaceId: appController.workspace?.workspaceId,
        hashCode: key,
        userId: appController.userLogged!.userId,
        ParameterByKeyModel.fromJson);
    return list.isNotEmpty;
  }

  Future<List<ParameterByKeyModel>> getList({required String key}) async {
    var list = await getAll<ParameterByKeyModel>(
        workspaceId: appController.workspace?.workspaceId,
        hashCode: key,
        userId: appController.userLogged!.userId,
        ParameterByKeyModel.fromJson);
    return list;
  }
}
