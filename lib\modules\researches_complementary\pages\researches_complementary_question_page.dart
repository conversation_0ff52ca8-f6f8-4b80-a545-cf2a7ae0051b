import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class ResearchesComplementaryQuestionPage extends StatelessWidget {
  const ResearchesComplementaryQuestionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ResearchesComplementaryController>(
        "ResearchesComplementaryController", builder: (ctx) {
      return Scaffold(
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: LabelWidget(
            title: ctx.selected!.titulo!,
            fontSize: ctx.selected!.titulo!.length < 70
                ? DeviceSize.fontSize(14, 18)
                : DeviceSize.fontSize(10, 12),
            fontWeight: FontWeight.bold,
            overflow: TextOverflow.ellipsis,
            maxLines: 4,
          ),
          leading: Icon<PERSON>utton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              if (!ctx.selected!.isAnswered!) {
                showDialog(
                  context: Get.context!,
                  barrierDismissible: false,
                  builder: ((context) => AlertDialog(
                        title: const Text(
                            "Deseja mesmo fechar a pesquisa complementar?"),
                        content: const Text(
                            "Ao sair toda a sua pesquisa se perderá"),
                        actions: [
                          TextButton(
                            child: const Text('Cancelar'),
                            onPressed: () {
                              GetC.close();
                            },
                          ),
                          TextButton(
                            onPressed: () {
                              ctx.clearComplementaryResearch();
                              Navigator.of(context).pop();
                              GetC.close();
                            },
                            child: const Text("Confirmar"),
                          )
                        ],
                      )),
                );
              } else {
                Navigator.of(context).pop();
              }
            },
          ),
          actions: [
            if (ctx.selected!.isSync != SyncEnum.finished)
              IconButton(
                onPressed: () async {
                  await ctx.saveResearch();
                },
                icon: const Icon(
                  Icons.save,
                  color: whiteColor,
                  //size: 16,
                ),
              )
          ],
          elevation: 0,
        ),
        body: Container(
          color: Colors.white10,
          width: double.infinity,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                Expanded(
                  child: ListView.builder(
                    itemBuilder: (_, index) {
                      return ctx.questionsWidgets[index];
                    },
                    itemCount: ctx.questionsWidgets.length,
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
