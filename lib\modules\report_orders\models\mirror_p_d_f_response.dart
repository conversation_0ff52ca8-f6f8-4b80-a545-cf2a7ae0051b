class MirrorPDFResponse {
  int? idPedido;
  String? filePdf;
  String? fileName;

  MirrorPDFResponse({
    this.idPedido,
    this.filePdf,
    this.fileName,
  });

  MirrorPDFResponse.fromJson(Map<String, dynamic> json) {
    idPedido = json['IdPedido'];
    filePdf = json['FilePdf'];
    fileName = json['FileName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPedido'] = idPedido;
    data['FilePdf'] = filePdf;
    data['FileName'] = fileName;
    return data;
  }
}
