#!/usr/bin/env bash

trap "kill 0" SIGINT


source "$(dirname "$0")/bootstrap.sh"

# <PERSON><PERSON> padrão (opcional)
GENERATE=""
FLAVOR=""
ENVIRONMENT=""
MODE=""
MODE_XCODE=""
OUTPUT_DIR=""
VERSION=""
BUILD_ALL=0
AUTO_PUBLISH_RELEASE=0
AUTO_PUBLISH_TEST=0

# Função para exibir ajuda
usage() {
  colored_print red "Uso: $(basename "$0") -g|--generate <apk,aab,appbundle,ipa,ios> -f|--flavor <pharmalink|itrade> -e|--env|--environment <developer|staging|production> -p|--auto-publish-release -t|--auto-publish-test"
  colored_print red "Uso: $(basename "$0") -a|--all"
  exit 1
}

# Parse de argumentos
colored_print yellow "Analisando argumentos..."
colored_print yellow "Argumentos recebidos: $*"
while [[ "$#" -gt 0 ]]; do
  case $1 in
    -p|--auto-publish-release)
      AUTO_PUBLISH_RELEASE=1
      shift 1
      ;;
    -t|--auto-publish-test)
      AUTO_PUBLISH_TEST=1
      shift 1
      ;;
    -a|--all)
      BUILD_ALL=1
      shift 1
      ;;
    -g|--generate)
      case $2 in
        aab|appbundle)
          GENERATE="appbundle"
          ;;
        ipa|ios)
          GENERATE="ipa"
          ;;
        *)
          GENERATE="$2"
          ;;
      esac
      shift 2
      ;;
    -f|--flavor)
      FLAVOR="$2"
      shift 2
      ;;
    -e|--env|--environment)
      ENVIRONMENT="$2"
      shift 2
      ;;
    -*)
      echo "❌ Opção desconhecida: $1"
      usage
      ;;
    *)
      echo "❌ Argumento inválido: $1"
      usage
      ;;
  esac
done

# Se BUILD_ALL for 1, executa todas as builds possiveis
if [[ $BUILD_ALL -eq 1 ]]; then
  colored_print green "Executando todas as builds possíveis..."

  for _GENERATE in apk aab ipa; do
    for _FLAVOR in pharmalink itrade; do
      for _ENVIRONMENT in developer staging production; do
        colored_print green "Iniciando build para: ${_GENERATE}, ${_FLAVOR}, ${_ENVIRONMENT}"
        
        # Chama o script com os parâmetros necessários
        "$0" --generate "$_GENERATE" --flavor "$_FLAVOR" --environment "$_ENVIRONMENT"
        if [[ $? -ne 0 ]]; then
          colored_print red "❌ Build falhou para: ${_GENERATE}, ${_FLAVOR}, ${_ENVIRONMENT}"
          exit 1
        fi
        
        colored_print green "✅ Build concluída com sucesso para: ${_GENERATE}, ${_FLAVOR}, ${_ENVIRONMENT}"
      done
    done
  done
  
  colored_print green "✅ Todas as builds foram executadas com sucesso!"
  
  exit 0
fi

# Validação obrigatória
if [[ -z "$GENERATE" || -z "$FLAVOR" || -z "$ENVIRONMENT" ]]; then
  colored_print red "❌ Faltam parâmetros obrigatórios."
  usage
fi

# Validação de valores
if [[ "$GENERATE" != "apk" && "$GENERATE" != "aab" && "$GENERATE" != "appbundle" && "$GENERATE" != "ipa" && "$GENERATE" != "ios" ]]; then
  colored_print red  "❌ Tipo inválido: ${GENERATE}. Use 'apk', 'aab', 'appbundle', 'ipa' ou 'ios'."
  usage
fi

if [[ "$FLAVOR" != "pharmalink" && "$FLAVOR" != "itrade" ]]; then
  colored_print red  "❌ Flavor inválido: ${FLAVOR}. Use 'pharmalink' ou 'itrade'."
  usage
fi

if [[ "$ENVIRONMENT" != "developer" && "$ENVIRONMENT" != "staging" && "$ENVIRONMENT" != "production" ]]; then
  colored_print red  "❌ Ambiente inválido: ${ENVIRONMENT}. Use 'developer', 'staging' ou 'production'."
  usage
fi

case $ENVIRONMENT in
  production)
    MODE_XCODE="Release"
    MODE="release"
  ;;
  staging)
    MODE_XCODE="Profile"
    MODE="profile"
  ;;
  *)
    MODE_XCODE="Debug"
    MODE="debug"
esac

OUTPUT_DIR="$BASE_PRJ_PATH/dist"
VERSION=$(grep '^version:' pubspec.yaml | cut -d ' ' -f2)

# Exibe os valores recebidos
colored_print green  "✅ Gerar: ${GENERATE}"
colored_print green  "✅ Flavor: ${FLAVOR}"
colored_print green  "✅ Ambiente: ${ENVIRONMENT}"
colored_print green  "✅ Modo: ${MODE}"
colored_print green  "✅ Modo Xcode: ${MODE_XCODE}"
colored_print green  "✅ Diretorio do projeto: ${BASE_PRJ_PATH}"
colored_print green  "✅ Versão: ${VERSION}"
colored_print green  "✅ Diretório de saída: ${OUTPUT_DIR}"
colored_print green  "✅ Publicar para o AppDistribution/TestFlight: ${AUTO_PUBLISH_TEST}"
colored_print green  "✅ Publicar para o AppStore/PlayStore: ${AUTO_PUBLISH_RELEASE}"


function clean {
  executar flutter clean
}

function pub_get {
  executar flutter pub get
}

function run_dependencies_configurations() {
  colored_print yellow  "Executando configurações de dependências..."
  
  executar flutter pub run flutter_flavorizr -f
  
  executar dart run flutter_launcher_icons -f "${BASE_PRJ_PATH}/flutter_launcher_icons-${FLAVOR}.yaml"

  executar dart run flutter_native_splash:create --flavor "${FLAVOR}"
}

function build_android {
  executar flutter build "$GENERATE" \
    --flavor "$FLAVOR" \
    --dart-define=ENV="$ENVIRONMENT" \
    --$MODE

  colored_print yellow "criando o diretório de saída: ${OUTPUT_DIR}"
  executar mkdir -p "$OUTPUT_DIR"

  BUILD_APP_OUTPUTS_DIR="${BASE_PRJ_PATH}/build/app/outputs/flutter-apk/"
  if [ -d "$BUILD_APP_OUTPUTS_DIR" ]; then
    colored_print yellow "Movendo os arquivos *.apk gerados para o diretório de saída: ${OUTPUT_DIR}"
    executar find "$BUILD_APP_OUTPUTS_DIR" -type f \( -name "*.apk" \) -exec mv -f -v {} "${OUTPUT_DIR}/app-android-${FLAVOR}-v${VERSION}-${MODE}.apk" \;
  fi

  BUILD_APP_OUTPUTS_DIR="${BASE_PRJ_PATH}/build/app/outputs/bundle/"
  if [ -d "$BUILD_APP_OUTPUTS_DIR" ]; then
    colored_print yellow "Movendo os arquivos *.aab gerados para o diretório de saída: ${OUTPUT_DIR}"
    executar find "$BUILD_APP_OUTPUTS_DIR" -type f \( -name "*.aab" \) -exec mv -f -v {} "${OUTPUT_DIR}/app-android-${FLAVOR}-v${VERSION}-${MODE}.aab" \;
  fi

  colored_print green  "Flutter Build concluído com sucesso!"
}

function build_ios() {
  # Caminhos
  PROJECT_PATH="${BASE_PRJ_PATH}/ios"
  WORKSPACE_PATH="${PROJECT_PATH}/Runner.xcworkspace"
  SCHEME=$FLAVOR
  CONFIGURATION="${MODE_XCODE}-${SCHEME}"
  ARCHIVE_PATH="${PROJECT_PATH}/build/Runner.xcarchive"
  EXPORT_OPTIONS_PLIST="${PROJECT_PATH}/ExportOptions.${MODE_XCODE}.plist"
  EXPORT_PATH="${OUTPUT_DIR}/app-ios-${FLAVOR}-v${VERSION}-${MODE_XCODE}/"

  # executar install_ruby_env change-ios-envs
  executar install_ruby_env

  executar flutter precache --ios

  colored_print yellow "Compilando o projeto iOS com Flutter..."
  executar flutter build "$GENERATE" \
    --dart-define=ENV="$ENVIRONMENT" \
    --flavor "$FLAVOR" \
    --no-codesign \
    --$MODE

   colored_print green "Limpando o diretório de construção..."
   executar xcodebuild clean \
     -workspace "$WORKSPACE_PATH" \
     -scheme "${SCHEME}" \
     -configuration "$CONFIGURATION"


  colored_print yellow "Arquivando o projeto..."
  executar xcodebuild archive \
    -workspace "$WORKSPACE_PATH" \
    -scheme "${SCHEME}" \
    -configuration "$CONFIGURATION" \
    -archivePath "$ARCHIVE_PATH" \
    -destination 'generic/platform=iOS' \
    -quiet

  # Verificar se o arquivo de arquivamento foi gerado
  if [ ! -d "$ARCHIVE_PATH" ]; then
      colored_print red "Erro: Arquivamento falhou, o diretório ${ARCHIVE_PATH} não foi encontrado."
      exit 1
  fi

  colored_print yellow "Exportando o IPA..."
  executar xcodebuild \
    -exportArchive \
    -archivePath "$ARCHIVE_PATH" \
    -exportOptionsPlist "$EXPORT_OPTIONS_PLIST" \
    -exportPath "$EXPORT_PATH"

  # Verificar se algum arquivo IPA foi gerado
  IPA_FILE=$(find "$EXPORT_PATH" -name "*.ipa" | head -n 1)
  if [ -z "$IPA_FILE" ]; then
      colored_print red "Erro: Exportação falhou, nenhum arquivo IPA foi encontrado em ${EXPORT_PATH}."
      exit 1
  fi


  colored_print green "Build and export complete. IPA is located at: ${IPA_FILE}"

}

function publish_test_appdistribution() {
  colored_print yellow "Publicando para o AppDistribution..."

}

function publish_playstore() {
  colored_print yellow "Publicando para o PlayStore..."

}


# Limpa o projeto flutter
clean

# Obtem os pacotes flutter
pub_get

# Construcao do projeto
if [[ "$GENERATE" == "ipa" ]]; then
  build_ios
else
  build_android
fi

# Publica para teste
if [[ $AUTO_PUBLISH_TEST == 1 ]]; then
  if [[ "$GENERATE" == "ipa" ]]; then
    publish_test_appdistribution
  else
    publish_test_appdistribution
  fi
fi

# Publica para producao
if [[ $AUTO_PUBLISH_RELEASE == 1 ]]; then

  if [[ "$ENVIRONMENT" != "production" ]]; then
    colored_print red "❌ Ambiente inválido: ${ENVIRONMENT}. Só é possível publicar uma release de produção. Use 'production'."
    exit 1
  fi

  if [[ "$GENERATE" == "ipa" ]]; then
    publish_playstore
  else
    publish_playstore
  fi
fi
