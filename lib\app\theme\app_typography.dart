import 'package:google_fonts/google_fonts.dart';
import 'package:pharmalink/app/utils/responsive_widget.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';

double displayExtraLarge = 38.sp;
double displarLarge = 26.sp;
double displayMedium = 23.sp;
double heading = 20.sp;
double title = 18.sp;
double subHeading = 15.sp;
double body = 15.sp;
double caption = 13.sp;
double tiny = 11.sp;
double buttonLarge = 15.sp;
double buttonSmall = 13.sp;

const FontWeight bold = FontWeight.w700;
const FontWeight medium = FontWeight.w500;
const FontWeight regular = FontWeight.w300;

const String gilroyFontFamily = 'Gilroy';
const String nexaFontFamily = 'Nexa';
String nunitoFontFamily = GoogleFonts.nunitoSans().fontFamily!;

abstract class TxtStyles {
  static _textColor() {
    return Get.isDarkMode ? null : textColor;
  }

  static dpLarge() {
    return Responsive.sizes(mobile: 38.sp, tablet: 32.sp, desktop: 38);
  }

  static dpMedium() {
    return Responsive.sizes(mobile: 26.sp, tablet: 22.sp, desktop: 26);
  }

  static dpSmall() {
    return Responsive.sizes(mobile: 23.sp, tablet: 19.sp, desktop: 23);
  }

  static hdMedium() {
    return Responsive.sizes(mobile: 20.sp, tablet: 18.sp, desktop: 20);
  }

  static hdSmall() {
    return Responsive.sizes(mobile: 15.sp, tablet: 13.sp, desktop: 15);
  }

  static titleLg() {
    return Responsive.sizes(mobile: 18.sp, tablet: 15.sp, desktop: 18);
  }

  static titleMd() {
    return Responsive.sizes(mobile: 17.sp, tablet: 14.sp, desktop: 17);
  }

  static titleSm() {
    return Responsive.sizes(mobile: 15.sp, tablet: 12.sp, desktop: 15);
  }

  static bodyLg() {
    return Responsive.sizes(mobile: 15.sp, tablet: 13.sp, desktop: 15);
  }

  static bodyMd() {
    return Responsive.sizes(mobile: 13.sp, tablet: 10.sp, desktop: 13);
  }

  static bodySm() {
    return Responsive.sizes(mobile: 11.sp, tablet: 9.sp, desktop: 11);
  }

  static TextStyle displayLarge(BuildContext context) => Theme.of(context)
      .textTheme
      .displayLarge!
      .copyWith(fontSize: dpLarge(), color: _textColor());
  static TextStyle displayMedium(BuildContext context) => Theme.of(context)
      .textTheme
      .displayMedium!
      .copyWith(fontSize: dpMedium(), color: _textColor());
  static TextStyle displaySmall(BuildContext context) => Theme.of(context)
      .textTheme
      .displaySmall!
      .copyWith(fontSize: dpSmall(), color: _textColor());
  static TextStyle headlineMedium(BuildContext context) => Theme.of(context)
      .textTheme
      .headlineMedium!
      .copyWith(fontSize: hdMedium(), color: _textColor());
  static TextStyle headlineSmall(BuildContext context) => Theme.of(context)
      .textTheme
      .headlineSmall!
      .copyWith(fontSize: hdSmall(), color: _textColor());
  static TextStyle titleLarge(BuildContext context) =>
      Theme.of(context).textTheme.titleLarge!.copyWith(fontSize: titleLg());
  static TextStyle titleMedium(BuildContext context) =>
      Theme.of(context).textTheme.titleMedium!.copyWith(fontSize: titleMd());
  static TextStyle titleSmall(BuildContext context) =>
      Theme.of(context).textTheme.titleSmall!.copyWith(fontSize: titleSm());
  static TextStyle bodyLarge(BuildContext context) =>
      Theme.of(context).textTheme.bodyLarge!.copyWith(fontSize: bodyLg());
  static TextStyle bodyMedium(BuildContext context) =>
      Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: bodyMd());
  static TextStyle bodySmall(BuildContext context) =>
      Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: bodySm());
}
