import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/orders_resume/enuns/status_syncronization_enum.dart';
import 'package:pharmalink/modules/report_orders_sync/pages/widgets/report_orders_sync_card_order_widget.dart';

class ReportOrdersSyncPage extends StatelessWidget {
  const ReportOrdersSyncPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<ReportOrdersSyncController>(builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: const LabelWidget(
            title: "Sincronizar Pedidos",
            fontSize: 18,
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ),
        body: Column(
          children: [
            SelectAllCheckbox(
              isChecked: ctrl.selectAllInitial,
              onChanged: (bool value) {
                ctrl.setAllSelecteds(value);
              },
              title: "Selecionar todos os pedidos",
            ),
            Expanded(
              child: ListView.builder(
                itemCount: ctrl.ordersList.length,
                itemBuilder: (context, index) {
                  var order = ctrl.ordersList[index];
                  return ReportOrdersSyncCardOrdersWidget(
                    data: order,
                    orderId: order.transactionKey!,
                    title: order.storeData?.razaoSocial ??
                        order.payLoad?.pdvName ??
                        "-",
                    canEdit: getStatusSynchronizationCanEdit(order.status!),
                    state: getStatusSynchronizationText(order.status ?? 0),
                    stateColor:
                        getStatusSynchronizationColor(order.status ?? 0),
                    date: order.createAt!
                        .formatDate(formatType: DateFormatType.ddMMyyyy),
                    isOnline: order.isOnline ?? true,
                    fileAttachmentStatus: order.fileAttachment?.status,
                  );
                },
              ),
            ),
          ],
        ),
        floatingActionButton: CustomFloatingActionButton(
          backgroundColor: themesController.getPrimaryColor(),
          onPressed: () async {
            //ctrl.startSyncronizations();
            await ctrl.syncOrders();
          },
          badgeCount: ctrl.badgeCount,
          child: const Icon(
            FontAwesomeIcons.arrowsRotate,
            color: whiteColor,
          ),
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
