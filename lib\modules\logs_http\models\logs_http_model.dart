import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class LogsHttpModel extends SqfLiteBase<LogsHttpModel> {
  int? workspaceId;
  String? workspaceName;
  String? userName;
  String? url;
  String? headers;
  String? request;
  String? response;
  DateTime? createdAt;
  String? method;
  int? statusCode;
  bool? isFlutterError;
  LogsHttpModel({
    this.workspaceId,
    this.workspaceName,
    this.userName,
    this.url,
    this.headers,
    this.method,
    this.request,
    this.response,
    this.createdAt,
    this.statusCode,
    this.isFlutterError,
  }) : super(DatabaseModels.logsHttpModel);

  LogsHttpModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.logsHttpModel) {
    workspaceId = json['workspaceId'];
    workspaceName = json['workspaceName'];
    userName = json['userName'];
    url = json['url'];
    headers = json['headers'];
    request = json['request'];
    method = json['method'];
    response = json['response'];
    createdAt =
        json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null;

    statusCode = json['statusCode'];
    isFlutterError = json['isFlutterError'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workspaceId'] = workspaceId;
    data['workspaceName'] = workspaceName;
    data['userName'] = userName;
    data['url'] = url;
    data['headers'] = headers;
    data['request'] = request;
    data['method'] = method;
    data['response'] = response;
    data['createdAt'] = createdAt?.toIso8601String();
    data['statusCode'] = statusCode;
    data['isFlutterError'] = isFlutterError;

    return data;
  }

  Future<LogsHttpModel> getFirst() async {
    var list = await getAll<LogsHttpModel>(
        workspaceId: appController.workspace?.workspaceId,
        LogsHttpModel.fromJson);
    return list.first;
  }

  Future<List<LogsHttpModel>> getList() async {
    var list = await getAll<LogsHttpModel>(
        workspaceId: appController.workspace?.workspaceId,
        LogsHttpModel.fromJson);
    return list;
  }
}
