import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class NavigationPagePage extends StatefulWidget {
  const NavigationPagePage({super.key});

  @override
  State<NavigationPagePage> createState() => _NavigationPagePageState();
}

class _NavigationPagePageState extends State<NavigationPagePage>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    super.didChangeAppLifecycleState(state);
    await navigationPageController.didChangeAppLifecycleState(state);
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<NavigationPageController>(builder: (ctrl) {
      return Scaffold(
        key: GlobalKey(),
        backgroundColor: whiteColor,
        body: IndexedStack(
          index: navigationPageController.selectedIndex,
          children: const [
            HomePage(),
            StoreRoutesPage(),
            ReportsPage(),
            SettingsPage()
          ],
        ),
        bottomNavigationBar: BottomNavigationBar(
          backgroundColor: whiteColor,
          type: BottomNavigationBarType.fixed,
          items: const <BottomNavigationBarItem>[
            BottomNavigationBarItem(
              icon: Icon(FontAwesomeIcons.house),
              label: 'Home',
            ),
            BottomNavigationBarItem(
              icon: Icon(FontAwesomeIcons.car),
              label: 'Rotas',
            ),
            BottomNavigationBarItem(
              icon: Icon(FontAwesomeIcons.fileContract),
              label: 'Relatórios',
            ),
            BottomNavigationBarItem(
              icon: Icon(FontAwesomeIcons.gear),
              label: 'Configurações',
            ),
          ],
          currentIndex: navigationPageController.selectedIndex,
          selectedItemColor: themesController.getColorA(),
          elevation: 0,
          unselectedItemColor: themesController.getColorD(),
          onTap: navigationPageController.changePage,
          enableFeedback: true,
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
