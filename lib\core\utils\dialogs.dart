import 'package:get/get.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class Dialogs {
  static Future<void> info(String title, String message,
      {String? buttonName, VoidCallback? buttonOnPressed}) async {
    return showDialog<void>(
      context: Get.context!,
      barrierDismissible:
          false, // impede o usuário de fechar o diálogo ao tocar fora dele
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          titlePadding: EdgeInsets.zero, // Remove default padding
          title: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            decoration: BoxDecoration(
              color: themesController.getPrimaryColor(),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: DeviceSize.fontSize(15, 20),
                    ),
                  ),
                ),
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Get.back(),
                ),
              ],
            ),
          ),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                parseStyledText(message),
              ],
            ),
          ),
          actions: <Widget>[
            ElevatedButton(
              onPressed: buttonOnPressed ?? () => Get.back(),
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: themesController.getPrimaryColor(),
                side: BorderSide(color: themesController.getPrimaryColor()),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: Text(buttonName ?? 'Ok'.toUpperCase()),
            ),
          ],
        );
      },
    );
  }

  static Future<void> confirm(String title, String message,
      {String? buttonNameOk,
      String? buttonNameCancel,
      VoidCallback? onPressedOk,
      VoidCallback? onPressedCancel, 
      bool? cancelable = true,
    }) async {
    return showDialog<void>(
      context: Get.context!,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          titlePadding: EdgeInsets.zero, // Remove default padding
          title: Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
            decoration: BoxDecoration(
              color: themesController.getPrimaryColor(),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(10),
                topRight: Radius.circular(10),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: DeviceSize.fontSize(15, 20),
                    ),
                  ),
                ),
                if (cancelable == true)
                IconButton(
                  icon: const Icon(Icons.close, color: Colors.white),
                  onPressed: () => Get.back(),
                ) else const SizedBox(height: 40),
              ],
            ),
          ),
          content: SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                parseStyledText(message),
              ],
            ),
          ),
          actions: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Flexible(
                  child: ElevatedButton(
                    onPressed: onPressedCancel ?? () => Get.back(),
                    style: ElevatedButton.styleFrom(
                      foregroundColor: themesController.getPrimaryColor(),
                      backgroundColor: Colors.white,
                      side:
                          BorderSide(color: themesController.getPrimaryColor()),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    child: LabelWidget(
                      title: buttonNameCancel ?? 'Não Obrigado'.toUpperCase(),
                      textAlign: TextAlign.center,
                      fontSize: DeviceSize.fontSize(12, 20),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const Gap(10),
                Flexible(
                  child: ElevatedButton(
                    onPressed: onPressedOk ?? () => Get.back(),
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white,
                      backgroundColor: themesController.getPrimaryColor(),
                      side:
                          BorderSide(color: themesController.getPrimaryColor()),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                    ),
                    child: LabelWidget(
                      title: buttonNameOk ?? 'Sim'.toUpperCase(),
                      textAlign: TextAlign.center,
                      fontSize: DeviceSize.fontSize(12, 20),
                      fontWeight: FontWeight.bold,
                      textColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        );
      },
    );
  }

  static RichText parseStyledText(String text) {
    final textSpans = <TextSpan>[];
    final regex = RegExp(r'\*(.*?)\*');
    final matches = regex.allMatches(text);

    int lastMatchEnd = 0;

    for (final match in matches) {
      if (match.start > lastMatchEnd) {
        textSpans.add(TextSpan(
            text: text.substring(lastMatchEnd, match.start),
            style: const TextStyle(fontWeight: FontWeight.normal)));
      }

      textSpans.add(TextSpan(
          text: match.group(1),
          style: const TextStyle(fontWeight: FontWeight.bold)));

      lastMatchEnd = match.end;
    }

    if (lastMatchEnd < text.length) {
      textSpans.add(TextSpan(
          text: text.substring(lastMatchEnd),
          style: const TextStyle(fontWeight: FontWeight.normal)));
    }

    return RichText(
      text: TextSpan(
        style: const TextStyle(
            color: Colors.black, fontSize: 16), // Cor padrão do texto
        children: textSpans,
      ),
    );
  }
}
