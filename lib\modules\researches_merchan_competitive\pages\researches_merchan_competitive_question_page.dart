import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class ResearchesMerchanCompetitiveQuestionPage extends StatelessWidget {
  const ResearchesMerchanCompetitiveQuestionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ResearchesMerchanCompetitiveController>(
        "ResearchesMerchanCompetitiveController", builder: (ctrl) {
      return Scaffold(
        backgroundColor: themesController.getPrimaryColor(),
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelWidget(
                title: "Adicionar",
                fontSize: DeviceSize.fontSize(14, 18),
                fontWeight: FontWeight.bold,
              ),
              const LabelWidget(title: "Merchandising Concorrente")
            ],
          ),
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              ctrl.setTabIndexBack();
            },
          ),
          actions: [
            IconButton(
                onPressed: () async {
                  await ctrl.next();
                },
                icon: const Icon(
                  Icons.arrow_forward,
                  color: whiteColor,
                ))
          ],
          elevation: 0,
        ),
        body: SingleChildScrollView(
          child: Container(
            color: themesController.getPrimaryColor(),
            width: double.infinity,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  children: [
                    Flexible(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Container(
                          color:
                              ctrl.tabIndex == 0 ? Colors.grey : Colors.white,
                          height: 10,
                        ),
                      ),
                    ),
                    Flexible(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Container(
                          color:
                              ctrl.tabIndex == 1 ? Colors.grey : Colors.white,
                          height: 10,
                        ),
                      ),
                    ),
                    Flexible(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Container(
                          color:
                              ctrl.tabIndex == 2 ? Colors.grey : Colors.white,
                          height: 10,
                        ),
                      ),
                    ),
                    Flexible(
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Container(
                          color:
                              ctrl.tabIndex == 3 ? Colors.grey : Colors.white,
                          height: 10,
                        ),
                      ),
                    )
                  ],
                ),
                20.toHeightSpace(),
                LabelWidget(
                  title: ctrl.getTitleName(),
                  fontSize: DeviceSize.fontSize(22, 25),
                  fontWeight: FontWeight.bold,
                  textColor: Colors.white,
                ),
                20.toHeightSpace(),
                Visibility(
                  visible: ctrl.tabIndex == 0,
                  child: Column(
                    children: ctrl.merchanData!.familias!.map((e) {
                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: CustomInkWell(
                          onTap: () {
                            ctrl.setFamily(e);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.white, width: 2),
                              color: ctrl.awserModel!.idFamilia == e.idFamilia!
                                  ? Colors.white
                                  : Colors.transparent,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(
                                child: LabelWidget(
                                  title: e.descricao ?? "-",
                                  fontSize: DeviceSize.fontSize(14, 17),
                                  textColor:
                                      ctrl.awserModel!.idFamilia == e.idFamilia!
                                          ? themesController.getPrimaryColor()
                                          : Colors.white,
                                  fontWeight:
                                      ctrl.awserModel!.idFamilia == e.idFamilia!
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
                //merchandising
                Visibility(
                  visible: ctrl.tabIndex == 1,
                  child: Column(
                    children: ctrl.merchanData!.merchandising!.map((e) {
                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: CustomInkWell(
                          onTap: () {
                            ctrl.setMerchandising(e);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.white, width: 2),
                              color: ctrl.awserModel!.idMerchandising ==
                                      e.idMerchandising!
                                  ? Colors.white
                                  : Colors.transparent,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(
                                child: LabelWidget(
                                  title: e.descricao ?? "-",
                                  fontSize: DeviceSize.fontSize(14, 17),
                                  textColor: ctrl.awserModel!.idMerchandising ==
                                          e.idMerchandising!
                                      ? themesController.getPrimaryColor()
                                      : Colors.white,
                                  fontWeight:
                                      ctrl.awserModel!.idMerchandising ==
                                              e.idMerchandising!
                                          ? FontWeight.bold
                                          : FontWeight.normal,
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
                //merchandising
                Visibility(
                  visible: ctrl.tabIndex == 2,
                  child: Column(
                    children: ctrl.merchanData!.tipoAtivacao!.map((e) {
                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: CustomInkWell(
                          onTap: () {
                            ctrl.setTipeActivation(e);
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.white, width: 2),
                              color: ctrl.awserModel!.idTipoAtivacao ==
                                      e.idMerchandisingTipoAtivacao!
                                  ? Colors.white
                                  : Colors.transparent,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(
                                child: LabelWidget(
                                  title: e.descricao ?? "-",
                                  fontSize: DeviceSize.fontSize(14, 17),
                                  textColor: ctrl.awserModel!.idTipoAtivacao ==
                                          e.idMerchandisingTipoAtivacao!
                                      ? themesController.getPrimaryColor()
                                      : Colors.white,
                                  fontWeight: ctrl.awserModel!.idTipoAtivacao ==
                                          e.idMerchandisingTipoAtivacao!
                                      ? FontWeight.bold
                                      : FontWeight.normal,
                                ),
                              ),
                            ),
                          ),
                        ),
                      );
                    }).toList(),
                  ),
                ),
                //Quantidade
                Visibility(
                  visible: ctrl.tabIndex == 3,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: TextFormField(
                      keyboardType: TextInputType.number,
                      controller: TextEditingController(
                          text: ctrl.awserModel!.quantidade?.toString()),
                      textAlign: TextAlign.center,
                      decoration: const InputDecoration(
                        filled: true,
                        fillColor: Colors.white,
                      ),
                      inputFormatters: const [
                        // NumberTextInputFormatter.withSeparator()
                      ],
                      onChanged: (value) {
                        ctrl.setQuantity(value);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
