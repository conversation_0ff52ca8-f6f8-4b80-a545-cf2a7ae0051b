import 'package:get/get.dart';

import 'getx_controller_instrumentado.dart';

class GetBuilderInstrumentado<T extends GetxControllerInstrumentado<T>>
    extends GetBuilder<T> {
  GetBuilderInstrumentado(
      String dynatraceActionName, {
        super.key,
        super.init,
        super.global,
        required super.builder,
        super.autoRemove,
        super.assignId,
        super.initState,
        super.filter,
        super.tag,
        super.id,
        super.didUpdateWidget,
      }) : super(
    dispose: (state) {
      state.controller?.disposeDynatraceActions();
    },
    didChangeDependencies: (state) {
      state.controller?.initDynatraceAction(dynatraceActionName);
    },
  );
}
