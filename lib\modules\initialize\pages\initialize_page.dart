import 'dart:math';

import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class InitializePage extends StatefulWidget {
  const InitializePage({super.key});

  @override
  State<InitializePage> createState() => _InitializePageState();
}

class _InitializePageState extends State<InitializePage>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(seconds: 5), // ajuste a duração conforme desejar
      vsync: this,
    )..repeat(); // isso faz com que a animação repita indefinidamente
  }

  @override
  void dispose() {
    _controller.dispose(); // não se esqueça de fazer a limpeza!
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<InitializeController>(
      "InitializePage",
      builder: (ctrl) {
        return Scaffold(
          body: Container(
            width: double.infinity,
            height: double.infinity,
            color: Colors.white,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                AnimatedBuilder(
                  animation: _controller,
                  builder: (context, child) {
                    return Transform.rotate(
                      angle: _controller.value * 5 * pi, // rotação em radianos
                      child: child,
                    );
                  },
                  child: Image.asset(
                    F.iconlogo, // substitua pelo seu asset
                    width: 72, // ajuste o tamanho conforme necessário
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
