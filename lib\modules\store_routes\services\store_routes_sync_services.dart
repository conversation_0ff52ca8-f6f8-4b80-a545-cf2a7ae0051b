import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/call_context.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/routes/models/list_routes_model.dart';
import 'package:pharmalink/modules/routes/models/pdvs_dia_model.dart';
import 'package:pharmalink/modules/routes/models/route_list_model.dart';
import 'package:pharmalink/modules/routes/models/routes_list_request_model.dart';
import 'package:pharmalink/modules/routes/models/routes_save_model.dart';
import 'package:pharmalink/modules/stores/enuns/visit_status_enum.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';
import 'package:pharmalink/modules/visits/controller/visits_controller.dart';
import 'package:pharmalink/modules/visits/models/visits_by_routes_response_model.dart';

class StoreRoutesSyncService extends GetxService with TraceableController {
  // Inicializa o serviço de sincronização de rotas de lojas
  Future<StoreRoutesSyncService> init() async {
    return this;
  }

  // Sincroniza as rotas das lojas fornecidas
  Future<void> syncStoreRoutes(List<StoresModel> stores) async {
    return trace('syncStoreRoutes', () async {
      if (stores.isEmpty) {
        await syncRoutesVisitsAndSearches();
        return;
      }

      await VisitsByRoutesResponseModel().removeOldVisits();

      final storeNeedSync = _getStoresNeedingSync(stores);

      if (storeNeedSync.isNotEmpty) {
        await _saveRoutes(storeNeedSync);
      } else {
        await syncRoutesVisitsAndSearches();
      }
    });
  }

  Future<void> syncRoutesVisitsAndSearches() async {
    await syncRoutesGetList(syncVisits: true);
  }

  // Obtém as lojas que precisam de sincronização
  List<PdvsDiaModel> _getStoresNeedingSync(List<StoresModel> stores) {
    return stores
        .where((x) =>
            x.dataExtra!.canVisitaSync == true &&
            x.dataExtra!.canPlanned == true)
        .map((e) => PdvsDiaModel(
            idEndereco: e.idEndereco,
            idLoja: e.idLoja,
            idPdv: e.idPdv,
            idSetorVisita: null))
        .toList();
  }

  // Salva as rotas das lojas que precisam de sincronização
  Future<void> _saveRoutes(List<PdvsDiaModel> storeNeedSync) async {
    final responseSave = await routesApi.saveRoutes(
      model: RoutesSaveModel(
          data: DateTime.now(),
          userId: appController.userLogged!.userId!,
          pdvsDia: storeNeedSync),
    );
    responseSave.when(sucess: (data) async {
      await syncRoutesVisitsAndSearches();
    }, error: (error) {
      appLog("Error: syncStoreRoutes", data: {"error": error.error});
    });
  }

  // Sincroniza a lista de rotas
  Future<void> syncRoutesGetList({Function? onNext, bool? syncVisits}) async {
    final responseList = await routesApi.getRoutesList(
      model: RoutesListRequestModel(
        dataInicio: DateTime.now(),
        dataTermino: DateTime.now(),
        userId: appController.userLogged!.userId!,
      ),
    );
    responseList.when(sucess: (data) async {
      await _processRoutesList(data);
      // Execute the provided function if it's not null

      //Sync Visits
      if (syncVisits == true) {
        final visitController = Get.find<VisitsController>();
        await visitController.getSyncVisits();

        await synchronizationsController.startSyncResearches(
            hideMsg: true, sendAnwsers: false);
        await synchronizationsController.startSyncSalasInfo();
      }

      if (onNext != null) {
        onNext();
      }
    }, error: (error) {
      appLog("Error: syncRoutesGetList", data: {"error": error.error});
      if (onNext != null) {
        onNext();
      }
    });
  }

  // Processa a lista de rotas recebida
  Future<void> _processRoutesList(List<ListRoutesModel> data) async {
    for (var item in data) {
      await dbContext.addData(
        clearCurrentData: true,
        workspaceId: appController.workspace?.workspaceId,
        userId: appController.userLogged!.userId!,
        data: item,
        storeId: item.idPdv,
        key: DatabaseModels.routersListModel,
      );
    }

    final storeListRouts = extractAllListaRota(data);
    await updateDataStore(storeListRouts);
    await storeRoutesPanelController.resetAndRefetch();
    await storeRoutesPlannedController.resetAndRefetch();
  }

  // Extrai todas as listas de rotas dos dados recebidos
  List<ListaRota> extractAllListaRota(List<ListRoutesModel> data) {
    List<ListaRota> idLojas = [];

    for (var routeModel in data) {
      if (routeModel.roteiro?.listaRota != null) {
        for (var listaRota in routeModel.roteiro!.listaRota!) {
          if (listaRota.idLoja != null) {
            idLojas.add(listaRota);
          }
        }
      }
    }
    return idLojas;
  }

  // Atualiza os dados das lojas com base nas rotas recebidas
  Future<void> updateDataStore(List<ListaRota> data) async {
    final storesList = await StoresModel().getList();
    if (storesList.isEmpty) return;

    for (var route in data) {
      final storeToUpdate =
          storesList.where((x) => x.idLoja == route.idLoja).firstOrNull;

      if (storeToUpdate != null) {
        _updateStoreData(storeToUpdate, route);
        await storeRoutesController.updateStore(storeToUpdate);
      }
    }
  }

  // Atualiza os dados de uma loja específica com base na rota fornecida
  void _updateStoreData(StoresModel store, ListaRota route) {
    bool isVisita = route.visita == null || route.visita!.idVisita == null;

    if (route.idRota != null && !isVisita) {
      isVisita = true;
    }

    store
      ..dataExtra!.canVisitaSync = true
      ..dataExtra!.canPlanned = true
      ..dataExtra!.hasRouteSync = true
      ..dataExtra!.currentDate = DateTime.now()
      ..dataExtra!.sync = false
      ..dataExtra!.routeId = route.idRota
      ..dataExtra!.roteiroId = route.idRoteiro
      ..dataExtra!.order = store.dataExtra!.orderLocal ?? route.ordem
      ..dataExtra!.isVisita = isVisita
      ..dataExtra!.statusVisit = store.dataExtra!.statusVisit == null
          ? StatusVisitEnum.notInitilized
          : (store.dataExtra!.statusVisit == StatusVisitEnum.partialSaved
              ? StatusVisitEnum.partialSaved
              : (store.dataExtra!.statusVisit == StatusVisitEnum.completeSaved
                  ? StatusVisitEnum.completeSaved
                  : (store.dataExtra!.statusVisit ==
                          StatusVisitEnum.synchronized
                      ? StatusVisitEnum.synchronized
                      : StatusVisitEnum.notInitilized)))
      ..dataExtra!.isVisitaCanEdit =
          route.visita == null || route.visita!.idVisita == null
      ..dataExtra!.isVisitaSync =
          route.visita != null && route.visita!.idVisita != null;
  }
}
