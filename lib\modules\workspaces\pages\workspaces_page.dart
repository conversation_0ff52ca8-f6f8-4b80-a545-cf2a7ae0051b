import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/workspaces/controller/workspaces_controller.dart';

class WorkspacesPage extends StatelessWidget {
  WorkspacesPage({super.key});
  final bool isSettings = Get.arguments?['settings'] ?? false;
  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        // backgroundColor: Colors.blue,
        body: GetBuilderInstrumentado<WorkspacesController>("WorkspacesPage",
            builder: (ctrl) {
          return RefreshIndicator(
            onRefresh: () async {
              await ctrl.refreshData();
            },
            child: Column(
              children: [
                TextField(
                  controller: workspacesController.controllerSearch,
                  style: TextStyle(fontSize: 22.sp, color: Colors.green),
                  onChanged: (value) {
                    if (value.isEmpty || value.length >= 3) {
                      workspacesController.setSearch(value);
                    }
                  },
                  decoration: const InputDecoration(
                    fillColor: Colors.transparent,
                    prefixIconColor: Colors.red,
                    focusColor: Colors.red,
                    prefixIcon: Icon(
                      Icons.dashboard_customize_rounded,
                      color: Colors.green,
                    ),
                    hintText: 'Pesquise por um workspace',
                    hintStyle: TextStyle(color: Colors.green),
                    contentPadding: EdgeInsets.all(20),
                    focusedBorder: UnderlineInputBorder(
                      borderSide: BorderSide(color: Colors.green, width: 2.0),
                    ),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(color: Colors.green, width: 2.0),
                    ),
                  ),
                ),
                if (isSettings)
                  CustomInkWell(
                    onTap: () {
                      GetC.close();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(12.0),
                      child: Container(
                        padding: const EdgeInsets.only(bottom: 5),
                        decoration: const BoxDecoration(
                            border: Border(
                                bottom: BorderSide(
                                    width: 0.3, color: Colors.grey))),
                        width: MediaQuery.of(context).size.width,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(vertical: 5),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    appController.workspace!.name!,
                                    style: const TextStyle(
                                        fontSize: 24,
                                        overflow: TextOverflow.ellipsis,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Text(
                                    appController.userLogged!.nome!,
                                    style: const TextStyle(
                                        fontSize: 16,
                                        overflow: TextOverflow.ellipsis,
                                        fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                            ),
                            Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.grey,
                              size: 20.sp,
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                Expanded(
                  child: ListView.builder(
                      itemCount: workspacesController.dataList.length,
                      itemBuilder: (BuildContext context, int index) {
                        return workspacesController.dataList[index].name!
                                .containsInsesitive(
                                    workspacesController.search.toLowerCase())
                            ? CustomInkWell(
                                onTap: () {
                                  workspacesController.setWorkspace(
                                      workspacesController.dataList[index]);
                                },
                                child: Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: Container(
                                    padding: const EdgeInsets.only(bottom: 5),
                                    decoration: const BoxDecoration(
                                        border: Border(
                                            bottom: BorderSide(
                                                width: 0.3,
                                                color: Colors.grey))),
                                    width: MediaQuery.of(context).size.width,
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.symmetric(
                                              vertical: 5),
                                          child: Text(
                                            workspacesController
                                                .dataList[index].name!,
                                            style: const TextStyle(
                                              fontSize: 24,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                        ),
                                        Icon(
                                          Icons.arrow_forward_ios,
                                          color: Colors.grey,
                                          size: 20.sp,
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              )
                            : Container();
                      }),
                )
              ],
            ),
          );
        }),

        floatingActionButton: FloatingActionButton(
          onPressed: () async {
            await workspacesController.refreshData();
          },
          backgroundColor: paletaCorBotaoDefault,
          child: const Icon(
            Icons.sync,
            color: whiteColor,
          ),
        ),
        bottomSheet: const VersionWidget(),
      ),
    );
  }
}
