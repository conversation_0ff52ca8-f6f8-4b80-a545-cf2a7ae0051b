class OrdersSyncResponse {
  ResponseMetadata? responseMetadata;

  OrdersSyncResponse({this.responseMetadata});

  OrdersSyncResponse.fromJson(Map<String, dynamic> json) {
    responseMetadata = json['ResponseMetadata'] != null
        ? ResponseMetadata.fromJson(json['ResponseMetadata'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (responseMetadata != null) {
      data['ResponseMetadata'] = responseMetadata!.toJson();
    }
    return data;
  }
}

class ResponseMetadata {
  String? requestId;
  int? hTTPStatusCode;
  HTTPHeaders? hTTPHeaders;
  int? retryAttempts;

  ResponseMetadata(
      {this.requestId,
      this.hTTPStatusCode,
      this.hTTPHeaders,
      this.retryAttempts});

  ResponseMetadata.fromJson(Map<String, dynamic> json) {
    requestId = json['RequestId'];
    hTTPStatusCode = json['HTTPStatusCode'];
    hTTPHeaders = json['HTTPHeaders'] != null
        ? HTTPHeaders.fromJson(json['HTTPHeaders'])
        : null;
    retryAttempts = json['RetryAttempts'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['RequestId'] = requestId;
    data['HTTPStatusCode'] = hTTPStatusCode;
    if (hTTPHeaders != null) {
      data['HTTPHeaders'] = hTTPHeaders!.toJson();
    }
    data['RetryAttempts'] = retryAttempts;
    return data;
  }
}

class HTTPHeaders {
  String? server;
  String? date;
  String? contentType;
  String? contentLength;
  String? connection;
  String? xAmznRequestid;
  String? xAmzCrc32;

  HTTPHeaders(
      {this.server,
      this.date,
      this.contentType,
      this.contentLength,
      this.connection,
      this.xAmznRequestid,
      this.xAmzCrc32});

  HTTPHeaders.fromJson(Map<String, dynamic> json) {
    server = json['server'];
    date = json['date'];
    contentType = json['content-type'];
    contentLength = json['content-length'];
    connection = json['connection'];
    xAmznRequestid = json['x-amzn-requestid'];
    xAmzCrc32 = json['x-amz-crc32'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['server'] = server;
    data['date'] = date;
    data['content-type'] = contentType;
    data['content-length'] = contentLength;
    data['connection'] = connection;
    data['x-amzn-requestid'] = xAmznRequestid;
    data['x-amz-crc32'] = xAmzCrc32;
    return data;
  }
}
