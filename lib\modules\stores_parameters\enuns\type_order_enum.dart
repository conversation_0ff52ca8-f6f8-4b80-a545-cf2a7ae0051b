class TyperOrderStringEnum {
  static const String padrao = "pad";
  static const String especial = "esp";
  static const String rep = "rep";
  static const String bonificado = "bon";
  static const String flex = "flex";
}

class TyperOrderEnum {
  static const int padrao = 1;
  static const int especial = 2;
  static const int bonificado = 3;
  static const int rep = 4;
  static const int flex = 5;
}

String getTyperOrderText(int status) {
  switch (status) {
    case TyperOrderEnum.padrao:
      return TyperOrderStringEnum.padrao;
    case TyperOrderEnum.especial:
      return TyperOrderStringEnum.especial;
    case TyperOrderEnum.bonificado:
      return TyperOrderStringEnum.bonificado;
    case TyperOrderEnum.rep:
      return TyperOrderStringEnum.rep;
    case TyperOrderEnum.flex:
      return TyperOrderStringEnum.flex;
    default:
      return ""; // Ou qualquer outro valor padrão
  }
}

int getTyperOrderValue(String orderText) {
  switch (orderText) {
    case TyperOrderStringEnum.padrao:
      return TyperOrderEnum.padrao;
    case TyperOrderStringEnum.especial:
      return TyperOrderEnum.especial;
    case TyperOrderStringEnum.bonificado:
      return TyperOrderEnum.bonificado;
    case TyperOrderStringEnum.rep:
      return TyperOrderEnum.rep;
    case TyperOrderStringEnum.flex:
      return TyperOrderEnum.flex;
    default:
      return 0; // Ou qualquer outro valor padrão
  }
}
