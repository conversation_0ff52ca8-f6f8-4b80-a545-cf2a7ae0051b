import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';

// ignore: must_be_immutable
class QuestionListWidget extends StatefulWidget {
  QuestionListWidget(
      {super.key,
      required this.title,
      required this.awsers,
      this.selectedAwser,
      required this.onAnswerSelected,
      required this.readonly,
      this.isRequired});

  final bool readonly;
  final String title;
  final List<QuestionListModel> awsers;
  late int? selectedAwser;
  final Function(int?) onAnswerSelected;
  final bool? isRequired;
  @override
  State<QuestionListWidget> createState() => _QuestionListWidgetState();
}

class _QuestionListWidgetState extends State<QuestionListWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          CustomInkWell(
            onTap: widget.readonly ? null : () => openAwnsers(),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  flex: 2,
                  child: LabelWidget(
                    title: widget.title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    fontSize: DeviceSize.fontSize(16, 18),
                    textColor: widget.readonly != true
                        ? Colors.grey.shade600
                        : Colors.grey.shade400,
                  ),
                ),
                15.toWidthSpace(),
                if (widget.selectedAwser != null)
                  Flexible(
                    flex: 2,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Flexible(
                          flex: 2,
                          child: LabelWidget(
                              title: widget.awsers
                                  .where((element) =>
                                      element.value == widget.selectedAwser!)
                                  .first
                                  .text!,
                              fontSize: DeviceSize.fontSize(16, 18),
                              maxLines: 1,
                              textColor: widget.readonly != true
                                  ? Colors.black
                                  : Colors.grey.shade400,
                              overflow: TextOverflow.ellipsis),
                        ),
                        Flexible(
                          child: IconButton(
                            icon: Icon(
                              FontAwesomeIcons.caretDown,
                              color: Colors.grey.shade400,
                            ),
                            onPressed:
                                widget.readonly ? null : () => openAwnsers(),
                          ),
                        ),
                      ],
                    ),
                  ),
                if (widget.selectedAwser == null)
                  Flexible(
                    flex: 1,
                    child: IconButton(
                      icon: Icon(
                        FontAwesomeIcons.caretDown,
                        color: Colors.grey.shade400,
                      ),
                      onPressed: widget.readonly ? null : () => openAwnsers(),
                    ),
                  ),
              ],
            ),
          ),
          Divider(
            thickness: 1,
            color:
                widget.isRequired == true ? Colors.red : Colors.grey.shade300,
          )
        ],
      ),
    );
  }

  void openAwnsers() {
    showDialog(
      context: Get.context!,
      builder: (context) {
        return AlertDialog(
          title: Text(widget.title),
          content: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: widget.awsers.map((item) {
              return RadioListTile<QuestionListModel>(
                title: Text(item.text!),
                value: item,
                groupValue: widget.awsers.any((element) => element.isSelected!)
                    ? widget.awsers
                        .where((element) => element.isSelected!)
                        .first
                    : QuestionListModel(),
                onChanged: (QuestionListModel? resp) {
                  setState(() {
                    widget.awsers.map((e) => e.isSelected = false).toList();
                    item.isSelected = true;
                    widget.selectedAwser = item.value!;
                    widget.onAnswerSelected(resp?.value);
                  });
                  GetC.close();
                },
              );
            }).toList(),
          ),
          actions: [
            TextButton(
              child: const Text('Cancelar'),
              onPressed: () {
                GetC.close();
              },
            ),
          ],
        );
      },
    );
  }
}
