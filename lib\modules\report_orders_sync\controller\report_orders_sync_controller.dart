import 'dart:developer';
import 'dart:io';

import 'package:intl/intl.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/notification/models/notification_model.dart';
import 'package:pharmalink/modules/orders/models/order_attachment_file_request_model.dart';
import 'package:pharmalink/modules/orders_resume/enuns/status_syncronization_enum.dart';
import 'package:pharmalink/modules/orders_resume/models/file_attachment.dart';
import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';

class ReportOrdersSyncController
    extends GetxControllerInstrumentado<ReportOrdersSyncController> {
  ReportOrdersSyncController();

  List<SyncronizationModel> ordersList = [];
  int badgeCount = 0;
  RxBool selectAllInitial = false.obs;

  @override
  Future<void> onReady() async {
    await getData();
    log('onReady ReportOrdersSyncController');
    super.onReady();
  }

  Future<void> getData() async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("Sincronizar Pedidos");
    try {
      ordersList.clear();
      final syncList = await SyncronizationModel().getList(
          workspaceId: appController.workspace!.workspaceId!, storeId: null);
      if (syncList.isNotEmpty) {
        final syncListFinal = syncList
            .where((e) => (e.status ==
                    getStatusSynchronizationValue(
                        StatusSynchronization.notSent) ||
                e.status ==
                    getStatusSynchronizationValue(StatusSynchronization.sent) ||
                e.status ==
                    getStatusSynchronizationValue(
                        StatusSynchronization.sentForApproval) ||
                e.fileAttachment?.status == FileAttachmentStatus.waiting))
            .toList();
        subAction.reportEvent(
            '${syncListFinal.length} itens encontrados, iniciando mapeamento da classe');

        if (syncListFinal.isNotEmpty) {
          syncListFinal.map((e) {
            e.isSelected = false;
            ordersList.add(e);
          }).toList();
          ordersList.sort((a, b) => b.createAt!.compareTo(a.createAt!));
        }
        subAction.reportEvent('itens mapeados');
      }
      selectAllInitial = false.obs;
      setAllSelecteds(false);
    } catch (e, stack) {
      subAction.reportZoneStacktrace(e, stack);
    } finally {
      badgeRefresh();
      leaveAction();
    }
  }

  String getTypeOrderName(int? id) {
    switch (id) {
      case 2:
        return "Pedido Especial";
      case 4:
        return "Pedido Representante";
      case 1:
        return "Pedido Padrão";
      default:
        return "";
    }
  }

  String getOrderTotal(SyncronizationModel data) {
    double total = 0.0;

    if (data.payLoad?.orderInfo != null) {
      for (var order in data.payLoad!.orderInfo!) {
        if (order.payLoad != null) {
          for (var item in order.payLoad!) {
            total = item.totalLiquidoSKU ?? 0.0;
          }
        }
      }
    }

    // Formata o total para o padrão brasileiro (R$ 0,00)
    return NumberFormat.currency(
      locale: 'pt_BR',
      symbol: 'R\$',
      decimalDigits: 2,
    ).format(total);
  }

  void setAllSelecteds(bool state) {
    for (var e in ordersList) {
      e.isSelected = state;
    }
    badgeRefresh();
  }

  void badgeRefresh() {
    badgeCount = ordersList.where((e) => e.isSelected == true).length;

    update();
  }

  ///sincronizar pedidos
  Future<void> syncOrders() async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("SincronizarPedidos");
    try {
      subAction.reportEvent('Validando conexão');
      bool isConnected = await appController
          .withDynatraceAction(dynatraceAction)
          .checkConnectivity();
      if (!isConnected) {
        subAction.reportEvent('Sem internet');
        SnackbarCustom.snackbarError(AppStrings.noInternet);
        return;
      }
      subAction.reportEvent('Validando token');
      await appController
          .withDynatraceAction(dynatraceAction)
          .isValidToken(noMessages: true);

      if (appController
              .withDynatraceAction(dynatraceAction)
              .hasErrorRefreshToken ==
          true) {
        subAction.reportEvent('Token expirado');
        SnackbarCustom.snackbarError(AppStrings.tokenExpired);
        Get.offAndToNamed(RoutesPath.login);
        return;
      }

      final orderIds = ordersList
          .where((e) =>
              e.isSelected == true &&
                  e.status ==
                      getStatusSynchronizationValue(
                          StatusSynchronization.notSent) ||
              e.status ==
                  getStatusSynchronizationValue(StatusSynchronization.sent) ||
              e.status ==
                  getStatusSynchronizationValue(
                      StatusSynchronization.sentForApproval))
          .map((e) => e.transactionKey!)
          .toList();

      if (orderIds.isEmpty) {
        subAction
            .reportEvent('Nenhum pedido foi selecionado para sincronização.');
        SnackbarCustom.snackbarError(AppStrings.noOrderSelected);
        return;
      }
      if (orderIds.isNotEmpty) {
        await storeRoutesController
            .withControllerAction(this)
            .syncStoreData(forceUpdate: true, isUpdateList: true);

        await verifiyMessages();

        subAction.reportEvent(
            'Iniciando processo de sincronização de pedidos - passo 5');
        await synchronizationsController
            .withDynatraceAction(dynatraceAction)
            .syncOrders(ordersToSync: orderIds);

        await Future.delayed(const Duration(seconds: 5));

        subAction.reportEvent(
            'Iniciando processo de sincronização de pedidos - passo 6');
        await synchronizationsController
            .withDynatraceAction(dynatraceAction)
            .syncOrdersRetrieve();

        await getData();
        subAction.reportEvent(
            'Iniciando processo de sincronização de pedidos - finalizado com sucesso');
      }
      final fileOrders = ordersList
          .where((e) =>
              e.status == 4 &&
              e.fileAttachment?.status == FileAttachmentStatus.waiting)
          .map((e) => e)
          .toList();

      if (fileOrders.isNotEmpty) {
        subAction.reportEvent('Sincronização dos anexos iniciado');
        await syncFileOrders(fileOrders);
        await getData();
        subAction.reportEvent('Sincronização dos anexos concluido');
      }
    } catch (e, stack) {
      subAction.reportZoneStacktrace(e, stack);
      rethrow;
    } finally {
      leaveAction();
    }
  }

  Future<void> syncFileOrders(List<SyncronizationModel> fileOrders) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("SincronizarArquivos");
    try {
      subAction.reportEvent('Sincronizando arquivos');

      for (var fileOrder in fileOrders) {
        final fileAttachment = fileOrder.fileAttachment;
        if (fileAttachment != null) {
          final result = await ordersApi.sendFileOrders(
            model: OrderAttachmentFileRequestModel(
              fileName: fileAttachment.fileName,
              fileBytes:
                  File(fileAttachment.selectedFilePath!).readAsBytesSync(),
              filePath: fileAttachment.selectedFilePath,
              fileType: fileAttachment.fileName?.split('.').last,
              userId: fileOrder.userId,
              orderGroupId: fileOrder.transactionKey,
              orderSentForApproval: false,
              orderId: 0,
            ),
          );
          if (result.data != null) {
            fileOrder.fileAttachment?.status = FileAttachmentStatus.synced;
            fileOrder.fileAttachment?.message = result.data;
          } else {
            fileOrder.fileAttachment?.status = FileAttachmentStatus.error;
            fileOrder.fileAttachment?.message = result.error?.message;
          }

          await dbContext.withControllerAction(this).addData(
                clearCurrentData: true,
                data: fileOrder,
                hashCode: fileOrder.transactionKey,
                workspaceId: appController.workspace?.workspaceId,
                storeId: fileOrder.payLoad!.pdvId,
                userId: appController.userLogged!.userId,
                key: DatabaseModels.syncronization,
              );
        }
      }
    } catch (e, stack) {
      subAction.reportZoneStacktrace(e, stack);
    } finally {
      leaveAction();
    }
  }

  Future<void> verifiyMessages() async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("VerificaMensagens");
    try {
      subAction.reportEvent('Buscando se há notificação');
      final message = await NotificationModel().getFirstMessage(1);
      if (message != null) {
        subAction.reportEvent('Notificação encontrada para ser exibida');
        message.isShow = true;
        await dbContext.addData(
          clearCurrentData: true,
          key: DatabaseModels.notificationModel,
          userId: message.id,
          data: message,
          workspaceId: appController.workspace!.workspaceId!,
        );
        await Dialogs.info(
          AppStrings.attention,
          message.message!,
          buttonName: "Entendi",
        );
      }
    } catch (e, stack) {
      subAction.reportZoneStacktrace(e, stack);
    } finally {
      leaveAction();
    }
  }
}
