import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';

class ResearchesMerchanIndustryDataModel
    extends SqfLiteBase<ResearchesMerchanIndustryDataModel> {
  int? workspaceId;
  int? routeId;
  int? isSync;
  List<ResearchesMerchanIndustryFamilias>? familias;
  List<ResearchesMerchanIndustryMerchandising>? merchandising;
  List<ResearchesMerchanIndustryTipoAtivacao>? tipoAtivacao;
  List<ResearchesMerchanIndustryPesquisaMerchandisingItens>?
      pesquisaMerchandisingItens;

  ResearchesMerchanIndustryDataModel(
      {this.workspaceId,
      this.routeId,
      this.isSync,
      this.familias,
      this.merchandising,
      this.tipoAtivacao,
      this.pesquisaMerchandisingItens})
      : super(DatabaseModels.researchesMerchanIndustryDataModel);

  ResearchesMerchanIndustryDataModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.researchesMerchanIndustryDataModel) {
    workspaceId = json['workspaceId'];
    routeId = json['routeId'];
    isSync = json['isSync'] ?? SyncEnum.none;
    if (json['Familias'] != null) {
      familias = <ResearchesMerchanIndustryFamilias>[];
      json['Familias'].forEach((v) {
        familias!.add(ResearchesMerchanIndustryFamilias.fromJson(v));
      });
    }
    if (json['Merchandising'] != null) {
      merchandising = <ResearchesMerchanIndustryMerchandising>[];
      json['Merchandising'].forEach((v) {
        merchandising!.add(ResearchesMerchanIndustryMerchandising.fromJson(v));
      });
    }
    if (json['TipoAtivacao'] != null) {
      tipoAtivacao = <ResearchesMerchanIndustryTipoAtivacao>[];
      json['TipoAtivacao'].forEach((v) {
        tipoAtivacao!.add(ResearchesMerchanIndustryTipoAtivacao.fromJson(v));
      });
    }
    if (json['PesquisaMerchandisingItens'] != null) {
      pesquisaMerchandisingItens =
          <ResearchesMerchanIndustryPesquisaMerchandisingItens>[];
      json['PesquisaMerchandisingItens'].forEach((v) {
        pesquisaMerchandisingItens!.add(
            ResearchesMerchanIndustryPesquisaMerchandisingItens.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workspaceId'] = workspaceId;
    data['routeId'] = routeId;
    data['isSync'] = isSync;
    if (familias != null) {
      data['Familias'] = familias!.map((v) => v.toJson()).toList();
    }
    if (merchandising != null) {
      data['Merchandising'] = merchandising!.map((v) => v.toJson()).toList();
    }
    if (tipoAtivacao != null) {
      data['TipoAtivacao'] = tipoAtivacao!.map((v) => v.toJson()).toList();
    }
    if (pesquisaMerchandisingItens != null) {
      data['PesquisaMerchandisingItens'] =
          pesquisaMerchandisingItens!.map((v) => v.toJson()).toList();
    }
    return data;
  }

  Future<List<ResearchesMerchanIndustryDataModel>> getList(
      {required int workspaceId, required int routeId}) async {
    var list = await getAll<ResearchesMerchanIndustryDataModel>(
        workspaceId: workspaceId,
        storeId: routeId,
        ResearchesMerchanIndustryDataModel.fromJson);
    return list;
  }

  Future<List<ResearchesMerchanIndustryDataModel>> getListToSync(
      {required int workspaceId}) async {
    var list = await getAll<ResearchesMerchanIndustryDataModel>(
        workspaceId: workspaceId, ResearchesMerchanIndustryDataModel.fromJson);
    return (list.isNotEmpty &&
            list.any((element) => element.isSync == SyncEnum.awaited))
        ? list.where((element) => element.isSync == SyncEnum.awaited).toList()
        : [];
  }
}

class ResearchesMerchanIndustryFamilias {
  int? idFamilia;
  String? descricao;
  String? codigo;
  String? foto;
  String? caminhoFoto;
  bool? apagado;
  bool? trade;
  bool? promovido;
  String? centroCusto;
  String? dataInclusao;
  String? dataAlteracao;
  bool? concorrente;

  ResearchesMerchanIndustryFamilias(
      {this.idFamilia,
      this.descricao,
      this.codigo,
      this.foto,
      this.caminhoFoto,
      this.apagado,
      this.trade,
      this.promovido,
      this.centroCusto,
      this.dataInclusao,
      this.dataAlteracao,
      this.concorrente});

  ResearchesMerchanIndustryFamilias.fromJson(Map<String, dynamic> json) {
    idFamilia = json['IdFamilia'];
    descricao = json['Descricao'];
    codigo = json['Codigo'];
    foto = json['Foto'];
    caminhoFoto = json['CaminhoFoto'];
    apagado = json['Apagado'];
    trade = json['Trade'];
    promovido = json['Promovido'];
    centroCusto = json['CentroCusto'];
    dataInclusao = json['DataInclusao'];
    dataAlteracao = json['DataAlteracao'];
    concorrente = json['Concorrente'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdFamilia'] = idFamilia;
    data['Descricao'] = descricao;
    data['Codigo'] = codigo;
    data['Foto'] = foto;
    data['CaminhoFoto'] = caminhoFoto;
    data['Apagado'] = apagado;
    data['Trade'] = trade;
    data['Promovido'] = promovido;
    data['CentroCusto'] = centroCusto;
    data['DataInclusao'] = dataInclusao;
    data['DataAlteracao'] = dataAlteracao;
    data['Concorrente'] = concorrente;
    return data;
  }
}

class ResearchesMerchanIndustryMerchandising {
  int? idMerchandising;
  String? descricao;

  ResearchesMerchanIndustryMerchandising(
      {this.idMerchandising, this.descricao});

  ResearchesMerchanIndustryMerchandising.fromJson(Map<String, dynamic> json) {
    idMerchandising = json['IdMerchandising'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdMerchandising'] = idMerchandising;
    data['Descricao'] = descricao;
    return data;
  }
}

class ResearchesMerchanIndustryTipoAtivacao {
  int? idMerchandisingTipoAtivacao;
  String? descricao;

  ResearchesMerchanIndustryTipoAtivacao(
      {this.idMerchandisingTipoAtivacao, this.descricao});

  ResearchesMerchanIndustryTipoAtivacao.fromJson(Map<String, dynamic> json) {
    idMerchandisingTipoAtivacao = json['IdMerchandisingTipoAtivacao'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdMerchandisingTipoAtivacao'] = idMerchandisingTipoAtivacao;
    data['Descricao'] = descricao;
    return data;
  }
}

class ResearchesMerchanIndustryPesquisaMerchandisingItens {
  int? idPesquisaMerchandising;
  int? idMerchandising;
  String? merchandisingDescricao;
  int? idFamilia;
  String? familiaDescricao;
  int? idTipoAtivacao;
  String? tipoAtivacaoDescricao;
  int? quantidade;

  ResearchesMerchanIndustryPesquisaMerchandisingItens(
      {this.idPesquisaMerchandising,
      this.idMerchandising,
      this.merchandisingDescricao,
      this.idFamilia,
      this.familiaDescricao,
      this.idTipoAtivacao,
      this.tipoAtivacaoDescricao,
      this.quantidade});

  ResearchesMerchanIndustryPesquisaMerchandisingItens.fromJson(
      Map<String, dynamic> json) {
    idPesquisaMerchandising = json['IdPesquisaMerchandising'];
    idMerchandising = json['IdMerchandising'];
    merchandisingDescricao = json['MerchandisingDescricao'];
    idFamilia = json['IdFamilia'];
    familiaDescricao = json['FamiliaDescricao'];
    idTipoAtivacao = json['IdTipoAtivacao'];
    tipoAtivacaoDescricao = json['TipoAtivacaoDescricao'];
    quantidade = json['Quantidade'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPesquisaMerchandising'] = idPesquisaMerchandising;
    data['IdMerchandising'] = idMerchandising;
    data['MerchandisingDescricao'] = merchandisingDescricao;
    data['IdFamilia'] = idFamilia;
    data['FamiliaDescricao'] = familiaDescricao;
    data['IdTipoAtivacao'] = idTipoAtivacao;
    data['TipoAtivacaoDescricao'] = tipoAtivacaoDescricao;
    data['Quantidade'] = quantidade;
    return data;
  }
}
