import 'dart:developer';

import 'package:pharmalink/app_constants.dart';
import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/core/utils/dynatrace/getx_controller_instrumentado.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/logs_http/models/logs_http_model.dart';

class LogsHttpController
    extends GetxControllerInstrumentado<LogsHttpController> {
  LogsHttpController();

  LogsHttpModel? selected;
  List<LogsHttpModel> dataList = [];

  Future<void> openLogs() async {
    dataList = await LogsHttpModel().getList();
    dataList.sort((a, b) => b.createdAt!.compareTo(a.createdAt!));
    update();

    Get.toNamed(RoutesPath.logsHttp);
  }

  Future<void> addLog(LogsHttpModel log) async {
    await dbContext.withControllerAction(this).addData(
        key: DatabaseModels.logsHttpModel,
        data: log,
        workspaceId: appController.workspace!.workspaceId,
        clearCurrentData: false);

    dataList = await LogsHttpModel().getList();
    dataList.sort((a, b) => b.createdAt!.compareTo(a.createdAt!));
    update();
  }

  Future<void> purgeLogs() async {
    if (appController.workspace == null) return;
    try {
      var httpExpirationDays = DateTime.now()
          .subtract(Duration(days: appController.httpExpirationDays));

      await dbContext.deleteByKey(
        key: DatabaseModels.logsHttpModel,
        workspaceId: appController.workspace?.workspaceId,
        isLog: true,
        createdAt: httpExpirationDays,
      );
    } catch (e) {
      log(e.toString());
    }
  }
}
