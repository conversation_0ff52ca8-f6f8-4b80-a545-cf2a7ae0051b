
import 'package:pharmalink/core/extensions/bool_extensions.dart';
import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/core/http_result/http_result.dart';
import 'package:pharmalink/core/utils/constants.dart';
import 'package:pharmalink/modules/home/<USER>/dashboard_request_model.dart';
import 'package:pharmalink/modules/home/<USER>/dashboard_response_model.dart';

abstract class IHomeApi {
  Future<HttpResult<List<DashboardResponse>>> getDashboardReport(DashboardRequest request);
}

class HomeApi extends IHomeApi{
   final HttpManager _httpManager;
  HomeApi(this._httpManager);
  @override
  Future<HttpResult<List<DashboardResponse>>> getDashboardReport(DashboardRequest request) async{

    final result = await _httpManager.restRequest(
      url: 'relatorioAnaliticoPedido/listarRelatorioAnaliticoPedido',
      body: request.toJson(),
      method: HttpMethods.post,
    );

    if (result.data.isNotEmpty && result.statusCode!.isStatusOk()) {
      if (result.data is List) {
        final response = (result.data as List)
            .map((item) => DashboardResponse.fromJson(item))
            .toList();

        return HttpResult.sucess(response);
      } else {
        return HttpResult.error(getOthersStatusCodes(result));
      }
    }
    else if(!result.statusCode!.isStatusOk()){
      return HttpResult.error(getOthersStatusCodes(result));
    }

    return  HttpResult.sucess([]);
  }

}