import 'package:dynatrace_flutter_plugin/dynatrace_flutter_plugin.dart';
import 'package:pharmalink/app_constants.dart';

class DynatraceLogger {
  static void logToDynatrace(String title, dynamic data) {
    if (globalParams.getCurrentStore() != null) {
      title = "$title: ${globalParams.getCurrentStore()!.cNPJ}";
    }
    DynatraceRootAction reportAll = Dynatrace().enterAction("Log: $title");
    try {
      if (data is List) {
        for (var i = 0; i < data.length; i++) {
          _processObject(reportAll, '${i}_', data[i]);
        }
      } else {
        _processObject(reportAll, '', data);
      }
    } finally {
      reportAll.leaveAction();
    }
  }

  static void _processObject(
      DynatraceRootAction reportAll, String prefix, dynamic obj) {
    if (obj == null) return;

    Map<String, dynamic> map = {};
    if (obj is Map<String, dynamic>) {
      map = obj;
    } else if (obj is Map) {
      map = Map<String, dynamic>.from(obj);
    } else {
      map = _objectToMap(obj);
    }

    map.forEach((key, value) {
      if (_isBasicType(value)) {
        reportAll.reportStringValue('$prefix$key', _convertToString(value));
      }
    });
  }

  static Map<String, dynamic> _objectToMap(dynamic obj) {
    return obj.toJson()
        as Map<String, dynamic>; // Assume que o objeto tem um método toJson()
  }

  static bool _isBasicType(dynamic value) {
    return value is String ||
        value is num ||
        value is bool ||
        value is DateTime;
  }

  static String _convertToString(dynamic value) {
    if (value is DateTime) {
      return value.toIso8601String();
    } else if (value is bool) {
      return value ? 'true' : 'false';
    } else {
      return value.toString();
    }
  }
}
