import 'dart:collection';

import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/stores/models/stores_model.dart';

class StoreRoutesPanelController
    extends GetxControllerInstrumentado<StoreRoutesPanelController>
    with TraceableController {
  StoreRoutesPanelController();
  TextEditingController searchController = TextEditingController();
  TextEditingController searchCityController = TextEditingController();
  String? searchStores;
  String? searchStoresByCity;

  int? typeFilter;
  bool completed = false;
  bool notStarted = false;
  bool inProgress = false;

  List<StoresModel> storesFullList = [];
  List<StoresModel> storesList = [];

// Variáveis para paginação
  int currentPage = 1;
  int itemsPerPage = 25;
  bool isLastPage = false;
  bool isLoading = false;

  // Scroll controller para detectar o final da lista
  ScrollController scrollController = ScrollController();

  // Variável para controlar a visibilidade do botão de voltar ao topo
  bool showScrollTopButton = false;

  final rx = 0.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeScrollController();
  }

  @override
  void onClose() {
    scrollController.dispose();
    super.onClose();
  }

  void _initializeScrollController() {
    scrollController.addListener(_scrollListener);
  }

  Future<void> resetAndRefetch() async {
    resetPagination();
    await fetchStores();
    rx.value++;
// Volte ao topo da lista
    if (scrollController.hasClients) {
      scrollController.jumpTo(0);
    }
    // Ensure the UI is updated with the new scroll controller
    update();
  }

  void updateData() {
    update();
  }

  void _scrollListener() {
    if (scrollController.offset >= scrollController.position.maxScrollExtent &&
        !scrollController.position.outOfRange &&
        !isLoading &&
        !isLastPage) {
      loadMoreItems();
    }

    // Atualizar a lógica de exibição do botão
    showScrollTopButton = scrollController.offset > 0;
    update();
  }

  Future<void> loadMoreItems() async {
    if (isLoading || isLastPage) return;
    isLoading = true;
    update();

    currentPage++;
    await Future.delayed(const Duration(seconds: 1)); // Simula carregamento
    setFilterMetaStores(loadMore: true);

    isLoading = false;
    update();
  }

  void scrollToTop() {
    scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  void clearFilters() {
    searchStores = null;
    searchStoresByCity = null;
    searchCityController.text = "";
    searchController.text = "";
    typeFilter = null;
    completed = false;
    notStarted = false;
    inProgress = false;
    setFilterMetaStores();
    resetPagination();
    setFilterMetaStores();
  }

  void resetPagination() {
    currentPage = 1;
    isLastPage = false;
    storesList.clear();
  }

  void setTypeFilter(int? type) {
    typeFilter = type;
    update();
  }

  void setCompleted(bool? v) {
    completed = v ?? false;
    if (v == true) {
      typeFilter = 2;
      notStarted = false;
      inProgress = false;
    } else {
      typeFilter = null;
    }
    setFilterMetaStores();
  }

  void setInProgress(bool? v) {
    inProgress = v ?? false;
    if (v == true) {
      typeFilter = 1;
      notStarted = false;
      completed = false;
    } else {
      typeFilter = null;
    }
    setFilterMetaStores();
  }

  void setNotStarted(bool? v) {
    notStarted = v ?? false;
    if (v == true) {
      typeFilter = 0;
      completed = false;
      inProgress = false;
    } else {
      typeFilter = null;
    }
    setFilterMetaStores();
  }

  void setSearchStores(String? v) {
    searchStores = v;
    update();
    setFilterMetaStores();
  }

  void setSearchStoresByCity(String? v) {
    searchStoresByCity = v;
    setFilterMetaStores();
  }

  void clearSearchStores({bool searchStore = true}) {
    if (searchStore) {
      searchStores = null;
      searchController.text = "";
    } else {
      searchStoresByCity = null;
      searchCityController.text = "";
    }
    setFilterMetaStores();
  }

  Future<void> fetchStores() async {
    final response = await StoresModel().getList(
      hashCode: null,
    );

    storesFullList =
        response.where((x) => x.dataExtra?.canPlanned == false).toList();
    storesFullList.sort((a, b) => a.razaoSocial!.compareTo(b.razaoSocial!));
    setFilterMetaStores(loadMore: false);
    storeRoutesController.setCnpjPanel(storesFullList.length);
    rx.value++;
  }

  Future<void> setCanOrderOfflineSync(StoresModel item, bool? v) async {
    item.dataExtra?.canOrderOfflineSync = v;
    //Resetar os dados offline
    item.dataExtra?.offlineDateSync = null;
    item.dataExtra?.offlineStatus = null;
    item.dataExtra?.offlineId = null;
    item.dataExtra?.offlineTotalPages = null;
    item.dataExtra?.offlineCurrentPages = null;
    item.dataExtra?.offlineMessage = null;
    item.dataExtra?.offlineSyncStatus = SyncEnum.none;
    item.dataExtra?.offlineProgress = 0;
    item.dataExtra?.offlineIsSync = false;

    await storeRoutesController.updateStore(item);
    update();
  }

  Future<void> setCanVisitaSync(StoresModel item, bool? v) async {
    item.dataExtra?.canVisitaSync = v;
    await storeRoutesController.updateStore(item);
    update();
  }

  void setFilterMetaStores({bool loadMore = false}) {
    List<StoresModel> filteredList = [];
    if (!loadMore) {
      resetPagination();
    }

    if (storesFullList.isNotEmpty) {
      storesFullList = _removeDuplicates(storesFullList);

      filteredList = storesFullList.where((x) {
        bool baseCondition;
        if (searchStores != null &&
            (searchStoresByCity != null && searchStoresByCity!.isNotEmpty)) {
          bool isCNPJSearch = containsOnlyNumbersAndPunctuation(searchStores!);
          baseCondition = (isCNPJSearch
                  ? cleanCNPJ(x.cNPJ!).contains(cleanCNPJ(searchStores!))
                  : (x.razaoSocial!.containsInsesitive(searchStores!) ||
                      x.nomeFantasia!.containsInsesitive(searchStores!))) &&
              (x.enderecoPdv != null &&
                  x.enderecoPdv!.containsInsesitive(searchStoresByCity!));
        } else if (searchStores != null) {
          bool isCNPJSearch = containsOnlyNumbersAndPunctuation(searchStores!);
          baseCondition = isCNPJSearch
              ? cleanCNPJ(x.cNPJ!).contains(cleanCNPJ(searchStores!))
              : (x.razaoSocial!.containsInsesitive(searchStores!) ||
                  x.nomeFantasia!.containsInsesitive(searchStores!));
        } else if (searchStoresByCity != null &&
            searchStoresByCity!.isNotEmpty) {
          baseCondition = x.enderecoPdv != null &&
              x.enderecoPdv!.containsInsesitive(searchStoresByCity!);
        } else {
          baseCondition = true;
        }

        if (typeFilter == null) return baseCondition;

        bool filterCondition;
        switch (typeFilter) {
          case 2:
            filterCondition = x.visitasRealizadas! >= x.metaDeVisita!;
            break;
          case 0:
            filterCondition = x.metaDeVisita! > 0 && x.visitasRealizadas! == 0;
            break;
          case 1:
            filterCondition = x.metaDeVisita! > x.visitasRealizadas! &&
                x.visitasRealizadas! > 0;
            break;
          default:
            filterCondition = false;
            break;
        }

        return baseCondition && filterCondition;
      }).toList();
    }

    filteredList = _removeDuplicates(filteredList);

    int startIndex = (currentPage - 1) * itemsPerPage;
    int endIndex = startIndex + itemsPerPage;
    if (endIndex > filteredList.length) {
      endIndex = filteredList.length;
      isLastPage = true;
    }

    List<StoresModel> pageItems = filteredList.sublist(startIndex, endIndex);

    if (loadMore) {
      storesList.addAll(pageItems);
    } else {
      storesList = pageItems;
    }

    update();
  }

  bool containsOnlyNumbersAndPunctuation(String str) {
    return RegExp(r'^[0-9./-]+$').hasMatch(str);
  }

  String cleanCNPJ(String cnpj) {
    return cnpj.replaceAll(RegExp(r'[^\d]'), '');
  }

  // Método auxiliar para remover duplicatas
  List<StoresModel> _removeDuplicates(List<StoresModel> list) {
    return LinkedHashSet<StoresModel>.from(list).toList();
  }
}
