import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';

class VisitGoalsModel extends SqfLiteBase<VisitGoalsModel> {
  int? idVisita;
  String? objetivo;

  VisitGoalsModel({idVisita, objetivo})
      : super(DatabaseModels.visitisInfoModel);

  VisitGoalsModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.visitGoalsModel) {
    idVisita = json['idVisita'];
    objetivo = json['Objetivo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['idVisita'] = idVisita;
    data['Objetivo'] = objetivo;
    return data;
  }
}
