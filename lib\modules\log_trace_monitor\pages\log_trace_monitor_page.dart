import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:pharmalink/core/os/memory_info.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/widgets/label/formatted_json_widget.dart';

class LogTraceMonitorPage extends StatefulWidget {
  const LogTraceMonitorPage({super.key});

  @override
  State<LogTraceMonitorPage> createState() => _LogTraceMonitorPageState();
}

class _LogTraceMonitorPageState extends State<LogTraceMonitorPage> {
  String memoryUsage = 'RAM: 0 MB';
  Timer? _timer;

  @override
  void initState() {
    getMemoryUsage();
    super.initState();
  }

  @override
  void dispose() {
    _timer?.cancel(); // Cancel the timer
    super.dispose();
  }

  Future<void> getMemoryUsage() async {
    final currentMemoryUsage = await MemoryInfo.getMemoryUsage();
    setState(() {
      memoryUsage = currentMemoryUsage;
    });

    _timer = Timer.periodic(const Duration(seconds: 15), (timer) async {
      final currentMemoryUsage = await MemoryInfo.getMemoryUsage();
      // Check if the widget is still mounted
      if (mounted) {
        setState(() {
          memoryUsage = currentMemoryUsage;
        });
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<LogTraceMonitorController>(builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: LabelWidget(
            title: "Auditoria",
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
          actions: [
            Center(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                child: LabelWidget(
                  title: memoryUsage,
                  fontWeight: FontWeight.bold,
                  fontSize: 15,
                  textColor: Colors.white,
                ),
              ),
            ),
            IconButton(
              icon: const Icon(Icons.more_vert),
              onPressed: () {
                showMenu(
                  context: context,
                  position: const RelativeRect.fromLTRB(100, 100, 0, 0),
                  items: [
                    PopupMenuItem(
                      child: ListTile(
                        leading: const Icon(Icons.settings),
                        title: const Text('Cache Config'),
                        onTap: () {
                          GetC.close();
                          Get.toNamed(RoutesPath.cacheConfig);
                        },
                      ),
                    ),
                  ],
                );
              },
            ),
            //  IconButton(
            //   onPressed: () {
            //     Navigator.of(context).push(
            //       MaterialPageRoute(
            //           builder: (context) => MonitoringPage(
            //                 appBarColor: themesController.getPrimaryColor(),
            //                 accentColor: themesController.getPrimaryColor(),
            //               )),
            //     );
            //   },
            //   icon: const Icon(
            //     FontAwesomeIcons.memory,
            //   ),
            // ),
          ],
          leading: IconButton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              GetC.close();
            },
          ),
        ),
        body: Column(
          children: [
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [
                  Expanded(
                    child: TextField(
                      controller: ctrl.searchController,
                      decoration: const InputDecoration(
                        hintText: 'Pesquisar logs...',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        ctrl.searchQuery = value;
                      },
                    ),
                  ),
                  const Gap(8),
                  IconButton(
                    icon: const Icon(Icons.search),
                    onPressed: () {
                      ctrl.filterLogs();
                    },
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () {
                      ctrl.searchQuery = "";
                      ctrl.searchController.text = "";
                      ctrl.filterLogs();
                    },
                  ),
                ],
              ),
            ),
            const Gap(5),
            const Divider(thickness: 1),
            const Gap(5),
            Expanded(
              child: ListView.builder(
                itemCount: ctrl.dataList.length,
                itemBuilder: (context, index) {
                  final item = ctrl.dataList[index];
                  // Substitua isso pelo seu próprio widget que exibe um item
                  return Card(
                    elevation: 3,
                    child: ExpansionTile(
                      leading: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          color: item.type == "Erro:"
                              ? themesController.getColorButton()
                              : themesController.getBackgroundColor(),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            LabelWidget(
                              title: item.createAt?.formatDate(
                                      formatType: DateFormatType.ddMM,
                                      applyTimezone: false) ??
                                  '--/--',
                              fontSize: DeviceSize.fontSize(16, 20),
                              textColor: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                            LabelWidget(
                              title: item.createAt?.formatDate(
                                      formatType: DateFormatType.hHmmss,
                                      applyTimezone: false) ??
                                  '--:--:--',
                              fontSize: DeviceSize.fontSize(12, 12),
                              textColor: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ],
                        ),
                      ),
                      title: LabelWidget(
                        fontSize: DeviceSize.fontSize(14, 20),
                        fontWeight: item.type == "Erro:"
                            ? FontWeight.bold
                            : FontWeight.normal,
                        textColor:
                            item.type == "Erro:" ? Colors.red : Colors.black,
                        title: item.type ?? "Sem identificação",
                      ),
                      subtitle: Row(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: LabelWidget(
                              fontSize: DeviceSize.fontSize(12, 14),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                              title: item.controllerName != null
                                  ? '${item.controllerName ?? ""}.${item.methodName ?? ""}'
                                  : item.textData ?? "-",
                            ),
                          ),
                        ],
                      ),
                      children: <Widget>[
                        if (item.textData != null)
                          ListTile(
                            title: LabelWidget(
                              title: 'Log:',
                              fontSize: DeviceSize.fontSize(12, 14),
                            ),
                            subtitle: LabelWidget(title: item.textData ?? ""),
                          ),
                        if (item.jsonData != null)
                          ListTile(
                            title: LabelWidget(
                              title: 'Json Data:',
                              fontSize: DeviceSize.fontSize(12, 14),
                            ),
                            subtitle: FormattedJsonWidget(
                                jsonString: item.jsonData ?? ""),
                          ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
        bottomNavigationBar: BottomAppBar(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: TextButton(
                  onPressed: () async {
                    Dialogs.confirm(
                      "Enviar informações para o suporte",
                      "Deseja enviar as informações de log para o suporte? Isso ajudará na análise e resolução de possíveis problemas.",
                      onPressedOk: () async {
                        GetC.close();
                        if (kDebugMode) {
                          await ctrl.sendAndShareBackup();
                          //await ctrl.sendBackup();
                        } else {
                          await ctrl.sendBackup();
                        }
                      },
                      onPressedCancel: () {
                        GetC.close();
                      },
                      buttonNameCancel: "Não",
                      buttonNameOk: "Enviar",
                    );
                  },
                  child: const Text('Enviar informações para o suporte'),
                ),
              ),
            ],
          ),
        ),
        bottomSheet: const VersionWidget(),
      );
    });
  }
}
