import 'package:sqlite_explorer/exports/basic_exports.dart';
import 'package:sqlite_explorer/exports/get_exports.dart';
import 'package:sqlite_explorer/modules/home/<USER>/home_controller.dart';
import 'package:sqlite_explorer/widgets/widgets_exports.dart';

class HomeHeader extends StatelessWidget {
  const HomeHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<HomeController>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          color: Colors.black12,
          padding: const EdgeInsets.all(10.0),
          child: const Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                flex: 3,
                child: LabelWidget(
                  title: "SQLite Explorer - Pharmalink",
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  textColor: whiteColor,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
