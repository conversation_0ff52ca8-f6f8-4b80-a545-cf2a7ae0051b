import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class ResearchesMerchanCompetitivePage extends StatelessWidget {
  const ResearchesMerchanCompetitivePage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ResearchesMerchanCompetitiveController>(
        "ResearchesMerchanCompetitiveController", builder: (ctrl) {
      return Scaffold(
          backgroundColor: whiteColor,
          appBar: AppBar(
            backgroundColor: themesController.getPrimaryColor(),
            title: LabelWidget(
              title: "Merchandising Concorrente",
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              textColor: whiteColor,
            ),
            leading: I<PERSON><PERSON><PERSON>on(
              icon: const Icon(
                Icons.arrow_back,
                color: whiteColor,
              ),
              onPressed: () {
                GetC.close();
              },
            ),
          ),
          body: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  LabelWidget(
                    title: "Respostas",
                    fontSize: DeviceSize.fontSize(13, 16),
                  ),
                  20.toHeightSpace(),
                  ...ctrl.awsersList.map((e) => SizedBox(
                        width: double.infinity,
                        child: Card(
                          elevation: 4,
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    LabelWidget(
                                      title: e.familiaDescricao ?? "-",
                                      fontSize: DeviceSize.fontSize(16, 20),
                                      fontWeight: FontWeight.bold,
                                    ),
                                    IconButton(
                                      onPressed: () {
                                        ctrl.openMenuAwnser(e);
                                      },
                                      icon: Icon(
                                        FontAwesomeIcons.ellipsisVertical,
                                        color:
                                            themesController.getColorButton(),
                                        size: DeviceSize.fontSize(18, 21),
                                      ),
                                    )
                                  ],
                                ),
                                20.toHeightSpace(),
                                LabelWidget(
                                  title:
                                      "${e.merchandisingDescricao ?? "-"} - ${e.tipoAtivacaoDescricao ?? "-"}",
                                  fontSize: DeviceSize.fontSize(15, 19),
                                ),
                                LabelWidget(
                                  title: "${e.quantidade ?? "0"} unidades",
                                  fontSize: DeviceSize.fontSize(14, 18),
                                  textColor: Colors.grey.shade600,
                                ),
                                10.toHeightSpace(),
                              ],
                            ),
                          ),
                        ),
                      ))
                ],
              ),
            ),
          ),
          floatingActionButton: FloatingActionButton(
            onPressed: () {
              ctrl.openQuestions();
            },
            backgroundColor: themesController.getPrimaryColor(),
            child: const Icon(Icons.add),
          ));
    });
  }
}
