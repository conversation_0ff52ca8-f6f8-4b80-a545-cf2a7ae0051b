import 'package:flutter/widgets.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/visits/models/visit_selected_model.dart';

class VisitsSyncRequestModel {
  int? idRota;
  int? idRoteiro;
  int? idPdv;
  int? ordem;
  int? idVisita;
  DateTime? dataInclusao;
  int? idVisitaStatus;
  int? idVisitaMotivo;
  int? idVisitaAcompanhamento;
  int? idVisitaPeriodo;
  String? comentario;
  String? objetivo;
  bool? enviado;
  String? mensagem;
  bool? visita;
  DateTime? dataCheckIn;
  DateTime? dataCheckOut;
  double? latitudeCheckIn;
  double? longitudeCheckIn;
  double? latitudeCheckOut;
  double? longitudeCheckOut;
  List<VisitsSyncRequestVisitImage>? visitaImagem;
  int? tipoVisita;
  String? acompanhante;
  List<VisitSelectedModel>? visitSelectedList;

  VisitsSyncRequestModel({
    this.idRota,
    this.idRoteiro,
    this.idPdv,
    this.ordem,
    this.idVisita,
    this.dataInclusao,
    this.idVisitaStatus,
    this.idVisitaMotivo,
    this.idVisitaAcompanhamento,
    this.idVisitaPeriodo,
    this.comentario,
    this.objetivo,
    this.enviado,
    this.mensagem,
    this.visita,
    this.dataCheckIn,
    this.dataCheckOut,
    this.latitudeCheckIn,
    this.longitudeCheckIn,
    this.latitudeCheckOut,
    this.longitudeCheckOut,
    this.visitaImagem,
    this.tipoVisita,
    this.acompanhante,
    this.visitSelectedList,
  });

  VisitsSyncRequestModel.fromJson(Map<String, dynamic> json) {
    idRota = json['IdRota'];
    idRoteiro = json['IdRoteiro'];
    idPdv = json['IdPdv'];
    ordem = json['Ordem'];
    idVisita = json['IdVisita'];
    dataInclusao =
        json['DataInclusao'] != null
            ? DateTime.parse(json['DataInclusao'])
            : null;
    idVisitaStatus = json['IdVisitaStatus'];
    idVisitaMotivo = json['IdVisitaMotivo'];
    idVisitaAcompanhamento = json['IdVisitaAcompanhamento'];
    idVisitaPeriodo = json['IdVisitaPeriodo'];
    comentario = json['Comentario'];
    objetivo = json['Objetivo'];
    enviado = json['Enviado'];
    mensagem = json['Mensagem'];
    visita = json['Visita'];
    dataCheckIn =
        json['DataCheckIn'] != null
            ? DateTime.parse(json['DataCheckIn'])
            : null;
    dataCheckOut =
        json['DataCheckOut'] != null
            ? DateTime.parse(json['DataCheckOut'])
            : null;
    latitudeCheckIn = json['LatitudeCheckIn'];
    longitudeCheckIn = json['LongitudeCheckIn'];
    latitudeCheckOut = json['LatitudeCheckOut'];
    longitudeCheckOut = json['LongitudeCheckOut'];
    if (json['VisitaImagem'] != null) {
      visitaImagem = <VisitsSyncRequestVisitImage>[];
      json['VisitaImagem'].forEach((v) {
        visitaImagem!.add(VisitsSyncRequestVisitImage.fromJson(v));
      });
    }
    tipoVisita = json['TipoVisita'];
    acompanhante = json['Acompanhante'];
    visitSelectedList =
        json['AcompanhamentoVisitasDto'] != null
            ? (json['AcompanhamentoVisitasDto'] as List)
                .map((e) => VisitSelectedModel.fromJson(e))
                .toList()
            : [];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdRota'] = idRota;
    data['IdRoteiro'] = idRoteiro;
    data['IdPdv'] = idPdv;
    data['Ordem'] = ordem;
    data['IdVisita'] = idVisita;
    data['DataInclusao'] = dataInclusao?.toIso8601String();
    data['IdVisitaStatus'] = idVisitaStatus;
    data['IdVisitaMotivo'] = idVisitaMotivo;
    data['IdVisitaPeriodo'] = idVisitaPeriodo;
    data['Comentario'] = comentario;
    data['Objetivo'] = objetivo;
    data['Enviado'] = enviado;
    data['Mensagem'] = mensagem;
    data['Visita'] = visita;
    data['DataCheckIn'] = dataCheckIn?.toIso8601String();
    data['DataCheckOut'] = dataCheckOut?.toIso8601String();
    data['LatitudeCheckIn'] = latitudeCheckIn;
    data['LongitudeCheckIn'] = longitudeCheckIn;
    data['LatitudeCheckOut'] = latitudeCheckOut;
    data['LongitudeCheckOut'] = longitudeCheckOut;
    if (visitaImagem != null) {
      data['VisitaImagem'] = visitaImagem!.map((v) => v.toJson()).toList();
    }
    data['TipoVisita'] = tipoVisita;
    data['AcompanhamentoVisitasDto'] =
        visitSelectedList?.map((e) => e.toJson()).toList();
    return data;
  }
}

class VisitsSyncRequestVisitImage {
  int? idVisitaImagem;
  int? idRota;
  String? arquivo;
  String? nomeImagem;
  String? caminhoCompleto;
  String? descricao;
  Orientation? orientation;
  TextEditingController descricaoImagemController = TextEditingController();

  VisitsSyncRequestVisitImage({
    this.idVisitaImagem,
    this.idRota,
    this.arquivo,
    this.nomeImagem,
    this.caminhoCompleto,
    this.descricao,
    this.orientation,
  });

  VisitsSyncRequestVisitImage.fromJson(Map<String, dynamic> json) {
    idVisitaImagem = json['IdVisitaImagem'];
    idRota = json['IdRota'];
    arquivo = json['Arquivo'];
    nomeImagem = json['NomeImagem'];
    caminhoCompleto = json['CaminhoCompleto'];
    descricao = json['Descricao'];
    orientation =
        json['Orientation'] != null
            ? Orientation.values.firstWhere(
              (element) => element.name == json['Orientation'],
              orElse: () => Orientation.portrait,
            )
            : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdVisitaImagem'] = idVisitaImagem;
    data['IdRota'] = idRota;
    data['Arquivo'] = arquivo;
    data['NomeImagem'] = nomeImagem;
    data['CaminhoCompleto'] = caminhoCompleto;
    data['Descricao'] = descricao;
    data['Orientation'] = orientation?.name;
    return data;
  }
}
