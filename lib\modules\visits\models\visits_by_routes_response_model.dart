import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/stores/enuns/visit_status_enum.dart';
import 'package:pharmalink/modules/visits/models/visit_selected_model.dart';
import 'package:pharmalink/modules/visits/models/visits_sync_request_model.dart';

class VisitsByRoutesResponseModel
    extends SqfLiteBase<VisitsByRoutesResponseModel> {
  int? idRota;
  int? idRoteiro;
  int? idLoja;
  int? idVisita;
  String? dataVisita;
  List<StatusVisita>? statusVisita;
  int? idStatusVisitaSelecionado;
  List<MotivosVisitaNaoVisitada>? motivosVisitaNaoVisitada;
  List<MotivosVisitaJustificada>? motivosVisitaJustificada;
  int? idMotivoVisitaSelecionado;
  List<AcompanhamentosVisita>? acompanhamentosVisita;
  int? idAcompanhamentoVisitaSelecionado;
  List<PeriodosVisita>? periodosVisita;
  int? idPeriodoVisitaSelecionado;
  String? comentarioVisita;
  String? objetivoVisita;
  bool? enviar;
  int? tipoVisita;
  String? acompanhante;
  String? mensagem;
  List<VisitaImagem>? visitaImagem;
  List<VisitsSyncRequestVisitImage>? images;
  DateTime? startTime;
  int? isSync;
  DateTime? endTime;
  double? latitudeCheckIn; // Você pode definir o tipo apropriado aqui
  double? longitudeCheckIn; // Você pode definir o tipo apropriado aqui
  double? latitudeCheckOut; // Você pode definir o tipo apropriado aqui
  double? longitudeCheckOut; // Você pode definir o tipo apropriado aqui
  int? statusVisit;
  DateTime? currentDate;
  String? lastVisitGoal;
  List<VisitSelectedModel>? visitSelectedList;

  VisitsByRoutesResponseModel({
    this.idRota,
    this.idRoteiro,
    this.idLoja,
    this.idVisita,
    this.dataVisita,
    this.statusVisita,
    this.idStatusVisitaSelecionado,
    this.motivosVisitaNaoVisitada,
    this.motivosVisitaJustificada,
    this.idMotivoVisitaSelecionado,
    this.acompanhamentosVisita,
    this.idAcompanhamentoVisitaSelecionado,
    this.periodosVisita,
    this.idPeriodoVisitaSelecionado,
    this.comentarioVisita,
    this.objetivoVisita,
    this.enviar,
    this.tipoVisita,
    this.acompanhante,
    this.mensagem,
    this.visitaImagem,
    this.images,
    this.startTime,
    this.endTime,
    this.latitudeCheckIn,
    this.longitudeCheckIn,
    this.latitudeCheckOut,
    this.longitudeCheckOut,
    this.isSync,
    this.statusVisit,
    this.currentDate,
    this.lastVisitGoal,
    this.visitSelectedList,
  }) : super(DatabaseModels.visitsByRoutesResponseModel);

  VisitsByRoutesResponseModel.fromJson(Map<String, dynamic> json)
    : super(DatabaseModels.visitsByRoutesResponseModel) {
    idRota = json['IdRota'];
    idRoteiro = json['IdRoteiro'];
    idLoja = json['IdLoja'];
    idVisita = json['IdVisita'];
    dataVisita = json['DataVisita'];
    if (json['StatusVisita'] != null) {
      statusVisita = <StatusVisita>[];
      json['StatusVisita'].forEach((v) {
        statusVisita!.add(StatusVisita.fromJson(v));
      });
    }
    idStatusVisitaSelecionado = json['IdStatusVisitaSelecionado'];
    if (json['MotivosVisitaNaoVisitada'] != null) {
      motivosVisitaNaoVisitada = <MotivosVisitaNaoVisitada>[];
      json['MotivosVisitaNaoVisitada'].forEach((v) {
        motivosVisitaNaoVisitada!.add(MotivosVisitaNaoVisitada.fromJson(v));
      });
    }
    if (json['MotivosVisitaJustificada'] != null) {
      motivosVisitaJustificada = <MotivosVisitaJustificada>[];
      json['MotivosVisitaJustificada'].forEach((v) {
        motivosVisitaJustificada!.add(MotivosVisitaJustificada.fromJson(v));
      });
    }
    idMotivoVisitaSelecionado = json['IdMotivoVisitaSelecionado'];
    if (json['AcompanhamentosVisita'] != null) {
      acompanhamentosVisita = <AcompanhamentosVisita>[];
      json['AcompanhamentosVisita'].forEach((v) {
        acompanhamentosVisita!.add(AcompanhamentosVisita.fromJson(v));
      });
    }
    idAcompanhamentoVisitaSelecionado =
        json['IdAcompanhamentoVisitaSelecionado'];
    if (json['PeriodosVisita'] != null) {
      periodosVisita = <PeriodosVisita>[];
      json['PeriodosVisita'].forEach((v) {
        periodosVisita!.add(PeriodosVisita.fromJson(v));
      });
    }
    idPeriodoVisitaSelecionado = json['IdPeriodoVisitaSelecionado'];
    comentarioVisita = json['ComentarioVisita'];
    objetivoVisita = json['ObjetivoVisita'];
    enviar = json['Enviar'];
    tipoVisita = json['TipoVisita'];
    acompanhante = json['Acompanhante'];
    isSync = json['isSync'];
    mensagem = json['Mensagem'];
    latitudeCheckIn = json['LatitudeCheckIn'];
    longitudeCheckIn = json['LongitudeCheckIn'];
    latitudeCheckOut = json['LatitudeCheckOut'];
    longitudeCheckOut = json['LongitudeCheckOut'];
    if (json['VisitaImagem'] != null) {
      visitaImagem = <VisitaImagem>[];
      json['VisitaImagem'].forEach((v) {
        visitaImagem!.add(VisitaImagem.fromJson(v));
      });
    }
    images =
        json['images'] != null
            ? (json['images'] as List)
                .map((e) => VisitsSyncRequestVisitImage.fromJson(e))
                .toList()
            : null;
    startTime =
        json['startTime'] != null ? DateTime.parse(json['startTime']) : null;
    endTime = json['endTime'] != null ? DateTime.parse(json['endTime']) : null;
    statusVisit = json['statusVisit'] ?? StatusVisitEnum.notInitilized;

    currentDate =
        json['currentDate'] != null
            ? DateTime.parse(json['currentDate'])
            : null;
    lastVisitGoal = json['lastVisitGoal'];
    visitSelectedList =
        json['AcompanhamentoVisitasDto'] != null
            ? (json['AcompanhamentoVisitasDto'] as List)
                .map((e) => VisitSelectedModel.fromJson(e))
                .toList()
            : [];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdLoja'] = idLoja;
    data['IdRota'] = idRota;
    data['IdRoteiro'] = idRoteiro;
    data['IdVisita'] = idVisita;
    data['DataVisita'] = dataVisita;
    if (statusVisita != null) {
      data['StatusVisita'] = statusVisita!.map((v) => v.toJson()).toList();
    }
    data['IdStatusVisitaSelecionado'] = idStatusVisitaSelecionado;
    if (motivosVisitaNaoVisitada != null) {
      data['MotivosVisitaNaoVisitada'] =
          motivosVisitaNaoVisitada!.map((v) => v.toJson()).toList();
    }
    if (motivosVisitaJustificada != null) {
      data['MotivosVisitaJustificada'] =
          motivosVisitaJustificada!.map((v) => v.toJson()).toList();
    }
    data['IdMotivoVisitaSelecionado'] = idMotivoVisitaSelecionado;
    if (acompanhamentosVisita != null) {
      data['AcompanhamentosVisita'] =
          acompanhamentosVisita!.map((v) => v.toJson()).toList();
    }
    data['IdAcompanhamentoVisitaSelecionado'] =
        idAcompanhamentoVisitaSelecionado;
    if (periodosVisita != null) {
      data['PeriodosVisita'] = periodosVisita!.map((v) => v.toJson()).toList();
    }
    data['IdPeriodoVisitaSelecionado'] = idPeriodoVisitaSelecionado;
    data['ComentarioVisita'] = comentarioVisita;
    data['ObjetivoVisita'] = objetivoVisita;
    data['Enviar'] = enviar;
    data['TipoVisita'] = tipoVisita;
    data['Acompanhante'] = acompanhante;
    data['Mensagem'] = mensagem;
    data['isSync'] = isSync;
    data['LatitudeCheckIn'] = latitudeCheckIn;
    data['LongitudeCheckIn'] = longitudeCheckIn;
    data['LatitudeCheckOut'] = latitudeCheckOut;
    data['LongitudeCheckOut'] = longitudeCheckOut;
    if (visitaImagem != null) {
      data['VisitaImagem'] = visitaImagem!.map((v) => v.toJson()).toList();
    }
    data['images'] = images?.map((e) => e.toJson()).toList();
    data['startTime'] = startTime?.toIso8601String();
    data['endTime'] = endTime?.toIso8601String();
    data['statusVisit'] = statusVisit ?? StatusVisitEnum.notInitilized;
    data['currentDate'] = currentDate?.toIso8601String();
    data['lastVisitGoal'] = lastVisitGoal;
    data['AcompanhamentoVisitasDto'] =
        visitSelectedList?.map((e) => e.toJson()).toList();
    return data;
  }

  Future<VisitsByRoutesResponseModel?> getFirst({
    required int workspaceId,
    required int routeId,
  }) async {
    var list = await getAll<VisitsByRoutesResponseModel>(
        workspaceId: workspaceId,
        storeId: routeId,
        userId: appController.userLogged!.userId,
        VisitsByRoutesResponseModel.fromJson);
    return list.isNotEmpty ? list.first : null;
  }

  Future<bool> exists({required int workspaceId}) async {
    var list = await getAll<VisitsByRoutesResponseModel>(
        workspaceId: workspaceId,
        userId: appController.userLogged!.userId,
        VisitsByRoutesResponseModel.fromJson);
    return list.isNotEmpty;
  }

  Future<List<VisitsByRoutesResponseModel>> getList({
    required int workspaceId,
    bool? notFiltred,
  }) async {
    var list = await getAll<VisitsByRoutesResponseModel>(
        workspaceId: workspaceId,
        userId: appController.userLogged!.userId,
        VisitsByRoutesResponseModel.fromJson);
    return notFiltred == true
        ? list
        : list.where((element) => element.currentDate != null).toList();
  }

  Future<List<VisitsByRoutesResponseModel>> getListToSync({
    required int workspaceId,
  }) async {
    var list = await getAll<VisitsByRoutesResponseModel>(
        workspaceId: workspaceId,
        userId: appController.userLogged!.userId,
        VisitsByRoutesResponseModel.fromJson);
    return list.isNotEmpty
        ? list.where((element) => element.isSync == SyncEnum.awaited).toList()
        : [];
  }

  Future<void> removeOldVisits() async {
    // Remover as visitas que não foram sincronizadas no dia anterior
    final list = await getList(
      workspaceId: appController.workspace!.workspaceId!,
      notFiltred: true,
    );

    final today = DateTime.now();
    final todayDate = DateTime(today.year, today.month, today.day);
    final newList =
        list.where((element) {
          if (element.currentDate == null) return true;
          final elementDate = DateTime(
            element.currentDate!.year,
            element.currentDate!.month,
            element.currentDate!.day,
          );
          return elementDate.isBefore(todayDate);
        }).toList();

    if (newList.isNotEmpty) {
      for (var item in newList) {
        await dbContext.deleteByKey(
          key: DatabaseModels.visitsByRoutesResponseModel,
          storeId: item.idRota,
          userId: appController.userLogged!.userId,
          workspaceId: appController.workspace!.workspaceId,
        );
      }
    }
  }
}

class StatusVisita {
  int? idVisitaStatus;
  String? descricao;

  StatusVisita({this.idVisitaStatus, this.descricao});

  StatusVisita.fromJson(Map<String, dynamic> json) {
    idVisitaStatus = json['IdVisitaStatus'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdVisitaStatus'] = idVisitaStatus;
    data['Descricao'] = descricao;
    return data;
  }
}

class MotivosVisitaNaoVisitada {
  int? idVisitaMotivo;
  String? descricao;

  MotivosVisitaNaoVisitada({this.idVisitaMotivo, this.descricao});

  MotivosVisitaNaoVisitada.fromJson(Map<String, dynamic> json) {
    idVisitaMotivo = json['IdVisitaMotivo'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdVisitaMotivo'] = idVisitaMotivo;
    data['Descricao'] = descricao;
    return data;
  }
}

class MotivosVisitaJustificada {
  int? idVisitaMotivo;
  String? descricao;

  MotivosVisitaJustificada({this.idVisitaMotivo, this.descricao});

  MotivosVisitaJustificada.fromJson(Map<String, dynamic> json) {
    idVisitaMotivo = json['IdVisitaMotivo'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdVisitaMotivo'] = idVisitaMotivo;
    data['Descricao'] = descricao;
    return data;
  }
}

class AcompanhamentosVisita {
  int? idVisitaAcompanhamento;
  String? descricao;

  AcompanhamentosVisita({this.idVisitaAcompanhamento, this.descricao});

  AcompanhamentosVisita.fromJson(Map<String, dynamic> json) {
    idVisitaAcompanhamento = json['IdVisitaAcompanhamento'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdVisitaAcompanhamento'] = idVisitaAcompanhamento;
    data['Descricao'] = descricao;
    return data;
  }
}

class PeriodosVisita {
  int? idVisitaPeriodo;
  String? descricao;

  PeriodosVisita({this.idVisitaPeriodo, this.descricao});

  PeriodosVisita.fromJson(Map<String, dynamic> json) {
    idVisitaPeriodo = json['IdVisitaPeriodo'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdVisitaPeriodo'] = idVisitaPeriodo;
    data['Descricao'] = descricao;
    return data;
  }
}

class VisitaImagem {
  int? idVisitaImagem;
  int? idRota;
  String? descricao;
  String? arquivo;
  String? nomeImagem;
  String? caminhoCompleto;

  TextEditingController descricaoImagemController = TextEditingController();

  VisitaImagem({
    this.idVisitaImagem,
    this.idRota,
    this.descricao,
    this.arquivo,
    this.nomeImagem,
    this.caminhoCompleto,
  });

  VisitaImagem.fromJson(Map<String, dynamic> json) {
    idVisitaImagem = json['IdVisitaImagem'];
    idRota = json['IdRota'];
    descricao = json['Descricao'];
    arquivo = json['Arquivo'];
    nomeImagem = json['NomeImagem'];
    caminhoCompleto = json['CaminhoCompleto'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdVisitaImagem'] = idVisitaImagem;
    data['IdRota'] = idRota;
    data['Descricao'] = descricao;
    data['Arquivo'] = arquivo;
    data['NomeImagem'] = nomeImagem;
    data['CaminhoCompleto'] = caminhoCompleto;
    return data;
  }
}

class TipoVisita {
  int? idTipoVisita;
  String? descricao;

  TipoVisita({this.idTipoVisita, this.descricao});

  TipoVisita.fromJson(Map<String, dynamic> json) {
    idTipoVisita = json['IdTipoVisita'];
    descricao = json['Descricao'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdTipoVisita'] = idTipoVisita;
    data['Descricao'] = descricao;
    return data;
  }
}
