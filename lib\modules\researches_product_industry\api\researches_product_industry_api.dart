import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/researches_product_industry/models/researches_product_industry_model.dart';
import 'package:pharmalink/modules/researches_product_industry/models/researches_product_industry_save_model.dart';

abstract class IResearchesProductIndustryApi {
  Future<HttpResponse<ResearchesProductIndustryDataModel>> getProductIndustry(
      {required int routeId});

  Future<HttpResponse<String?>> sendProductIndustry(
      {required ResearchesProductIndustrySaveSync model});
}

class ResearchesProductIndustryApi extends IResearchesProductIndustryApi {
  final HttpManager _httpManager;
  ResearchesProductIndustryApi(this._httpManager);

  @override
  Future<HttpResponse<ResearchesProductIndustryDataModel>> getProductIndustry(
      {required int routeId}) async {
    return await _httpManager.request<ResearchesProductIndustryDataModel>(
      path: 'pesquisasProduto/listarProdutosPesquisaPorIdRota/$routeId',
      method: HttpMethods.get,
      parser: (data) {
        return ResearchesProductIndustryDataModel.fromJson(data);
      },
    );
  }

  @override
  Future<HttpResponse<String?>> sendProductIndustry(
      {required ResearchesProductIndustrySaveSync model}) async {
    return await _httpManager.request<String?>(
      path: 'pesquisasProduto/salvarPesquisaProduto',
      method: HttpMethods.post,
      body: model.toJson(),
      parser: (data) {
        return data.toString();
      },
    );
  }
}
