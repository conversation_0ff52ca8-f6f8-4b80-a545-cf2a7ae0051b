import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/store_routes_panel/pages/widgets/store_routes_panel_card_widget.dart';

class StoreRoutesPanelPage extends StatelessWidget {
  const StoreRoutesPanelPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<StoreRoutesPanelController>(
      builder: (ctrl) {
        return Scaffold(
          body: ListView(
            controller: ctrl.scrollController,
            padding: const EdgeInsets.symmetric(horizontal: 4),
            children: [
              const Gap(10),
              SearchTextFieldWidget(
                label: 'Pesquise por um PDV',
                hintText: 'Pesquise pelo CNPJ, Razão Social ou Nome Fantasia',
                borderColor: themesController.getIconColor(),
                iconColor: themesController.getIconColor(),
                iconSize: 18,
                labelColor: Colors.grey,
                labelFontSize: DeviceSize.fontSize(16, 18),
                keyboardType: TextInputType.text,
                controller: ctrl.searchController,
                onChanged: ctrl.setSearchStores,
                trailingIcon: FontAwesomeIcons.magnifyingGlass,
                trailingTap: () {
                  //ctrl.clearSearchStores();
                },
              ),
              const Gap(5),
              SearchTextFieldWidget(
                label: 'Pesquise por cidade',
                hintText: 'Pesquise pelo nome da cidade, bairro ou estado',
                borderColor: themesController.getIconColor(),
                iconColor: themesController.getIconColor(),
                iconSize: 18,
                labelColor: Colors.grey,
                labelFontSize: DeviceSize.fontSize(16, 18),
                keyboardType: TextInputType.text,
                controller: ctrl.searchCityController,
                onChanged: ctrl.setSearchStoresByCity,
                trailingIcon: FontAwesomeIcons.magnifyingGlass,
                trailingTap: () {
                  //ctrl.clearSearchStores();
                },
              ),
              const Gap(6),
              LabelWidget(
                title: 'Meta de visitação',
                textColor: Colors.grey,
                fontSize: DeviceSize.fontSize(16, 18),
              ),

              CheckBoxHorizontalWidget(
                items: [
                  CheckboxItem(
                    type: 2,
                    label: 'Concluída',
                    value: ctrl.completed,
                    onChanged: ctrl.setCompleted,
                  ),
                  CheckboxItem(
                    type: 1,
                    label: 'Em Andamento',
                    value: ctrl.inProgress,
                    onChanged: ctrl.setInProgress,
                  ),
                  CheckboxItem(
                    type: 0,
                    label: 'Não Iniciada',
                    value: ctrl.notStarted,
                    onChanged: ctrl.setNotStarted,
                  ),
                ],
              ),
              const Gap(8),
              //Clear Filters
              Align(
                alignment: Alignment.centerRight,
                child: GestureDetector(
                  onTap: () {
                    ctrl.clearFilters();
                  },
                  child: LabelWidget(
                    title: 'Limpar Filtros',
                    fontSize: DeviceSize.fontSize(13, 14),
                    textColor: Colors.blue.shade800,
                  ),
                ),
              ),
              const Gap(8),
              if (ctrl.storesList.isEmpty)
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const SizedBox(height: 40),
                    const Icon(
                      Icons.store_mall_directory_outlined,
                      size: 60,
                      color: Colors.grey,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'Nenhum PDV encontrado',
                      style: TextStyle(
                        fontSize: DeviceSize.fontSize(16, 18),
                        color: Colors.grey,
                      ),
                    ),
                  ],
                )
              else
                ...ctrl.storesList
                    .map((item) => StoreRoutesPanelCardWidget(data: item)),
              if (ctrl.isLoading)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  child: Center(
                    child: CircularProgressIndicator(
                      color: themesController.getBackgroundColor(),
                    ),
                  ),
                ),
            ],
          ),
          floatingActionButton: ctrl.showScrollTopButton
              ? SizedBox(
                  width: 40,
                  height: 40,
                  child: FloatingActionButton(
                    onPressed: ctrl.scrollToTop,
                    backgroundColor: themesController.getColorButton(),
                    child: const Icon(Icons.arrow_upward, size: 20),
                  ),
                )
              : null,
        );
      },
    );
  }
}
