import 'package:flutter/material.dart';

class MultiSelectCategoryField<T> extends StatelessWidget {
  final List<T> allItems;
  final List<T> selectedItems;
  final String Function(T) itemLabel;
  final bool Function(T a, T b) areItemsEqual;
  final Function(T) onCategoryToggled;
  final VoidCallback onClearAll;
  final TextEditingController displayController;
  final String? hintText;

  const MultiSelectCategoryField({
    super.key,
    required this.allItems,
    required this.selectedItems,
    required this.itemLabel,
    required this.areItemsEqual,
    required this.onCategoryToggled,
    required this.onClearAll,
    required this.displayController,
    this.hintText = "Selecione os itens",
  });

  void _showSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return _MultiSelectDialog<T>(
          allItems: allItems,
          selectedItems: selectedItems,
          itemLabel: itemLabel,
          areItemsEqual: areItemsEqual,
          onItemToggled: onCategoryToggled,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => _showSelectionDialog(context),
      child: AbsorbPointer(
        child: TextFormField(
          controller: displayController,
          readOnly: true,
          decoration: InputDecoration(
            hintText: displayController.text.isEmpty ? hintText : null,
            suffixIcon:
                selectedItems.isNotEmpty
                    ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: onClearAll,
                    )
                    : const Icon(Icons.arrow_drop_down),
          ),
        ),
      ),
    );
  }
}

class _MultiSelectDialog<T> extends StatefulWidget {
  final List<T> allItems;
  final List<T> selectedItems;
  final String Function(T) itemLabel;
  final bool Function(T a, T b) areItemsEqual;
  final Function(T) onItemToggled;

  const _MultiSelectDialog({
    required this.allItems,
    required this.selectedItems,
    required this.itemLabel,
    required this.areItemsEqual,
    required this.onItemToggled,
  });

  @override
  State<_MultiSelectDialog<T>> createState() => _MultiSelectDialogState<T>();
}

class _MultiSelectDialogState<T> extends State<_MultiSelectDialog<T>> {
  String _searchTerm = '';
  final TextEditingController _searchController = TextEditingController();
  late List<T> _filteredItems;

  @override
  void initState() {
    super.initState();
    _filteredItems = List.from(widget.allItems);

    _searchController.addListener(() {
      if (mounted) {
        setState(() {
          _searchTerm = _searchController.text.toLowerCase();
          _filterItems();
        });
      }
    });
  }

  void _filterItems() {
    if (_searchTerm.isEmpty) {
      _filteredItems = List.from(widget.allItems);
    } else {
      _filteredItems =
          widget.allItems
              .where(
                (item) =>
                    widget.itemLabel(item).toLowerCase().contains(_searchTerm),
              )
              .toList();
    }
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Selecionar Itens'),
      contentPadding: const EdgeInsets.fromLTRB(20, 20, 20, 0),
      content: SizedBox(
        width: double.maxFinite,
        height: MediaQuery.of(context).size.height * 0.7,
        child: Column(
          children: [
            TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Buscar...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon:
                    _searchTerm.isNotEmpty
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: () => _searchController.clear(),
                        )
                        : null,
              ),
            ),
            if (widget.selectedItems.isNotEmpty)
              Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.15,
                ),
                margin: const EdgeInsets.symmetric(vertical: 8),
                child: SingleChildScrollView(
                  child: Wrap(
                    spacing: 6,
                    children:
                        widget.selectedItems.map((item) {
                          return Chip(
                            label: Text(widget.itemLabel(item)),
                            onDeleted: () {
                              setState(() {
                                widget.onItemToggled(item);
                              });
                            },
                          );
                        }).toList(),
                  ),
                ),
              ),
            Expanded(
              child:
                  _filteredItems.isEmpty
                      ? const Center(child: Text('Nenhum item encontrado.'))
                      : ListView.builder(
                        itemCount: _filteredItems.length,
                        itemBuilder: (_, index) {
                          final item = _filteredItems[index];
                          final isSelected = widget.selectedItems.any(
                            (selected) => widget.areItemsEqual(selected, item),
                          );
                          return CheckboxListTile(
                            value: isSelected,
                            title: Text(widget.itemLabel(item)),
                            onChanged: (_) {
                              setState(() {
                                widget.onItemToggled(item);
                              });
                            },
                          );
                        },
                      ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.pop(context);
          },
          child: const Text('FECHAR'),
        ),
      ],
    );
  }
}
