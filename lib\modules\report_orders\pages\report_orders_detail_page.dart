import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/report_orders/pages/widgets/report_orders_search_detail.dart';

class ReportOrdersDetailPage extends StatelessWidget {
  const ReportOrdersDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ReportOrdersController>(
      "Ver pedido completo - ReportOrdersDetailPage",
      builder: (ctrl) {
        return Scaffold(
          backgroundColor: whiteColor,
          appBar: AppBar(
            backgroundColor: themesController.getPrimaryColor(),
            title: LabelWidget(
              title: "Pedido número ${ctrl.selected!.nrPedido!}",
              fontSize: DeviceSize.fontSize(18, 21),
              fontWeight: FontWeight.w600,
              textColor: whiteColor,
            ),
            leading: Icon<PERSON>utton(
              icon: const Icon(Icons.arrow_back, color: whiteColor),
              onPressed: () {
                Get.back();
              },
            ),
          ),
          body: SingleChildScrollView(
            child: SizedBox(
              width: MediaQuery.sizeOf(context).width,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    LabelWidget(
                      title: "Itens do pedido",
                      fontSize: DeviceSize.fontSize(12, 15),
                    ),
                    5.toHeightSpace(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children:
                          ctrl.selected!.itens!.map((e) {
                            return SizedBox(
                              width: MediaQuery.sizeOf(context).width,
                              child: Card(
                                elevation: 3,
                                child: Padding(
                                  padding: const EdgeInsets.all(12.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      LabelWidget(
                                        title: e.produto ?? "-",
                                        fontSize: DeviceSize.fontSize(13, 16),
                                        fontWeight: FontWeight.bold,
                                      ),
                                      LabelWidget(
                                        title: "EAN: ${e.ean ?? e.dUN ?? "-"}",
                                        fontSize: DeviceSize.fontSize(12, 15),
                                      ),
                                      10.toHeightSpace(),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          ReportOrdersSearchDetailWidget(
                                            title: "Preço de fábrica",
                                            value:
                                                (e.valorBrutoSolicitado! /
                                                        e.quantidadeSolicitada!)
                                                    .formatReal(),
                                          ),
                                          ReportOrdersSearchDetailWidget(
                                            title: "Cod. Produto",
                                            value: e.codigo?.toString() ?? '-',
                                          ),
                                          ReportOrdersSearchDetailWidget(
                                            title: "Cat. Produto",
                                            value: e.categoria ?? "-",
                                          ),
                                        ],
                                      ),
                                      10.toHeightSpace(),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Flexible(
                                            child:
                                                ReportOrdersSearchDetailWidget(
                                                  title:
                                                      "Quantidade\nSolicitada",
                                                  value:
                                                      e.quantidadeSolicitada
                                                          ?.toString() ??
                                                      "0",
                                                ),
                                          ),
                                          Flexible(
                                            child:
                                                ReportOrdersSearchDetailWidget(
                                                  title:
                                                      "Quantidade\nRespondida",
                                                  value:
                                                      e.quantidadeRespondida
                                                          ?.toString() ??
                                                      "0",
                                                ),
                                          ),
                                          Flexible(
                                            child:
                                                ReportOrdersSearchDetailWidget(
                                                  title: "Quantidade\nFaturada",
                                                  value:
                                                      e.quantidadeFaturada
                                                          ?.toString() ??
                                                      "0",
                                                ),
                                          ),
                                        ],
                                      ),
                                      10.toHeightSpace(),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Flexible(
                                            child: ReportOrdersSearchDetailWidget(
                                              title: "Desconto Solicitado",
                                              value:
                                                  "${e.descontoPrograma?.toString() ?? "0"} %",
                                            ),
                                          ),
                                          Flexible(
                                            child: ReportOrdersSearchDetailWidget(
                                              title: "Desconto Faturado",
                                              value:
                                                  "${e.descontoNotaFiscal?.toString() ?? "0"} %",
                                            ),
                                          ),
                                        ],
                                      ),
                                      10.toHeightSpace(),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Flexible(
                                            child:
                                                ReportOrdersSearchDetailWidget(
                                                  title: "Retorno",
                                                  value:
                                                      e.statusRetornoItem ??
                                                      "-",
                                                ),
                                          ),
                                          Flexible(
                                            child: ReportOrdersSearchDetailWidget(
                                              title:
                                                  "Motivo do Não\nAtendimento",
                                              value:
                                                  e.motivoDeNaoAtendimento ??
                                                  "-",
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          }).toList(),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
