import 'package:pharmalink/modules/orders/models/orders_products_list_model.dart';

extension OrdersProductsDiscountRangeExtensions
    on OrdersProductsDiscountRangeModel? {
  double getDiscountNegotiation() {
    double result = 0;
    if (this == null) return result;
    if (this!.comercialCondition == null) return result;
    result = (this!.comercialCondition!.percentualDescontoNegociacao1 ?? 0) +
        (this!.comercialCondition!.percentualDescontoNegociacao2 ?? 0) +
        (this!.comercialCondition!.percentualDescontoNegociacao3 ?? 0) +
        (this!.comercialCondition!.percentualDescontoNegociacao4 ?? 0);
    return result;
  }

  double getDiscountNegotiated() {
    double result = 0;
    if (this == null) return result;
    if (this!.comercialCondition == null) return result;
    result = this!.comercialCondition!.percentualDescontoNegociado!;

    return result;
  }

  double getDiscountManager() {
    double result = 0;
    if (this == null) return result;
    if (this!.comercialCondition == null) return result;
    result = this!.comercialCondition!.descontoGestor!;

    return result;
  }

  double getDiscountBase() {
    double result = 0;
    if (this == null) return result;
    if (this!.comercialCondition == null) return result;
    result = this!.comercialCondition!.percentualDescontoBase!;
    if (result > 99.99) result = 99.99;
    return result;
  }

  double getDiscountApply() {
    double result = 0;
    if (this == null) return result;
    if (this!.comercialCondition == null) return result;
    result = this!.comercialCondition!.descontoDisponivel!;
    if (result > 99.99) result = 99.99;
    return result;
  }

  double getDiscountRep() {
    double result = 0;
    if (this == null) return result;
    if (this!.comercialCondition == null) return result;
    result = this!.comercialCondition!.descontoDisponivel!;
    if (result > 99.99) result = 99.99;
    return result;
  }

  double getDiscountMaxRep() {
    double result = 0;
    if (this == null) return result;
    if (this!.comercialCondition == null) return result;
    result = this!.comercialCondition!.descontoGestor! +
        this!.comercialCondition!.descontoDisponivel! +
        this!.comercialCondition!.percentualDescontoBase!;
    if (result > 99.99) result = 99.99;
    return result;
  }

  double getDiscountMax() {
    double result = 0;
    if (this == null) return result;
    if (this!.comercialCondition == null) return result;
    result = this!.comercialCondition!.descontoGestor! +
        this!.comercialCondition!.descontoDisponivel!;
    if (result > 99.99) result = 99.99;
    return result;
  }

  double getDiscountRange() {
    double result = 0;
    if (this == null) return result;
    if (this!.comercialCondition == null) return result;
    result = this!.comercialCondition!.percentualDescontoNegociacao1! +
        this!.comercialCondition!.percentualDescontoNegociacao2! +
        this!.comercialCondition!.percentualDescontoNegociado! +
        this!.comercialCondition!.percentualDescontoNegociacao3! +
        this!.comercialCondition!.percentualDescontoNegociacao4! +
        this!.comercialCondition!.percentualDescontoCupom! +
        this!.comercialCondition!.percentualDescontoBase!;
    if (result > 99.99) result = 99.99;
    return result;
  }

  double getDiscountMin() {
    double result = 0;
    if (this == null) return result;
    if (this!.comercialCondition == null) return result;
    result = this!.comercialCondition!.percentualDescontoBase!;
    if (result > 99.99) result = 99.99;
    return result;
  }

  double getDiscountMinEsp() {
    double result = 0;
    if (this == null) return result;
    result = this!.discountMin!;
    if (result > 99.99) result = 99.99;
    return result;
  }

  double getDiscountMaxEsp() {
    double result = 0;
    if (this == null) return result;
    result = this!.discountMax!;
    if (result > 99.99) result = 99.99;
    return result;
  }
}
