import 'package:flutter/material.dart';

class OrderTypeColors {
  // Pedido Padrão (1)
  static const Color padraoBg = Color(0xFFE3F2FD);
  static const Color padraoText = Color(0xFF1565C0);

  // Pedido Especial (2)
  static const Color especialBg = Color(0xFFFFF3E0);
  static const Color especialText = Color(0xFFE65100);

  // Pedido Representante (4)
  static const Color representanteBg = Color(0xFFF1F8E9);
  static const Color representanteText = Color(0xFF33691E);

  static String getTypeOrderName(int id) {
    switch (id) {
      case 2:
        return "Pedido Especial";
      case 4:
        return "Pedido Representante";
      case 1:
      default:
        return "Pedido Padrão";
    }
  }

  static Color getBackgroundColor(int orderTypeId) {
    switch (orderTypeId) {
      case 2:
        return especialBg;
      case 4:
        return representanteBg;
      case 1:
      default:
        return padraoBg;
    }
  }

  static Color getTextColor(int orderTypeId) {
    switch (orderTypeId) {
      case 2:
        return especialText;
      case 4:
        return representanteText;
      case 1:
      default:
        return padraoText;
    }
  }
}
