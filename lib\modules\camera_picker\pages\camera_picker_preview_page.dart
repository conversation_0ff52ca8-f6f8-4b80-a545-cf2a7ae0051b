import 'dart:io';
import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/camera_picker/controller/camera_picker_controller.dart';
import 'package:pharmalink/modules/camera_picker/models/camera_picker_model.dart';
import 'package:pharmalink/modules/camera_picker/widgets/rotated_image_preview.dart';

class CameraPickerPreviewPage extends StatelessWidget {
  const CameraPickerPreviewPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<CameraPickerController>(
      "CameraPickerPreviewPage",
      builder: (ctrl) {
        return SafeArea(
          child: Scaffold(
            backgroundColor: Colors.black,
            body: OrientationBuilder(
              builder: (context, orientation) {
                return SizedBox(
                  width: double.infinity,
                  child: Column(
                    children: [
                      Expanded(
                        child: Stack(
                          alignment: Alignment.bottomCenter,
                          children: [
                            PhotoViewPreview(
                              imagePath: ctrl.fileCurrent!.path,
                              orientation: ctrl.orientation,
                            ),
                            Positioned(
                              top: 16,
                              left: 16,
                              child: CircleAvatar(
                                backgroundColor: Colors.grey.shade800,
                                maxRadius: 20,
                                minRadius: 20,
                                child: IconButton(
                                  icon: const FaIcon(FontAwesomeIcons.xmark),
                                  iconSize: 18,
                                  onPressed: () {
                                    Get.back();
                                  },
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Flexible(
                              child: CustomInkWell(
                                onTap: () {
                                  Get.back();
                                  Get.back();
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(2.0),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(30.0),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: LabelWidget(
                                      title: "Cancelar",
                                      fontSize: DeviceSize.fontSize(16, 20),
                                      textColor: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            const SizedBox(width: 10),
                            Flexible(
                              child: CustomInkWell(
                                onTap: () {
                                  try {
                                    if (ctrl.isProcessingPicture == false) {
                                      ctrl.setProcessingPicture(true);
                                      Get.back();
                                      Get.back(
                                        result: CameraPickerModel(
                                          file: File(ctrl.fileCurrent!.path),
                                          path: ctrl.fileCurrent!.path,
                                          name: ctrl.fileCurrent!.name,
                                          size:
                                              File(
                                                ctrl.fileCurrent!.path,
                                              ).lengthSync(),
                                          orientation: ctrl.orientation,
                                        ),
                                      );
                                      ctrl.setProcessingPicture(false);
                                    }
                                  } catch (e) {
                                    ctrl.setProcessingPicture(false);
                                  }
                                },
                                child: Container(
                                  padding: const EdgeInsets.all(2.0),
                                  decoration: BoxDecoration(
                                    border: Border.all(color: Colors.grey),
                                    borderRadius: BorderRadius.circular(30.0),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: LabelWidget(
                                      title: "Usar esta foto",
                                      fontSize: DeviceSize.fontSize(16, 20),
                                      textColor: Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        );
      },
    );
  }
}
