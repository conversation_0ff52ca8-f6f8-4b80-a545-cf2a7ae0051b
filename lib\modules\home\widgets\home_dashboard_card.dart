import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class HomeDashboardCard extends StatelessWidget {
  const HomeDashboardCard({
    super.key,
    this.width,
    required this.label,
    required this.value,
    this.cardBackgroundColor,
    this.labelColor,
  });
  final double? width;
  final String label;
  final String value;
  final Color? cardBackgroundColor;
  final Color? labelColor;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      child: Card(
        color: cardBackgroundColor ?? themesController.getPrimaryColor(),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              LabelWidget(
                title: label,
                fontSize: DeviceSize.fontSize(13, 16),
                textColor: labelColor ?? themesController.getMenuColor(),
              ),
              LabelWidget(
                title: value,
                fontSize: DeviceSize.fontSize(24, 30),
                textColor: labelColor ?? themesController.getMenuColor(),
              )
            ],
          ),
        ),
      ),
    );
  }
}
