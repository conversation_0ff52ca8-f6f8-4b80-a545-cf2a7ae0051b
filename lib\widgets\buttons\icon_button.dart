import 'package:pharmalink/exports/basic_exports.dart';

class IconButtonWidget extends StatefulWidget {
  const IconButtonWidget({
    super.key,
    this.title,
    required this.icon,
    this.onTap,
    this.textColor,
    this.width,
    this.iconBadge,
    this.colorButton,
    this.inline,
  });

  final String? title;
  final Icon icon;
  final Future<void> Function()? onTap;
  final Color? textColor;
  final double? width;
  final Icon? iconBadge;
  final Color? colorButton;
  final bool? inline;

  @override
  IconButtonWidgetState createState() => IconButtonWidgetState();
}

class IconButtonWidgetState extends State<IconButtonWidget> {
  bool _isProcessing = false;

  Future<void> _handleTap() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    if (widget.onTap != null) {
      await widget.onTap!();
    }

    setState(() {
      _isProcessing = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return CustomInkWell(
      onTap: _handleTap,
      child: widget.inline == true
          ? Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 2.h),
              child: Container(
                color: Colors.transparent,
                child: Row(
                  children: [
                    if (_isProcessing)
                      const SyncLoading()
                    else ...[
                      if (widget.iconBadge == null) widget.icon,
                      if (widget.iconBadge != null)
                        SizedBox(
                          width: 50.0,
                          height: 24.0,
                          child: Stack(
                            fit: StackFit.loose,
                            children: [
                              Positioned(top: 0, left: 0, child: widget.icon),
                              Positioned(
                                  top: 0, left: 26, child: widget.iconBadge!),
                            ],
                          ),
                        ),
                      if (widget.title != null) const Gap(10),
                      if (widget.title != null)
                        LabelWidget(
                          title: widget.title!,
                          fontSize: 12.sp,
                          textColor: widget.textColor ??
                              themesController.getIconColor(),
                        ),
                    ],
                  ],
                ),
              ),
            )
          : Container(
              color: widget.colorButton ?? Colors.transparent,
              width: widget.width,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 8.h),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (_isProcessing)
                      const SyncLoading()
                    else ...[
                      if (widget.iconBadge == null) widget.icon,
                      if (widget.iconBadge != null)
                        SizedBox(
                          width: 50.0,
                          height: 24.0,
                          child: Stack(
                            fit: StackFit.loose,
                            children: [
                              Positioned(top: 0, left: 0, child: widget.icon),
                              Positioned(
                                  top: 0, left: 26, child: widget.iconBadge!),
                            ],
                          ),
                        ),
                      if (widget.title != null) const Gap(5),
                      if (widget.title != null)
                        LabelWidget(
                          title: widget.title!,
                          fontSize: 12.sp,
                          textColor: widget.textColor ??
                              themesController.getIconColor(),
                        ),
                    ],
                  ],
                ),
              ),
            ),
    );
  }
}
