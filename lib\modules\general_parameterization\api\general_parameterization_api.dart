import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/general_parameterization/models/general_parameterization_model.dart';
import 'package:pharmalink/modules/general_parameterization/models/general_settings_order_discount_registration_model.dart';
import 'package:pharmalink/modules/general_parameterization/models/parameter_by_key_model.dart';
import 'package:pharmalink/modules/general_parameterization/models/system_parameterization_model.dart';
import 'package:pharmalink/modules/orders/models/orders_filter_default_model.dart';

abstract class IGeneralParameterizationApi {
  Future<HttpResponse<ProductParameterModel>> getParameters();
  Future<HttpResponse<List<ProductFilterDefaultModel>>> getParameterCustom();
  Future<HttpResponse<SystemParameterizationModel>> getSystemParameterization();
  Future<HttpResponse<GeneralSettingsOrderDiscountRegistrationModel>>
      getGeneralSettingsOrderDiscountRegistration();

  Future<HttpResponse<ParameterByKeyModel>> getParameterByKey(String key);
}

class GeneralParameterizationApi extends IGeneralParameterizationApi {
  final HttpManager _httpManager;
  GeneralParameterizationApi(this._httpManager);

  @override
  Future<HttpResponse<SystemParameterizationModel>>
      getSystemParameterization() async {
    return await _httpManager.request<SystemParameterizationModel>(
      path: 'parametrizacoesSistema/obterParametrizacoes',
      method: HttpMethods.post,
      body: {"userId": appController.userLogged!.userId},
      parser: (data) {
        return SystemParameterizationModel.fromJson(data);
      },
    );
  }

  @override
  Future<HttpResponse<ProductParameterModel>> getParameters() async {
    return await _httpManager.request<ProductParameterModel>(
      path: 'parametrizacoesGerais/listarParametrizacoesCadastro',
      method: HttpMethods.get,
      parser: (data) {
        return ProductParameterModel.fromJson(data);
      },
    );
  }

  @override
  Future<HttpResponse<GeneralSettingsOrderDiscountRegistrationModel>>
      getGeneralSettingsOrderDiscountRegistration() async {
    return await _httpManager
        .request<GeneralSettingsOrderDiscountRegistrationModel>(
      path: 'parametrizacoesGeraisCadastroDescontoPedido/listar',
      method: HttpMethods.get,
      parser: (data) {
        return GeneralSettingsOrderDiscountRegistrationModel.fromJson(data);
      },
    );
  }

  @override
  Future<HttpResponse<List<ProductFilterDefaultModel>>>
      getParameterCustom() async {
    return await _httpManager.request<List<ProductFilterDefaultModel>>(
      path: 'categoriaFiltroPersonalizado/listarCategoriasDeFiltroPedido',
      method: HttpMethods.get,
      parser: (data) {
        if (data is List) {
          return data
              .map((item) => ProductFilterDefaultModel.fromJson(item))
              .toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }

  @override
  Future<HttpResponse<ParameterByKeyModel>> getParameterByKey(
      String key) async {
    return await _httpManager.request<ParameterByKeyModel>(
      path: 'parametrizacoesGerais/obterParametrizacaoPorChave/$key',
      method: HttpMethods.get,
      parser: (data) {
        return ParameterByKeyModel.fromJson(data);
      },
    );
  }
}
