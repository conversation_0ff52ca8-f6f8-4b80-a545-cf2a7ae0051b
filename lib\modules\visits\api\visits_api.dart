import 'package:pharmalink/core/http_manager/http_manager.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/visits/models/visits_by_routes_response_model.dart';
import 'package:pharmalink/modules/visits/models/visits_goals_model.dart';
import 'package:pharmalink/modules/visits/models/visits_info_model.dart';
import 'package:pharmalink/modules/visits/models/visits_sync_request_model.dart';
import 'package:pharmalink/modules/visits/models/visits_sync_result_model.dart';

abstract class IVisitsApi {
  Future<HttpResponse<VisitsByRoutesResponseModel>> getVisitsByRoute(
      {required int routeId});

  Future<HttpResponse<List<VisitSyncResultModel>>> syncVisits(
      {required List<VisitsSyncRequestModel> model});

  Future<HttpResponse<List<VisitisInfoModel>>> getVisitsInfo();

  Future<HttpResponse<List<VisitGoalsModel>>> getVisitGoals(
      {required int routeId, required int storeId});
}

class VisitsApi extends IVisitsApi {
  final HttpManager _httpManager;
  VisitsApi(this._httpManager);

  @override
  Future<HttpResponse<VisitsByRoutesResponseModel>> getVisitsByRoute(
      {required int routeId}) async {
    return await _httpManager.request<VisitsByRoutesResponseModel>(
      path: 'visitas/obterRegistroVisitaPorIdRota/$routeId',
      method: HttpMethods.post,
      parser: (data) {
        return VisitsByRoutesResponseModel.fromJson(data);
      },
    );
  }

  @override
  Future<HttpResponse<List<VisitSyncResultModel>>> syncVisits(
      {required List<VisitsSyncRequestModel> model}) async {
    return await _httpManager.request<List<VisitSyncResultModel>>(
      path: 'visitas/salvarRegistrosVisitas',
      method: HttpMethods.post,
      bodyObject: model,
      parser: (data) {
        if (data is List) {
          return data
              .map((item) => VisitSyncResultModel.fromJson(item))
              .toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }

  @override
  Future<HttpResponse<List<VisitisInfoModel>>> getVisitsInfo() async {
    return await _httpManager.request<List<VisitisInfoModel>>(
      path: 'visitas/listarParametrizacaoVisita',
      method: HttpMethods.get,
      parser: (data) {
        if (data is List) {
          return data.map((item) => VisitisInfoModel.fromJson(item)).toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }

  @override
  Future<HttpResponse<List<VisitGoalsModel>>> getVisitGoals(
      {required int routeId, required int storeId}) async {
    return await _httpManager.request<List<VisitGoalsModel>>(
      path: 'visitas/obterObjetivoVisitaAnterior/$storeId/$routeId',
      method: HttpMethods.get,
      parser: (data) {
        if (data is List) {
          return data.map((item) => VisitGoalsModel.fromJson(item)).toList();
        } else {
          throw Exception(
              'Ocorreu um erro no retorno da api. Tente novamente!');
        }
      },
    );
  }
}
