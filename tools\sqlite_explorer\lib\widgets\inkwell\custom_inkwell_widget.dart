import 'package:flutter/material.dart';

class CustomInkWell extends StatefulWidget {
  final GestureTapCallback? onTap;
  final Widget child;
  final Color? splashColor;
  const CustomInkWell({
    super.key,
    this.onTap,
    required this.child,
    this.splashColor,
  });

  @override
  CustomInkWellState createState() => CustomInkWellState();
}

class CustomInkWellState extends State<CustomInkWell> {
  bool _isProcessing = false;

  Future<void> _handleTap() async {
    if (_isProcessing) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      if (widget.onTap != null) {
        widget.onTap!();
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        InkWell(
          onTap: _handleTap,
          splashColor: widget.splashColor,
          child: widget.child,
        ),
        // if (_isProcessing)
        //   Positioned.fill(
        //     child: Container(
        //       color: Colors.black.withOpacity(0.3),
        //       child: const Center(
        //         child: CircularProgressIndicator(),
        //       ),
        //     ),
        //   ),
      ],
    );
  }
}
