class DatabaseModels {
  static const loginResponseModel = 'LoginResponseModel';
  static const workspacesModel = 'WorkspacesModel';
  static const currentWorkspace = 'CurrentWorkspace';
  static const themesModel = 'themesModel';
  static const storesModel = 'storesModel';
  static const synchronizationsModel = 'synchronizationsModel';
  static const notificationModel = 'notificationModel';
  static const visitsByRoutesResponseModel = 'visitsByRoutesResponseModel';
  static const visitisInfoModel = 'visitisInfoModel';
  static const storesTakeModel = 'storesTakeModel';
  static const storeSelected = 'storeSelected';
  static const storeParametersModel = 'storeParametersModel';

  static const settingsAppModel = 'settingsAppModel';
  static const routersListModel = "routersListModel";
  static const logsHttpModel = 'logsHttpModel';
  static const productParameterModel = "productParameterModel";
  static const generalSettingsOrderDiscountRegistrationModel =
      "generalSettingsOrderDiscountRegistrationModel";
  static const productsMixModel = "productsMixModel";
  static const productFilterDefaultModel = "productFilterDefaultModel";
  static const ordersLocalDataModel = "ordersLocalDataModel";
  static const storeOrders = 'storeOrders';
  static const storesSync = 'storesSync';
  static const syncronization = 'syncronization';
  static const systemParameterizationPermissoesAcessoModel =
      'systemParameterizationPermissoesAcessoModel';
  static const researchesMerchanIndustryDataModel =
      'ResearchesMerchanIndustryDataModel';
  static const researchesProductIndustryDataModel =
      "ResearchesProductIndustryDataModel";

  static const researchesMerchanCompetitiveDataModel =
      "ResearchesMerchanCompetitiveDataModel";
  static const contentModel = 'contentModel';
  static const researchesProductConcurrentModel =
      'researchesProductConcurrentModel';
  static const researchesTradeMarketingModel = 'researchesTradeMarketingModel';
  static const researchesComplementaryModel = 'researchesComplementaryModel';
  static const researchesShareOfShelfModel = 'researchesShareOfShelfModel';
  static const startSyncResponseModel = 'startSyncResponseModel';
  static const getSyncResultModel = 'getSyncResultModel';
  static const visitGoalsModel = 'visitGoalsModel';
  static const synchronizationData = 'synchronizationData';
  static const syncResultItemsModel = 'syncResultItemsModel';
  static const tabloidProductsResponse = 'tabloidProductsResponse';
  static const getMixSpecialProductsResponse = 'getMixSpecialProductsResponse';
  static const parameterByKeyModel = 'parameterByKeyModel';
  static const logTraceMonitorModel = 'logTraceMonitorModel';
}
