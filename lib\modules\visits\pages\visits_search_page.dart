import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/export.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class VisitsSearchPage extends StatelessWidget {
  const VisitsSearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<VisitsController>("VisitsSearchPage",
        builder: (ctrl) {
      return ctrl.visitsSearch.isNotEmpty
          ? ListView.builder(
              itemCount: ctrl.visitsSearch.length,
              itemBuilder: (context, index) {
                return Container(
                  color: Colors.grey.shade600,
                  child: CustomInkWell(
                    onTap: ctrl.visitsSearch[index].onTap,
                    child: Card(
                      elevation: 3,
                      child: Container(
                        color: ctrl.visitsSearch[index].cardColor,
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 15.w,
                        ),
                        child: ListTile(
                          leading: SvgPicture.asset(
                            ctrl.visitsSearch[index].icon,
                            // color: Colors.grey.shade300,
                            width: DeviceSize.width(28, 32),
                            height: DeviceSize.height(28, 32),
                          ),
                          title: LabelWidget(
                            title: ctrl.visitsSearch[index].title,
                            textColor: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: DeviceSize.fontSize(18, 20),
                          ),
                          trailing: Icon(
                            Icons.chevron_right,
                            color: Colors.white,
                            size: DeviceSize.fontSize(24, 32),
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            )
          : const Center(
              child: Text("Nenhum resultado encontrado"),
            );
    });
  }
}
