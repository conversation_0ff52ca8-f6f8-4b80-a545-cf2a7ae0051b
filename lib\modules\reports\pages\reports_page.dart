import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class ReportsPage extends StatelessWidget {
  const ReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<ReportsController>("ReportsController",
        initState: (state) {
      Future.delayed(const Duration(milliseconds: 10), () {
        reportsController.setTypes();
      });
    }, builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: LabelWidget(
            title: "Relatórios",
            fontSize: DeviceSize.fontSize(18, 21),
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
          leadingWidth: 0,
          leading: const SizedBox.shrink(),
        ),
        body: Padding(
          padding: const EdgeInsets.all(8.0),
          child: ListView.builder(
            itemCount: ctrl.types.length, // por exemplo, 10 itens
            itemBuilder: (context, index) {
              final item = ctrl.types[index];
              return ListTile(
                leading: item.icon,
                title: LabelWidget(
                  title: item.title!,
                  fontSize: DeviceSize.fontSize(18, 21),
                ),
                trailing: const Icon(
                  Icons.navigate_next,
                  color: Colors.black,
                ),
                onTap: item.onTap!,
              );
            },
          ),
        ),
      );
    });
  }
}
