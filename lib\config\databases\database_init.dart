import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:archive/archive.dart';
import 'package:path/path.dart';
import 'package:pharmalink/config/databases/call_context.dart';
import 'package:pharmalink/config/databases/ilocal_db.dart';
import 'package:pharmalink/core/utils/dynatrace/instrumentado.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

class SqfLiteHub {
  static Future<void> init() async {
    if (Platform.isWindows) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }
  }
}

Database? _database;

class LocalDb with ILocalDbInstrumentado implements ILocalDb {
  Future get database async {
    if (_database != null) return _database;
    _database = await _initializeDb('Pharmalink.db');
    return _database;
  }

  @override
  Future<String> get databasePath async {
    final dbpath = await getDatabasesPath();
    return join(dbpath, 'Pharmalink.db');
  }

  Future _initializeDb(String filePath) async {
    final dbpath = await getDatabasesPath();
    final path = join(dbpath, filePath);
    return await openDatabase(
      path,
      version: 4,
      onCreate: _createDB,
      onUpgrade: _onUpgrade,
    );
  }

  FutureOr<void> _createDB(Database db, int version) async {
    db.execute('''
              CREATE TABLE LocalData (id INTEGER PRIMARY KEY,
              KEY VARCHAR(150) NOT NULL,
              WORKSPACE_ID INT NULL,
              STORE_ID INT NULL,
              USERID VARCHAR(50) NULL,
              HASHCODE VARCHAR(255) NULL,
              VALUE JSON NOT NULL,
              IS_ONLINE BIT NULL,
              CREATEAT DATETIME NULL)
              ''');

    await _createIndicesVersion4(db);
  }

  FutureOr<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Adicione aqui as alterações para a versão 2
      var columns = await db.rawQuery('PRAGMA table_info(LocalData)');
      bool isOnlineColumnExists =
          columns.any((column) => column['name'] == 'IS_ONLINE');

      if (!isOnlineColumnExists) {
        await db.execute('ALTER TABLE LocalData ADD COLUMN IS_ONLINE BIT NULL');
      }
    }
    if (oldVersion < 3) {
      // Adicione aqui as alterações para a versão 3
      var columns = await db.rawQuery('PRAGMA table_info(LocalData)');
      bool createAtColumnExists =
          columns.any((column) => column['name'] == 'CREATEAT');

      if (!createAtColumnExists) {
        await db
            .execute('ALTER TABLE LocalData ADD COLUMN CREATEAT DATETIME NULL');
        await db.execute(
            'UPDATE LocalData SET CREATEAT = datetime("now") WHERE CREATEAT IS NULL');
      }
    }
    if (oldVersion < 4) {
      // Criando índices na atualização para a versão 4
      await _createIndicesVersion4(db);
    }
  }

  Future<void> _createIndicesVersion4(Database db) async {
    // Índice para a coluna KEY
    try {
      await db.execute('CREATE INDEX idx_localdata_key ON LocalData (KEY)');
    } catch (e) {
      log("Índice idx_localdata_key já existe. Erro: $e");
    }

    // Índice para WORKSPACE_ID
    try {
      await db.execute(
          'CREATE INDEX idx_localdata_workspace_id ON LocalData (WORKSPACE_ID)');
    } catch (e) {
      log("Índice idx_localdata_workspace_id já existe. Erro: $e");
    }

    // Índice para STORE_ID
    try {
      await db.execute(
          'CREATE INDEX idx_localdata_store_id ON LocalData (STORE_ID)');
    } catch (e) {
      log("Índice idx_localdata_store_id já existe. Erro: $e");
    }

    // Índice para USERID
    try {
      await db
          .execute('CREATE INDEX idx_localdata_userid ON LocalData (USERID)');
    } catch (e) {
      log("Índice idx_localdata_userid já existe. Erro: $e");
    }

    // Índice para HASHCODE
    try {
      await db.execute(
          'CREATE INDEX idx_localdata_hashcode ON LocalData (HASHCODE)');
    } catch (e) {
      log("Índice idx_localdata_hashcode já existe. Erro: $e");
    }

    // Índice para IS_ONLINE
    try {
      await db.execute(
          'CREATE INDEX idx_localdata_is_online ON LocalData (IS_ONLINE)');
    } catch (e) {
      log("Índice idx_localdata_is_online já existe. Erro: $e");
    }

    // Índice para CREATEAT
    try {
      await db.execute(
          'CREATE INDEX idx_localdata_createat ON LocalData (CREATEAT)');
    } catch (e) {
      log("Índice idx_localdata_createat já existe. Erro: $e");
    }
  }

  @override
  Future<void> clearDataBase() async {
    final db = await database;
    db.rawQuery('PRAGMA busy_timeout=120000');
    await db!.rawDelete('DELETE FROM LocalData ');
    DBLogger.log('Limpar base de dados', textData: 'DELETE FROM LocalData');
  }

  @override
  Future<void> deleteByKey({
    String? key,
    int? workspaceId,
    String? userId,
    int? storeId,
    String? hashCode,
    bool? isOnline,
    bool? isLog,
    DateTime? createdAt,
  }) async {
    final db = await database;
    db.rawQuery('PRAGMA busy_timeout=120000');

    final whereClauses = <String>[];
    final whereArgs = <dynamic>[];

    if (key != null) {
      whereClauses.add('KEY = ?');
      whereArgs.add(key);
    }

    if (workspaceId != null) {
      whereClauses.add('WORKSPACE_ID = ?');
      whereArgs.add(workspaceId);
    }

    if (storeId != null) {
      whereClauses.add('STORE_ID = ?');
      whereArgs.add(storeId);
    }
    if (userId != null) {
      whereClauses.add('USERID = ?');
      whereArgs.add(userId);
    }
    if (hashCode != null) {
      whereClauses.add('HASHCODE = ?');
      whereArgs.add(hashCode);
    }

    if (isOnline != null) {
      whereClauses.add('IS_ONLINE = ?');
      whereArgs.add(isOnline ? 1 : 0); // Convert boolean to integer
    }
    if (createdAt != null) {
      whereClauses.add('CREATEAT <= ?');
      whereArgs.add(createdAt.toIso8601String());
    }

    if (whereClauses.isEmpty) {
      throw ArgumentError('Pelo menos um parâmetro deve ser fornecido.');
    }

    final whereStatement = whereClauses.join(' AND ');
    final sql = 'DELETE FROM LocalData WHERE $whereStatement';

    await db!.rawDelete(sql, whereArgs);
    if (isLog != true) {
      DBLogger.log('Excluir Registro: $key', textData: '$sql: $whereArgs');
    }
  }

  @override
  Future<void> addData({
    String? key,
    int? workspaceId,
    String? userId,
    int? storeId,
    String? hashCode,
    dynamic data,
    bool? clearCurrentData,
    bool? isOnline,
    bool? isLog,
  }) async {
    var (leaveAction, subAction) = dynatraceAction.subActionReport("addData");

    try {
      subAction.reportEvent("Obter instancia db");
      final db = await database;

      subAction.reportEvent("Executando query PRAGMA");
      db.rawQuery('PRAGMA busy_timeout=120000');

      if (clearCurrentData == true) {
        subAction.reportEvent("Removendo item antigo");
        await deleteByKey(
          key: key,
          workspaceId: workspaceId,
          userId: userId,
          storeId: storeId,
          hashCode: hashCode,
          isOnline: isOnline,
          isLog: true,
        );
      }

      if (data is List) {
        subAction.reportEvent("Inserindo em batch");
        await _batchInsert(db, key, workspaceId, storeId, userId, hashCode,
            data, isOnline, isLog);
      } else {
        subAction.reportEvent("Inserindo em single");
        await _singleInsert(db, key, workspaceId, storeId, userId, hashCode,
            data, isOnline, isLog);
      }
    } finally {
      leaveAction();
    }
  }

  Future<void> _batchInsert(
    Database db,
    String? key,
    int? workspaceId,
    int? storeId,
    String? userId,
    String? hashCode,
    List data,
    bool? isOnline,
    bool? isLog,
  ) async {
    final batch = db.batch();

    for (var element in data) {
      if (element is List) {
        for (var item in element) {
          _addInsertToBatch(batch, key, workspaceId, storeId, userId, hashCode,
              item, isOnline, isLog);
        }
      } else {
        _addInsertToBatch(batch, key, workspaceId, storeId, userId, hashCode,
            element, isOnline, isLog);
      }
    }

    await batch.commit(noResult: true);
  }

  void _addInsertToBatch(
    Batch batch,
    String? key,
    int? workspaceId,
    int? storeId,
    String? userId,
    String? hashCode,
    dynamic data,
    bool? isOnline,
    bool? isLog,
  ) {
    final jsonData = jsonEncode(data);
    final compressedData = GZipEncoder().encode(utf8.encode(jsonData));
    final compressedDataString = base64Encode(compressedData!);
    batch.insert(
      'LocalData',
      {
        'KEY': key,
        'WORKSPACE_ID': workspaceId,
        'STORE_ID': storeId,
        'USERID': userId,
        'HASHCODE': hashCode,
        'VALUE': compressedDataString,
        'IS_ONLINE': isOnline == true ? 1 : 0,
        'CREATEAT': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    // if (isLog != true) {
    //   DBLogger.log(
    //     'Salvar registro: $key',
    //     jsonData: jsonEncode(
    //       {
    //         'KEY': key,
    //         'WORKSPACE_ID': workspaceId,
    //         'STORE_ID': storeId,
    //         'USERID': userId,
    //         'HASHCODE': hashCode,
    //         'VALUE': jsonData,
    //         'IS_ONLINE': isOnline == true ? 1 : 0,
    //         'CREATEAT': DateTime.now().toIso8601String(),
    //       },
    //     ),
    //   );
    // }
  }

  Future<void> _singleInsert(
    Database db,
    String? key,
    int? workspaceId,
    int? storeId,
    String? userId,
    String? hashCode,
    dynamic data,
    bool? isOnline,
    bool? isLog,
  ) async {
    final jsonData = jsonEncode(data);
    final compressedData = GZipEncoder().encode(utf8.encode(jsonData));
    final compressedDataString = base64Encode(compressedData!);
    await db.insert(
      'LocalData',
      {
        'KEY': key,
        'WORKSPACE_ID': workspaceId,
        'STORE_ID': storeId,
        'USERID': userId,
        'HASHCODE': hashCode,
        'VALUE':
            compressedDataString, // Armazena os dados comprimidos como base64
        'IS_ONLINE': isOnline == true ? 1 : 0,
        'CREATEAT': DateTime.now().toIso8601String(),
      },
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
    // if (isLog != true) {
    //   DBLogger.log(
    //     'Salvar registro: $key',
    //     jsonData: jsonEncode(
    //       {
    //         'KEY': key,
    //         'WORKSPACE_ID': workspaceId,
    //         'STORE_ID': storeId,
    //         'USERID': userId,
    //         'HASHCODE': hashCode,
    //         'VALUE': jsonData,
    //         'IS_ONLINE': isOnline == true ? 1 : 0,
    //         'CREATEAT': DateTime.now().toIso8601String(),
    //       },
    //     ),
    //   );
    // }
  }

  @override
  Future<List<dynamic>> readAllByKey({
    String? key,
    int? workspaceId,
    String? userId,
    int? storeId,
    String? hashCode,
    bool? isOnline,
    DateTime? createdAt,
  }) async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("readAllByKey");

    try {
      subAction.reportEvent("Obter instancia db");
      final db = await database;

      subAction.reportEvent("Executando query PRAGMA");
      db.rawQuery('PRAGMA busy_timeout=120000');

      final whereClauses = <String>[];
      final whereArgs = <dynamic>[];

      if (key != null) {
        whereClauses.add('KEY = ?');
        whereArgs.add(key);
      }

      if (workspaceId != null) {
        whereClauses.add('WORKSPACE_ID = ?');
        whereArgs.add(workspaceId);
      }

      if (storeId != null) {
        whereClauses.add('STORE_ID = ?');
        whereArgs.add(storeId);
      }
      if (userId != null) {
        whereClauses.add('USERID = ?');
        whereArgs.add(userId);
      }
      if (hashCode != null) {
        whereClauses.add('HASHCODE = ?');
        whereArgs.add(hashCode);
      }
      if (isOnline != null) {
        whereClauses.add('IS_ONLINE = ?');
        whereArgs.add(isOnline ? 1 : 0);
      }
      if (createdAt != null) {
        whereClauses.add('CREATEAT <= ?');
        whereArgs.add(createdAt.toIso8601String());
      }

      if (whereClauses.isEmpty) {
        throw ArgumentError('Pelo menos um parâmetro deve ser fornecido.');
      }

      final whereStatement = whereClauses.join(' AND ');
      final sql = 'SELECT * FROM LocalData WHERE $whereStatement';

      subAction.reportEvent("Buscando tudo por key");
      final data = await db!.rawQuery(sql, whereArgs);
      subAction.reportEvent("Busca concluida");

      // Descomprimir os dados se necessário
      final decompressedData = data.map((row) {
        final value = row['VALUE'];
        try {
          final compressedValue = base64Decode(value);
          final decompressedValue =
              utf8.decode(GZipDecoder().decodeBytes(compressedValue));
          return {
            ...row,
            'VALUE': decompressedValue,
          };
        } catch (e) {
          // Se a descompressão falhar, assume que os dados não estão comprimidos
          return {
            ...row,
            'VALUE': value,
          };
        }
      }).toList();

      return decompressedData;
    } catch (e) {
      log("Erro ao ler dados por chave: $e");
      rethrow;
    } finally {
      leaveAction();
    }
  }

  @override
  Future<List<dynamic>> readAllData() async {
    var (leaveAction, subAction) =
        dynatraceAction.subActionReport("readAllData");

    try {
      subAction.reportEvent("Obter instancia db");
      final db = await database;

      subAction.reportEvent("Executando query PRAGMA");
      db.rawQuery('PRAGMA busy_timeout=120000');

      subAction.reportEvent("Buscando tudo");
      final allData = await db!.query('LocalData');
      subAction.reportEvent("Busca concluida");

      // Descomprimir os dados se necessário
      final decompressedData = allData.map((row) {
        final value = row['VALUE'];
        try {
          final compressedValue = base64Decode(value);
          final decompressedValue =
              utf8.decode(GZipDecoder().decodeBytes(compressedValue));
          return {
            ...row,
            'VALUE': decompressedValue,
          };
        } catch (e) {
          // Se a descompressão falhar, assume que os dados não estão comprimidos
          return {
            ...row,
            'VALUE': value,
          };
        }
      }).toList();

      return decompressedData;
    } catch (e) {
      log("Erro ao ler todos os dados: $e");
      rethrow;
    } finally {
      leaveAction();
    }
  }

  @override
  Future<dynamic> getById<T>({int? id}) async {
    final db = await database;
    db.rawQuery('PRAGMA busy_timeout=120000');
    final allData =
        await db!.rawQuery('SELECT * FROM LocalData WHERE id = ?', [id]);

    if (allData.isNotEmpty) {
      final row = allData.first;
      final value = row['VALUE'];
      try {
        final compressedValue = base64Decode(value);
        final decompressedValue =
            utf8.decode(GZipDecoder().decodeBytes(compressedValue));
        return {
          ...row,
          'VALUE': decompressedValue,
        };
      } catch (e) {
        // Se a descompressão falhar, assume que os dados não estão comprimidos
        return {
          ...row,
          'VALUE': value,
        };
      }
    }

    return null;
  }
}
