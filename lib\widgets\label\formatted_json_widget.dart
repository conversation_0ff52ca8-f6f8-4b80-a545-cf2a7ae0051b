import 'dart:convert';

import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class FormattedJsonWidget extends StatelessWidget {
  final String jsonString;

  const FormattedJsonWidget({required this.jsonString, super.key});

  @override
  Widget build(BuildContext context) {
    String formattedJson = _formatJson(jsonString);
    return SelectableText(
      formattedJson,
      style: TextStyle(fontSize: DeviceSize.fontSize(12, 14)),
    );
  }

  String _formatJson(String jsonString) {
    try {
      final jsonObject = json.decode(jsonString);
      const encoder = JsonEncoder.withIndent('  ');

      // Check if the root JSON object contains a 'VALUE' field
      if (jsonObject is Map<String, dynamic> &&
          jsonObject.containsKey('VALUE')) {
        final valueContent = jsonObject['VALUE'];
        if (valueContent is String) {
          try {
            final nestedJsonObject = json.decode(valueContent);
            jsonObject['VALUE'] =
                nestedJsonObject; // Store the nested JSON object
          } catch (e) {
            // If decoding fails, keep the original string
            jsonObject['VALUE'] = valueContent;
          }
        }
      }

      return encoder.convert(jsonObject);
    } catch (e) {
      return 'Invalid JSON';
    }
  }
}
