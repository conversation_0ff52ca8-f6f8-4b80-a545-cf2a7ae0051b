import 'package:camera/camera.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/camera_picker/controller/camera_picker_controller.dart';

class CameraPickerPage extends StatelessWidget {
  const CameraPickerPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<CameraPickerController>("CameraPickerPage",
        builder: (ctrl) {
      return SafeArea(
        child: Scaffold(
          backgroundColor: Colors.black,
          body: OrientationBuilder(
            builder: (context, orientation) {
              return SizedBox(
                width: double.infinity,
                child: Column(
                  children: [
                    Expanded(
                      child: Stack(
                        children: [
                          FutureBuilder<void>(
                            future: ctrl.initializeControllerFuture,
                            builder: (context, snapshot) {
                              if (snapshot.connectionState ==
                                  ConnectionState.done) {
                                return CameraPreview(ctrl.cameraController!);
                              } else {
                                return const Center(
                                    child: CircularProgressIndicator());
                              }
                            },
                          ),
                          Positioned(
                            top: 16,
                            left: 16,
                            child: IconButton(
                              icon: const FaIcon(
                                FontAwesomeIcons.xmark,
                                color: Colors.white,
                              ),
                              iconSize: 24,
                              onPressed: () {
                                Get.back();
                              },
                            ),
                          ),
                          Positioned(
                            top: 16,
                            right: 16,
                            child: IconButton(
                              icon: Icon(
                                ctrl.flashIcon,
                                color: Colors.white,
                              ),
                              iconSize: 24,
                              onPressed: () {
                                ctrl.turnLight();
                              },
                            ),
                          ),
                          Positioned(
                            bottom: 10,
                            right: 16,
                            child: CircleAvatar(
                              backgroundColor: Colors.grey.shade800,
                              maxRadius: 26,
                              minRadius: 26,
                              child: IconButton(
                                icon:
                                    const FaIcon(FontAwesomeIcons.cameraRotate),
                                iconSize: 24,
                                onPressed: () async {
                                  await ctrl.changeCamera();
                                },
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 10,
                            left: MediaQuery.of(context).size.width / 2.8,
                            child: IconButton(
                              iconSize: 80,
                              icon: const FaIcon(
                                FontAwesomeIcons.circleDot,
                                color: Colors.white54,
                              ),
                              onPressed: ctrl.isProcessingPicture
                                  ? null
                                  : () async {
                                      try {
                                        if (ctrl.isProcessingPicture == false) {
                                          ctrl.setProcessingPicture(true);

                                          Future.delayed(
                                              const Duration(milliseconds: 250),
                                              () async {
                                            await ctrl.capture(
                                                ctrl.selectedOption,
                                                orientation);
                                            ctrl.setProcessingPicture(false);
                                          });
                                        }
                                      } catch (e) {
                                        ctrl.setProcessingPicture(false);
                                      }
                                    },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
      );
    });
  }
}
