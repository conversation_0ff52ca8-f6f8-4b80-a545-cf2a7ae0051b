import 'package:pharmalink/core/utils/device_size.dart';
import 'package:pharmalink/core/utils/dynatrace/get_builder_instrumentado.dart';
import 'package:pharmalink/core/utils/load_widget.dart';
import 'package:pharmalink/core/utils/snackbar_custom.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';
import 'package:pharmalink/modules/q_atester/pages/q_atester_item_widget.dart';

class QATesterPage extends StatelessWidget {
  const QATesterPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilderInstrumentado<QATesterController>("QATesterPage",
        builder: (ctrl) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: LabelWidget(
            title: "QA - Teste",
            fontSize: DeviceSize.fontSize(18, 21),
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
          leading: Icon<PERSON>utton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              Get.back();
            },
          ),
        ),
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LabelWidget(
                  title: "QATester",
                  fontSize: 20.sp,
                ),
                20.toHeightSpace(),
                QaTesterItemWidget(
                  title: "Logins:",
                  value: ctrl.info.loginResponseModel?.toString() ?? "0",
                ),
                QaTesterItemWidget(
                  title: "Workspaces:",
                  value: ctrl.info.workspacesModel?.toString() ?? "0",
                ),
                QaTesterItemWidget(
                  title: "Workspace Atual:",
                  value: ctrl.info.currentWorkspace?.toString() ?? "0",
                ),
                QaTesterItemWidget(
                  title: "Themas",
                  value: ctrl.info.themesModel?.toString() ?? "0",
                ),
                QaTesterItemWidget(
                  title: "Lojas",
                  value: ctrl.info.storesModel?.toString() ?? "0",
                ),
                QaTesterItemWidget(
                  title: "Lojas Selecionadas",
                  value: ctrl.info.storesTakeModel?.toString() ?? "0",
                ),
                // QaTesterItemWidget(
                //   title: "storeSelected",
                //   value: ctrl.info.storeSelected?.toString() ?? "0",
                // ),
                QaTesterItemWidget(
                  title: "Parâmetros das Lojas",
                  value: ctrl.info.storeParametersModel?.toString() ?? "0",
                ),
                // QaTesterItemWidget(
                //   title: "settingsAppModel",
                //   value: ctrl.info.settingsAppModel?.toString() ?? "0",
                // ),
                // QaTesterItemWidget(
                //   title: "Rotas",
                //   value: ctrl.info.routersListModel?.toString() ?? "0",
                // ),
                QaTesterItemWidget(
                  title: "Produtos - Parâmetros",
                  value: ctrl.info.productParameterModel?.toString() ?? "0",
                ),
                QaTesterItemWidget(
                  title: "Produtos Mix Ideal",
                  value: ctrl.info.productsMixModel?.toString() ?? "0",
                ),
                QaTesterItemWidget(
                  title: "Filtros Defaults",
                  value: ctrl.info.productFilterDefaultModel?.toString() ?? "0",
                ),
                QaTesterItemWidget(
                  title: "Pedidos",
                  value: ctrl.info.storeOrders?.toString() ?? "0",
                ),
                QaTesterItemWidget(
                  title: "Permissões Acesso (Sistema)",
                  value: ctrl.info.systemParameterizationPermissoesAcessoModel
                          ?.toString() ??
                      "0",
                ),
                QaTesterItemWidget(
                  title: "Share of Shel",
                  value: ctrl.info.shareOfShelf?.toString() ?? "0",
                ),
                QaTesterItemWidget(
                  title: "Log de Auditoria",
                  value: ctrl.info.logTraceMonitorModel?.toString() ?? "0",
                ),
                PrimaryButtonWidget(
                  titleButtom: 'Resetar Pedidos',
                  isLoading: ctrl.loginButtonLoading,
                  onTap: () async {
                    await ctrl.clearOrders();
                  },
                ),
                PrimaryButtonWidget(
                  titleButtom: 'Resetar Visitas/Pesquisas',
                  isLoading: ctrl.loginButtonLoading,
                  onTap: () async {
                    await ctrl.clearVisits();
                  },
                ),
                PrimaryButtonWidget(
                  titleButtom: 'Resetar Rotas',
                  isLoading: ctrl.loginButtonLoading,
                  onTap: () async {
                    await ctrl.clearStores();
                  },
                ),
                PrimaryButtonWidget(
                  titleButtom: 'Resetar Logs de Auditoria',
                  isLoading: ctrl.loginButtonLoading,
                  onTap: () async {
                    await ctrl.clearLogTraceMonitor();
                  },
                ),
                PrimaryButtonWidget(
                  titleButtom: 'Gerar Logs de Auditoria',
                  isLoading: ctrl.loginButtonLoading,
                  onTap: () async {
                    await ctrl.generateLogTraceMonitor();
                  },
                ),
                PrimaryButtonWidget(
                  titleButtom: 'Testar Conexao',
                  isLoading: ctrl.loginButtonLoading,
                  onTap: () async {
                    await appController.testInternetConnection(
                        closeMessage: false);
                  },
                ),
                PrimaryButtonWidget(
                  titleButtom: 'Show Popup',
                  isLoading: ctrl.loginButtonLoading,
                  onTap: () async {
                    ctrl.showPopup();
                  },
                ),
                PrimaryButtonWidget(
                  titleButtom: 'Show Snack',
                  isLoading: ctrl.loginButtonLoading,
                  onTap: () async {
                    ctrl.showSnackbar(context);
                  },
                ),
                PrimaryButtonWidget(
                  titleButtom: 'Testar Instancias',
                  isLoading: ctrl.loginButtonLoading,
                  onTap: () async {
                    final load1 = PlkLoading();
                    final load2 = PlkLoading();
                    load1.show(title: AppStrings.load);
                    SnackbarCustom.snackbarError(AppStrings.noInternet);
                    Future.delayed(const Duration(seconds: 2), () {
                      load2.show(title: "Segundo");
                    });

                    Future.delayed(const Duration(seconds: 7), () {
                      load1.hide();
                    });

                    Future.delayed(const Duration(seconds: 10), () {
                      load2.hide();
                    });
                  },
                ),
                PrimaryButtonWidget(
                  titleButtom: 'Simular Rotas Dia Anterior',
                  isLoading: ctrl.loginButtonLoading,
                  onTap: () async {
                    await ctrl.simulateRoutesPreviousDay();
                  },
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
