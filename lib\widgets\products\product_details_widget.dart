import 'package:cached_network_image/cached_network_image.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/modules/stores_parameters/enuns/type_order_enum.dart';
import 'package:pharmalink/widgets/products/widget/product_info_widget.dart';

class ProductDetailsWidget extends StatefulWidget {
  const ProductDetailsWidget({super.key, required this.product});
  final ProductDetailModel product;

  @override
  State<ProductDetailsWidget> createState() => _ProductDetailsWidgetState();
}

class _ProductDetailsWidgetState extends State<ProductDetailsWidget> {
  final PageController _pageController = PageController(viewportFraction: 1);

  int _currentPage = 0;

  Widget _buildPageContent(int index) {
    // Retorne o conteúdo de cada página com base no índice
    switch (index) {
      case 0:
        return Wrap(
          spacing: 0.0,
          runSpacing: 4.0,
          children: [
            ProductDetailInfoWidget(
              title: "Quantidade Mínima",
              value: widget.product.qtyMin?.toString() ?? "0",
            ),
            ProductDetailInfoWidget(
              title: "Quantidade Utilizada",
              value: widget.product.qtyReal?.toString() ?? "0",
            ),
            ProductDetailInfoWidget(
              title: "Desconto Representante",
              value:
                  widget.product.discountRep?.formatPercentWithSymbol() ?? "0",
            ),
            ProductDetailInfoWidget(
              title: "Desconto Gestor",
              value:
                  widget.product.discountManager?.formatPercentWithSymbol() ??
                  "0%",
            ),
            ProductDetailInfoWidget(
              title: "Desconto Aplicado",
              value:
                  globalParams.getTypeOrderId() == TyperOrderEnum.padrao
                      ? "%"
                      : widget.product.discountApply
                              ?.formatPercentWithSymbol() ??
                          "0",
            ),
            if (ordersController.isOrderPad() || ordersController.isOrderRep())
              ProductDetailInfoWidget(
                title: "Preço Distribuidor",
                value: widget.product.priceDistributor ?? "Não",
              ),
            if ((ordersController.isOrderPad() ||
                    ordersController.isOrderRep()) &&
                ordersController.isShowStock())
              ProductDetailInfoWidget(
                title: "Estoque",
                value: widget.product.stock?.toString() ?? "-",
              ),
            ProductDetailInfoWidget(
              title: "Faixas de Desconto",
              value: widget.product.discountRange ?? "",
              valueFontSize: 16.sp,
            ),
            if (ordersController.isOrderPad() || ordersController.isOrderRep())
              ProductDetailInfoWidget(
                title: "Desconto Base",
                value:
                    widget.product.discountBase?.formatPercentWithSymbol() ??
                    "0",
              ),
            if (ordersController.isOrderPad() || ordersController.isOrderRep())
              ProductDetailInfoWidget(
                title: "Desconto Negociado",
                value:
                    widget.product.discountNegotiated
                        ?.formatPercentWithSymbol() ??
                    "0",
              ),
            if (ordersController.isOrderPad() || ordersController.isOrderRep())
              ProductDetailInfoWidget(
                title: "Desconto Negociação",
                value:
                    widget.product.discountNegotiation
                        ?.formatPercentWithSymbol() ??
                    "0",
              ),
          ],
        );
      case 1:
      default:
        return widget.product.metricaMdtr != null
            ? Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                LabelWidget(title: "Média Trimestre", fontSize: 12.sp),
                5.toHeightSpace(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    LabelWidget(
                      title:
                          "Bruto: ${widget.product.metricaMdtr!.vlrMedia?.formatReal() ?? ""}",
                      fontSize: 14.sp,
                    ),
                    20.toWidthSpace(),
                    LabelWidget(
                      title:
                          "Unit: ${widget.product.metricaMdtr!.qtdMedia ?? ""}",
                      fontSize: 14.sp,
                    ),
                  ],
                ),
                20.toHeightSpace(),
                LabelWidget(title: "Objetivo", fontSize: 12.sp),
                5.toHeightSpace(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    LabelWidget(
                      title:
                          "Bruto: ${widget.product.metricaMdtr!.vlrObjetivo?.formatReal() ?? ""}",
                      fontSize: 14.sp,
                    ),
                    20.toWidthSpace(),
                    LabelWidget(
                      title:
                          "Unit: ${widget.product.metricaMdtr!.qtdObjetivo ?? ""}",
                      fontSize: 14.sp,
                    ),
                  ],
                ),
                20.toHeightSpace(),
                LabelWidget(title: "Realizado", fontSize: 12.sp),
                5.toHeightSpace(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    LabelWidget(
                      title:
                          "Bruto: ${widget.product.metricaMdtr!.vlrRealizado?.formatReal() ?? ""}",
                      fontSize: 14.sp,
                    ),
                    20.toWidthSpace(),
                    LabelWidget(
                      title:
                          "Unit: ${widget.product.metricaMdtr!.qtdRealizado ?? ""}",
                      fontSize: 14.sp,
                    ),
                  ],
                ),
              ],
            )
            : const Center(child: Text("Sem descrição"));
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    return SingleChildScrollView(
      child: SizedBox(
        height: size.height * 0.82,
        child: Stack(
          children: [
            Positioned(
              top: 60,
              left: 0,
              child: Container(
                width: size.width,
                height: size.height,
                color: Colors.white,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    70.toHeightSpace(),
                    LabelWidget(
                      title: widget.product.name,
                      fontSize: 20.sp,
                      fontWeight: FontWeight.bold,
                      textAlign: TextAlign.center,
                    ),
                    LabelWidget(
                      title:
                          '${widget.product.codeLabel}: ${widget.product.code}',
                      fontSize: 14.sp,
                      fontWeight: FontWeight.normal,
                    ),
                    20.toHeightSpace(),
                    Visibility(
                      visible: ordersController.canShowMdtr(
                        widget.product.metricaMdtr,
                      ),
                      child: DotsIndicator(
                        dotsCount: 2,
                        position: _currentPage.toDouble(),
                        decorator: const DotsDecorator(
                          activeColor: Colors.blue,
                        ),
                      ),
                    ),
                    20.toHeightSpace(),
                    SizedBox(
                      height: 400,
                      child: PageView.builder(
                        controller: _pageController,
                        onPageChanged: (int page) {
                          setState(() {
                            _currentPage = page;
                          });
                        },
                        itemBuilder: (context, index) {
                          return _buildPageContent(index);
                        },
                        itemCount:
                            ordersController.canShowMdtr(
                                  widget.product.metricaMdtr,
                                )
                                ? 2
                                : 1,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            Positioned(
              top: 0,
              left: size.width * 0.37,
              child: _buildProductCircleImage(),
            ),

            // ... Continue listando as outras informações do produto
          ],
        ),
      ),
    );
  }

  Widget _buildProductCircleImage() {
    return Container(
      width: 120, // O dobro do raio
      height: 120, // O dobro do raio
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(2, 2), // Mudanças de posição da sombra
          ),
        ],
      ),
      child: CircleAvatar(
        radius: 60,
        backgroundColor: Colors.white,
        child:
            widget.product.photo != null
                ? CachedNetworkImage(
                  fit: BoxFit.fitWidth,
                  imageUrl: widget.product.photo ?? "",
                  placeholder:
                      (context, url) => Image.asset(AppImages.productGeneric),
                  errorWidget:
                      (context, url, error) =>
                          Image.asset(AppImages.productGeneric),
                )
                : Image.asset(
                  AppImages.productGeneric,
                ), // Caminho para a imagem padrão
      ),
    );
  }
}
