import com.android.build.gradle.AppExtension

val android = project.extensions.getByType(AppExtension::class.java)

android.apply {
    flavorDimensions("flavor-type")

    productFlavors {
        create("itrade") {
            dimension = "flavor-type"
            applicationId = "br.com.interplayers.itrade"
            resValue(type = "string", name = "app_name", value = "iTrade")
        }
        create("pharmalink") {
            dimension = "flavor-type"
            applicationId = "br.com.interplayers.pharmalink"
            resValue(type = "string", name = "app_name", value = "Pharmalink")
        }
    }
}