# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Android related
/android/**/gradle-wrapper.jar
/android/.gradle
/android/captures/
/android/gradlew
/android/gradlew.bat
/android/local.properties
/android/**/GeneratedPluginRegistrant.java
#/android/key.properties
/android/.idea/
# *.jks

# iOS/XCode related
/ios/**/*.mode1v3
/ios/**/*.mode2v3
/ios/**/*.moved-aside
/ios/**/*.pbxuser
/ios/**/*.perspectivev3
/ios/**/*sync/
/ios/**/.sconsign.dblite
/ios/**/.tags*
/ios/**/.vagrant/
/ios/**/DerivedData/
/ios/**/Icon?
/ios/**/Pods/
/ios/**/.symlinks/
/ios/**/profile
/ios/**/xcuserdata
/ios/.generated/
/ios/Flutter/App.framework
/ios/Flutter/Flutter.framework
/ios/Flutter/Flutter.podspec
/ios/Flutter/Generated.xcconfig
/ios/Flutter/app.flx
/ios/Flutter/app.zip
/ios/Flutter/flutter_assets/
/ios/Flutter/flutter_export_environment.sh
/ios/ServiceDefinitions.json
/ios/Runner/GeneratedPluginRegistrant.*
/.vs

# FVM Version Cache
.fvm/

# Outros
dist/
.ruby-version
/ios/build/
.venv/
