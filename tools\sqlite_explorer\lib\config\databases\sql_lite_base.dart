import 'dart:convert';
import 'dart:developer';

import 'package:sqlite_explorer/app_constants.dart';

abstract class SqfLiteBase<T> {
  late String keyModel;
  SqfLiteBase(this.keyModel);

  Future<void> save({int? storeId, bool? clearCurrentData}) async {
    await dbContext.addData(
        key: keyModel,
        // workspaceId: appController.workspace!.workspaceId,
        storeId: storeId,
        clearCurrentData: clearCurrentData,
        data: this);
  }

  Future<void> saveByKey({required String key, bool? clearCurrentData}) async {
    await dbContext.addData(
        key: key,
        // workspaceId: appController.workspace!.workspaceId,
        data: this,
        clearCurrentData: clearCurrentData);
  }

  Future<T?> get(T Function(Map<String, dynamic>) from<PERSON>son) async {
    var list = await getAll<T>(from<PERSON>son);
    return list.first;
  }

  // ignore: avoid_shadowing_type_parameters
  Future<List<T>> getAll<T>(
    T Function(Map<String, dynamic>) from<PERSON>son, {
    int? workspaceId,
    int? storeId,
    String? userId,
    String? hashCode,
    String? key,
    bool? isOnline,
  }) async {
    try {
      var resultList = await dbContext.readAllByKey(
        key: key ?? keyModel,
        workspaceId: workspaceId,
        storeId: storeId,
        userId: userId,
        hashCode: hashCode,
        isOnline: isOnline,
      );

      return resultList.expand((e) {
        try {
          final decodedData = jsonDecode(e['VALUE']);
          if (decodedData == null) {
            return <T>[];
          }
          if (decodedData is List) {
            return decodedData.map((item) {
              if (item is Map<String, dynamic>) {
                return fromJson(item);
              }
              return null;
            }).whereType<T>();
          }
          if (decodedData is Map<String, dynamic>) {
            return [fromJson(decodedData)];
          }
          return <T>[];
        } catch (ex) {
          return <T>[];
        }
      }).toList();
    } catch (e) {
      log('Error in getAll: $e');
      return <T>[];
    }
  }

  // ignore: avoid_shadowing_type_parameters
  Future<List<T>> getAllByKey<T>(
      String key,
      int? workspaceId,
      int? storeId,
      String? userId,
      String? hashCode,
      T Function(Map<String, dynamic>) fromJson) async {
    var resultList = await dbContext.readAllByKey(
      key: key,
      workspaceId: workspaceId,
      storeId: storeId,
      userId: userId,
      hashCode: hashCode,
    );
    return resultList
        .map((e) {
          final decodedData = jsonDecode(e['VALUE']);
          if (decodedData is List) {
            return decodedData
                .map((item) => fromJson(item[0] as Map<String, dynamic>))
                .toList() as T;
          }
          return fromJson(decodedData as Map<String, dynamic>);
        })
        .where((e) => e != null)
        .cast<T>()
        .toList();
  }
  // Mais métodos podem ser adicionados aqui, como delete, getByWhere, etc.
}
