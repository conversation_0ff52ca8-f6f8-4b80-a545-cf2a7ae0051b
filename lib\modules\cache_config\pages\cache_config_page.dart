import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class CacheConfigPage extends StatelessWidget {
  const CacheConfigPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GetBuilder<CacheConfigController>(builder: (controller) {
      return Scaffold(
        backgroundColor: whiteColor,
        appBar: AppBar(
          backgroundColor: themesController.getPrimaryColor(),
          title: LabelWidget(
            title: "Cache Config",
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            textColor: whiteColor,
          ),
          leading: Icon<PERSON>utton(
            icon: const Icon(
              Icons.arrow_back,
              color: whiteColor,
            ),
            onPressed: () {
              GetC.close();
            },
          ),
        ),
        body: Column(
          children: [
            const SizedBox(height: 32),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: LabelWidget(
                title:
                    "Atenção! Você está prestes a redefinir o cache do aplicativo.",
                textAlign: TextAlign.center,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            const Padding(
              padding: EdgeInsets.symmetric(horizontal: 24),
              child: LabelWidget(
                fontSize: 16,
                title: "Esta ação irá:\n\n"
                    "• Limpar todos os dados armazenados localmente\n"
                    "• Restaurar o aplicativo para as configurações originais\n"
                    "• Encerrar sua sessão atual\n\n"
                    "Após a redefinição, você precisará:\n\n"
                    "• Selecionar novamente seu workspace\n"
                    "• Fazer login novamente na sua conta\n"
                    "• Reconfigurar suas preferências",
                textAlign: TextAlign.left,
              ),
            ),
            const Spacer(),
            PrimaryButtonWidget(
              titleButtom: "Redefinir Cache",
              onTap: () async {
                Dialogs.confirm(
                  "Redefinir Cache",
                  "Você tem certeza que deseja redefinir o cache?",
                  onPressedOk: () async {
                    GetC.close();
                    controller.resetCache();
                    Get.offAllNamed(RoutesPath.workspaces);
                  },
                  onPressedCancel: () {
                    GetC.close();
                  },
                  buttonNameCancel: "Não",
                  buttonNameOk: "Enviar",
                );
              },
            ),
          ],
        ),
      );
    });
  }
}
