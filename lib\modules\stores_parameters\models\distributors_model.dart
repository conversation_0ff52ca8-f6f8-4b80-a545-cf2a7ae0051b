import 'package:pharmalink/modules/orders_resume/models/order_sync_model.dart';
import 'package:pharmalink/modules/stores_parameters/models/distribuidor_model.dart';

class DistributorsModel {
  int? distributorsId;
  String? name;
  int? ordemDePreferencia;
  int? ordemSelected;
  int? ordemMelhorAtendimento;

  DistribuidorModel? distribuidor;
  bool? isSelected;
  OrdersSyncPayLoadModel? payLoad;

  DistributorsModel({
    this.distributorsId,
    this.name,
    this.ordemDePreferencia,
    this.ordemMelhorAtendimento,
    this.ordemSelected,
    this.distribuidor,
    this.isSelected,
    this.payLoad,
  });
}
