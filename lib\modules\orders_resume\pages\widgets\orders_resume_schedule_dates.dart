import 'package:pharmalink/exports/basic_exports.dart';
import 'package:pharmalink/exports/get_exports.dart';
import 'package:pharmalink/modules/app_modules.dart';

class OrdersResumeScheduleDateWidget extends StatelessWidget {
  const OrdersResumeScheduleDateWidget({
    super.key,
    required this.date,
  });
  final DateTime date;
  @override
  Widget build(BuildContext context) {
    return GetBuilder<OrdersResumeController>(builder: (ctrl) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                LabelWidget(
                  title: date.formatDate(formatType: DateFormatType.ddMMyyyy),
                  fontSize: 16.sp,
                ),
                IconButton(
                    onPressed: () => ctrl.removeDate(date),
                    icon: Icon(
                      FontAwesomeIcons.xmark,
                      color: themesController.getPrimaryColor(),
                    ))
              ],
            ),
            const Divider(thickness: 1)
          ],
        ),
      );
    });
  }
}
