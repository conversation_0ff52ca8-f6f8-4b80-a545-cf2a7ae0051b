import 'package:pharmalink/config/databases/database_models.dart';
import 'package:pharmalink/config/databases/sql_lite_base.dart';
import 'package:pharmalink/core/enuns/sync_enum.dart';
import 'package:pharmalink/exports/basic_exports.dart';

class ResearchesShareOfShelfModel
    extends SqfLiteBase<ResearchesShareOfShelfModel> {
  int? workspaceId;
  int? storeId;
  int? routeId;
  int? isSync;
  int? idPesquisa;
  String? descricao;
  int? idCiclo;
  String? ciclo;
  int? idSubCanalVenda;
  String? subCanalVenda;
  int? idRede;
  String? rede;
  String? tamanhoPdv;
  String? status;
  List<ResearchesShareOfShelfProdutos>? produtos;
  List<int>? pdvRelacionadosPesquisa;

  ResearchesShareOfShelfModel({
    this.workspaceId,
    this.storeId,
    this.routeId,
    this.isSync,
    this.idPesquisa,
    this.descricao,
    this.idCiclo,
    this.ciclo,
    this.idSubCanalVenda,
    this.subCanalVenda,
    this.idRede,
    this.rede,
    this.tamanhoPdv,
    this.status,
    this.produtos,
    this.pdvRelacionadosPesquisa,
  }) : super(DatabaseModels.researchesShareOfShelfModel);

  ResearchesShareOfShelfModel.fromJson(Map<String, dynamic> json)
      : super(DatabaseModels.researchesShareOfShelfModel) {
    workspaceId = json['workspaceId'];
    storeId = json['storeId'];
    routeId = json['routeId'];
    isSync = json['isSync'] ?? SyncEnum.none;
    idPesquisa = json['IdPesquisa'];
    descricao = json['Descricao'];
    idCiclo = json['IdCiclo'];
    ciclo = json['Ciclo'];
    idSubCanalVenda = json['IdSubCanalVenda'];
    subCanalVenda = json['SubCanalVenda'];
    idRede = json['IdRede'];
    rede = json['Rede'];
    tamanhoPdv = json['TamanhoPdv'];
    status = json['Status'];
    if (json['Produtos'] != null) {
      produtos = <ResearchesShareOfShelfProdutos>[];
      json['Produtos'].forEach((v) {
        produtos!.add(ResearchesShareOfShelfProdutos.fromJson(v));
      });
    }
    pdvRelacionadosPesquisa = json['PdvRelacionadosPesquisa'].cast<int>();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workspaceId'] = workspaceId;
    data['storeId'] = storeId;
    data['routeId'] = routeId;
    data['isSync'] = isSync ?? SyncEnum.none;
    data['IdPesquisa'] = idPesquisa;
    data['Descricao'] = descricao;
    data['IdCiclo'] = idCiclo;
    data['Ciclo'] = ciclo;
    data['IdSubCanalVenda'] = idSubCanalVenda;
    data['SubCanalVenda'] = subCanalVenda;
    data['IdRede'] = idRede;
    data['Rede'] = rede;
    data['TamanhoPdv'] = tamanhoPdv;
    data['Status'] = status;
    if (produtos != null) {
      data['Produtos'] = produtos!.map((v) => v.toJson()).toList();
    }
    data['PdvRelacionadosPesquisa'] = pdvRelacionadosPesquisa;

    return data;
  }

  Future<ResearchesShareOfShelfModel> getFirst() async {
    var list = await getAll<ResearchesShareOfShelfModel>(
        ResearchesShareOfShelfModel.fromJson);
    return list.first;
  }

  Future<List<ResearchesShareOfShelfModel>> getList() async {
    var list = await getAll<ResearchesShareOfShelfModel>(
        workspaceId: appController.workspace!.workspaceId!,
        ResearchesShareOfShelfModel.fromJson);
    return list;
  }

  Future<List<ResearchesShareOfShelfModel>> getListToSync(
      {required int workspaceId}) async {
    var list = await getAll<ResearchesShareOfShelfModel>(
        workspaceId: workspaceId, ResearchesShareOfShelfModel.fromJson);
    return (list.isNotEmpty &&
            list.any((element) => element.isSync == SyncEnum.awaited))
        ? list.where((element) => element.isSync == SyncEnum.awaited).toList()
        : [];
  }
}

class ResearchesShareOfShelfProdutos {
  int? idPesquisaProduto;
  int? idPesquisa;
  int? idProduto;
  String? produto;
  int? idFamilia;
  String? familia;
  double? shareIdeal;
  double? shareApurado;
  int? facingGondola;
  int? facingProduto;
  String? image;
  bool? imageConvert;
  int? tipoResposta;

  ResearchesShareOfShelfProdutos({
    this.idPesquisaProduto,
    this.idPesquisa,
    this.idProduto,
    this.produto,
    this.idFamilia,
    this.familia,
    this.shareIdeal,
    this.shareApurado,
    this.facingGondola,
    this.facingProduto,
    this.image,
    this.imageConvert,
    this.tipoResposta,
  });

  ResearchesShareOfShelfProdutos.fromJson(Map<String, dynamic> json) {
    idPesquisaProduto = json['IdPesquisaProduto'];
    idPesquisa = json['IdPesquisa'];
    idProduto = json['IdProduto'];
    produto = json['Produto'];
    idFamilia = json['IdFamilia'];
    familia = json['Familia'];
    shareIdeal = json['ShareIdeal'];
    shareApurado = json['ShareApurado'];
    facingGondola = json['FacingGondola'];
    facingProduto = json['FacingProduto'];
    image = json['Imagem'];
    imageConvert = json['ImageConvert'];
    tipoResposta = json['TipoResposta'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['IdPesquisaProduto'] = idPesquisaProduto;
    data['IdPesquisa'] = idPesquisa;
    data['IdProduto'] = idProduto;
    data['Produto'] = produto;
    data['IdFamilia'] = idFamilia;
    data['Familia'] = familia;
    data['ShareIdeal'] = shareIdeal;
    data['ShareApurado'] = shareApurado;
    data['FacingGondola'] = facingGondola;
    data['FacingProduto'] = facingProduto;
    data['Imagem'] = image;
    data['ImageConvert'] = imageConvert;
    data['TipoResposta'] = tipoResposta;
    return data;
  }
}
